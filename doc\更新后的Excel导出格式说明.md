# 更新后的Excel导出格式说明

## 概述

根据提供的图片，严格调整了Excel导出格式为两Sheet格式：比对结果Sheet + 一致文件列表Sheet，包含不同颜色和样式的文字说明，确保格式完全符合要求。

## 列头结构

### 主要列头（严格按照图片）
| 序号 | 列名 | 说明 |
|------|------|------|
| 0 | 服务器类型 | 基线服务器/目标服务器 |
| 1 | IP | 服务器IP地址 |
| 2 | hostname | 主机名 |
| 3 | 汇总 | 总文件数量 |
| 4 | 缺失 | 缺失文件数量 |
| 5 | 多出 | 多出文件数量 |
| 6 | 不一致 | 不一致文件数量 |
| 7 | 一致 | 一致文件数量 |

## Excel格式结构

### 两Sheet格式：

#### Sheet1: 比对结果
包含四个区域：

##### 1. 标题区域（黑色粗体）
- **位置**：顶部区域
- **格式**：合并A-H列，左对齐，黑色粗体
- **内容**：
  - 环境一致性比对结果报告
  - 比对对象：************* vs *************
  - 比对时间：2025-07-22 15:39:55

##### 2. 说明文字区域（红色粗体）
- **位置**：标题区域下方
- **格式**：合并A-H列，左对齐，红色粗体
- **内容**：
  - 比对规则:分别生成基线和抽检服务显应用目录文件的MDS，然后比对MD5
  - 计算公式:基线汇总数=摘检汇总数-铀检多出数 +抽检缺失数
  - 路径说明:
  - 缺失路径:显示的是基线服务器的绝对路径信息，即抽检服务器和基线服务器相比，抽检服务器缺失的文件信息
  - 多出路径:显示的是抽检服务器的绝对路径信息，即抽检服务器和基线服务器相比，抽检服务器缺失的文件信息
  - 不一致路径:显示的是抽检服务器的绝对路经信自

##### 3. 汇总表格区域
- **位置**：说明文字下方
- **格式**：8列表格，居中对齐，列头有蓝色背景
- **内容**：
  - 第一行：基线服务器统计信息（只显示汇总数，缺失/多出/不一致/一致列为空）
  - 第二行：目标服务器统计信息（显示完整统计数据）

##### 4. 详细文件列表区域（🆕 增强功能）
- **位置**：汇总表格下方
- **格式**：
  - 类型列：单独一列，居左对齐
  - 文件路径列：合并B-G列，居左对齐，完整边框
  - 🆕 **差异说明列**：H列，详细说明文件差异原因
- **🎨 背景颜色区分**：
  - 缺失文件：浅蓝色背景
  - 多出文件：浅绿色背景
  - 不一致文件：浅黄色背景
  - 一致文件：浅灰色背景
- **内容**：
  - 缺失文件列表（差异说明：文件被删除）
  - 多出文件列表（差异说明：新增文件）
  - 不一致文件列表（差异说明：具体差异原因）

#### Sheet2: 一致文件列表（🆕 增强功能）
- **格式**：与详细文件列表相同，包含差异说明列
- **背景颜色**：浅灰色背景，便于区分
- **内容**：所有一致的文件，类型均为"一致"，差异说明为"文件完全一致"

## 🆕 最新功能增强

### 差异说明列
在文件列表区域新增"差异说明"列，提供详细的差异原因说明：

#### 差异说明内容
| 文件类型 | 差异说明 | 说明 |
|----------|----------|------|
| 缺失文件 | 文件被删除 | 基线服务器有该文件，目标服务器没有 |
| 多出文件 | 新增文件 | 目标服务器有该文件，基线服务器没有 |
| 不一致文件 | 具体差异原因 | 根据比较策略显示具体差异 |
| 一致文件 | 文件完全一致 | 所有比较维度都相同 |

#### 不一致文件的差异说明
- **MD5_ONLY策略**：文件内容变化（MD5不同）
- **COMPREHENSIVE策略**：
  - 文件大小不同
  - 权限不同
  - MD5值不同
  - 文件大小不同、权限不同（多项差异组合）

### 背景颜色区分
为不同类型的文件行设置不同的背景颜色，提供直观的视觉区分：

#### 颜色方案
| 文件类型 | 背景颜色 | 视觉效果 |
|----------|----------|----------|
| 缺失文件 | 浅蓝色 | 突出显示被删除的文件 |
| 多出文件 | 浅绿色 | 突出显示新增的文件 |
| 不一致文件 | 浅黄色 | 突出显示需要关注的差异文件 |
| 一致文件 | 浅灰色 | 低调显示正常的文件 |

### 列结构调整
为了容纳新的"差异说明"列，调整了列的合并范围：
- **文件路径列**：从原来的B-H列合并，调整为B-G列合并
- **差异说明列**：新增H列，专门显示差异说明信息
- **列宽优化**：为差异说明列设置25个字符宽度，确保内容完整显示

## 重要更新

### 统计数据显示规则
- **基线服务器行**：只显示服务器类型、IP、hostname和汇总数，缺失/多出/不一致/一致列为空
- **目标服务器行**：显示完整的统计数据，包括相对于基线服务器的缺失、多出、不一致、一致文件数量
- **统计逻辑**：所有统计数据都是基于基线服务器作为参照标准计算得出

### 列头结构说明
1. **服务器类型**：基线服务器/目标服务器
2. **IP**：服务器IP地址
3. **hostname**：服务器主机名
4. **汇总**：文件总数
5. **缺失**：缺失文件数量（基线服务器为空）
6. **多出**：多出文件数量（基线服务器为空）
7. **不一致**：不一致文件数量（基线服务器为空）
8. **一致**：一致文件数量（基线服务器为空）

## 方法调用

### 1. 带完整IP和主机名信息的调用
```java
@Autowired
private FileComparisonComponent fileComparisonComponent;

// 调用新的方法，传递完整信息
FileComparisonRequestDto request = new FileComparisonRequestDto();
request.setSourceContent(sourceContent);      // 源内容字符串
request.setTargetContent(targetContent);      // 目标内容字符串
request.setBaseServerIp("**********");       // 源IP地址
request.setTargetServerIp("***********");    // 目标IP地址
request.setBaselineServer("A-ECIF-APP01");    // 源主机名
request.setTargetServer("B-ECIF-APP02");      // 目标主机名
fileComparisonComponent.compareAndExport(request, response);
```

### 2. 服务层调用
```java
@Autowired
private IFileComparisonService fileComparisonService;

// 先进行比较
FileComparisonRequestDto request = new FileComparisonRequestDto();
request.setSourceContent(sourceContent);
request.setTargetContent(targetContent);
FileComparisonResultDto result = fileComparisonService.compareFileContents(request);

// 使用带IP和主机名的导出方法
fileComparisonService.exportComparisonResult(
    result, 
    sourceIp, sourceHostname, 
    targetIp, targetHostname, 
    response
);
```

## 数据来源

### 参数传递
- **源IP地址**：调用方传递
- **源主机名**：调用方传递
- **目标IP地址**：调用方传递
- **目标主机名**：调用方传递

### 自动提取
从分析的字符串中自动提取：
- **汇总数量**：总文件数
- **缺失数量**：基线中有但目标中没有的文件数
- **多出数量**：目标中有但基线中没有的文件数
- **不一致数量**：两边都有但内容不同的文件数
- **一致数量**：两边都有且内容相同的文件数

## 示例数据

### 汇总信息示例
| 服务器类型 | IP | hostname | 汇总 | 缺失 | 多出 | 不一致 | 一致 |
|-----------|----|---------|----|----|----|------|-----|
| 基线服务器 | ********** | A-ECIF-APP01 | 1207 | 4 | 8 | 28 | 1675 |
| 目标服务器 | *********** | B-ECIF-APP02 | 1711 | 4 | 8 | 28 | 1675 |

### 详细文件列表示例
| 类型 | 文件路径 |
|------|----------|
| 缺失 | /usr/IBM/WebSphere8/AppServer/profiles/AppSrv01/installedApps/cell01/wastar.tar.20210127.gz |
| 多出 | /usr/IBM/WebSphere8/AppServer/profiles/AppSrv02/installedApps/cell01/ECIF_SENDER.ear/CECIFSENDER.war/WEB-INF/ibm-web-bnd.xml |
| 不一致 | /usr/IBM/WebSphere8/AppServer/profiles/AppSrv02/installedApps/cell01/wastar.tar.gz |

## 技术实现

### 核心类更新
1. **FileComparisonExportDto**：更新为新的8列结构
2. **FileComparisonServiceImpl**：
   - 支持IP和主机名参数
   - 完全使用POI原生API实现，移除FastExcel依赖
   - 两Sheet导出：比对结果 + 一致文件列表
   - 支持不同颜色和样式的文字格式
3. **FileComparisonComponent**：新增带完整参数的方法

### Excel导出特性
- **两Sheet格式**：比对结果Sheet + 一致文件列表Sheet
- **复杂格式支持**：
  - 标题文字：黑色粗体（环境一致性比对结果报告、比对对象、比对时间）
  - 说明文字：红色粗体（比对规则、计算公式、路径说明）
  - 表格汇总：居中显示，列头有蓝色背景
  - 文件路径：合并多列居左显示，完整边框
- **自动列宽调整**：根据内容类型设置合适列宽
- **样式丰富**：边框、背景色、字体颜色、粗体等完整样式配置
- **边框完整性**：确保所有合并单元格都有完整边框

## 使用场景

1. **环境一致性比对**：比较不同环境的配置文件
2. **版本差异分析**：分析不同版本间的文件差异
3. **部署验证**：验证部署后的文件一致性
4. **审计报告**：生成详细的比对审计报告

## 优势特点

1. **格式标准化**：严格按照提供的图片格式设计
2. **信息完整**：包含IP、主机名等完整服务器信息
3. **数据详细**：提供汇总和详细两个层次的信息
4. **易于理解**：清晰的列头和数据结构
5. **便于分析**：支持进一步的数据分析和处理
