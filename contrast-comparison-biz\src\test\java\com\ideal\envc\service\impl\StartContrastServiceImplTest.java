package com.ideal.envc.service.impl;

import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.mapper.StartContrastMapper;
import com.ideal.envc.model.bean.StartContrastQueryBean;
import com.ideal.envc.model.bean.StartPlanBean;
import com.ideal.envc.model.dto.*;
import com.ideal.envc.service.IStartContrastBaseService;
import com.ideal.envc.service.IStartContrastCommonService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * StartContrastServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class StartContrastServiceImplTest {

    @Mock
    private StartContrastMapper startContrastMapper;

    @Mock
    private IStartContrastBaseService startContrastBaseService;

    @Mock
    private IStartContrastCommonService startContrastCommonService;

    @InjectMocks
    private StartContrastServiceImpl startContrastService;

    private UserDto userDto;
    private List<StartPlanBean> startPlanList;
    private StartResult successResult;

    @BeforeEach
    void setUp() {
        // 初始化用户DTO
        userDto = new UserDto();
        userDto.setId(1L);
        
        // 初始化方案列表
        startPlanList = new ArrayList<>();
        StartPlanBean planBean = new StartPlanBean();
        planBean.setId(100L);
        planBean.setName("测试方案");
        startPlanList.add(planBean);
        
        // 初始化成功结果
        successResult = new StartResult();
        successResult.setSuccess(true);
    }

    @Test
    @DisplayName("测试方案级启动-成功")
    void startByPlans_Success() throws EngineServiceException {
        // 准备测试数据
        PlanLevelStartDto startDto = new PlanLevelStartDto();
        startDto.setPlanIds(Arrays.asList(100L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        startDto.setFrom(2); // 手动触发

        // 模拟依赖行为
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doReturn(successResult).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("方案级"), any(UserDto.class));

        // 执行测试方法
        StartResult result = startContrastService.startByPlans(startDto, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(successResult, result);
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("方案级"), any(UserDto.class));
    }

    @Test
    @DisplayName("测试方案级启动-通过方案名称模糊查询成功")
    void startByPlans_SuccessWithPlanNameLike() throws EngineServiceException {
        // 准备测试数据
        PlanLevelStartDto startDto = new PlanLevelStartDto();
        startDto.setPlanNameLike("测试方案");
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        startDto.setFrom(2);

        // 模拟依赖行为
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doReturn(successResult).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("方案级"), any(UserDto.class));

        // 执行测试方法
        StartResult result = startContrastService.startByPlans(startDto, userDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("方案级"), any(UserDto.class));
    }

    @Test
    @DisplayName("测试方案级启动-from为null时使用默认值")
    void startByPlans_SuccessWithNullFrom() throws EngineServiceException {
        // 准备测试数据
        PlanLevelStartDto startDto = new PlanLevelStartDto();
        startDto.setPlanIds(Arrays.asList(100L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        startDto.setFrom(null); // from为null

        // 模拟依赖行为
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doReturn(successResult).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), eq(2), eq("方案级"), any(UserDto.class)); // 默认值为2

        // 执行测试方法
        StartResult result = startContrastService.startByPlans(startDto, userDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), eq(2), eq("方案级"), any(UserDto.class));
    }

    @Test
    @DisplayName("测试方案级启动-参数为空")
    void startByPlans_NullDto() throws EngineServiceException {
        // 执行测试方法
        StartResult result = startContrastService.startByPlans(null, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("启动参数对象为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试方案级启动-方案ID和名称同时为空")
    void startByPlans_EmptyPlanIdsAndName() throws EngineServiceException {
        // 准备测试数据
        PlanLevelStartDto startDto = new PlanLevelStartDto();
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 执行测试方法
        StartResult result = startContrastService.startByPlans(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("需要启动的方案ID集合和方案名称不能同时为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试方案级启动-未找到启动信息")
    void startByPlans_NoStartPlans() throws EngineServiceException {
        // 准备测试数据
        PlanLevelStartDto startDto = new PlanLevelStartDto();
        startDto.setPlanIds(Arrays.asList(100L, 101L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 返回空列表
        doReturn(new ArrayList<StartPlanBean>()).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByPlans(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到启动信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试方案级启动-处理异常")
    void startByPlans_Exception() throws EngineServiceException {
        // 准备测试数据
        PlanLevelStartDto startDto = new PlanLevelStartDto();
        startDto.setPlanIds(Arrays.asList(100L, 101L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 抛出异常
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doThrow(new EngineServiceException("测试异常")).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByPlans(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("方案级启动异常：测试异常", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试方案级启动-Mapper查询异常")
    void startByPlans_MapperException() throws EngineServiceException {
        // 准备测试数据
        PlanLevelStartDto startDto = new PlanLevelStartDto();
        startDto.setPlanIds(Arrays.asList(100L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - Mapper抛出异常
        doThrow(new RuntimeException("数据库查询异常")).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByPlans(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("方案级启动异常：数据库查询异常", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试系统级启动-成功")
    void startBySystems_Success() throws EngineServiceException {
        // 准备测试数据
        SystemLevelStartDto startDto = new SystemLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        startDto.setFrom(2); // 手动触发

        // 模拟依赖行为
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doReturn(successResult).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("系统级"), any(UserDto.class));

        // 执行测试方法
        StartResult result = startContrastService.startBySystems(startDto, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(successResult, result);
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("系统级"), any(UserDto.class));
    }

    @Test
    @DisplayName("测试系统级启动-参数为空")
    void startBySystems_NullDto() throws EngineServiceException {
        // 执行测试方法
        StartResult result = startContrastService.startBySystems(null, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("启动参数对象为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试系统级启动-方案ID为空")
    void startBySystems_NullPlanId() throws EngineServiceException {
        // 准备测试数据
        SystemLevelStartDto startDto = new SystemLevelStartDto();
        startDto.setSystemIds(Arrays.asList(1001L, 1002L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 执行测试方法
        StartResult result = startContrastService.startBySystems(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("方案ID为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试系统级启动-未找到启动信息")
    void startBySystems_NoStartPlans() throws EngineServiceException {
        // 准备测试数据
        SystemLevelStartDto startDto = new SystemLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 返回空列表
        doReturn(new ArrayList<StartPlanBean>()).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startBySystems(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到启动信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试系统级启动-处理异常")
    void startBySystems_Exception() throws EngineServiceException {
        // 准备测试数据
        SystemLevelStartDto startDto = new SystemLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 抛出异常
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doThrow(new EngineServiceException("系统级启动异常")).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startBySystems(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("系统级启动异常：系统级启动异常", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试节点级启动-成功")
    void startByNodes_Success() throws EngineServiceException {
        // 准备测试数据
        NodeLevelStartDto startDto = new NodeLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemId(1001L);
        startDto.setNodeIds(Arrays.asList(10001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        startDto.setFrom(2); // 手动触发

        // 模拟依赖行为
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doReturn(successResult).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("节点级"), any(UserDto.class));

        // 执行测试方法
        StartResult result = startContrastService.startByNodes(startDto, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(successResult, result);
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("节点级"), any(UserDto.class));
    }

    @Test
    @DisplayName("测试节点级启动-参数为空")
    void startByNodes_NullDto() throws EngineServiceException {
        // 执行测试方法
        StartResult result = startContrastService.startByNodes(null, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("启动参数对象为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试节点级启动-方案ID为空")
    void startByNodes_NullPlanId() throws EngineServiceException {
        // 准备测试数据
        NodeLevelStartDto startDto = new NodeLevelStartDto();
        startDto.setNodeIds(Arrays.asList(10001L, 10002L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 执行测试方法
        StartResult result = startContrastService.startByNodes(startDto, userDto);
        
        // 验证结果 - 由于源代码中方案ID为空时不会直接返回错误，而是会查询数据库
        // 所以这里需要模拟数据库返回空结果的情况
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到启动信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试节点级启动-系统ID为空")
    void startByNodes_NullSystemId() throws EngineServiceException {
        // 准备测试数据
        NodeLevelStartDto startDto = new NodeLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setNodeIds(Arrays.asList(10001L, 10002L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 执行测试方法
        StartResult result = startContrastService.startByNodes(startDto, userDto);
        
        // 验证结果 - 由于源代码中系统ID为空时不会直接返回错误，而是会查询数据库
        // 所以这里需要模拟数据库返回空结果的情况
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到启动信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试节点级启动-未找到启动信息")
    void startByNodes_NoStartPlans() throws EngineServiceException {
        // 准备测试数据
        NodeLevelStartDto startDto = new NodeLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemId(1001L);
        startDto.setNodeIds(Arrays.asList(10001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 返回空列表
        doReturn(new ArrayList<StartPlanBean>()).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByNodes(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到启动信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试节点级启动-处理异常")
    void startByNodes_Exception() throws EngineServiceException {
        // 准备测试数据
        NodeLevelStartDto startDto = new NodeLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemId(1001L);
        startDto.setNodeIds(Arrays.asList(10001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 抛出异常
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doThrow(new EngineServiceException("节点级启动异常")).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByNodes(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("节点级启动异常：节点级启动异常", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试规则级启动-成功")
    void startByRules_Success() throws EngineServiceException {
        // 准备测试数据
        RuleLevelStartDto startDto = new RuleLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemId(1001L);
        startDto.setNodeId(10001L);
        startDto.setRuleIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        startDto.setFrom(2); // 手动触发

        // 模拟依赖行为
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doReturn(successResult).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("规则级"), any(UserDto.class));

        // 执行测试方法
        StartResult result = startContrastService.startByRules(startDto, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(successResult, result);
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("规则级"), any(UserDto.class));
    }

    @Test
    @DisplayName("测试规则级启动-参数为空")
    void startByRules_NullDto() throws EngineServiceException {
        // 执行测试方法
        StartResult result = startContrastService.startByRules(null, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("启动参数对象为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试规则级启动-方案ID为空")
    void startByRules_NullPlanId() throws EngineServiceException {
        // 准备测试数据
        RuleLevelStartDto startDto = new RuleLevelStartDto();
        startDto.setSystemId(1001L);
        startDto.setNodeId(10001L);
        startDto.setRuleIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 执行测试方法
        StartResult result = startContrastService.startByRules(startDto, userDto);
        
        // 验证结果 - 由于源代码中方案ID为空时不会直接返回错误，而是会查询数据库
        // 所以这里需要模拟数据库返回空结果的情况
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到方案信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试规则级启动-系统ID为空")
    void startByRules_NullSystemId() throws EngineServiceException {
        // 准备测试数据
        RuleLevelStartDto startDto = new RuleLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setNodeId(10001L);
        startDto.setRuleIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 执行测试方法
        StartResult result = startContrastService.startByRules(startDto, userDto);
        
        // 验证结果 - 由于源代码中系统ID为空时不会直接返回错误，而是会查询数据库
        // 所以这里需要模拟数据库返回空结果的情况
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到方案信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试规则级启动-节点ID为空")
    void startByRules_NullNodeId() throws EngineServiceException {
        // 准备测试数据
        RuleLevelStartDto startDto = new RuleLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemId(1001L);
        startDto.setRuleIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 执行测试方法
        StartResult result = startContrastService.startByRules(startDto, userDto);
        
        // 验证结果 - 由于源代码中节点ID为空时不会直接返回错误，而是会查询数据库
        // 所以这里需要模拟数据库返回空结果的情况
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到方案信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试规则级启动-未找到方案信息")
    void startByRules_NoStartPlans() throws EngineServiceException {
        // 准备测试数据
        RuleLevelStartDto startDto = new RuleLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemId(1001L);
        startDto.setNodeId(10001L);
        startDto.setRuleIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 返回空列表
        doReturn(new ArrayList<StartPlanBean>()).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByRules(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到方案信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试规则级启动-处理异常")
    void startByRules_Exception() throws EngineServiceException {
        // 准备测试数据
        RuleLevelStartDto startDto = new RuleLevelStartDto();
        startDto.setPlanId(100L);
        startDto.setSystemId(1001L);
        startDto.setNodeId(10001L);
        startDto.setRuleIds(Arrays.asList(1001L));
        startDto.setUserId(1L);
        startDto.setUserName("admin");
        
        // 模拟依赖行为 - 抛出异常
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doThrow(new EngineServiceException("规则级启动异常")).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByRules(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("规则级启动异常：规则级启动异常", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试任务级启动-成功")
    void startByTasks_Success() throws EngineServiceException {
        // 准备测试数据
        TaskLevelStartDto startDto = new TaskLevelStartDto();
        startDto.setTaskIds(Arrays.asList(1L, 2L));
        startDto.setUserId(1L);
        startDto.setUserName("测试用户");
        startDto.setFrom(2); // 手动触发
        
        // 模拟依赖行为
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doReturn(successResult).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByTasks(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(successResult, result);
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), eq("任务级"), any(UserDto.class));
    }

    @Test
    @DisplayName("测试任务级启动-参数为空")
    void startByTasks_NullDto() throws EngineServiceException {
        // 执行测试方法
        StartResult result = startContrastService.startByTasks(null, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("启动参数对象为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试任务级启动-任务ID列表为空")
    void startByTasks_EmptyTaskIds() throws EngineServiceException {
        // 准备测试数据
        TaskLevelStartDto startDto = new TaskLevelStartDto();
        startDto.setTaskIds(new ArrayList<>());
        startDto.setUserId(1L);
        startDto.setUserName("测试用户");
        
        // 执行测试方法
        StartResult result = startContrastService.startByTasks(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("任务ID列表为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试任务级启动-任务ID列表为null")
    void startByTasks_NullTaskIds() throws EngineServiceException {
        // 准备测试数据
        TaskLevelStartDto startDto = new TaskLevelStartDto();
        startDto.setTaskIds(null);
        startDto.setUserId(1L);
        startDto.setUserName("测试用户");
        
        // 执行测试方法
        StartResult result = startContrastService.startByTasks(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("任务ID列表为空", result.getMessage());
        
        // 验证依赖未调用
        verify(startContrastMapper, never()).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试任务级启动-未找到任务信息")
    void startByTasks_NoStartPlans() throws EngineServiceException {
        // 准备测试数据
        TaskLevelStartDto startDto = new TaskLevelStartDto();
        startDto.setTaskIds(Arrays.asList(1L, 2L));
        startDto.setUserId(1L);
        startDto.setUserName("测试用户");
        
        // 模拟依赖行为 - 返回空列表
        doReturn(new ArrayList<StartPlanBean>()).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByTasks(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未找到任务信息", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, never()).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }

    @Test
    @DisplayName("测试任务级启动-处理异常")
    void startByTasks_Exception() throws EngineServiceException {
        // 准备测试数据
        TaskLevelStartDto startDto = new TaskLevelStartDto();
        startDto.setTaskIds(Arrays.asList(1L, 2L));
        startDto.setUserId(1L);
        startDto.setUserName("测试用户");
        
        // 模拟依赖行为 - 抛出异常
        doReturn(startPlanList).when(startContrastMapper).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        doThrow(new EngineServiceException("任务级启动异常")).when(startContrastCommonService).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
        
        // 执行测试方法
        StartResult result = startContrastService.startByTasks(startDto, userDto);
        
        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("任务级启动异常：任务级启动异常", result.getMessage());
        
        // 验证依赖调用
        verify(startContrastMapper, times(1)).selectStartPlansByQuery(any(StartContrastQueryBean.class));
        verify(startContrastCommonService, times(1)).processStart(
                anyList(), anyLong(), anyString(), anyInt(), anyString(), any(UserDto.class));
    }
} 