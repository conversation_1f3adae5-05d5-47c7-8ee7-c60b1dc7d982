package com.ideal.envc.model.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 启动结果DTO
 *
 * <AUTHOR>
 */
public class StartResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 消息
     */
    private String message;

    /**
     * 运行实例ID列表
     */
    private List<Long> instanceIds;

    /**
     * 失败的消息ID列表
     */
    private List<String> failedMessageIds;

    public StartResult() {
        this.success = true;
        this.instanceIds = new ArrayList<>();
        this.failedMessageIds = new ArrayList<>();
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<Long> getInstanceIds() {
        return instanceIds;
    }

    public void setInstanceIds(List<Long> instanceIds) {
        this.instanceIds = instanceIds;
    }

    public List<String> getFailedMessageIds() {
        return failedMessageIds;
    }

    public void setFailedMessageIds(List<String> failedMessageIds) {
        this.failedMessageIds = failedMessageIds;
    }

    /**
     * 添加运行实例ID
     *
     * @param instanceId 运行实例ID
     */
    public void addInstanceId(Long instanceId) {
        if (instanceId != null) {
            this.instanceIds.add(instanceId);
        }
    }

    /**
     * 添加失败的消息ID
     *
     * @param messageId 消息ID
     */
    public void addFailedMessageId(String messageId) {
        if (messageId != null) {
            this.failedMessageIds.add(messageId);
            this.success = false;
        }
    }

    @Override
    public String toString() {
        return "StartResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", instanceIds=" + instanceIds +
                ", failedMessageIds=" + failedMessageIds +
                '}';
    }
}
