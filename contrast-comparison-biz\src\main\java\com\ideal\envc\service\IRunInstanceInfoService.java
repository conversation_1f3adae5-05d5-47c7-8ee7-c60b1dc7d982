package com.ideal.envc.service;

import com.ideal.envc.model.dto.RunInstanceInfoDto;
import com.ideal.envc.model.dto.RunInstanceInfoQueryDto;
import com.github.pagehelper.PageInfo;

/**
 * 实例详情Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface IRunInstanceInfoService {
    /**
     * 查询实例详情
     *
     * @param id 实例详情主键
     * @return 实例详情
     */
    RunInstanceInfoDto selectRunInstanceInfoById(Long id);

    /**
     * 查询实例详情列表
     *
     * @param runInstanceInfoQueryDto 实例详情
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 实例详情集合
     */
    PageInfo<RunInstanceInfoDto> selectRunInstanceInfoList(RunInstanceInfoQueryDto runInstanceInfoQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增实例详情
     *
     * @param runInstanceInfoDto 实例详情
     * @return 结果
     */
    int insertRunInstanceInfo(RunInstanceInfoDto runInstanceInfoDto);

    /**
     * 修改实例详情
     *
     * @param runInstanceInfoDto 实例详情
     * @return 结果
     */
    int updateRunInstanceInfo(RunInstanceInfoDto runInstanceInfoDto);

    /**
     * 批量删除实例详情
     *
     * @param ids 需要删除的实例详情主键集合
     * @return 结果
     */
    int deleteRunInstanceInfoByIds(Long[] ids);
}
