package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class StartComputerNodeBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 系统ID */
    private Long businessSystemId;
    /** 源中心ID */
    private Long sourceCenterId;

    /** 源中心名称 */
    private String sourceCenterName;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 目标中心名称 */
    private String targetCenterName;
    /** 源设备ID */
    private Long sourceComputerId;
    /** 源设备IP */
    private String sourceComputerIp;
    /** 源设备端口 */
    private Integer sourceComputerPort;
    /** 目标设备ID */
    private Long targetComputerId;
    /** 目标设备IP */
    private String targetComputerIp;
    /** 目标设备端口 */
    private Integer targetComputerPort;
    /** 源设备操作系统 */
    private String sourceComputerOs;
    /** 目标设备操作系统 */
    private String targetComputerOs;
    /** 创建人ID */
    private List<StartRuleBean> rules;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setSourceCenterId(Long sourceCenterId){
        this.sourceCenterId = sourceCenterId;
    }

    public Long getSourceCenterId(){
        return sourceCenterId;
    }

    public void setTargetCenterId(Long targetCenterId){
        this.targetCenterId = targetCenterId;
    }

    public Long getTargetCenterId(){
        return targetCenterId;
    }

    public void setSourceComputerId(Long sourceComputerId){
        this.sourceComputerId = sourceComputerId;
    }

    public Long getSourceComputerId(){
        return sourceComputerId;
    }

    public void setSourceComputerIp(String sourceComputerIp){
        this.sourceComputerIp = sourceComputerIp;
    }

    public String getSourceComputerIp(){
        return sourceComputerIp;
    }

    public void setTargetComputerId(Long targetComputerId){
        this.targetComputerId = targetComputerId;
    }

    public Long getTargetComputerId(){
        return targetComputerId;
    }

    public void setTargetComputerIp(String targetComputerIp){
        this.targetComputerIp = targetComputerIp;
    }

    public String getTargetComputerIp(){
        return targetComputerIp;
    }


    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public Integer getSourceComputerPort() {
        return sourceComputerPort;
    }

    public void setSourceComputerPort(Integer sourceComputerPort) {
        this.sourceComputerPort = sourceComputerPort;
    }

    public Integer getTargetComputerPort() {
        return targetComputerPort;
    }

    public void setTargetComputerPort(Integer targetComputerPort) {
        this.targetComputerPort = targetComputerPort;
    }

    public String getSourceComputerOs() {
        return sourceComputerOs;
    }

    public void setSourceComputerOs(String sourceComputerOs) {
        this.sourceComputerOs = sourceComputerOs;
    }

    public String getTargetComputerOs() {
        return targetComputerOs;
    }

    public void setTargetComputerOs(String targetComputerOs) {
        this.targetComputerOs = targetComputerOs;
    }

    public List<StartRuleBean> getRules() {
        return rules;
    }

    public void setRules(List<StartRuleBean> rules) {
        this.rules = rules;
    }

    @Override
    public String toString() {
        return "StartComputerNodeBean{" +
                "id=" + id +
                ", businessSystemId=" + businessSystemId +
                ", sourceCenterId=" + sourceCenterId +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", targetCenterId=" + targetCenterId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", sourceComputerId=" + sourceComputerId +
                ", sourceComputerIp='" + sourceComputerIp + '\'' +
                ", sourceComputerPort=" + sourceComputerPort +
                ", targetComputerId=" + targetComputerId +
                ", targetComputerIp='" + targetComputerIp + '\'' +
                ", targetComputerPort=" + targetComputerPort +
                ", sourceComputerOs='" + sourceComputerOs + '\'' +
                ", targetComputerOs='" + targetComputerOs + '\'' +
                ", rules=" + rules +
                '}';
    }
}
