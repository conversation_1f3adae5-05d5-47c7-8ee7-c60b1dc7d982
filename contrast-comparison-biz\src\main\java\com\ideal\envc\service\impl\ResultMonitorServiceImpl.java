package com.ideal.envc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.ResultMonitorMapper;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.bean.ResultMonitorBean;
import com.ideal.envc.model.ContentDetailDto;
import com.ideal.envc.model.bean.RunFlowDetailBean;
import com.ideal.envc.model.dto.ContentCustomDto;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.ResultMonitorDto;
import com.ideal.envc.model.dto.ResultMonitorQueryDto;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.enums.RuleModelEnum;
import com.ideal.envc.service.IResultMonitorService;
import com.ideal.envc.component.FileComparisonComponent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 比对结果监控Service实现
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class ResultMonitorServiceImpl implements IResultMonitorService {
    private final Logger logger = LoggerFactory.getLogger(ResultMonitorServiceImpl.class);

    private final ResultMonitorMapper resultMonitorMapper;
    private final RunFlowResultMapper runFlowResultMapper;
    private final RunFlowMapper runFlowMapper;
    private final RunRuleMapper runRuleMapper;
    private final FileComparisonComponent fileComparisonComponent;
    private final SystemComputerMapper systemComputerMapper;

    public ResultMonitorServiceImpl(ResultMonitorMapper resultMonitorMapper, RunFlowResultMapper runFlowResultMapper, RunFlowMapper runFlowMapper, RunRuleMapper runRuleMapper, FileComparisonComponent fileComparisonComponent, SystemComputerMapper systemComputerMapper) {
        this.resultMonitorMapper = resultMonitorMapper;
        this.runFlowResultMapper = runFlowResultMapper;
        this.runFlowMapper = runFlowMapper;
        this.runRuleMapper = runRuleMapper;
        this.fileComparisonComponent = fileComparisonComponent;
        this.systemComputerMapper = systemComputerMapper;
    }

    /**
     * 查询比对结果列表
     *
     * @param resultMonitorQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 比对结果列表
     */
    @Override
    public PageInfo<ResultMonitorDto> selectResultMonitorList(ResultMonitorQueryDto resultMonitorQueryDto, Integer pageNum, Integer pageSize) {
        logger.info("查询比对结果列表，查询条件：{}", resultMonitorQueryDto);

        // 参数校验
        if (resultMonitorQueryDto == null) {
            logger.warn("查询比对结果列表参数不完整，查询条件不能为空");
            return new PageInfo<>(new ArrayList<>());
        }
        if(resultMonitorQueryDto.getModel()==null){
            resultMonitorQueryDto.setModel(RuleModelEnum.COMPARE.getCode());
        }
        // 分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<ResultMonitorBean> resultMonitorBeanList = resultMonitorMapper.selectResultMonitorList(
                resultMonitorQueryDto.getBusinessSystemName(),
                resultMonitorQueryDto.getModel(),
                resultMonitorQueryDto.getResult(),
                resultMonitorQueryDto.getFrom()
        );

        // 转换为DTO对象
        List<ResultMonitorDto> resultMonitorDtoList = BeanUtils.copy(resultMonitorBeanList, ResultMonitorDto.class);

        // 处理耗时格式
        for (ResultMonitorDto dto : resultMonitorDtoList) {
            // 从Bean列表中查找对应的Bean对象
            ResultMonitorBean correspondingBean = resultMonitorBeanList.stream()
                    .filter(bean -> bean.getId().equals(dto.getId()))
                    .findFirst()
                    .orElse(null);
            
            if (correspondingBean != null) {
                Long elapsedTime = correspondingBean.getElapsedTime();
                
                // 如果elapsedTime为null或0，说明任务正在运行中，需要计算当前耗时
                if (elapsedTime == null || elapsedTime == 0L) {
                    if (correspondingBean.getCreateTime() != null) {
                        elapsedTime = System.currentTimeMillis() - correspondingBean.getCreateTime().getTime();
                    } else {
                        elapsedTime = 0L;
                    }
                }
                
                dto.setElapsedTimeStr(ContrastToolUtils.formatElapsedTime(elapsedTime));
                
                // 设置触发方式的翻译
                if (correspondingBean.getFrom() != null) {
                    dto.setTriggerFrom(com.ideal.envc.model.enums.StartFromEnums.getNameByCode(correspondingBean.getFrom()));
                }
            }
        }

        PageInfo<ResultMonitorBean> pageInfo = new PageInfo<>(resultMonitorBeanList);
        return new PageInfo<>(resultMonitorDtoList);
    }

    /**
     * 根据流程ID查询比对详情
     *
     * @param flowId 流程ID
     * @return 比对详情
     */
    @Override
    public ContentDetailDto selectContentDetailByFlowId(Long flowId) throws ContrastBusinessException {
        logger.info("根据流程ID查询比对详情，flowId：{}", flowId);

        // 参数校验
        if (flowId == null) {
            logger.error("查询比对详情失败，flowId不能为空");
            throw new ContrastBusinessException("flowId不能为空");
        }

        try {
            // 根据flowId查询流程结果表
            RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultByFlowId(flowId);

            if (runFlowResult == null) {
                logger.warn("根据flowId未查询到流程结果数据，flowId：{}", flowId);
                throw new ContrastBusinessException("未查询到对应的流程结果数据");
            }
            
            // 获取内容，优先使用icontent，如果为空则使用istderr
            String content = runFlowResult.getContent();
            if (StringUtils.isBlank(content)) {
                content = runFlowResult.getStderr();
            }

            ContentDetailDto contentDetailDto = new ContentDetailDto();

            // 判断内容是否包含分隔符@$@
            if (StringUtils.isNotBlank(content) && content.contains("@$@")) {
                String[] parts = content.split("@\\$@", 2);
                if (parts.length >= 2) {
                    contentDetailDto.setSourceContent(parts[0]);
                    contentDetailDto.setTargetContent(parts[1]);
                } else {
                    // 如果分隔后只有一部分，则作为sourceContent
                    contentDetailDto.setSourceContent(parts[0]);
                    contentDetailDto.setTargetContent("");
                }
            } else {
                // 不包含分隔符，整个内容作为sourceContent
                contentDetailDto.setSourceContent(content != null ? content : "");
                contentDetailDto.setTargetContent("");
            }

            logger.info("查询比对详情成功，flowId：{}", flowId);
            return contentDetailDto;

        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("查询比对详情失败，flowId：{}", flowId, e);
            throw new ContrastBusinessException("查询比对详情失败：" + e.getMessage());
        }
    }

    @Override
    public String selectContentForCompareFileByFlowId(Long flowId) throws ContrastBusinessException {
        logger.info("selectContentForCompareFileByFlowId 根据流程ID查询比对详情，flowId：{}", flowId);
        String content = "";
        // 参数校验
        if (flowId == null) {
            logger.error("selectContentForCompareFileByFlowId 查询比对详情失败，flowId不能为空");
            throw new ContrastBusinessException("flowId不能为空");
        }

        try {
            // 根据flowId查询流程结果表
            RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultByFlowId(flowId);
           
            if (runFlowResult == null) {
                logger.warn(" selectContentForCompareFileByFlowId 根据flowId未查询到流程结果数据，flowId：{}", flowId);
                throw new ContrastBusinessException("未查询到对应的流程结果数据");
            }
            String sourcePath = "";
            String targetPath = "";
            //增加根据流程ID查询到对应运行规则的isourcePath和ipath路径
            RunFlowEntity runFlow = runFlowMapper.selectRunFlowByFlowId(flowId);
            if (runFlow != null) {
                RunRuleEntity runRule = runRuleMapper.selectRunRuleById(runFlow.getRunBizId());
                if (runRule != null) {
                    sourcePath = runRule.getSourcePath();
                    targetPath = runRule.getPath();
                }
            }

            // 获取内容，优先使用icontent，如果为空则使用istderr
            content = runFlowResult.getContent();
            if(StringUtils.isNotEmpty( content)){
                ContentCustomDto contentCustomDto = null;
                try{
                    contentCustomDto = JSON.parseObject(content, ContentCustomDto.class);
                    content = contentCustomDto.getContent();
                }catch (Exception e){
                    logger.error("selectContentForCompareFileByFlowId 解析contentCustomDto异常", e);
                }

            }
            if (StringUtils.isBlank(content)) {
                content = runFlowResult.getStderr();
            }
            if(content==null){
                content="";
            }
            // 查询源路径字符集
            content = content.replace("<span>Source</span>",
                    "<span><font class='compare_font'>Source</font>&nbsp;&nbsp;&nbsp;</span><span>" + sourcePath + "</span>");
            content = content.replace("<span>Target</span>",
                    "<span><font class='compare_font'>Target</font>&nbsp;&nbsp;&nbsp;</span><span>" + targetPath + "</span>");
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("selectContentForCompareFileByFlowId 处理异常", e);
            throw new ContrastBusinessException("查询比对详情失败：" + e.getMessage());
        }
        return content;
    }

    @Override
    public void exportComparisonReportByFlowId(Long flowId, HttpServletResponse response) throws ContrastBusinessException {
        logger.info("根据流程ID导出比对报表，flowId：{}", flowId);

        // 参数校验
        validateFlowId(flowId);

        try {
            // 查询流程结果和详情
            RunFlowResultEntity runFlowResult = queryFlowResult(flowId);
            RunFlowDetailBean runFlowDetailBean = queryFlowDetail(flowId);

            // 解析内容
            String content = getFlowContent(runFlowResult, flowId);
            ContentParseResult parseResult = parseContent(content, flowId);

            // 验证解析结果
            validateParseResult(parseResult, flowId);

            // 构建请求并导出
            FileComparisonRequestDto request = buildComparisonRequest(parseResult, runFlowDetailBean, flowId);
            fileComparisonComponent.compareAndExport(request, response);

            logger.info("导出比对报表成功，flowId：{}", flowId);

        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("导出比对报表失败，flowId：{}", flowId, e);
            throw new ContrastBusinessException("导出比对报表失败：" + e.getMessage());
        }
    }

    /**
     * 验证流程ID参数
     */
    private void validateFlowId(Long flowId) throws ContrastBusinessException {
        if (flowId == null) {
            logger.error("导出比对报表失败，flowId不能为空");
            throw new ContrastBusinessException("flowId不能为空");
        }
    }

    /**
     * 查询流程结果
     */
    private RunFlowResultEntity queryFlowResult(Long flowId) throws ContrastBusinessException {
        RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultByFlowId(flowId);
        if (runFlowResult == null) {
            logger.warn("根据flowId未查询到流程结果数据，flowId：{}", flowId);
            throw new ContrastBusinessException("未查询到对应的流程结果数据");
        }
        return runFlowResult;
    }

    /**
     * 查询流程详情
     */
    private RunFlowDetailBean queryFlowDetail(Long flowId) throws ContrastBusinessException {
        List<RunFlowDetailBean> runFlowDetailBeanList = runRuleMapper.selectRunRuleDetailByFlowId(flowId);
        if (runFlowDetailBeanList == null || runFlowDetailBeanList.isEmpty()) {
            throw new ContrastBusinessException("根据流程ID未查询到对应设备相关信息");
        }
        return runFlowDetailBeanList.get(0);
    }

    /**
     * 获取流程内容
     */
    private String getFlowContent(RunFlowResultEntity runFlowResult, Long flowId) throws ContrastBusinessException {
        String content = runFlowResult.getContent();
        if (StringUtils.isBlank(content)) {
            content = runFlowResult.getStderr();
        }

        if (StringUtils.isBlank(content)) {
            logger.warn("流程结果内容为空，flowId：{}", flowId);
            throw new ContrastBusinessException("流程结果内容为空，无法导出比对报表");
        }
        return content;
    }

    /**
     * 解析内容
     */
    private ContentParseResult parseContent(String content, Long flowId) {
        String sourceContent = "";
        String targetContent = "";

        // 尝试解析JSON格式的content
        try {
            ContentCustomDto contentCustomDto = JSON.parseObject(content, ContentCustomDto.class);
            if (contentCustomDto != null) {
                sourceContent = contentCustomDto.getSourceContent();
                targetContent = contentCustomDto.getTargetContent();
                logger.info("成功解析JSON格式的content，sourceContent长度：{}，targetContent长度：{}",
                        sourceContent != null ? sourceContent.length() : 0,
                        targetContent != null ? targetContent.length() : 0);
            }
        } catch (Exception e) {
            logger.warn("解析JSON格式的content失败，尝试使用分隔符方式解析，flowId：{}", flowId, e);
            ContentParseResult fallbackResult = parseFallbackContent(content);
            sourceContent = fallbackResult.getSourceContent();
            targetContent = fallbackResult.getTargetContent();
        }

        return new ContentParseResult(sourceContent, targetContent);
    }

    /**
     * 备用内容解析方法
     */
    private ContentParseResult parseFallbackContent(String content) {
        String sourceContent;
        String targetContent;

        // 如果JSON解析失败，尝试使用分隔符@$@方式解析
        if (content.contains("@$@")) {
            String[] parts = content.split("@\\$@", 2);
            if (parts.length >= 2) {
                sourceContent = parts[0];
                targetContent = parts[1];
                logger.info("使用分隔符方式解析content成功，sourceContent长度：{}，targetContent长度：{}",
                        sourceContent.length(), targetContent.length());
            } else {
                sourceContent = parts[0];
                targetContent = "";
                logger.info("使用分隔符方式解析content，只有源内容，sourceContent长度：{}", sourceContent.length());
            }
        } else {
            // 如果都无法解析，将整个内容作为源内容
            sourceContent = content;
            targetContent = "";
            logger.info("无法解析content，将整个内容作为源内容，长度：{}", sourceContent.length());
        }

        return new ContentParseResult(sourceContent, targetContent);
    }

    /**
     * 验证解析结果
     */
    private void validateParseResult(ContentParseResult parseResult, Long flowId) throws ContrastBusinessException {
        if (StringUtils.isBlank(parseResult.getSourceContent()) && StringUtils.isBlank(parseResult.getTargetContent())) {
            logger.warn("解析后的源内容和目标内容都为空，flowId：{}", flowId);
            throw new ContrastBusinessException("解析后的比对内容为空，无法导出比对报表");
        }
    }

    /**
     * 构建比较请求
     */
    private FileComparisonRequestDto buildComparisonRequest(ContentParseResult parseResult, RunFlowDetailBean runFlowDetailBean, Long flowId) {
        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(parseResult.getSourceContent());
        request.setTargetContent(parseResult.getTargetContent());
        request.setBaseServerIp(runFlowDetailBean.getSourceComputerIp());
        request.setTargetServerIp(runFlowDetailBean.getTargetComputerIp());
        request.setFlowId(flowId);
        request.setBaselineServer(runFlowDetailBean.getSourceCenterName());
        request.setTargetServer(runFlowDetailBean.getTargetComputerName());

        request.setPath(runFlowDetailBean.getPath());
        request.setSourcePath(runFlowDetailBean.getSourcePath());
        request.setBusinessSystemId(runFlowDetailBean.getBusinessSystemId());
        request.setBusinessSystemName(runFlowDetailBean.getBusinessSystemName());
        request.setSourceCenterName(runFlowDetailBean.getSourceCenterName());
        request.setTargetCenterName(runFlowDetailBean.getTargetCenterName());

        return request;
    }

    /**
     * 解析内容结果类
     */
    private static class ContentParseResult {
        private String sourceContent;
        private String targetContent;

        public ContentParseResult(String sourceContent, String targetContent) {
            this.sourceContent = sourceContent;
            this.targetContent = targetContent;
        }

        public String getSourceContent() { return sourceContent; }
        public String getTargetContent() { return targetContent; }
    }

}
