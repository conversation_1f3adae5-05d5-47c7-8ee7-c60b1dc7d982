package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.service.IRunInstanceService;
import com.ideal.envc.model.dto.RunInstanceDto;
import com.ideal.envc.model.dto.RunInstanceQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 实例Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class RunInstanceServiceImpl implements IRunInstanceService {
    private final Logger logger = LoggerFactory.getLogger(RunInstanceServiceImpl.class);

    private final RunInstanceMapper runInstanceMapper;

    public RunInstanceServiceImpl(RunInstanceMapper runInstanceMapper) {
        this.runInstanceMapper = runInstanceMapper;
    }

    /**
     * 查询实例
     *
     * @param id 实例主键
     * @return 实例
     */
    @Override
    public RunInstanceDto selectRunInstanceById(Long id) {
        RunInstanceEntity runInstance = runInstanceMapper.selectRunInstanceById(id);
        return BeanUtils.copy(runInstance, RunInstanceDto.class);
    }

    /**
     * 查询实例列表
     *
     * @param runInstanceQueryDto 实例
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 实例
     */
    @Override
    public PageInfo<RunInstanceDto> selectRunInstanceList(RunInstanceQueryDto runInstanceQueryDto, Integer pageNum, Integer pageSize) {
        RunInstanceEntity query = BeanUtils.copy(runInstanceQueryDto, RunInstanceEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<RunInstanceEntity> runInstanceList = runInstanceMapper.selectRunInstanceList(query);
        return PageDataUtil.toDtoPage(runInstanceList, RunInstanceDto.class);
    }

    /**
     * 新增实例
     *
     * @param runInstanceDto 实例
     * @return 结果
     */
    @Override
    public int insertRunInstance(RunInstanceDto runInstanceDto) {
        RunInstanceEntity runInstance = BeanUtils.copy(runInstanceDto, RunInstanceEntity.class);
        return runInstanceMapper.insertRunInstance(runInstance);
    }

    /**
     * 修改实例
     *
     * @param runInstanceDto 实例
     * @return 结果
     */
    @Override
    public int updateRunInstance(RunInstanceDto runInstanceDto) {
        RunInstanceEntity runInstance = BeanUtils.copy(runInstanceDto, RunInstanceEntity.class);
        return runInstanceMapper.updateRunInstance(runInstance);
    }

    /**
     * 批量删除实例
     *
     * @param ids 需要删除的实例主键
     * @return 结果
     */
    @Override
    public int deleteRunInstanceByIds(Long[] ids) {
        return runInstanceMapper.deleteRunInstanceByIds(ids);
    }
}
