package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.model.bean.PlanSystemListBean;
import com.ideal.envc.model.bean.PlanRelationListBean;
import com.ideal.envc.model.dto.PlanSystemListDto;
import com.ideal.envc.model.dto.PlanSystemQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.exception.ContrastBusinessException;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.PlanRelationMapper;
import com.ideal.envc.model.entity.PlanRelationEntity;
import com.ideal.envc.service.IPlanRelationService;
import com.ideal.envc.model.dto.PlanRelationDto;
import com.ideal.envc.model.dto.PlanRelationQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import com.ideal.envc.model.dto.PlanRelationBatchDto;
import com.ideal.common.util.batch.BatchHandler;
import java.sql.SQLTransientException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 方案信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class PlanRelationServiceImpl implements IPlanRelationService {
    private final Logger logger = LoggerFactory.getLogger(PlanRelationServiceImpl.class);

    private final PlanRelationMapper planRelationMapper;
    private final BatchHandler batchHandler;

    public PlanRelationServiceImpl(PlanRelationMapper planRelationMapper, BatchHandler batchHandler) {
        this.planRelationMapper = planRelationMapper;
        this.batchHandler = batchHandler;
    }

    /**
     * 查询方案信息
     *
     * @param id 方案信息主键
     * @return 方案信息
     */
    @Override
    public PlanRelationDto selectPlanRelationById(Long id) throws ContrastBusinessException {
        try {
            if (id == null) {
                logger.error("查询方案信息失败：ID为空");
                throw new ContrastBusinessException("方案ID不能为空");
            }
            PlanRelationEntity planRelation = planRelationMapper.selectPlanRelationById(id);
            if (planRelation == null) {
                logger.error("查询方案信息失败：未找到ID为{}的记录", id);
                throw new ContrastBusinessException("未找到对应的方案信息");
            }
            return BeanUtils.copy(planRelation, PlanRelationDto.class);
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("查询方案信息系统异常", e);
            throw new ContrastBusinessException("查询方案信息失败：" + e.getMessage());
        }
    }

    /**
     * 查询方案信息列表
     *
     * @param planRelationQueryDto 方案信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 方案信息
     */
    @Override
    public PageInfo<PlanRelationDto> selectPlanRelationList(PlanRelationQueryDto planRelationQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            PageMethod.startPage(pageNum, pageSize);
            List<PlanRelationListBean> planRelationList = planRelationMapper.selectPlanRelationList(planRelationQueryDto);
            return PageDataUtil.toDtoPage(planRelationList, PlanRelationDto.class);
        } catch (Exception e) {
            logger.error("查询方案信息列表系统异常", e);
            throw new ContrastBusinessException("查询方案信息列表失败：" + e.getMessage());
        }
    }


    /**
     * 新增方案信息
     *
     * @param planRelationDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPlanRelation(PlanRelationDto planRelationDto, UserDto userDto) throws ContrastBusinessException {
        try {
            if (planRelationDto == null) {
                logger.error("新增方案信息失败：参数为空");
                throw new ContrastBusinessException("方案信息不能为空");
            }
            if (userDto == null) {
                logger.error("新增方案信息失败：用户信息为空");
                throw new ContrastBusinessException("用户信息不能为空");
            }
            if (planRelationDto.getEnvcPlanId() == null) {
                logger.error("新增方案信息失败：环境方案ID为空");
                throw new ContrastBusinessException("环境方案ID不能为空");
            }
            if (planRelationDto.getBusinessSystemId() == null) {
                logger.error("新增方案信息失败：业务系统ID为空");
                throw new ContrastBusinessException("业务系统ID不能为空");
            }

            // 检查是否已存在相同的记录
            List<PlanRelationEntity> existingRelations = planRelationMapper.selectPlanRelationByPlanAndSystem(
                planRelationDto.getEnvcPlanId(), 
                planRelationDto.getBusinessSystemId()
            );
            if (!existingRelations.isEmpty()) {
                if (existingRelations.size() > 1) {
                    logger.warn("发现多条重复的方案系统关联记录，方案ID={}，系统ID={}，重复记录数量={}，这可能表明数据存在异常", 
                        planRelationDto.getEnvcPlanId(), 
                        planRelationDto.getBusinessSystemId(),
                        existingRelations.size());
                }
                logger.error("新增方案信息失败：该方案与系统的关联关系已存在，方案ID={}，系统ID={}", 
                    planRelationDto.getEnvcPlanId(), 
                    planRelationDto.getBusinessSystemId());
                throw new ContrastBusinessException("该方案与系统的关联关系已存在");
            }

            // 转换为实体对象
            PlanRelationEntity planRelation = BeanUtils.copy(planRelationDto, PlanRelationEntity.class);
            
            // 设置创建人和更新人信息
            planRelation.setCreatorId(userDto.getId());
            planRelation.setCreatorName(userDto.getFullName());

            int result = planRelationMapper.insertPlanRelation(planRelation);
            if (result > 0) {
                logger.info("新增方案信息成功，操作人：{}", userDto.getFullName());
            } else {
                logger.error("新增方案信息失败");
                throw new ContrastBusinessException("新增方案信息失败");
            }
            return result;
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("新增方案信息系统异常", e);
            throw new ContrastBusinessException("新增方案信息失败：" + e.getMessage());
        }
    }


    /**
     * 修改方案信息
     *
     * @param planRelationDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePlanRelation(PlanRelationDto planRelationDto, UserDto userDto) throws ContrastBusinessException {
        try {
            if (planRelationDto == null) {
                logger.error("修改方案信息失败：参数为空");
                throw new ContrastBusinessException("方案信息不能为空");
            }
            if (planRelationDto.getId() == null) {
                logger.error("修改方案信息失败：ID为空");
                throw new ContrastBusinessException("方案ID不能为空");
            }
            if (userDto == null) {
                logger.error("修改方案信息失败：用户信息为空");
                throw new ContrastBusinessException("用户信息不能为空");
            }

            // 检查记录是否存在
            PlanRelationEntity existingPlan = planRelationMapper.selectPlanRelationById(planRelationDto.getId());
            if (existingPlan == null) {
                logger.error("修改方案信息失败：未找到ID为{}的记录", planRelationDto.getId());
                throw new ContrastBusinessException("未找到要修改的记录");
            }

            // 转换为实体对象并设置更新人信息
            PlanRelationEntity planRelation = BeanUtils.copy(planRelationDto, PlanRelationEntity.class);

            int result = planRelationMapper.updatePlanRelation(planRelation);
            if (result > 0) {
                logger.info("修改方案信息成功，ID={}，操作人：{}", planRelation.getId(), userDto.getFullName());
            } else {
                logger.error("修改方案信息失败，ID={}", planRelation.getId());
                throw new ContrastBusinessException("修改方案信息失败");
            }
            return result;
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("修改方案信息系统异常", e);
            throw new ContrastBusinessException("修改方案信息失败：" + e.getMessage());
        }
    }


    /**
     * 批量删除方案信息
     *
     * @param ids 需要删除的方案信息主键集合
     * @param userDto 当前操作用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePlanRelationByIds(Long[] ids, UserDto userDto) throws ContrastBusinessException {
        try {
            if (ids == null || ids.length == 0) {
                logger.error("删除方案信息失败：ID列表为空");
                throw new ContrastBusinessException("删除ID不能为空");
            }
            if (userDto == null) {
                logger.error("删除方案信息失败：用户信息为空");
                throw new ContrastBusinessException("用户信息不能为空");
            }

            // 验证所有记录是否存在
            for (Long id : ids) {
                PlanRelationEntity plan = planRelationMapper.selectPlanRelationById(id);
                if (plan == null) {
                    logger.error("删除方案信息失败：未找到ID为{}的记录", id);
                    throw new ContrastBusinessException("ID为" + id + "的方案不存在");
                }
            }

            int result = planRelationMapper.deletePlanRelationByIds(ids);
            if (result > 0) {
                logger.info("删除方案信息成功，ID数量={}，操作人：{}", ids.length, userDto.getFullName());
            } else {
                logger.error("删除方案信息失败，ID数量={}", ids.length);
                throw new ContrastBusinessException("删除方案信息失败");
            }
            return result;
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("删除方案信息系统异常", e);
            throw new ContrastBusinessException("删除方案信息失败：" + e.getMessage());
        }
    }

    /**
     * 查询方案绑定系统列表
     *
     * @param planSystemQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 方案绑定系统列表
     */
    @Override
    public PageInfo<PlanSystemListDto> selectPlanSystemList(PlanSystemQueryDto planSystemQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            if (planSystemQueryDto == null || planSystemQueryDto.getPlanId() == null) {
                logger.error("查询方案绑定系统列表失败：方案ID为空");
                throw new ContrastBusinessException("方案ID不能为空");
            }

            // 分页查询
            PageMethod.startPage(pageNum, pageSize);
            List<PlanSystemListBean> beans = planRelationMapper.selectPlanSystemList(
                    planSystemQueryDto.getPlanId(),
                    planSystemQueryDto.getBusinessSystemName()
            );

            // 转换为DTO对象
            List<PlanSystemListDto> dtos = BeanUtils.copy(beans, PlanSystemListDto.class);

            // 构建分页结果
            PageInfo<PlanSystemListBean> pageInfo = new PageInfo<>(beans);
            PageInfo<PlanSystemListDto> result = new PageInfo<>(dtos);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());

            return result;
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("查询方案绑定系统列表系统异常", e);
            throw new ContrastBusinessException("查询方案绑定系统列表失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsertPlanRelation(PlanRelationBatchDto planRelationBatchDto, UserDto userDto) throws ContrastBusinessException {
        try {
            if (planRelationBatchDto == null || planRelationBatchDto.getEnvcPlanId() == null 
                || planRelationBatchDto.getBusinessSystemIdList() == null 
                || planRelationBatchDto.getBusinessSystemIdList().isEmpty()) {
                throw new ContrastBusinessException("参数不能为空");
            }

            // 将系统ID列表转换为PlanRelationDto列表
            List<PlanRelationDto> planRelationDtoList = planRelationBatchDto.getBusinessSystemIdList().stream()
                .map(systemId -> {
                    PlanRelationDto dto = new PlanRelationDto();
                    dto.setEnvcPlanId(planRelationBatchDto.getEnvcPlanId());
                    dto.setBusinessSystemId(systemId);
                    dto.setCreatorId(userDto.getId());
                    dto.setCreatorName(userDto.getFullName());
                    return dto;
                })
                .collect(Collectors.toList());
            List<PlanRelationEntity> planRelationEntityList = BeanUtils.copy(planRelationDtoList, PlanRelationEntity.class);
            batchHandler.batchData(planRelationEntityList, planRelationMapper::insertPlanRelation, 500);

            return planRelationDtoList.size();
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("批量保存方案与系统关系失败", e);
            throw new ContrastBusinessException("批量保存方案与系统关系失败：" + e.getMessage());
        }
    }

    @Override
    public PageInfo<PlanSystemListDto> selectAvailablePlanSystemList(PlanSystemQueryDto planSystemQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            if (planSystemQueryDto == null || planSystemQueryDto.getPlanId() == null) {
                logger.error("查询方案可绑定系统列表失败：方案ID为空");
                throw new ContrastBusinessException("方案ID不能为空");
            }

            // 1. 查询当前方案已绑定的系统ID列表
            List<Long> boundSystemIds = planRelationMapper.selectBoundSystemIdsByPlanId(planSystemQueryDto.getPlanId());
            
            // 2. 分页查询可绑定的系统列表
            PageMethod.startPage(pageNum, pageSize);
            List<PlanSystemListBean> beans = planRelationMapper.selectAvailablePlanSystemList(
                    planSystemQueryDto.getPlanId(),
                    boundSystemIds,
                    planSystemQueryDto.getBusinessSystemName()
            );

            // 3. 转换为DTO对象
            List<PlanSystemListDto> dtos = new ArrayList<>();
            if(!beans.isEmpty()){
                dtos =  BeanUtils.copy(beans, PlanSystemListDto.class);

            }

            // 4. 构建分页结果
            PageInfo<PlanSystemListBean> pageInfo = new PageInfo<>(beans);
            PageInfo<PlanSystemListDto> result = new PageInfo<>(dtos);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());

            return result;
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("查询方案可绑定系统列表系统异常", e);
            throw new ContrastBusinessException("查询方案可绑定系统列表失败：" + e.getMessage());
        }
    }
}
