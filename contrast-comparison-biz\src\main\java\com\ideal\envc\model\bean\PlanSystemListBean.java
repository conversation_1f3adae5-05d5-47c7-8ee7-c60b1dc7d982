package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.Date;

/**
 * 方案绑定系统列表Bean对象
 *
 * <AUTHOR>
 */
public class PlanSystemListBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统描述
     */
    private String businessSystemDesc;

    /**
     * 关系绑定时间
     */
    private Date createTime;

    /**
     * 绑定关系人
     */
    private String creatorName;

    private String businessSystemCode;

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemDesc() {
        return businessSystemDesc;
    }

    public void setBusinessSystemDesc(String businessSystemDesc) {
        this.businessSystemDesc = businessSystemDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessSystemCode() {
        return businessSystemCode;
    }

    public void setBusinessSystemCode(String businessSystemCode) {
        this.businessSystemCode = businessSystemCode;
    }
}
