package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.model.dto.RunRuleContentDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IRunRuleContentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 节点规则内容Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/runRuleContent")
@Validated
public class RunRuleContentController {

    private static final Logger logger = LoggerFactory.getLogger(RunRuleContentController.class);

    private final IRunRuleContentService runRuleContentService;

    public RunRuleContentController(IRunRuleContentService runRuleContentService) {
        this.runRuleContentService = runRuleContentService;
    }

    /**
     * 新增节点规则内容
     *
     * @param runRuleContentDto 节点规则内容数据
     * @return 新增结果
     */
    @PostMapping("/save")
    public R<Void> save(@Valid @RequestBody RunRuleContentDto runRuleContentDto) {
        try {
            if (runRuleContentService.insertRunRuleContent(runRuleContentDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (IllegalArgumentException e) {
            logger.error("新增节点规则内容参数错误", e);
            return R.fail(ResponseCodeEnum.ADD_PARAM_ERROR.getCode(), ResponseCodeEnum.ADD_PARAM_ERROR.getDesc());
        } catch (Exception e) {
            logger.error("新增节点规则内容系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存节点规则内容
     *
     * @param runRuleContentDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@Valid @RequestBody RunRuleContentDto runRuleContentDto) {
        try {
            runRuleContentService.updateRunRuleContent(runRuleContentDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (IllegalArgumentException e) {
            logger.error("修改节点规则内容参数错误", e);
            return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), ResponseCodeEnum.UPDATE_PARAM_ERROR.getDesc());
        } catch (Exception e) {
            logger.error("修改节点规则内容系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 删除节点规则内容
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        try {
            if (ids == null || ids.length == 0) {
                logger.warn("删除节点规则内容失败：主键列表为空");
                return R.fail(ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), ResponseCodeEnum.DELETE_PARAM_ERROR.getDesc());
            }
            runRuleContentService.deleteRunRuleContentByIds(ids);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (IllegalArgumentException e) {
            logger.error("删除节点规则内容参数错误", e);
            return R.fail(ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), ResponseCodeEnum.DELETE_PARAM_ERROR.getDesc());
        } catch (Exception e) {
            logger.error("删除节点规则内容系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
} 