package com.ideal.envc.producer;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.RunInstanceInfoStateMessage;
import com.ideal.envc.model.enums.MessageTopicEnum;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 运行实例信息状态生产者单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RunInstanceInfoStateProducerTest {

    @Mock
    private IPublisher publisher;

    @InjectMocks
    private RunInstanceInfoStateProducer runInstanceInfoStateProducer;

    private RunInstanceInfoStateMessage stateMessage;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        stateMessage = new RunInstanceInfoStateMessage();
        stateMessage.setInstanceInfoId(100L);
        stateMessage.setRuleId(1001L);
        stateMessage.setTimestamp(System.currentTimeMillis());
        stateMessage.setStatus(1);
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_成功")
    void testSendRunInstanceInfoStateMessage_Success() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        String expectedChannel = MessageTopicEnum.RUN_INSTANCE_INFO_STATE_TOPIC.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_消息为null")
    void testSendRunInstanceInfoStateMessage_NullMessage() {
        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(null);

        // 验证结果
        assertFalse(result);

        // 验证没有调用publisher
        verifyNoInteractions(publisher);
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_发送异常")
    void testSendRunInstanceInfoStateMessage_CommunicationException() throws CommunicationException {
        // 设置Mock行为 - 抛出异常
        doThrow(new CommunicationException("发送失败")).when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertFalse(result);

        // 验证方法调用
        String expectedChannel = MessageTopicEnum.RUN_INSTANCE_INFO_STATE_TOPIC.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_验证消息内容")
    void testSendRunInstanceInfoStateMessage_VerifyMessageContent() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证消息内容
        verify(publisher, times(1)).apply(anyString(), argThat(msg -> {
            Message<?> message = (Message<?>) msg;
            // 验证消息头
            Object instanceInfoIdHeader = message.getHeaders().get("instanceInfoId");
            assertEquals(100L, instanceInfoIdHeader);

            // 验证消息体
            String payload = (String) message.getPayload();
            RunInstanceInfoStateMessage parsedMessage = JSON.parseObject(payload, RunInstanceInfoStateMessage.class);
            assertEquals(stateMessage.getInstanceInfoId(), parsedMessage.getInstanceInfoId());
            assertEquals(stateMessage.getRuleId(), parsedMessage.getRuleId());
            assertEquals(stateMessage.getTimestamp(), parsedMessage.getTimestamp());
            assertEquals(stateMessage.getStatus(), parsedMessage.getStatus());

            return true;
        }));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_验证频道名称")
    void testSendRunInstanceInfoStateMessage_VerifyChannelName() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证频道名称
        String expectedChannel = MessageTopicEnum.RUN_INSTANCE_INFO_STATE_TOPIC.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_instanceInfoId为null")
    void testSendRunInstanceInfoStateMessage_NullInstanceInfoId() throws CommunicationException {
        // 设置测试数据
        stateMessage.setInstanceInfoId(null);

        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证消息头中instanceInfoId为null
        verify(publisher, times(1)).apply(anyString(), argThat(msg -> {
            Message<?> message = (Message<?>) msg;
            Object instanceInfoIdHeader = message.getHeaders().get("instanceInfoId");
            assertNull(instanceInfoIdHeader);
            return true;
        }));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_ruleId为null")
    void testSendRunInstanceInfoStateMessage_NullRuleId() throws CommunicationException {
        // 设置测试数据
        stateMessage.setRuleId(null);

        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(publisher, times(1)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_timestamp为null")
    void testSendRunInstanceInfoStateMessage_NullTimestamp() throws CommunicationException {
        // 设置测试数据
        stateMessage.setTimestamp(null);

        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(publisher, times(1)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_多次发送")
    void testSendRunInstanceInfoStateMessage_MultipleSends() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行多次发送
        boolean result1 = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);
        boolean result2 = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);
        boolean result3 = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result1);
        assertTrue(result2);
        assertTrue(result3);

        // 验证方法调用次数
        verify(publisher, times(3)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_不同的消息内容")
    void testSendRunInstanceInfoStateMessage_DifferentMessages() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 创建不同的消息
        RunInstanceInfoStateMessage message1 = new RunInstanceInfoStateMessage();
        message1.setInstanceInfoId(100L);
        message1.setRuleId(1001L);
        message1.setTimestamp(1000L);
        message1.setStatus(0);

        RunInstanceInfoStateMessage message2 = new RunInstanceInfoStateMessage();
        message2.setInstanceInfoId(200L);
        message2.setRuleId(2001L);
        message2.setTimestamp(2000L);
        message2.setStatus(1);

        // 执行测试方法
        boolean result1 = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(message1);
        boolean result2 = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(message2);

        // 验证结果
        assertTrue(result1);
        assertTrue(result2);

        // 验证方法调用次数
        verify(publisher, times(2)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_运行时异常")
    void testSendRunInstanceInfoStateMessage_RuntimeException() throws CommunicationException {
        // 设置Mock行为 - 抛出运行时异常
        doThrow(new RuntimeException("系统异常")).when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法并验证异常
        assertThrows(RuntimeException.class, () -> {
            runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);
        });

        // 验证方法调用
        verify(publisher, times(1)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_JSON序列化")
    void testSendRunInstanceInfoStateMessage_JsonSerialization() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证JSON序列化
        verify(publisher, times(1)).apply(anyString(), argThat(msg -> {
            Message<?> message = (Message<?>) msg;
            String payload = (String) message.getPayload();
            assertNotNull(payload);
            assertFalse(payload.isEmpty());
            
            // 验证可以反序列化
            RunInstanceInfoStateMessage parsedMessage = JSON.parseObject(payload, RunInstanceInfoStateMessage.class);
            assertNotNull(parsedMessage);
            
            return true;
        }));
    }

    @Test
    @DisplayName("测试发送运行实例信息状态消息_状态值验证")
    void testSendRunInstanceInfoStateMessage_StatusValidation() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 测试不同状态值
        int[] statusValues = {0, 1, 2, -1};
        
        for (int status : statusValues) {
            stateMessage.setStatus(status);
            boolean result = runInstanceInfoStateProducer.sendRunInstanceInfoStateMessage(stateMessage);
            assertTrue(result);
        }

        // 验证方法调用次数
        verify(publisher, times(statusValues.length)).apply(anyString(), any(Message.class));
    }
} 