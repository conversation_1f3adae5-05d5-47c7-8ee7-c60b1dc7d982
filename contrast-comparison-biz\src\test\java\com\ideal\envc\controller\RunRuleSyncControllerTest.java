package com.ideal.envc.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.RunRuleSyncDto;
import com.ideal.envc.model.dto.RunRuleSyncQueryDto;
import com.ideal.envc.service.IRunRuleSyncService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 节点规则同步结果Controller单元测试
 */
@ExtendWith(MockitoExtension.class)
public class RunRuleSyncControllerTest {

    private MockMvc mockMvc;

    @Mock
    private IRunRuleSyncService runRuleSyncService;

    @InjectMocks
    private RunRuleSyncController runRuleSyncController;

    private ObjectMapper objectMapper;
    private RunRuleSyncDto runRuleSyncDto;
    private List<RunRuleSyncDto> runRuleSyncDtoList;
    private PageInfo<RunRuleSyncDto> pageInfo;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(runRuleSyncController).build();
        objectMapper = new ObjectMapper();

        // 初始化测试数据
        runRuleSyncDto = new RunRuleSyncDto();
        runRuleSyncDto.setId(1L);
        runRuleSyncDto.setEnvcRunRuleId(100L);
        runRuleSyncDto.setCreatorId(1001L);
        runRuleSyncDto.setCreatorName("admin");
        runRuleSyncDto.setCreateTime(new Date());
        runRuleSyncDto.setEndTime(new Date());
        runRuleSyncDto.setResult(0);
        runRuleSyncDto.setState(1);
        runRuleSyncDto.setElapsedTime(5000L);

        // 初始化列表数据
        runRuleSyncDtoList = new ArrayList<>();
        runRuleSyncDtoList.add(runRuleSyncDto);

        // 初始化分页数据
        pageInfo = new PageInfo<>(runRuleSyncDtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("测试查询节点规则同步结果列表")
    void testList() throws Exception {
        // 准备测试数据
        TableQueryDto<RunRuleSyncQueryDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        // 模拟服务方法，使用更灵活的参数匹配
        when(runRuleSyncService.selectRunRuleSyncList(any(), any(Integer.class), any(Integer.class)))
                .thenReturn(pageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/sync/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].id").value(1))
                .andExpect(jsonPath("$.data.list[0].envcRunRuleId").value(100));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runRuleSyncService).selectRunRuleSyncList(any(), any(Integer.class), any(Integer.class));
    }

    @Test
    @DisplayName("测试查询节点规则同步结果详细信息")
    void testGetInfo() throws Exception {
        // 模拟服务方法，使用更灵活的参数匹配
        when(runRuleSyncService.selectRunRuleSyncById(any(Long.class))).thenReturn(runRuleSyncDto);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/sync/get")
                        .param("id", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.envcRunRuleId").value(100));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runRuleSyncService).selectRunRuleSyncById(any(Long.class));
    }

    @Test
    @DisplayName("测试新增保存节点规则同步结果-成功")
    void testSave_Success() throws Exception {
        // 模拟服务方法返回值大于0表示成功
        when(runRuleSyncService.insertRunRuleSync(any(RunRuleSyncDto.class))).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/sync/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleSyncDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleSyncService).insertRunRuleSync(any(RunRuleSyncDto.class));
    }

    @Test
    @DisplayName("测试新增保存节点规则同步结果-失败")
    void testSave_Fail() throws Exception {
        // 模拟服务方法返回值为0表示失败
        when(runRuleSyncService.insertRunRuleSync(any(RunRuleSyncDto.class))).thenReturn(0);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/sync/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleSyncDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("130200"));

        // 验证服务方法调用
        verify(runRuleSyncService).insertRunRuleSync(any(RunRuleSyncDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点规则同步结果")
    void testUpdate() throws Exception {
        // 模拟服务方法
        doReturn(1).when(runRuleSyncService).updateRunRuleSync(any(RunRuleSyncDto.class));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/sync/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleSyncDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleSyncService).updateRunRuleSync(any(RunRuleSyncDto.class));
    }

    @Test
    @DisplayName("测试删除节点规则同步结果")
    void testRemove() throws Exception {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 模拟服务方法，使用更灵活的参数匹配
        when(runRuleSyncService.deleteRunRuleSyncByIds(any(Long[].class))).thenReturn(2);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/sync/remove")
                        .param("ids", "1", "2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runRuleSyncService).deleteRunRuleSyncByIds(any(Long[].class));
    }
} 