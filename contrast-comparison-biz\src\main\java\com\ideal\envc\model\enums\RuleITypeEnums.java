package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则类型枚举
 * <AUTHOR>
 */
public enum RuleITypeEnums {

    /**
     * 目录类型
     */
    DIRECTORY(0L, "目录比对"),

    /**
     * 文件类型
     */
    FILE(1L, "文件获取"),

    /**
     * 脚本类型
     */
    SCRIPT(2L, "脚本获取");

    /**
     * 规则类型代码
     */
    private final Long code;

    /**
     * 规则类型名称
     */
    private final String name;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<Long, RuleITypeEnums> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleITypeEnums::getCode, Function.identity()));

    /**
     * 构造函数
     * @param code 规则类型代码
     * @param name 规则类型名称
     */
    RuleITypeEnums(Long code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取规则类型代码
     * @return 规则类型代码
     */
    public Long getCode() {
        return code;
    }

    /**
     * 获取规则类型名称
     * @return 规则类型名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据规则类型代码获取枚举实例
     * @param code 规则类型代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static RuleITypeEnums getByCode(Long code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据规则类型代码获取规则类型名称
     * @param code 规则类型代码
     * @return 规则类型名称，如果不存在则返回"未知规则类型"
     */
    public static String getNameByCode(Long code) {
        return Optional.ofNullable(getByCode(code))
                .map(RuleITypeEnums::getName)
                .orElse("未知规则类型");
    }

    /**
     * 判断给定的规则类型代码是否在枚举允许的范围内
     * @param code 规则类型代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(Long code) {
        return code != null && CODE_MAP.containsKey(code);
    }
}
