package com.ideal.envc.model.bean;

import java.io.Serializable;

/**
 * 系统列表Bean对象
 *
 * <AUTHOR>
 */
public class SystemListBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统编码
     */
    private String businessSystemCode;

    /**
     * 业务系统描述
     */
    private String businessSystemDesc;

    /**
     * 业务系统绑定人
     */
    private String creatorName;

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemCode() {
        return businessSystemCode;
    }

    public void setBusinessSystemCode(String businessSystemCode) {
        this.businessSystemCode = businessSystemCode;
    }

    public String getBusinessSystemDesc() {
        return businessSystemDesc;
    }

    public void setBusinessSystemDesc(String businessSystemDesc) {
        this.businessSystemDesc = businessSystemDesc;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
}
