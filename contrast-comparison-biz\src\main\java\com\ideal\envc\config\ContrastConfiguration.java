package com.ideal.envc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 一致性比对服务读取配类
 * <AUTHOR>
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "contrast.mq")
public class ContrastConfiguration implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 与引擎Mq交互配置
     */
    private Engine engine;

    /**
     * 与平台管理交互MQ配置
     */
    private Platform platform;

    /**
     * 获取引擎配置
     * @return 引擎配置
     */
    public Engine getEngine() {
        return engine;
    }

    /**
     * 设置引擎配置
     * @param engine 引擎配置
     */
    public void setEngine(Engine engine) {
        this.engine = engine;
    }

    /**
     * 获取平台配置
     * @return 平台配置
     */
    public Platform getPlatform() {
        return platform;
    }

    /**
     * 设置平台配置
     * @param platform 平台配置
     */
    public void setPlatform(Platform platform) {
        this.platform = platform;
    }

    /**
     * 与引擎Mq交互配置类
     */
    public static class Engine implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 下发任务到MQ的topic配置
         */
        private MqConfig sendTask;

        /**
         * 引擎响应操作结果的MQ的TOPIC配置
         */
        private MqConfig taskResult;

        /**
         * 获取下发任务配置
         * @return 下发任务配置
         */
        public MqConfig getSendTask() {
            return sendTask;
        }

        /**
         * 设置下发任务配置
         * @param sendTask 下发任务配置
         */
        public void setSendTask(MqConfig sendTask) {
            this.sendTask = sendTask;
        }

        /**
         * 获取操作结果配置
         * @return 操作结果配置
         */
        public MqConfig getTaskResult() {
            return taskResult;
        }

        /**
         * 设置操作结果配置
         * @param taskResult 操作结果配置
         */
        public void setTaskResult(MqConfig taskResult) {
            this.taskResult = taskResult;
        }
    }

    /**
     * 与平台管理交互MQ配置类
     */
    public static class Platform implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 设备联动MQ
         */
        private MqConfig computer;

        /**
         * 业务系统联动MQ
         */
        private MqConfig businessSystem;

        /**
         * 获取设备联动MQ配置
         * @return 设备联动MQ配置
         */
        public MqConfig getComputer() {
            return computer;
        }

        /**
         * 设置设备联动MQ配置
         * @param computer 设备联动MQ配置
         */
        public void setComputer(MqConfig computer) {
            this.computer = computer;
        }

        /**
         * 获取业务系统联动MQ配置
         * @return 业务系统联动MQ配置
         */
        public MqConfig getBusinessSystem() {
            return businessSystem;
        }

        /**
         * 设置业务系统联动MQ配置
         * @param businessSystem 业务系统联动MQ配置
         */
        public void setBusinessSystem(MqConfig businessSystem) {
            this.businessSystem = businessSystem;
        }
    }

    /**
     * MQ配置类
     */
    public static class MqConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 主题
         */
        private String topic;

        /**
         * 组
         */
        private String group;

        /**
         * 是否启用，默认为true
         */
        private Boolean enabled = Boolean.TRUE;

        /**
         * 获取主题
         * @return 主题
         */
        public String getTopic() {
            return topic;
        }

        /**
         * 设置主题
         * @param topic 主题
         */
        public void setTopic(String topic) {
            this.topic = topic;
        }

        /**
         * 获取组
         * @return 组
         */
        public String getGroup() {
            return group;
        }

        /**
         * 设置组
         * @param group 组
         */
        public void setGroup(String group) {
            this.group = group;
        }

        /**
         * 获取是否启用
         * @return 是否启用
         */
        public Boolean getEnabled() {
            return enabled;
        }

        /**
         * 设置是否启用
         * @param enabled 是否启用
         */
        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }
    }
}
