package com.ideal.envc.mapper;

import java.util.List;
import java.util.Map;
import com.ideal.envc.model.bean.TaskPlanListBean;
import com.ideal.envc.model.entity.TaskEntity;
import com.ideal.envc.model.bean.TaskListBean;
import com.ideal.envc.model.bean.TaskQueryBean;
import org.apache.ibatis.annotations.Param;

/**
 * 任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface TaskMapper {
    /**
     * 查询任务
     *
     * @param id 任务主键
     * @return 任务
     */
    TaskEntity selectTaskById(Long id);



    /**
     * 查询任务
     *
     * @param scheduledId 定时任务ID
     * @return 任务
     */
    TaskEntity selectTaskByScheduleId(@Param("scheduledId") Long scheduledId);

    /**
     * 查询任务列表
     *
     * @param task 任务
     * @return 任务集合
     */
    List<TaskEntity> selectTaskList(TaskEntity task);

    /**
     * 新增任务
     *
     * @param task 任务
     * @return 结果
     */
    int insertTask(TaskEntity task);

    /**
     * 修改任务
     *
     * @param task 任务
     * @return 结果
     */
    int updateTask(TaskEntity task);

    /**
     * 删除任务
     *
     * @param id 任务主键
     * @return 结果
     */
    int deleteTaskById(Long id);

    /**
     * 批量删除任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteTaskByIds(Long[] ids);

    /**
     * 检查同一方案ID和同一cron表达式的任务是否已存在
     *
     * @param envcPlanId 方案ID
     * @param cron cron表达式
     * @return 存在的任务数量
     */
    int checkTaskExists(Long envcPlanId, String cron);

    /**
     * 检查除指定ID外的同一方案ID和同一cron表达式的任务是否已存在
     *
     * @param id 任务ID
     * @param envcPlanId 方案ID
     * @param cron cron表达式
     * @return 存在的任务数量
     */
    int checkTaskExistsExcludeId(Long id, Long envcPlanId, String cron);

    /**
     * 更新任务周期表达式
     *
     * @param id 任务ID
     * @param cron cron表达式
     * @return 结果
     */
    int updateTaskCron(Long id, String cron);

    /**
     * 查询任务方案列表
     *
     * @param task 任务信息
     * @return 任务方案列表
     */
    List<TaskPlanListBean> selectTaskPlanList(TaskEntity task);

    /**
     * 根据任务ID集合查询定时ID集合
     *
     * @param taskIds 任务ID集合
     * @return 定时ID集合
     */
    List<Long> selectScheduledIdsByTaskIds(List<Long> taskIds);

    /**
     * 根据任务ID集合查询任务ID和定时ID信息
     *
     * @param taskIds 任务ID集合
     * @return 任务基本信息列表（包含ID和定时ID）
     */
    List<TaskListBean> selectTaskScheduledIdInfoByIds(List<Long> taskIds);

    /**
     * 根据任务ID集合查询任务基本信息用于组装名称映射
     *
     * @param taskIds 任务ID集合
     * @return 任务基本信息列表
     */
    List<TaskListBean> selectTaskNameInfoByIds(List<Long> taskIds);

    /**
     * 根据任务ID集合批量更新任务状态
     *
     * @param taskIds 任务ID集合
     * @param state 任务状态
     * @return 更新的行数
     */
    int updateTaskStateByIds(List<Long> taskIds, Integer state);

    /**
     * 根据任务ID更新定时ID和状态
     *
     * @param taskId 任务ID
     * @param scheduledId 定时ID
     * @param state 任务状态
     * @return 更新的行数
     */
    int updateTaskScheduledIdAndState(Long taskId, Long scheduledId, Integer state);

    /**
     * 查询任务列表（关联方案表）
     *
     * @param queryBean 查询条件
     * @return 任务列表
     */
    List<TaskListBean> selectTaskListWithPlan(TaskQueryBean queryBean);

    /**
     * 根据任务ID集合查询任务信息
     *
     * @param taskIds 任务ID集合
     * @return 任务信息列表
     */
    List<TaskEntity> selectTaskByIds(List<Long> taskIds);

    /**
     * 根据任务ID列表查询任务信息（包含方案名称）
     *
     * @param taskIds 任务ID列表
     * @return 任务信息列表
     */
    List<TaskListBean> selectTaskListByIds(List<Long> taskIds);

    /**
     * 查询任务详情（包含方案名称）
     *
     * @param id 任务主键
     * @return 任务详情
     */
    TaskListBean selectTaskDetailById(Long id);
}
