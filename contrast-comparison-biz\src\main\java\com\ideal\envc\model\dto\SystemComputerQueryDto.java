package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.List;

/**
 * 设备查询实体
 *
 * <AUTHOR>
 */
public class SystemComputerQueryDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 系统ID */
    private Long businessSystemId;
    /** 设备ID */
    private Long computerId;
    /** 代理IP */
    private String computerIp;
    /** 代理名称 */
    private String computerName;
    /** 中心ID */
    private Long centerId;
    /** 中心名称 */
    private String centerName;
    /** 存储时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 添加人ID */
    private Long creatorId;
    /** 创建人名称 */
    private String creatorName;

    /**
     * 排除设备ID集合
     */
    private List<Long> excludeComputerIds;

    /**
     * 指定设备ID集合
     */
    private List<Long> appointComputerIds;


    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setComputerId(Long computerId){
        this.computerId = computerId;
    }

    public Long getComputerId(){
        return computerId;
    }

    public void setComputerIp(String computerIp){
        this.computerIp = computerIp;
    }

    public String getComputerIp(){
        return computerIp;
    }

    public void setComputerName(String computerName){
        this.computerName = computerName;
    }

    public String getComputerName(){
        return computerName;
    }

    public void setCenterId(Long centerId){
        this.centerId = centerId;
    }

    public Long getCenterId(){
        return centerId;
    }

    public void setCenterName(String centerName){
        this.centerName = centerName;
    }

    public String getCenterName(){
        return centerName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }


    public List<Long> getExcludeComputerIds() {
        return excludeComputerIds;
    }

    public void setExcludeComputerIds(List<Long> excludeComputerIds) {
        this.excludeComputerIds = excludeComputerIds;
    }

    public List<Long> getAppointComputerIds() {
        return appointComputerIds;
    }

    public void setAppointComputerIds(List<Long> appointComputerIds) {
        this.appointComputerIds = appointComputerIds;
    }

    @Override
    public String toString() {
        return "SystemComputerQueryDto{" +
                "id=" + id +
                ", businessSystemId=" + businessSystemId +
                ", computerId=" + computerId +
                ", computerIp='" + computerIp + '\'' +
                ", computerName='" + computerName + '\'' +
                ", centerId=" + centerId +
                ", centerName='" + centerName + '\'' +
                ", createTime=" + createTime +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", excludeComputerIds=" + excludeComputerIds +
                ", appointComputerIds=" + appointComputerIds +
                '}';
    }
}

