package com.ideal.envc.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.exception.ScheduleJobOperateException;
import com.ideal.envc.interaction.model.CenterDto;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.mapper.TaskMapper;
import com.ideal.envc.model.bean.TaskListBean;
import com.ideal.envc.model.bean.TaskPlanListBean;
import com.ideal.envc.model.bean.TaskQueryBean;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.envc.model.dto.TaskCronUpdateDto;
import com.ideal.envc.model.dto.TaskDto;
import com.ideal.envc.model.dto.TaskOperateResultDto;
import com.ideal.envc.model.dto.TaskPlanListDto;
import com.ideal.envc.model.dto.TaskQueryDto;
import com.ideal.envc.model.dto.TaskStartOrStopDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.TaskEntity;
import com.ideal.envc.model.enums.JobHandlerEnum;
import com.ideal.envc.model.enums.TaskOperateEnums;
import com.ideal.envc.service.IJobOperateService;
import com.ideal.envc.service.ITaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class TaskServiceImpl implements ITaskService {
    private static final Logger logger = LoggerFactory.getLogger(TaskServiceImpl.class);

    private final TaskMapper taskMapper;
    private final SystemInteract systemInteract;
    private final IJobOperateService jobOperateService;

    public TaskServiceImpl(TaskMapper taskMapper, SystemInteract systemInteract, IJobOperateService jobOperateService) {
        this.taskMapper = taskMapper;
        this.systemInteract = systemInteract;
        this.jobOperateService = jobOperateService;
    }

    /**
     * 查询任务
     *
     * @param id 任务主键
     * @return 任务
     */
    @Override
    public TaskDto selectTaskById(Long id) {
        TaskEntity task = taskMapper.selectTaskById(id);
        if (task == null) {
            return null;
        }
        return BeanUtils.copy(task, TaskDto.class);
    }

    /**
     * 查询任务列表
     *
     * @param taskQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 任务列表
     */
    @Override
    public PageInfo<TaskDto> selectTaskList(TaskQueryDto taskQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            // 将QueryDto转换为QueryBean
            TaskQueryBean queryBean = BeanUtils.copy(taskQueryDto, TaskQueryBean.class);
            
            // 分页查询
            PageMethod.startPage(pageNum, pageSize);
            List<TaskListBean> taskList = taskMapper.selectTaskListWithPlan(queryBean);
            
            // 转换为DTO对象
            List<TaskDto> taskDtoList = BeanUtils.copy(taskList, TaskDto.class);
            
            if (!taskDtoList.isEmpty()) {
                // 收集所有中心ID（使用Stream和Set去重）
                Set<Long> centerIds = taskDtoList.stream()
                    .flatMap(task -> {
                        List<Long> ids = new ArrayList<>();
                        if (task.getSourceCenterId() != null) {
                            ids.add(task.getSourceCenterId());
                        }
                        if (task.getTargetCenterId() != null) {
                            ids.add(task.getTargetCenterId());
                        }
                        return ids.stream();
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
                
                if (!centerIds.isEmpty()) {
                    // 构建查询参数对象
                    CenterQueryDto centerQueryDto = new CenterQueryDto();
                    centerQueryDto.setAppointIds(new ArrayList<>(centerIds));
                    
                    // 调用平台管理接口获取中心数据
                    List<CenterDto> centerList = systemInteract.getCenterList(centerQueryDto);
                    
                    if (centerList != null && !centerList.isEmpty()) {
                        // 构建中心ID到名称的映射
                        Map<Long, String> centerMap = centerList.stream()
                                .collect(Collectors.toMap(CenterDto::getId, CenterDto::getName, (k1, k2) -> k1));
                        
                        // 设置中心名称
                        for (TaskDto task : taskDtoList) {
                            if (task.getSourceCenterId() != null) {
                                task.setSourceCenterName(centerMap.get(task.getSourceCenterId()));
                            }
                            if (task.getTargetCenterId() != null) {
                                task.setTargetCenterName(centerMap.get(task.getTargetCenterId()));
                            }
                        }
                    } else {
                        logger.warn("未查询到中心信息，centerIds: {}", centerIds);
                    }
                }
            }
            
            // 构建分页信息
            PageInfo<TaskListBean> pageInfo = new PageInfo<>(taskList);
            PageInfo<TaskDto> result = new PageInfo<>(taskDtoList);
            result.setTotal(pageInfo.getTotal());
            result.setPageNum(pageInfo.getPageNum());
            result.setPageSize(pageInfo.getPageSize());
            result.setPages(pageInfo.getPages());
            
            return result;
        } catch (Exception e) {
            logger.error("查询任务列表失败", e);
            throw new ContrastBusinessException("查询任务列表失败：" + e.getMessage());
        }
    }

    /**
     * 新增任务
     *
     * @param taskDto 任务
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    public int insertTask(TaskDto taskDto) throws ContrastBusinessException {
        try {
            // 验证cron表达式格式
            if (taskDto.getCron() != null) {
                ContrastToolUtils.validateCronExpression(taskDto.getCron());
            }

            // 验证同一方案ID和同一cron表达式的任务是否已存在
            checkTaskRule(taskDto);
            TaskEntity task = BeanUtils.copy(taskDto, TaskEntity.class);
            return taskMapper.insertTask(task);
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("新增任务时发生异常", e);
            throw new ContrastBusinessException("新增任务时发生系统异常：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public Long createTask(TaskDto taskDto, UserDto userDto) throws ScheduleJobOperateException, ContrastBusinessException {
        // 验证cron表达式格式
        if (taskDto.getCron() != null) {
            ContrastToolUtils.validateCronExpression(taskDto.getCron());
        }

        // 验证同一方案ID和同一cron表达式的任务是否已存在
        checkTaskRule(taskDto);
        TaskEntity task = BeanUtils.copy(taskDto, TaskEntity.class);
        // 设置创建人信息
        task.setCreatorId(userDto.getId());
        task.setCreatorName(userDto.getFullName());
        // 设置更新人信息
        task.setUpdatorId(userDto.getId());
        task.setUpdatorName(userDto.getFullName());
        // 设置任务默认状态（1：启用）
        task.setEnabled(1);
        // 设置任务默认启停状态（1：停止）
        task.setState(TaskOperateEnums.STOP.getCode());

        int taskCount = taskMapper.insertTask(task);
        if(taskCount<=0){
            throw new ContrastBusinessException("创建任务失败");
        }

        ContrastScheduleJobTaskDto contrastScheduleJobTaskDto = getContrastScheduleJobTaskDto(task.getId(), taskDto, userDto);
        int scheduleJobId = jobOperateService.createAndStartJob(contrastScheduleJobTaskDto);
        if(scheduleJobId<=0){
            throw new ScheduleJobOperateException("创建任务失败");
        }

        // 使用补偿机制确保数据一致性
        return updateTaskWithCompensation(task.getId(), scheduleJobId);
    }

    /**
     * 更新任务状态并提供补偿机制
     *
     * @param taskId 任务ID
     * @param scheduleJobId 定时任务ID
     * @return 任务ID
     * @throws ContrastBusinessException 业务异常
     */
    private Long updateTaskWithCompensation(Long taskId, int scheduleJobId) throws ContrastBusinessException {
        final int maxRetryTimes = 3;
        int retryCount = 0;
        Exception lastException = null;

        while (retryCount < maxRetryTimes) {
            try {
                taskMapper.updateTaskScheduledIdAndState(taskId, (long) scheduleJobId, TaskOperateEnums.START.getCode());
                logger.info("任务创建成功，任务ID：{}，定时任务ID：{}，重试次数：{}", taskId, scheduleJobId, retryCount);
                return taskId;
            } catch (Exception e) {
                lastException = e;
                retryCount++;
                logger.warn("更新任务状态失败，任务ID：{}，定时任务ID：{}，重试次数：{}/{}",
                        taskId, scheduleJobId, retryCount, maxRetryTimes, e);

                if (retryCount < maxRetryTimes) {
                    // 等待一段时间后重试
                    try {
                        Thread.sleep(100 * retryCount); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        // 所有重试都失败，执行补偿操作
        logger.error("更新任务状态失败，已达到最大重试次数，开始执行补偿操作删除外部定时任务，任务ID：{}，定时任务ID：{}",
                taskId, scheduleJobId, lastException);

        executeCompensationWithRetry(scheduleJobId);

        // 重新抛出原始异常，让事务回滚
        throw new ContrastBusinessException("更新任务状态失败，已执行补偿操作：" + lastException.getMessage(), lastException);
    }

    /**
     * 执行补偿操作（带重试机制）
     *
     * @param scheduleJobId 定时任务ID
     */
    private void executeCompensationWithRetry(int scheduleJobId) {
        final int maxCompensateRetryTimes = 2;
        int compensateRetryCount = 0;
        boolean compensateSuccess = false;

        while (compensateRetryCount < maxCompensateRetryTimes && !compensateSuccess) {
            try {
                boolean removeResult = jobOperateService.removeJob(scheduleJobId);
                if (removeResult) {
                    logger.info("补偿操作成功，已删除外部定时任务，定时任务ID：{}，重试次数：{}",
                            scheduleJobId, compensateRetryCount);
                    compensateSuccess = true;
                } else {
                    logger.warn("补偿操作失败，删除外部定时任务返回false，定时任务ID：{}，重试次数：{}/{}",
                            scheduleJobId, compensateRetryCount + 1, maxCompensateRetryTimes);
                }
            } catch (Exception compensateException) {
                logger.error("补偿操作异常，定时任务ID：{}，重试次数：{}/{}",
                        scheduleJobId, compensateRetryCount + 1, maxCompensateRetryTimes, compensateException);
            }

            compensateRetryCount++;
            if (!compensateSuccess && compensateRetryCount < maxCompensateRetryTimes) {
                try {
                    Thread.sleep(200 * compensateRetryCount); // 递增等待时间
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        if (!compensateSuccess) {
            logger.error("补偿操作最终失败，无法删除外部定时任务，定时任务ID：{}，请手动处理", scheduleJobId);
            // 这里可以发送告警或记录到专门的补偿失败表中
        }
    }

    /**
     * 检查任务规则
     *
     * @param taskDto 任务DTO
     * @throws ContrastBusinessException 业务异常
     */
    public void checkTaskRule(TaskDto taskDto) throws ContrastBusinessException {
        if (taskDto == null) {
            throw new ContrastBusinessException("任务参数不能为空");
        }
        if (taskDto.getEnvcPlanId() == null || taskDto.getEnvcPlanId() <= 0) {
            throw new ContrastBusinessException("任务方案ID不能为空");
        }
        if (taskDto.getCron() == null || taskDto.getCron().trim().isEmpty()) {
            throw new ContrastBusinessException("任务cron表达式不能为空");
        }

        // 验证同一方案ID和同一cron表达式的任务是否已存在
        try {
            int count = taskMapper.checkTaskExists(taskDto.getEnvcPlanId(), taskDto.getCron().trim());
            if (count > 0) {
                throw new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入");
            }
        } catch (Exception e) {
            if (e instanceof ContrastBusinessException) {
                throw e;
            }
            logger.error("检查任务是否存在时发生异常，方案ID：{}，cron表达式：{}",
                    taskDto.getEnvcPlanId(), taskDto.getCron(), e);
            throw new ContrastBusinessException("检查任务规则时发生系统异常：" + e.getMessage(), e);
        }
    }

    /**
     * 获取对比调度任务DTO
     *
     * @param taskId 任务ID
     * @param taskDto 任务DTO
     * @param userDto 用户DTO
     * @return 对比调度任务DTO
     */
    public ContrastScheduleJobTaskDto getContrastScheduleJobTaskDto(Long taskId, TaskDto taskDto, UserDto userDto) {
        if (taskDto == null) {
            return null;
        }
        ContrastScheduleJobTaskDto contrastScheduleJobTaskDto = new ContrastScheduleJobTaskDto();
        contrastScheduleJobTaskDto.setTaskId(taskId);
        contrastScheduleJobTaskDto.setPlanId(taskDto.getEnvcPlanId());
        contrastScheduleJobTaskDto.setCron(taskDto.getCron());
        contrastScheduleJobTaskDto.setTaskName("对比任务_" + taskId);
        if (userDto != null) {
            contrastScheduleJobTaskDto.setCreatorId(userDto.getId());
            contrastScheduleJobTaskDto.setCreateName(userDto.getFullName());
        }
        contrastScheduleJobTaskDto.setScheduleJobId(taskDto.getScheduledId());
        // 设置job处理器名称
        contrastScheduleJobTaskDto.setJobHandlerName(JobHandlerEnum.CONTRAST_JOB_HANDLER.getHandlerName());
        return contrastScheduleJobTaskDto;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, RuntimeException.class})
    public int modifyTask(TaskDto taskDto,UserDto userDto) throws ContrastBusinessException {
        // 验证除指定ID外的同一方案ID和同一cron表达式的任务是否已存在

        checkTaskMustParam(taskDto);
        TaskEntity taskEntity = taskMapper.selectTaskById(taskDto.getId());
        if(taskEntity==null){
            throw new ContrastBusinessException("任务不存在");
        }
        boolean exists = checkTaskExistsExcludeId(taskDto.getId(), taskDto.getEnvcPlanId(), taskDto.getCron());
        if (exists) {
            logger.warn("除指定ID外的同一方案ID和同一cron表达式的任务已存在，任务ID：{}，方案ID：{}，cron表达式：{}", taskDto.getId(), taskDto.getEnvcPlanId(), taskDto.getCron());
            throw new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入");
        }
        TaskEntity task = BeanUtils.copy(taskDto, TaskEntity.class);
        int rows = taskMapper.updateTask(task);

        if(rows>0 && !task.getCron().equals(taskEntity.getCron())){
            //调用更新任务表达式
            ContrastScheduleJobTaskDto contrastScheduleJobTaskDto = getContrastScheduleJobTaskDto(task.getId(), taskDto, userDto);
            boolean modify = jobOperateService.modifyJob(contrastScheduleJobTaskDto);
            if(!modify){
                throw new ContrastBusinessException("更新任务表达式失败");
            }
        }
        return rows;
    }

    /**
     * 操作任务（启动或停止）
     *
     * @param taskStartOrStopDto 任务启停参数
     * @param userDto 用户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operateTasks(TaskStartOrStopDto taskStartOrStopDto, UserDto userDto) throws ContrastBusinessException {
        // 参数校验
        validateOperateTasksParameters(taskStartOrStopDto, userDto);

        // 查询所有任务的当前状态
        List<TaskListBean> taskList = taskMapper.selectTaskListByIds(taskStartOrStopDto.getTaskIdList());
        if (taskList == null || taskList.isEmpty()) {
            throw new ContrastBusinessException("未找到指定的任务信息");
        }

        // 分离出状态已经符合目标状态的任务和需要操作的任务
        TaskOperateSeparationResult separationResult = separateTasksByTargetState(taskList, taskStartOrStopDto.getOperateType());
        
        // 如果有任务已经处于目标状态，生成提示信息
        handleTasksAlreadyInTargetState(separationResult.getTasksAlreadyInState(), taskStartOrStopDto.getOperateType(), separationResult.getTasksToOperate().isEmpty());

        List<TaskListBean> tasksToOperate = separationResult.getTasksToOperate();
        logger.info("开始操作任务，操作类型：{}，需要操作的任务数量：{}",
                TaskOperateEnums.getNameByCode(taskStartOrStopDto.getOperateType()),
                tasksToOperate.size());

        // 获取需要操作的任务的调度ID
        List<Long> taskIdsToOperate = tasksToOperate.stream()
                .map(TaskListBean::getId)
                .collect(Collectors.toList());
        
        List<Long> scheduledIds = taskMapper.selectScheduledIdsByTaskIds(taskIdsToOperate);
        if(scheduledIds==null || scheduledIds.isEmpty()){
            logger.warn("未找到对应的定时任务ID，任务ID列表：{}", taskIdsToOperate);
            throw new ContrastBusinessException("未找到对应的定时任务ID");
        }

        // 根据操作类型执行不同的处理逻辑
        executeTaskOperation(taskStartOrStopDto.getOperateType(), scheduledIds, userDto);
        
        // 更新任务状态
        updateTasksState(taskIdsToOperate, taskStartOrStopDto.getOperateType());
        
        logger.info("任务操作完成，操作类型：{}，成功操作的任务ID列表：{}",
                TaskOperateEnums.getNameByCode(taskStartOrStopDto.getOperateType()),
                taskIdsToOperate);
    }

    /**
     * 验证操作任务的参数
     */
    private void validateOperateTasksParameters(TaskStartOrStopDto taskStartOrStopDto, UserDto userDto) throws ContrastBusinessException {
        if(taskStartOrStopDto==null){
            throw new ContrastBusinessException("任务操作参数为空");
        }
        if(!TaskOperateEnums.isValidCode(taskStartOrStopDto.getOperateType())){
            throw new ContrastBusinessException("操作类型不在范围内");
        }
        if(taskStartOrStopDto.getTaskIdList() == null || taskStartOrStopDto.getTaskIdList().isEmpty()) {
            throw new ContrastBusinessException("任务ID列表不能为空");
        }
        if(userDto == null || userDto.getId() == null) {
            throw new ContrastBusinessException("用户信息不能为空");
        }
    }

    /**
     * 按目标状态分离任务
     */
    private TaskOperateSeparationResult separateTasksByTargetState(List<TaskListBean> taskList, Integer operateType) {
        List<TaskListBean> tasksToOperate = new ArrayList<>();
        List<TaskListBean> tasksAlreadyInState = new ArrayList<>();
        
        for (TaskListBean task : taskList) {
            if (task.getState() != null && task.getState().equals(operateType)) {
                tasksAlreadyInState.add(task);
            } else {
                tasksToOperate.add(task);
            }
        }
        
        return new TaskOperateSeparationResult(tasksToOperate, tasksAlreadyInState);
    }

    /**
     * 处理已经处于目标状态的任务
     */
    private void handleTasksAlreadyInTargetState(List<TaskListBean> tasksAlreadyInState, Integer operateType, boolean allTasksInTargetState) throws ContrastBusinessException {
        if (!tasksAlreadyInState.isEmpty()) {
            StringBuilder message = new StringBuilder();
            message.append("本次操作未执行：所选任务中存在已经处于");
            message.append(TaskOperateEnums.START.getCode().equals(operateType) ? "启动" : "停止");
            message.append("状态的任务。请重新选择需要");
            message.append(TaskOperateEnums.START.getCode().equals(operateType) ? "启动" : "停止");
            message.append("的任务。\n涉及任务：");
            
            for (int i = 0; i < tasksAlreadyInState.size(); i++) {
                TaskListBean task = tasksAlreadyInState.get(i);
                message.append(String.format("\n[%d] %s", 
                    task.getId(), task.getEnvcPlanName() != null ? task.getEnvcPlanName() : "未知方案"));
            }
            
            logger.warn(message.toString());
            
            // 如果所有任务都已经是目标状态，直接返回
            if (allTasksInTargetState) {
                throw new ContrastBusinessException(message.toString());
            }
        }
    }

    /**
     * 执行任务操作（启动或停止）
     */
    private void executeTaskOperation(Integer operateType, List<Long> scheduledIds, UserDto userDto) throws ContrastBusinessException {
        if(TaskOperateEnums.START.getCode().equals(operateType)) {
            executeStartOperation(scheduledIds, userDto);
        } else if(TaskOperateEnums.STOP.getCode().equals(operateType)) {
            executeStopOperation(scheduledIds, userDto);
        } else {
            throw new ContrastBusinessException("不支持的操作类型：" + operateType);
        }
    }

    /**
     * 执行启动操作
     */
    private void executeStartOperation(List<Long> scheduledIds, UserDto userDto) throws ContrastBusinessException {
        for (Long scheduledId : scheduledIds) {
            try {
                logger.info("当前操作人：{}，准备进行周期任务启动操作，任务定时ID：{}", userDto.getFullName(), scheduledId);
                
                TaskEntity task = taskMapper.selectTaskByScheduleId(scheduledId);
                if (task == null) {
                    logger.error("未找到对应的任务信息，定时任务ID：{}", scheduledId);
                    throw new ContrastBusinessException("未找到对应的任务信息，定时任务ID：" + scheduledId);
                }
                
                TaskDto taskDto = BeanUtils.copy(task, TaskDto.class);
                if (taskDto == null) {
                    throw new ContrastBusinessException("任务实体转换失败，任务ID：" + task.getId());
                }
                
                ContrastScheduleJobTaskDto contrastScheduleJobTaskDto = getContrastScheduleJobTaskDto(task.getId(), taskDto, userDto);
                if (contrastScheduleJobTaskDto == null) {
                    throw new ContrastBusinessException("创建定时任务DTO失败，任务ID：" + task.getId());
                }
                
                contrastScheduleJobTaskDto.setScheduleJobId(scheduledId);
                boolean modify = jobOperateService.modifyJob(contrastScheduleJobTaskDto);
                if(!modify){
                    throw new ContrastBusinessException("更新定时任务信息失败，任务ID：" + task.getId());
                }
                
                boolean startResult = jobOperateService.startJob(Math.toIntExact(scheduledId));
                if (!startResult) {
                    throw new ContrastBusinessException("启动定时任务失败，任务ID：" + task.getId() + "，定时任务ID：" + scheduledId);
                }
                
                logger.info("当前操作人：{}，完成周期任务启动操作，任务定时ID：{}，任务ID：{}，方案ID：{}", 
                    userDto.getFullName(), scheduledId, task.getId(), task.getEnvcPlanId());
            } catch (Exception e) {
                logger.error("启动任务过程中发生异常，定时任务ID：{}", scheduledId, e);
                throw new ContrastBusinessException("启动任务失败：" + e.getMessage());
            }
        }
    }

    /**
     * 执行停止操作
     */
    private void executeStopOperation(List<Long> scheduledIds, UserDto userDto) throws ContrastBusinessException {
        for (Long scheduledId : scheduledIds) {
            try {
                logger.info("当前操作人：{}，准备进行周期任务停止操作，任务定时ID：{}", userDto.getFullName(), scheduledId);
                
                TaskEntity task = taskMapper.selectTaskByScheduleId(scheduledId);
                if (task == null) {
                    logger.error("停止任务时，未找到对应的任务信息，定时任务ID：{}", scheduledId);
                    throw new ContrastBusinessException("未找到对应的任务信息，定时任务ID：" + scheduledId);
                }
                
                boolean stopResult = jobOperateService.stopJob(Math.toIntExact(scheduledId));
                if (!stopResult) {
                    logger.error("停止定时任务失败，任务ID：{}，定时任务ID：{}", task.getId(), scheduledId);
                    throw new ContrastBusinessException("停止定时任务失败，任务ID：" + task.getId() + "，定时任务ID：" + scheduledId);
                }
                
                logger.info("当前操作人：{}，完成周期任务停止操作，任务定时ID：{}", userDto.getFullName(), scheduledId);
            } catch (Exception e) {
                logger.error("停止任务过程中发生异常，定时任务ID：{}", scheduledId, e);
                throw new ContrastBusinessException("停止任务失败：" + e.getMessage());
            }
        }
    }

    /**
     * 更新任务状态
     */
    private void updateTasksState(List<Long> taskIdsToOperate, Integer operateType) throws ContrastBusinessException {
        try {
            int updateResult = taskMapper.updateTaskStateByIds(taskIdsToOperate, operateType);
            if (updateResult <= 0) {
                throw new ContrastBusinessException("更新任务状态失败，任务ID列表：" + taskIdsToOperate);
            }
        } catch (Exception e) {
            throw new ContrastBusinessException("更新任务状态时发生异常：" + e.getMessage());
        }
    }

    /**
     * 任务操作分离结果类
     */
    private static class TaskOperateSeparationResult {
        private final List<TaskListBean> tasksToOperate;
        private final List<TaskListBean> tasksAlreadyInState;

        public TaskOperateSeparationResult(List<TaskListBean> tasksToOperate, List<TaskListBean> tasksAlreadyInState) {
            this.tasksToOperate = tasksToOperate;
            this.tasksAlreadyInState = tasksAlreadyInState;
        }

        public List<TaskListBean> getTasksToOperate() {
            return tasksToOperate;
        }

        public List<TaskListBean> getTasksAlreadyInState() {
            return tasksAlreadyInState;
        }
    }

    /**
     * 检查任务必须参数
     *
     * @param taskDto 任务DTO
     * @throws ContrastBusinessException 业务异常
     */
    public void checkTaskMustParam(TaskDto taskDto) throws ContrastBusinessException {
        if (taskDto == null) {
            throw new ContrastBusinessException("任务参数不能为空");
        }
        if (taskDto.getId() == null || taskDto.getId() <= 0) {
            throw new ContrastBusinessException("任务ID不能为空");
        }
        if (taskDto.getEnvcPlanId() == null || taskDto.getEnvcPlanId() <= 0) {
            throw new ContrastBusinessException("任务方案ID不能为空");
        }
        if (taskDto.getCron() == null || taskDto.getCron().trim().isEmpty()) {
            throw new ContrastBusinessException("任务cron表达式不能为空");
        }
    }

    /**
     * 修改任务
     *
     * @param taskDto 任务
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    public int updateTask(TaskDto taskDto) throws ContrastBusinessException {
        try {
            // 验证cron表达式格式
            if (taskDto.getCron() != null) {
                ContrastToolUtils.validateCronExpression(taskDto.getCron());
            }

            // 验证除指定ID外的同一方案ID和同一cron表达式的任务是否已存在
            if (taskDto.getId() != null && taskDto.getEnvcPlanId() != null && taskDto.getCron() != null && !taskDto.getCron().trim().isEmpty()) {
                boolean exists = checkTaskExistsExcludeId(taskDto.getId(), taskDto.getEnvcPlanId(), taskDto.getCron());
                if (exists) {
                    logger.warn("除指定ID外的同一方案ID和同一cron表达式的任务已存在，任务ID：{}，方案ID：{}，cron表达式：{}", taskDto.getId(), taskDto.getEnvcPlanId(), taskDto.getCron());
                    throw new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入");
                }
            }

            TaskEntity task = BeanUtils.copy(taskDto, TaskEntity.class);
            return taskMapper.updateTask(task);
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("修改任务时发生异常，任务ID：{}", taskDto.getId(), e);
            throw new ContrastBusinessException("修改任务时发生系统异常：" + e.getMessage(), e);
        }
    }

    /**
     * 批量删除任务
     *
     * @param ids 需要删除的任务主键
     * @return 结果
     */
    @Override
    public int deleteTaskByIds(Long[] ids) {
        return taskMapper.deleteTaskByIds(ids);
    }

    /**
     * 检查同一方案ID和同一cron表达式的任务是否已存在
     *
     * @param envcPlanId 方案ID
     * @param cron cron表达式
     * @return 是否存在
     */
    @Override
    public boolean checkTaskExists(Long envcPlanId, String cron) {
        if (envcPlanId == null || cron == null || cron.trim().isEmpty()) {
            return false;
        }
        int count = taskMapper.checkTaskExists(envcPlanId, cron.trim());
        return count > 0;
    }

    /**
     * 检查除指定ID外的同一方案ID和同一cron表达式的任务是否已存在
     *
     * @param id 任务ID
     * @param envcPlanId 方案ID
     * @param cron cron表达式
     * @return 是否存在
     */
    @Override
    public boolean checkTaskExistsExcludeId(Long id, Long envcPlanId, String cron) {
        if (id == null || envcPlanId == null || cron == null || cron.trim().isEmpty()) {
            return false;
        }
        int count = taskMapper.checkTaskExistsExcludeId(id, envcPlanId, cron.trim());
        return count > 0;
    }

    /**
     * 更新任务周期表达式
     *
     * @param taskCronUpdateDto 任务周期表达式更新数据
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    public int updateTaskCron(TaskCronUpdateDto taskCronUpdateDto, UserDto userDto) throws ContrastBusinessException {
        logger.info("更新任务周期表达式：{}", taskCronUpdateDto);
        if(taskCronUpdateDto.getCron()==null || taskCronUpdateDto.getCron().trim().isEmpty()){
            throw new ContrastBusinessException("任务cron表达式不能为空");
        }

        // 验证cron表达式格式
        ContrastToolUtils.validateCronExpression(taskCronUpdateDto.getCron());

        if(taskCronUpdateDto.getId()==null || taskCronUpdateDto.getId()<=0){
            throw new ContrastBusinessException("任务ID不能为空");
        }
        // 查询任务信息
        TaskEntity taskEntity = taskMapper.selectTaskById(taskCronUpdateDto.getId());
        if (taskEntity == null) {
            logger.warn("任务不存在，任务ID：{}", taskCronUpdateDto.getId());
            throw new ContrastBusinessException("任务不存在");
        }

        // 验证同一方案ID和同一cron表达式的任务是否已存在
        boolean exists = checkTaskExistsExcludeId(taskCronUpdateDto.getId(), taskEntity.getEnvcPlanId(), taskCronUpdateDto.getCron());
        if (exists) {
            logger.warn("同一方案下已存在相同cron表达式的任务，任务ID：{}，方案ID：{}，cron表达式：{}",
                    taskCronUpdateDto.getId(), taskEntity.getEnvcPlanId(), taskCronUpdateDto.getCron());
            throw new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入");
        }

        // 更新cron表达式
        int rows = taskMapper.updateTaskCron(taskCronUpdateDto.getId(), taskCronUpdateDto.getCron().trim());
        if(rows>0 ){
            //调用更新任务表达式
            TaskDto taskDto = BeanUtils.copy(taskEntity,TaskDto.class);
            ContrastScheduleJobTaskDto contrastScheduleJobTaskDto = getContrastScheduleJobTaskDto(taskDto.getId(), taskDto, userDto);
            boolean modify = jobOperateService.modifyJob(contrastScheduleJobTaskDto);
            if(!modify){
                throw new ContrastBusinessException("更新任务表达式失败");
            }
        }
        return rows;
    }

    /**
     * 查询任务方案列表
     *
     * @param taskQueryDto 任务查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 任务方案列表
     */
    @Override
    public PageInfo<TaskPlanListDto> selectTaskPlanList(TaskQueryDto taskQueryDto, Integer pageNum, Integer pageSize) {
        logger.info("查询任务方案列表，查询条件：{}", taskQueryDto);

        // 构建查询条件
        TaskEntity taskEntity = new TaskEntity();
        if (taskQueryDto != null) {
            taskEntity.setId(taskQueryDto.getId());
            taskEntity.setEnvcPlanId(taskQueryDto.getEnvcPlanId());
            taskEntity.setCron(taskQueryDto.getCron());
            taskEntity.setState(taskQueryDto.getState());
            taskEntity.setSourceCenterId(taskQueryDto.getSourceCenterId());
            taskEntity.setTargetCenterId(taskQueryDto.getTargetCenterId());
            taskEntity.setScheduledId(taskQueryDto.getScheduledId());
        }

        // 分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<TaskPlanListBean> beans = taskMapper.selectTaskPlanList(taskEntity);

        // 获取中心列表并构建ID到名称的映射
        List<CenterDto> centerList = systemInteract.getCenterList(null);
        Map<Long, String> centerMap = centerList.stream()
                .collect(Collectors.toMap(CenterDto::getId, CenterDto::getName, (k1, k2) -> k1));

        // 转换为DTO对象并设置中心名称
        List<TaskPlanListDto> dtos = BeanUtils.copy(beans, TaskPlanListDto.class);
        dtos.forEach(dto -> {
            if (dto.getSourceCenterId() != null) {
                dto.setSourceCenterName(centerMap.get(dto.getSourceCenterId()));
            }
            if (dto.getTargetCenterId() != null) {
                dto.setTargetCenterName(centerMap.get(dto.getTargetCenterId()));
            }
        });

        // 构建分页结果
        PageInfo<TaskPlanListBean> pageInfo = new PageInfo<>(beans);
        PageInfo<TaskPlanListDto> result = new PageInfo<>(dtos);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());

        return result;
    }

    /**
     * 根据任务ID集合查询定时ID集合
     *
     * @param taskIds 任务ID集合
     * @return 定时ID集合
     */
    @Override
    public List<Long> selectScheduledIdsByTaskIds(List<Long> taskIds) {
        if (taskIds == null || taskIds.isEmpty()) {
            return Collections.emptyList();
        }
        logger.info("根据任务ID集合查询定时ID集合，任务ID集合：{}", taskIds);
        return taskMapper.selectScheduledIdsByTaskIds(taskIds);
    }

    /**
     * 根据任务ID更新定时ID和状态
     *
     * @param taskId 任务ID
     * @param scheduledId 定时ID
     * @param state 任务状态
     * @return 更新的行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateTaskScheduledIdAndState(Long taskId, Long scheduledId, Integer state) {
        if (taskId == null) {
            logger.warn("任务ID不能为空");
            return 0;
        }

        if (state == null) {
            logger.warn("任务状态不能为空");
            return 0;
        }

        logger.info("根据任务ID更新定时ID和状态，任务ID：{}，定时ID：{}，状态：{}", taskId, scheduledId, state);
        int rows = taskMapper.updateTaskScheduledIdAndState(taskId, scheduledId, state);
        if (rows > 0) {
            logger.info("更新成功，影响行数：{}", rows);
        } else {
            logger.warn("更新失败，可能任务ID不存在");
        }
        return rows;
    }

    /**
     * 删除定时任务并删除任务
     *
     * @param taskIdList 任务ID列表
     * @param userDto 用户信息
     * @return 操作结果，包含成功和失败的任务ID和名称列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TaskOperateResultDto removeScheduleJob(List<Long> taskIdList, UserDto userDto) {
        TaskOperateResultDto result = new TaskOperateResultDto();

        // 参数验证
        if (!validateRemoveScheduleJobParameters(taskIdList, userDto, result)) {
            return result;
        }

        logger.info("开始删除定时任务，任务ID列表：{}，操作人：{}",
                taskIdList, userDto.getFullName());

        try {
            // 查询任务基本信息并组装名称映射关系
            List<TaskListBean> taskInfoList = taskMapper.selectTaskNameInfoByIds(taskIdList);
            Map<Long, String> taskNameMap = taskInfoList.stream().collect(
                java.util.stream.Collectors.toMap(
                    TaskListBean::getId,
                    task -> {
                        String planName = task.getPlanName() != null ? task.getPlanName() : "未知方案";
                        String cron = task.getCron() != null ? task.getCron() : "";
                        return planName + "-" + cron;
                    },
                    // 如果有重复键，保留原有值
                    (existing, replacement) -> existing
                )
            );
            logger.info("查询到任务名称映射关系：{}", taskNameMap);

            // 删除定时任务
            processScheduleJobDeletion(taskIdList, taskNameMap, result);
            
            // 删除任务记录
            processTaskDeletion(taskIdList, taskNameMap, result);

            logger.info("删除定时任务和任务完成，结果：{}", result);
            return result;
        } catch (Exception e) {
            logger.error("删除定时任务异常，任务ID列表：{}，异常信息：{}",
                    taskIdList, e.getMessage(), e);
            result.setAllSuccess(false);
            result.setFailReason("删除定时任务异常：" + e.getMessage());
            return result;
        }
    }

    /**
     * 验证删除定时任务的参数
     */
    private boolean validateRemoveScheduleJobParameters(List<Long> taskIdList, UserDto userDto, TaskOperateResultDto result) {
        if (taskIdList == null || taskIdList.isEmpty()) {
            logger.warn("任务ID列表不能为空");
            result.setAllSuccess(false);
            result.setFailReason("任务ID列表不能为空");
            return false;
        }

        if (userDto == null || userDto.getId() == null) {
            logger.warn("用户信息不能为空");
            result.setAllSuccess(false);
            result.setFailReason("用户信息不能为空");
            return false;
        }
        return true;
    }

    /**
     * 处理定时任务删除
     */
    private void processScheduleJobDeletion(List<Long> taskIdList, Map<Long, String> taskNameMap, TaskOperateResultDto result) {
        // 查询任务对应的定时ID列表
        List<Long> scheduledIdList = selectScheduledIdsByTaskIds(taskIdList);
        if (scheduledIdList.isEmpty()) {
            logger.warn("没有找到任务对应的定时ID，任务ID列表：{}", taskIdList);
            return;
        }

        logger.info("找到任务对应的定时ID列表：{}", JSON.toJSONString(scheduledIdList));
        
        // 查询任务ID和定时ID信息并创建映射关系
        List<TaskListBean> taskScheduledInfoList = taskMapper.selectTaskScheduledIdInfoByIds(taskIdList);
        Map<Long, Long> taskToScheduledMap = taskScheduledInfoList.stream().collect(
            java.util.stream.Collectors.toMap(
                TaskListBean::getId,
                TaskListBean::getScheduledId,
                // 如果有重复键，保留原有值
                (existing, replacement) -> existing
            )
        );
        
        // 删除定时任务
        removeScheduleJobs(taskIdList, taskNameMap, taskToScheduledMap, result);
    }

    /**
     * 删除定时任务
     */
    private void removeScheduleJobs(List<Long> taskIdList, Map<Long, String> taskNameMap, 
                                   Map<Long, Long> taskToScheduledMap, TaskOperateResultDto result) {
        for (Long taskId : taskIdList) {
            String taskName = getTaskNameOrDefault(taskNameMap, taskId);
            
            Long scheduledIdLong = taskToScheduledMap.get(taskId);
            if (scheduledIdLong == null) {
                logger.warn("任务ID：{}，任务名称：{} 没有对应的定时ID", taskId, taskName);
                continue;
            }

            Integer scheduledId = scheduledIdLong.intValue();
            if (scheduledId <= 0) {
                logger.warn("任务ID：{}，任务名称：{} 对应的定时任务ID无效：{}", taskId, taskName, scheduledId);
                continue;
            }

            boolean removeResult = jobOperateService.removeJob(scheduledId);
            if (!removeResult) {
                logger.error("删除定时任务失败，任务ID：{}，任务名称：{}，定时任务ID：{}", taskId, taskName, scheduledId);
                result.addFailedTask(taskId, taskName);
            } else {
                logger.info("删除定时任务成功，任务ID：{}，任务名称：{}，定时任务ID：{}", taskId, taskName, scheduledId);
            }
        }
    }

    /**
     * 处理任务记录删除
     */
    private void processTaskDeletion(List<Long> taskIdList, Map<Long, String> taskNameMap, TaskOperateResultDto result) {
        // 删除任务，只删除没有失败的任务
        List<Long> tasksToDelete = new ArrayList<>(taskIdList);
        tasksToDelete.removeAll(result.getFailedTaskIds());

        if (tasksToDelete.isEmpty()) {
            return;
        }

        // 将任务ID列表转换为数组
        Long[] taskIdArray = tasksToDelete.toArray(new Long[0]);

        try {
            // 删除任务
            int deleteRows = taskMapper.deleteTaskByIds(taskIdArray);
            handleTaskDeletionResult(deleteRows, tasksToDelete, taskNameMap, result);
        } catch (Exception e) {
            logger.error("删除任务异常，任务ID列表：{}，异常信息：{}", tasksToDelete, e.getMessage(), e);
            addFailedTasks(tasksToDelete, taskNameMap, result);
            result.setFailReason("删除任务异常：" + e.getMessage());
        }
    }

    /**
     * 处理任务删除结果
     */
    private void handleTaskDeletionResult(int deleteRows, List<Long> tasksToDelete,
                                        Map<Long, String> taskNameMap, TaskOperateResultDto result) {
        if (deleteRows > 0) {
            logger.info("删除任务成功，删除行数：{}，任务ID列表：{}", deleteRows, tasksToDelete);
            addSuccessTasks(tasksToDelete, taskNameMap, result);
        } else {
            logger.error("删除任务失败，任务ID列表：{}", tasksToDelete);
            addFailedTasks(tasksToDelete, taskNameMap, result);
            result.setFailReason("删除任务失败");
        }
    }

    /**
     * 添加成功的任务到结果中
     */
    private void addSuccessTasks(List<Long> taskIds, Map<Long, String> taskNameMap, TaskOperateResultDto result) {
        for (Long taskId : taskIds) {
            String taskName = getTaskNameOrDefault(taskNameMap, taskId);
            result.addSuccessTask(taskId, taskName);
        }
    }

    /**
     * 添加失败的任务到结果中
     */
    private void addFailedTasks(List<Long> taskIds, Map<Long, String> taskNameMap, TaskOperateResultDto result) {
        for (Long taskId : taskIds) {
            String taskName = getTaskNameOrDefault(taskNameMap, taskId);
            result.addFailedTask(taskId, taskName);
        }
    }

    /**
     * 获取任务名称或默认值
     */
    private String getTaskNameOrDefault(Map<Long, String> taskNameMap, Long taskId) {
        String taskName = taskNameMap.get(taskId);
        return taskName != null ? taskName : "未知任务";
    }

    @Override
    public TaskDto selectTaskDetailById(Long id) throws ContrastBusinessException {
        try {
            // 查询任务基本信息（包含方案名称）
            TaskListBean taskDetail = taskMapper.selectTaskDetailById(id);
            if (taskDetail == null) {
                throw new ContrastBusinessException("未找到指定的任务信息");
            }

            // 转换为DTO
            TaskDto taskDto = BeanUtils.copy(taskDetail, TaskDto.class);
            
            // 获取中心名称
            Set<Long> centerIds = new HashSet<>();
            if (taskDetail.getSourceCenterId() != null && taskDetail.getSourceCenterId() > 0) {
                centerIds.add(taskDetail.getSourceCenterId());
            }
            if (taskDetail.getTargetCenterId() != null && taskDetail.getTargetCenterId() > 0) {
                centerIds.add(taskDetail.getTargetCenterId());
            }

            if (!centerIds.isEmpty()) {
                // 构建查询参数
                CenterQueryDto centerQueryDto = new CenterQueryDto();
                centerQueryDto.setAppointIds(new ArrayList<>(centerIds));

                // 调用平台管理接口获取中心信息
                List<CenterDto> centerList = systemInteract.getCenterList(centerQueryDto);
                
                if (centerList != null && !centerList.isEmpty()) {
                    // 构建中心ID到名称的映射
                    Map<Long, String> centerMap = centerList.stream()
                            .collect(Collectors.toMap(CenterDto::getId, CenterDto::getName, (k1, k2) -> k1));

                    // 设置中心名称
                    if (taskDetail.getSourceCenterId() != null && taskDetail.getSourceCenterId() > 0) {
                        taskDto.setSourceCenterName(centerMap.get(taskDetail.getSourceCenterId()));
                    }
                    if (taskDetail.getTargetCenterId() != null && taskDetail.getTargetCenterId() > 0) {
                        taskDto.setTargetCenterName(centerMap.get(taskDetail.getTargetCenterId()));
                    }
                } else {
                    logger.warn("未查询到中心信息，centerIds: {}", centerIds);
                }
            }

            return taskDto;
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("查询任务详情失败", e);
            throw new ContrastBusinessException("查询任务详情失败：" + e.getMessage());
        }
    }
}

