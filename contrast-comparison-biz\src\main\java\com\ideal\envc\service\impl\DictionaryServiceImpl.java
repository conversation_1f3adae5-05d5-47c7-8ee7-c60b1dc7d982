package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.DictionaryMapper;
import com.ideal.envc.model.entity.DictionaryEntity;
import com.ideal.envc.service.IDictionaryService;
import com.ideal.envc.model.dto.DictionaryDto;
import com.ideal.envc.model.dto.DictionaryQueryDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 字典码Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class DictionaryServiceImpl implements IDictionaryService {
    private final Logger logger = LoggerFactory.getLogger(DictionaryServiceImpl.class);

    private final DictionaryMapper dictionaryMapper;

    public DictionaryServiceImpl(DictionaryMapper dictionaryMapper) {
        this.dictionaryMapper = dictionaryMapper;
    }

    /**
     * 查询字典码
     *
     * @param id 字典码主键
     * @return 字典码
     */
    @Override
    public DictionaryDto selectDictionaryById(Long id) {
        DictionaryEntity dictionary = dictionaryMapper.selectDictionaryById(id);
        return BeanUtils.copy(dictionary, DictionaryDto.class);
    }

    /**
     * 查询字典码列表
     *
     * @param dictionaryQueryDto 字典码
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 字典码
     */
    @Override
    public PageInfo<DictionaryDto> selectDictionaryList(DictionaryQueryDto dictionaryQueryDto, Integer pageNum, Integer pageSize) {
        DictionaryEntity query = BeanUtils.copy(dictionaryQueryDto, DictionaryEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<DictionaryEntity> dictionaryList = dictionaryMapper.selectDictionaryList(query);
        return PageDataUtil.toDtoPage(dictionaryList, DictionaryDto.class);
    }

    /**
     * 新增字典码
     *
     * @param dictionaryDto 字典码
     * @return 结果
     */
    @Override
    public int insertDictionary(DictionaryDto dictionaryDto) {
        DictionaryEntity dictionary = BeanUtils.copy(dictionaryDto, DictionaryEntity.class);
        return dictionaryMapper.insertDictionary(dictionary);
    }

    /**
     * 修改字典码
     *
     * @param dictionaryDto 字典码
     * @return 结果
     */
    @Override
    public int updateDictionary(DictionaryDto dictionaryDto) {
        DictionaryEntity dictionary = BeanUtils.copy(dictionaryDto, DictionaryEntity.class);
        return dictionaryMapper.updateDictionary(dictionary);
    }

    /**
     * 批量删除字典码
     *
     * @param ids 需要删除的字典码主键
     * @return 结果
     */
    @Override
    public int deleteDictionaryByIds(Long[] ids) {
        return dictionaryMapper.deleteDictionaryByIds(ids);
    }

    /**
     * 验证字典码是否存在
     *
     * @param code 字典码
     * @return 验证结果，true表示存在，false表示不存在
     */
    @Override
    public Boolean validateDictionaryCode(String code) {
        logger.info("验证字典码是否存在: {}", code);
        if (code == null || code.trim().isEmpty()) {
            return false;
        }

        DictionaryEntity query = new DictionaryEntity();
        query.setCode(code);
        List<DictionaryEntity> list = dictionaryMapper.selectDictionaryList(query);

        return list != null && !list.isEmpty();
    }
}
