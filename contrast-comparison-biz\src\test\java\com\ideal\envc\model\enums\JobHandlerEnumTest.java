package com.ideal.envc.model.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JobHandlerEnum单元测试类
 * <AUTHOR>
 */
@DisplayName("JobHandlerEnum测试")
class JobHandlerEnumTest {

    @Test
    @DisplayName("测试获取处理器名称")
    void testGetHandlerName() {
        assertEquals("contrastJobHandler", JobHandlerEnum.CONTRAST_JOB_HANDLER.getHandlerName());
    }

    @Test
    @DisplayName("测试获取处理器描述")
    void testGetDesc() {
        assertEquals("对比任务处理器", JobHandlerEnum.CONTRAST_JOB_HANDLER.getDesc());
    }

    @Test
    @DisplayName("测试根据处理器名称获取枚举实例 - 存在的处理器")
    void testGetByHandlerName_Exists() {
        JobHandlerEnum result = JobHandlerEnum.getByHandlerName("contrastJobHandler");
        assertNotNull(result);
        assertEquals(JobHandlerEnum.CONTRAST_JOB_HANDLER, result);
    }

    @Test
    @DisplayName("测试根据处理器名称获取枚举实例 - 不存在的处理器")
    void testGetByHandlerName_NotExists() {
        JobHandlerEnum result = JobHandlerEnum.getByHandlerName("nonExistentHandler");
        assertNull(result);
    }

    @Test
    @DisplayName("测试判断处理器名称是否有效 - 有效名称")
    void testIsValidHandlerName_Valid() {
        assertTrue(JobHandlerEnum.isValidHandlerName("contrastJobHandler"));
    }

    @Test
    @DisplayName("测试判断处理器名称是否有效 - 无效名称")
    void testIsValidHandlerName_Invalid() {
        assertFalse(JobHandlerEnum.isValidHandlerName("nonExistentHandler"));
    }
} 