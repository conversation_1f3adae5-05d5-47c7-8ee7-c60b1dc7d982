package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.dto.RunRuleDto;
import com.ideal.envc.model.dto.RunRuleQueryDto;
import com.ideal.envc.model.entity.RunRuleEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * RunRuleServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
public class RunRuleServiceImplTest {

    @Mock
    private RunRuleMapper runRuleMapper;

    @InjectMocks
    private RunRuleServiceImpl runRuleService;

    private RunRuleEntity mockEntity;
    private RunRuleDto mockDto;
    private List<RunRuleEntity> runRuleEntityList;
    private PageInfo<RunRuleDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试对象
        mockEntity = new RunRuleEntity();
        mockEntity.setId(1L);
        mockEntity.setEnvcRunInstanceInfoId(100L);
        mockEntity.setModel(0);
        mockEntity.setType(1L);
        mockEntity.setPath("/test/path");
        mockEntity.setEncode("UTF-8");
        mockEntity.setWay(0);
        mockEntity.setRuleType(0);
        mockEntity.setEnabled(0);
        mockEntity.setChildLevel(0);
        mockEntity.setCreatorId(1L);
        mockEntity.setCreatorName("测试用户");
        mockEntity.setCreateTime(new Date());
        mockEntity.setEndTime(new Date());
        mockEntity.setResult(0);
        mockEntity.setState(1);
        mockEntity.setElapsedTime(1000L);

        // 初始化DTO对象
        mockDto = new RunRuleDto();
        mockDto.setId(1L);
        mockDto.setEnvcRunInstanceInfoId(100L);
        mockDto.setModel(0);
        mockDto.setType(1L);
        mockDto.setPath("/test/path");
        mockDto.setEncode("UTF-8");
        mockDto.setWay(0);
        mockDto.setRuleType(0);
        mockDto.setEnabled(0);
        mockDto.setChildLevel(0);
        mockDto.setCreatorId(1L);
        mockDto.setCreatorName("测试用户");
        mockDto.setCreateTime(new Date());
        mockDto.setEndTime(new Date());
        mockDto.setResult(0);
        mockDto.setState(1);
        mockDto.setElapsedTime(1000L);

        // 初始化列表数据
        runRuleEntityList = new ArrayList<>();
        runRuleEntityList.add(mockEntity);

        // 初始化分页对象
        pageInfo = new PageInfo<>();
        List<RunRuleDto> dtoList = new ArrayList<>();
        dtoList.add(mockDto);
        pageInfo.setList(dtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        pageInfo.setPages(1);
    }

    @Test
    @DisplayName("测试根据ID查询运行规则")
    void testSelectRunRuleById() {
        // 模拟Mapper方法调用
        doReturn(mockEntity).when(runRuleMapper).selectRunRuleById(anyLong());

        // 执行测试方法
        RunRuleDto result = runRuleService.selectRunRuleById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(100L, result.getEnvcRunInstanceInfoId());
        assertEquals(0, result.getModel());
        assertEquals(1L, result.getType());
        assertEquals("/test/path", result.getPath());
        assertEquals("UTF-8", result.getEncode());
        assertEquals(0, result.getWay());
        assertEquals(0, result.getRuleType());
        assertEquals(0, result.getEnabled());
        assertEquals(0, result.getChildLevel());
        assertEquals(1L, result.getCreatorId());
        assertEquals("测试用户", result.getCreatorName());
        assertEquals(0, result.getResult());
        assertEquals(1, result.getState());
        assertEquals(1000L, result.getElapsedTime());
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).selectRunRuleById(1L);
    }

    @Test
    @DisplayName("测试查询运行规则列表")
    void testSelectRunRuleList() {
        // 准备测试数据
        RunRuleQueryDto queryDto = new RunRuleQueryDto();
        queryDto.setId(101L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法调用
            doReturn(runRuleEntityList).when(runRuleMapper).selectRunRuleList(any(RunRuleEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunRuleDto.class))).thenReturn(pageInfo);

            // 执行测试方法
            PageInfo<RunRuleDto> result = runRuleService.selectRunRuleList(queryDto, pageNum, pageSize);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());
            assertEquals(1L, result.getList().get(0).getId());
            
            // 验证Mapper方法被调用
            verify(runRuleMapper, times(1)).selectRunRuleList(any(RunRuleEntity.class));
            
            // 验证PageDataUtil.toDtoPage方法被调用
            mockedPageDataUtil.verify(() -> PageDataUtil.toDtoPage(runRuleEntityList, RunRuleDto.class));
        }
    }

    @Test
    @DisplayName("测试新增运行规则")
    void testInsertRunRule() {
        // 模拟Mapper方法调用
        doReturn(1).when(runRuleMapper).insertRunRule(any(RunRuleEntity.class));

        // 执行测试方法
        int result = runRuleService.insertRunRule(mockDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).insertRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试更新运行规则")
    void testUpdateRunRule() {
        // 模拟Mapper方法调用
        doReturn(1).when(runRuleMapper).updateRunRule(any(RunRuleEntity.class));

        // 执行测试方法
        int result = runRuleService.updateRunRule(mockDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).updateRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试根据ID批量删除运行规则")
    void testDeleteRunRuleByIds() {
        // 准备测试参数
        Long[] ids = {1L, 2L, 3L};
        
        // 模拟Mapper方法调用
        doReturn(3).when(runRuleMapper).deleteRunRuleByIds(ids);

        // 执行测试方法
        int result = runRuleService.deleteRunRuleByIds(ids);

        // 验证结果
        assertEquals(3, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).deleteRunRuleByIds(ids);
    }

    @Test
    @DisplayName("测试根据ID查询运行规则 - 返回null")
    void testSelectRunRuleById_ReturnNull() {
        // 模拟Mapper方法返回null
        doReturn(null).when(runRuleMapper).selectRunRuleById(anyLong());

        // 执行测试方法
        RunRuleDto result = runRuleService.selectRunRuleById(999L);

        // 验证结果 - BeanUtils.copy(null, class)会返回一个空对象而不是null
        assertNotNull(result);
        assertEquals(null, result.getId());
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).selectRunRuleById(999L);
    }

    @Test
    @DisplayName("测试查询运行规则列表 - 空查询条件")
    void testSelectRunRuleList_NullQuery() {
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法调用
            doReturn(runRuleEntityList).when(runRuleMapper).selectRunRuleList(any(RunRuleEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunRuleDto.class))).thenReturn(pageInfo);

            // 执行测试方法 - 传入null查询条件
            PageInfo<RunRuleDto> result = runRuleService.selectRunRuleList(null, 1, 10);

            // 验证结果
            assertNotNull(result);
            
            // 验证Mapper方法被调用
            verify(runRuleMapper, times(1)).selectRunRuleList(any(RunRuleEntity.class));
        }
    }

    @Test
    @DisplayName("测试查询运行规则列表 - 空结果")
    void testSelectRunRuleList_EmptyResult() {
        // 准备测试数据
        RunRuleQueryDto queryDto = new RunRuleQueryDto();
        queryDto.setId(999L);
        
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法返回空列表
            doReturn(new ArrayList<>()).when(runRuleMapper).selectRunRuleList(any(RunRuleEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法返回空分页
            PageInfo<RunRuleDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunRuleDto.class))).thenReturn(emptyPageInfo);

            // 执行测试方法
            PageInfo<RunRuleDto> result = runRuleService.selectRunRuleList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.getList().size());
            
            // 验证Mapper方法被调用
            verify(runRuleMapper, times(1)).selectRunRuleList(any(RunRuleEntity.class));
        }
    }

    @Test
    @DisplayName("测试新增运行规则 - 插入失败")
    void testInsertRunRule_Fail() {
        // 模拟Mapper方法返回0表示失败
        doReturn(0).when(runRuleMapper).insertRunRule(any(RunRuleEntity.class));

        // 执行测试方法
        int result = runRuleService.insertRunRule(mockDto);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).insertRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试新增运行规则 - 空对象")
    void testInsertRunRule_NullDto() {
        // 模拟Mapper方法
        doReturn(1).when(runRuleMapper).insertRunRule(any(RunRuleEntity.class));

        // 执行测试方法 - 传入null
        int result = runRuleService.insertRunRule(null);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).insertRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试更新运行规则 - 更新失败")
    void testUpdateRunRule_Fail() {
        // 模拟Mapper方法返回0表示失败
        doReturn(0).when(runRuleMapper).updateRunRule(any(RunRuleEntity.class));

        // 执行测试方法
        int result = runRuleService.updateRunRule(mockDto);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).updateRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试更新运行规则 - 空对象")
    void testUpdateRunRule_NullDto() {
        // 模拟Mapper方法
        doReturn(1).when(runRuleMapper).updateRunRule(any(RunRuleEntity.class));

        // 执行测试方法 - 传入null
        int result = runRuleService.updateRunRule(null);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).updateRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试批量删除运行规则 - 空ID数组")
    void testDeleteRunRuleByIds_EmptyArray() {
        // 准备测试参数 - 空数组
        Long[] ids = new Long[]{};
        
        // 模拟Mapper方法调用
        doReturn(0).when(runRuleMapper).deleteRunRuleByIds(ids);

        // 执行测试方法
        int result = runRuleService.deleteRunRuleByIds(ids);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).deleteRunRuleByIds(ids);
    }

    @Test
    @DisplayName("测试批量删除运行规则 - null数组")
    void testDeleteRunRuleByIds_NullArray() {
        // 模拟Mapper方法调用
        doReturn(0).when(runRuleMapper).deleteRunRuleByIds(any());

        // 执行测试方法 - 传入null
        int result = runRuleService.deleteRunRuleByIds(null);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).deleteRunRuleByIds(null);
    }

    @Test
    @DisplayName("测试查询运行规则列表 - 大分页参数")
    void testSelectRunRuleList_LargePageParams() {
        // 准备测试数据
        RunRuleQueryDto queryDto = new RunRuleQueryDto();
        queryDto.setId(100L);
        
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法调用
            doReturn(runRuleEntityList).when(runRuleMapper).selectRunRuleList(any(RunRuleEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunRuleDto.class))).thenReturn(pageInfo);

            // 执行测试方法 - 使用大分页参数
            PageInfo<RunRuleDto> result = runRuleService.selectRunRuleList(queryDto, 999, 1000);

            // 验证结果
            assertNotNull(result);
            
            // 验证Mapper方法被调用
            verify(runRuleMapper, times(1)).selectRunRuleList(any(RunRuleEntity.class));
        }
    }

    @Test
    @DisplayName("测试根据ID查询运行规则 - 无效ID")
    void testSelectRunRuleById_InvalidId() {
        // 模拟Mapper方法返回null
        doReturn(null).when(runRuleMapper).selectRunRuleById(anyLong());

        // 执行测试方法 - 传入负数ID
        RunRuleDto result = runRuleService.selectRunRuleById(-1L);

        // 验证结果 - BeanUtils.copy(null, class)会返回一个空对象而不是null
        assertNotNull(result);
        assertEquals(null, result.getId());
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).selectRunRuleById(-1L);
    }

    @Test
    @DisplayName("测试新增运行规则 - 部分字段为空")
    void testInsertRunRule_PartialFields() {
        // 创建部分字段为空的DTO
        RunRuleDto partialDto = new RunRuleDto();
        partialDto.setId(1L);
        partialDto.setEnvcRunInstanceInfoId(100L);
        // 其他字段为null

        // 模拟Mapper方法
        doReturn(1).when(runRuleMapper).insertRunRule(any(RunRuleEntity.class));

        // 执行测试方法
        int result = runRuleService.insertRunRule(partialDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).insertRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试更新运行规则 - 部分字段为空")
    void testUpdateRunRule_PartialFields() {
        // 创建部分字段为空的DTO
        RunRuleDto partialDto = new RunRuleDto();
        partialDto.setId(1L);
        partialDto.setResult(1); // 只设置部分字段

        // 模拟Mapper方法
        doReturn(1).when(runRuleMapper).updateRunRule(any(RunRuleEntity.class));

        // 执行测试方法
        int result = runRuleService.updateRunRule(partialDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).updateRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试批量删除运行规则 - 单个ID")
    void testDeleteRunRuleByIds_SingleId() {
        // 准备测试参数 - 单个ID
        Long[] ids = new Long[]{1L};
        
        // 模拟Mapper方法调用
        doReturn(1).when(runRuleMapper).deleteRunRuleByIds(ids);

        // 执行测试方法
        int result = runRuleService.deleteRunRuleByIds(ids);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runRuleMapper, times(1)).deleteRunRuleByIds(ids);
    }
} 