package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 文件信息DTO
 *
 * <AUTHOR>
 */
public class FileInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件大小
     */
    private String fileSize;

    /**
     * 文件权限
     */
    private String permissions;

    /**
     * MD5值
     */
    private String md5;

    /**
     * 状态：一致、不一致、缺失、多出
     */
    private String status;

    /**
     * 备注信息
     */
    private String remark;

    public FileInfoDto() {
    }

    public FileInfoDto(String filePath, String fileSize, String permissions, String md5) {
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.permissions = permissions;
        this.md5 = md5;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getPermissions() {
        return permissions;
    }

    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "FileInfoDto{" +
                "filePath='" + filePath + '\'' +
                ", fileSize='" + fileSize + '\'' +
                ", permissions='" + permissions + '\'' +
                ", md5='" + md5 + '\'' +
                ", status='" + status + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}
