package com.ideal.envc.strategy.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.ContentCustomDto;
import com.ideal.envc.model.dto.EngineCompareOutPutDto;
import com.ideal.envc.model.dto.OutputParseResult;
import com.ideal.envc.model.enums.ActivityTypeEnum;
import com.ideal.envc.strategy.OutputParseStrategy;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 比对模式-目录类型输出解析策略
 */
@Component
public class CompareDirectoryOutputParseStrategy implements OutputParseStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(CompareDirectoryOutputParseStrategy.class);
    
    @Override
    public OutputParseResult parse(List<EngineActOutputBean> actOutputs,String actDefName) {
        if (actOutputs == null || actOutputs.isEmpty()) {
            return new OutputParseResult(false, "");
        }
        
        EngineActOutputBean engineActOutputBean = actOutputs.get(0);
        try {
            Map<String,Object> outPutMap = ContrastToolUtils.analysisOutPut(engineActOutputBean.getOutput());
            boolean ret = false;
            if(outPutMap!=null){
                ret = outPutMap.get(ContrastConstants.RET) != null && Boolean.parseBoolean(outPutMap.get(ContrastConstants.RET).toString());
            }
            String content = getContent(outPutMap,actDefName);
            ContentCustomDto contentCustomDto = new ContentCustomDto();
            contentCustomDto.setContent(content);
            contentCustomDto.setRet(ret);
            return new OutputParseResult(ret, JSON.toJSONString(contentCustomDto));
        } catch (Exception e) {
            logger.error("解析engineActOutputBean异常", e);
            return new OutputParseResult(false, "");
        }
    }
    private String getContent(Map<String,Object> outPutMap,String actType){
        String content = "";
        if(outPutMap!=null){
            if(ActivityTypeEnum.COMPARE_CONTENT.getCode().equals(actType) ){
                content = outPutMap.get(ContrastConstants.COMPARE_RESULT)!=null? outPutMap.get(ContrastConstants.COMPARE_RESULT).toString() :(outPutMap.get("err")!=null?outPutMap.get("err").toString():"");
            }
            else if(ActivityTypeEnum.SYNC_CONTENT.getCode().equals(actType)){
                content = outPutMap.get(ContrastConstants.SYNC_RESULT)!=null?outPutMap.get(ContrastConstants.SYNC_RESULT).toString():(outPutMap.get("err")!=null?outPutMap.get("err").toString():"");
            }
            else  {
                String stdOut = outPutMap.get(ContrastConstants.STDOUT)!=null?outPutMap.get(ContrastConstants.STDOUT).toString():"";
                if(StringUtils.isBlank(stdOut)){
                    String stdErr = outPutMap.get(ContrastConstants.STDERR)!=null?outPutMap.get(ContrastConstants.STDERR).toString():"";
                    if(StringUtils.isBlank(stdErr)){
                        content = outPutMap.get(ContrastConstants.ERR)!=null?outPutMap.get(ContrastConstants.ERR).toString():"";
                    }else{
                        content = stdErr;
                    }
                }else {
                    content = stdOut;
                }

            }
        }
        return content;
    }
    @Override
    public String getType() {
        return "COMPARE_DIRECTORY";
    }
} 