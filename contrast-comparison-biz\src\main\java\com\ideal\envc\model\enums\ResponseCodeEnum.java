package com.ideal.envc.model.enums;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 响应码枚举
 * <p>本枚举对应《doc/响应码及描述.md》文档，涵盖平台所有响应码及描述。</p>
 * Response code enum, corresponding to doc/响应码及描述.md
 *
 * <AUTHOR>
 */
public enum ResponseCodeEnum {
    // 操作成功
    SUCCESS("10000", "操作成功"),

    // 查询类异常码 1301xx
    QUERY_FAIL("130100", "查询失败"),
    DATA_NOT_FOUND("130101", "数据不存在"),
    QUERY_PARAM_ERROR("130102", "查询参数错误"),
    QUERY_TIMEOUT("130103", "查询超时"),
    QUERY_PERMISSION_DENIED("130104", "查询权限不足"),

    // 新增类异常码 1302xx
    ADD_FAIL("130200", "新增失败"),
    DATA_ALREADY_EXISTS("130201", "数据已存在"),
    ADD_PARAM_ERROR("130202", "新增参数错误"),
    ADD_PERMISSION_DENIED("130203", "新增权限不足"),
    BATCH_ADD_FAIL("130204", "批量新增失败"),
    VALIDATION_ERROR("130205", "验证错误"),
    ID_CANNOT_BE_NULL("130210", "id不能为空"),
    USER_ID_CANNOT_BE_NULL("130211", "用户id不能为空"),
    DEVICE_NO_CANNOT_BE_NULL("130212", "设备编号不能为空"),
    SYSTEM_NAME_CANNOT_BE_NULL("130213", "系统名称不能为空"),
    PARAM_FORMAT_ERROR("130214", "参数格式不正确"),
    FIELD_LENGTH_EXCEEDED("130215", "字段长度超限"),
    REQUIRED_FIELD_MISSING("130216", "必填字段缺失"),

    // 修改类异常码 1303xx
    UPDATE_FAIL("130300", "修改失败"),
    UPDATE_DATA_NOT_FOUND("130301", "数据不存在"),
    UPDATE_PARAM_ERROR("130302", "修改参数错误"),
    UPDATE_PERMISSION_DENIED("130303", "修改权限不足"),
    BATCH_UPDATE_FAIL("130304", "批量修改失败"),

    // 删除类异常码 1304xx
    DELETE_FAIL("130400", "删除失败"),
    DELETE_DATA_NOT_FOUND("130401", "数据不存在"),
    DELETE_PARAM_ERROR("130402", "删除参数错误"),
    DELETE_PERMISSION_DENIED("130403", "删除权限不足"),
    BATCH_DELETE_FAIL("130404", "批量删除失败"),
    
    // 操作类异常码 1305xx
    OPERATE_FAIL("130500", "操作失败"),
    TASK_ALREADY_IN_STATE("130501", "任务已处于该状态"),

    // 系统级别异常码 1399xx
    SYSTEM_ERROR("139900", "系统异常"),
    SERVICE_UNAVAILABLE("139901", "服务不可用"),
    UNKNOWN_ERROR("139902", "未知异常"),
    NETWORK_ERROR("139903", "网络异常"),
    DATABASE_ERROR("139904", "数据库异常"),
    EXTERNAL_SERVICE_FAIL("139905", "外部服务调用失败"),
    UNAUTHORIZED_ACCESS("139906", "未授权访问"),
    SESSION_EXPIRED("139907", "会话失效"),
    RESOURCE_LIMITED("139908", "资源受限"),

    // 启动相关异常码 131xx
    PLAN_START_FAIL("13100", "方案级启动失败"),
    SYSTEM_START_FAIL("13200", "系统级启动失败"),
    NODE_START_FAIL("13300", "节点级启动失败"),
    RULE_START_FAIL("13400", "规则级启动失败"),
    TASK_START_FAIL("13500", "任务级启动失败"),
    BATCH_ADD_DEVICE_FAIL("13001", "批量添加设备失败");

    private final String code;
    private final String desc;

    ResponseCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取响应码
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取响应描述
     * @return desc
     */
    public String getDesc() {
        return desc;
    }

    private static final Map<String, ResponseCodeEnum> CODE_MAP = Stream.of(values())
            .collect(Collectors.toMap(ResponseCodeEnum::getCode, Function.identity()));

    /**
     * 根据响应码获取枚举实例
     * @param code 响应码
     * @return 枚举实例，如果不存在则返回null
     */
    public static ResponseCodeEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }

    /**
     * 根据响应码获取描述
     * @param code 响应码
     * @return 响应描述，不存在则返回"未知响应码"
     */
    public static String getDescByCode(String code) {
        ResponseCodeEnum responseCode = getByCode(code);
        return responseCode != null ? responseCode.getDesc() : "未知响应码";
    }

    /**
     * 判断响应码是否有效
     * @param code 响应码
     * @return 有效返回true，否则false
     */
    public static boolean isValidCode(String code) {
        return code != null && CODE_MAP.containsKey(code);
    }
} 