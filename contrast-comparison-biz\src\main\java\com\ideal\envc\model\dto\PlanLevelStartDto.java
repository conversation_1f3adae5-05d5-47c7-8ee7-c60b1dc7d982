package com.ideal.envc.model.dto;

import java.util.List;

/**
 * 方案级启动DTO
 *
 * <AUTHOR>
 */
public class PlanLevelStartDto extends StartContrastBaseDto {
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID列表
     */
    private List<Long> planIds;

    /** 方案名称模糊查询 */
    private String planNameLike;

    /** 源中心ID */
    private Long sourceCenterId;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 触发来源 */
    private Integer from;

    public List<Long> getPlanIds() {
        return planIds;
    }

    public void setPlanIds(List<Long> planIds) {
        this.planIds = planIds;
    }

    public String getPlanNameLike() {
        return planNameLike;
    }

    public void setPlanNameLike(String planNameLike) {
        this.planNameLike = planNameLike;
    }

    @Override
    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    @Override
    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    @Override
    public Long getTargetCenterId() {
        return targetCenterId;
    }

    @Override
    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "PlanLevelStartDto{" +
                "planIds=" + planIds +
                ", planNameLike='" + planNameLike + '\'' +
                ", sourceCenterId=" + sourceCenterId +
                ", targetCenterId=" + targetCenterId +
                ", from=" + from +
                '}';
    }
}
