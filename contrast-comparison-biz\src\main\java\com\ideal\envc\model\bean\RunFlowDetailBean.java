package com.ideal.envc.model.bean;

import java.io.Serializable;

/**
 * 运行规则流程详细信息Bean对象
 * 用于封装根据流程ID查询的运行规则详细信息
 *
 * <AUTHOR>
 */
public class RunFlowDetailBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 源路径
     */
    private String sourcePath;

    /**
     * 路径
     */
    private String path;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 源设备ID
     */
    private Long sourceComputerId;

    /**
     * 目标设备ID
     */
    private Long targetComputerId;

    /**
     * 目标中心名称
     */
    private String targetCenterName;

    /**
     * 源中心名称
     */
    private String sourceCenterName;

    /**
     * 源设备IP
     */
    private String sourceComputerIp;

    /**
     * 目标设备IP
     */
    private String targetComputerIp;

    /**
     * 源设备名称
     */
    private String sourceComputerName;

    /**
     * 目标设备名称
     */
    private String targetComputerName;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public Long getTargetComputerId() {
        return targetComputerId;
    }

    public void setTargetComputerId(Long targetComputerId) {
        this.targetComputerId = targetComputerId;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getSourceComputerIp() {
        return sourceComputerIp;
    }

    public void setSourceComputerIp(String sourceComputerIp) {
        this.sourceComputerIp = sourceComputerIp;
    }

    public String getTargetComputerIp() {
        return targetComputerIp;
    }

    public void setTargetComputerIp(String targetComputerIp) {
        this.targetComputerIp = targetComputerIp;
    }

    public String getSourceComputerName() {
        return sourceComputerName;
    }

    public void setSourceComputerName(String sourceComputerName) {
        this.sourceComputerName = sourceComputerName;
    }

    public String getTargetComputerName() {
        return targetComputerName;
    }

    public void setTargetComputerName(String targetComputerName) {
        this.targetComputerName = targetComputerName;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    @Override
    public String toString() {
        return "RunFlowDetailBean{" +
                "sourcePath='" + sourcePath + '\'' +
                ", path='" + path + '\'' +
                ", businessSystemId=" + businessSystemId +
                ", sourceComputerId=" + sourceComputerId +
                ", targetComputerId=" + targetComputerId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", sourceComputerIp='" + sourceComputerIp + '\'' +
                ", targetComputerIp='" + targetComputerIp + '\'' +
                ", sourceComputerName='" + sourceComputerName + '\'' +
                ", targetComputerName='" + targetComputerName + '\'' +
                ", businessSystemName='" + businessSystemName + '\'' +
                '}';
    }
}
