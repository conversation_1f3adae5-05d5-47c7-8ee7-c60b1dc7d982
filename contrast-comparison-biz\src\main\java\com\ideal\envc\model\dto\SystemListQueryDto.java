package com.ideal.envc.model.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 系统列表查询条件数据传输对象
 *
 * <AUTHOR>
 */
public class SystemListQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统描述
     */
    private String businessSystemDesc;

    /**
     * 排除业务系统ID集合
     */
    private List<Long> excludeBusinessSystemIds;

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemDesc() {
        return businessSystemDesc;
    }

    public void setBusinessSystemDesc(String businessSystemDesc) {
        this.businessSystemDesc = businessSystemDesc;
    }

    public List<Long> getExcludeBusinessSystemIds() {
        return excludeBusinessSystemIds;
    }

    public void setExcludeBusinessSystemIds(List<Long> excludeBusinessSystemIds) {
        this.excludeBusinessSystemIds = excludeBusinessSystemIds;
    }

    @Override
    public String toString() {
        return "SystemListQueryDto{" +
                "businessSystemName='" + businessSystemName + '\'' +
                ", businessSystemDesc='" + businessSystemDesc + '\'' +
                ", excludeBusinessSystemIds=" + excludeBusinessSystemIds +
                '}';
    }
}
