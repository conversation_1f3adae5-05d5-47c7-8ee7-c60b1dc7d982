package com.ideal.envc.interaction.model;



/**
 * <AUTHOR>
 */
public class SystemComputerListQueryIo {

    /**
     * 查询页数
     */
    private Integer pageNum;
    /**
     * 每页条数
     */
    private Integer pageSize;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 设备分类（0：服务器设备，1：网络设备,2 硬件设备）
     */
    private Integer type;

    /**
     * 参数信息
     */
    private SystemComputerListQueryInnerIo queryParam;


    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public SystemComputerListQueryInnerIo getQueryParam() {
        return queryParam;
    }

    public void setQueryParam(SystemComputerListQueryInnerIo queryParam) {
        this.queryParam = queryParam;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
