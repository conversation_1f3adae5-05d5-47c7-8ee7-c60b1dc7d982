package com.ideal.envc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.exception.ScheduleJobOperateException;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.jobapi.core.apiclient.IdealXxlJobApiUtil;
import com.ideal.jobapi.core.model.IdealXxlJobInfoDto;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.mockStatic;

/**
 * 定时任务处理工具类的单元测试
 */
@ExtendWith(MockitoExtension.class)
public class JobOperateServiceImplTest {

    @InjectMocks
    private JobOperateServiceImpl jobOperateService;

    private ContrastScheduleJobTaskDto jobTaskDto;
    private ReturnT<String> successResult;
    private ReturnT<String> failResult;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        jobTaskDto = new ContrastScheduleJobTaskDto();
        jobTaskDto.setTaskId(1L);
        jobTaskDto.setTaskName("测试任务");
        jobTaskDto.setCron("0 0 12 * * ?");
        jobTaskDto.setCreateName("测试用户");
        jobTaskDto.setScheduleJobId(100L);
        
        // 添加类型参数
        successResult = new ReturnT<String>(200, "操作成功");
        failResult = new ReturnT<String>(500, "操作失败");
    }

    @Test
    @DisplayName("测试创建并启动定时任务 - 成功")
    void testCreateAndStartJobSuccess() throws ScheduleJobOperateException {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 设置Mock行为
            ReturnT<String> successResultWithId = new ReturnT<String>(200, "操作成功");
            successResultWithId.setContent("100"); // 设置 xxJob id
            
            mockedUtil.when(() -> IdealXxlJobApiUtil.addJob(any(IdealXxlJobInfoDto.class))).thenReturn(successResultWithId);
            mockedJson.when(() -> JSON.toJSONString(any(ContrastScheduleJobTaskDto.class))).thenReturn("{}");
            
            // 执行测试方法
            Integer result = jobOperateService.createAndStartJob(jobTaskDto);
            
            // 验证结果
            assertEquals(100, result);
        }
    }
    
    @Test
    @DisplayName("测试创建并启动定时任务 - 失败")
    void testCreateAndStartJobFail() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.addJob(any(IdealXxlJobInfoDto.class))).thenReturn(failResult);
            mockedJson.when(() -> JSON.toJSONString(any(ContrastScheduleJobTaskDto.class))).thenReturn("{}");
            
            // 执行测试方法并验证异常
            ScheduleJobOperateException exception = assertThrows(ScheduleJobOperateException.class, () -> {
                jobOperateService.createAndStartJob(jobTaskDto);
            });
            
            // 验证异常信息 - ScheduleJobOperateException在构造时将code和message合并为一个字符串
            String expectedMessage = "500操作失败";
            assertEquals(expectedMessage, exception.getMessage());
        }
    }
    
    @Test
    @DisplayName("测试创建并启动定时任务 - 返回null")
    void testCreateAndStartJobReturnNull() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.addJob(any(IdealXxlJobInfoDto.class))).thenReturn(null);
            mockedJson.when(() -> JSON.toJSONString(any(ContrastScheduleJobTaskDto.class))).thenReturn("{}");
            
            // a执行测试方法并验证异常
            ScheduleJobOperateException exception = assertThrows(ScheduleJobOperateException.class, () -> {
                jobOperateService.createAndStartJob(jobTaskDto);
            });
            
            // 验证异常信息
            assertEquals("TaskEntity ReturnT is null!", exception.getMessage());
        }
    }
    
    @Test
    @DisplayName("测试创建并启动定时任务 - Content为null")
    void testCreateAndStartJobContentNull() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 设置Mock行为
            ReturnT<String> resultWithNullContent = new ReturnT<String>(200, "操作成功");
            resultWithNullContent.setContent(null);
            
            mockedUtil.when(() -> IdealXxlJobApiUtil.addJob(any(IdealXxlJobInfoDto.class))).thenReturn(resultWithNullContent);
            mockedJson.when(() -> JSON.toJSONString(any(ContrastScheduleJobTaskDto.class))).thenReturn("{}");
            
            // 执行测试方法并验证异常
            ScheduleJobOperateException exception = assertThrows(ScheduleJobOperateException.class, () -> {
                jobOperateService.createAndStartJob(jobTaskDto);
            });
            
            // 验证异常信息
            assertEquals("TaskEntity xxJob id is null!", exception.getMessage());
        }
    }

    @Test
    @DisplayName("测试启动定时任务 - 成功")
    void testStartJobSuccess() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.startJob(anyInt())).thenReturn(successResult);
            
            // 执行测试方法
            Boolean result = jobOperateService.startJob(100);
            
            // 验证结果
            assertTrue(result);
        }
    }
    
    @Test
    @DisplayName("测试启动定时任务 - 失败")
    void testStartJobFail() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.startJob(anyInt())).thenReturn(failResult);
            
            // 执行测试方法
            Boolean result = jobOperateService.startJob(100);
            
            // 验证结果
            assertFalse(result);
        }
    }
    
    @Test
    @DisplayName("测试启动定时任务 - 无效ID")
    void testStartJobInvalidId() {
        // 执行测试方法 - ID为null
        Boolean resultNull = jobOperateService.startJob(null);
        
        // 验证结果
        assertFalse(resultNull);
        
        // 执行测试方法 - ID小于等于0
        Boolean resultZero = jobOperateService.startJob(0);
        
        // 验证结果
        assertFalse(resultZero);
    }

    @Test
    @DisplayName("测试修改定时任务 - 成功")
    void testModifyJobSuccess() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.updateJob(any(IdealXxlJobInfoDto.class))).thenReturn(successResult);
            mockedJson.when(() -> JSON.toJSONString(any(ContrastScheduleJobTaskDto.class))).thenReturn("{}");
            
            // 执行测试方法
            boolean result = jobOperateService.modifyJob(jobTaskDto);
            
            // 验证结果
            assertTrue(result);
        }
    }
    
    @Test
    @DisplayName("测试修改定时任务 - 失败")
    void testModifyJobFail() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.updateJob(any(IdealXxlJobInfoDto.class))).thenReturn(failResult);
            mockedJson.when(() -> JSON.toJSONString(any(ContrastScheduleJobTaskDto.class))).thenReturn("{}");
            
            // 执行测试方法
            boolean result = jobOperateService.modifyJob(jobTaskDto);
            
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("测试停止定时任务 - 成功")
    void testStopJobSuccess() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.stopJob(anyInt())).thenReturn(successResult);
            
            // 执行测试方法
            boolean result = jobOperateService.stopJob(100);
            
            // 验证结果
            assertTrue(result);
        }
    }
    
    @Test
    @DisplayName("测试停止定时任务 - 失败")
    void testStopJobFail() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.stopJob(anyInt())).thenReturn(failResult);
            
            // 执行测试方法
            boolean result = jobOperateService.stopJob(100);
            
            // 验证结果
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("测试删除定时任务 - 成功")
    void testRemoveJobSuccess() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.stopJob(anyInt())).thenReturn(successResult);
            mockedUtil.when(() -> IdealXxlJobApiUtil.removeJob(anyInt())).thenReturn(successResult);
            
            // 执行测试方法
            boolean result = jobOperateService.removeJob(100);
            
            // 验证结果
            assertTrue(result);
        }
    }
    
    @Test
    @DisplayName("测试删除定时任务 - 停止失败但删除成功")
    void testRemoveJobStopFailButRemoveSuccess() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.stopJob(anyInt())).thenReturn(failResult);
            mockedUtil.when(() -> IdealXxlJobApiUtil.removeJob(anyInt())).thenReturn(successResult);
            
            // 执行测试方法
            boolean result = jobOperateService.removeJob(100);
            
            // 验证结果
            assertTrue(result);
        }
    }
    
    @Test
    @DisplayName("测试删除定时任务 - 删除失败")
    void testRemoveJobFail() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为
            mockedUtil.when(() -> IdealXxlJobApiUtil.stopJob(anyInt())).thenReturn(successResult);
            mockedUtil.when(() -> IdealXxlJobApiUtil.removeJob(anyInt())).thenReturn(failResult);
            
            // 执行测试方法
            boolean result = jobOperateService.removeJob(100);
            
            // 验证结果
            assertFalse(result);
        }
    }
    
    @Test
    @DisplayName("测试删除定时任务 - 无效ID")
    void testRemoveJobInvalidId() {
        // 执行测试方法 - ID为null
        boolean resultNull = jobOperateService.removeJob(null);
        
        // 验证结果
        assertFalse(resultNull);
        
        // 执行测试方法 - ID小于等于0
        boolean resultZero = jobOperateService.removeJob(0);
        
        // 验证结果
        assertFalse(resultZero);
    }
    
    @Test
    @DisplayName("测试删除定时任务 - 发生异常")
    void testRemoveJobException() {
        try (MockedStatic<IdealXxlJobApiUtil> mockedUtil = mockStatic(IdealXxlJobApiUtil.class)) {
            
            // 设置Mock行为 - 停止操作成功，但删除操作抛出异常
            mockedUtil.when(() -> IdealXxlJobApiUtil.stopJob(anyInt())).thenReturn(successResult);
            mockedUtil.when(() -> IdealXxlJobApiUtil.removeJob(anyInt())).thenThrow(new RuntimeException("Test exception"));
            
            // 执行测试方法
            boolean result = jobOperateService.removeJob(100);
            
            // 验证结果
            assertFalse(result);
        }
    }
} 