package com.ideal.envc.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * 任务方案列表数据传输对象
 *
 * <AUTHOR>
 */
public class TaskPlanListDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 方案ID
     */
    private Long envcPlanId;

    /**
     * 方案名称
     */
    private String planName;

    /**
     * 方案描述
     */
    private String planDesc;

    /**
     * cron表达式
     */
    private String cron;

    /**
     * 任务状态（0：启用，1：禁用）
     */
    private Integer status;

    /**
     * 源中心ID
     */
    private Long sourceCenterId;

    /**
     * 目标中心ID
     */
    private Long targetCenterId;

    /**
     * 定时ID
     */
    private Long scheduledId;

    /**
     * 源中心名称
     */
    private String sourceCenterName;

    /**
     * 目标中心名称
     */
    private String targetCenterName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 更新人ID
     */
    private Long updatorId;

    /**
     * 更新人名称
     */
    private String updatorName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcPlanId() {
        return envcPlanId;
    }

    public void setEnvcPlanId(Long envcPlanId) {
        this.envcPlanId = envcPlanId;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanDesc() {
        return planDesc;
    }

    public void setPlanDesc(String planDesc) {
        this.planDesc = planDesc;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public Long getScheduledId() {
        return scheduledId;
    }

    public void setScheduledId(Long scheduledId) {
        this.scheduledId = scheduledId;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    @Override
    public String toString() {
        return "TaskPlanListDto{" +
                "id=" + id +
                ", envcPlanId=" + envcPlanId +
                ", planName='" + planName + '\'' +
                ", planDesc='" + planDesc + '\'' +
                ", cron='" + cron + '\'' +
                ", status=" + status +
                ", sourceCenterId=" + sourceCenterId +
                ", targetCenterId=" + targetCenterId +
                ", scheduledId=" + scheduledId +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", createTime=" + createTime +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", updateTime=" + updateTime +
                ", updatorId=" + updatorId +
                ", updatorName='" + updatorName + '\'' +
                '}';
    }
}
