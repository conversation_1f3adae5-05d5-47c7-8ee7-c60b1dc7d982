package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 方案关系列表查询结果Bean
 *
 * <AUTHOR>
 */
public class PlanRelationListBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 环境方案ID */
    private Long envcPlanId;

    /** 业务系统ID */
    private Long businessSystemId;

    /** 业务系统名称 */
    private String businessSystemName;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcPlanId() {
        return envcPlanId;
    }

    public void setEnvcPlanId(Long envcPlanId) {
        this.envcPlanId = envcPlanId;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
} 