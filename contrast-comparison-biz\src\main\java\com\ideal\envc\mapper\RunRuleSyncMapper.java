package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.RunRuleSyncEntity;

/**
 * 节点规则同步结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface RunRuleSyncMapper {
    /**
     * 查询节点规则同步结果
     *
     * @param id 节点规则同步结果主键
     * @return 节点规则同步结果
     */
    RunRuleSyncEntity selectRunRuleSyncById(Long id);

    /**
     * 查询节点规则同步结果列表
     *
     * @param runRuleSync 节点规则同步结果
     * @return 节点规则同步结果集合
     */
    List<RunRuleSyncEntity> selectRunRuleSyncList(RunRuleSyncEntity runRuleSync);

    /**
     * 新增节点规则同步结果
     *
     * @param runRuleSync 节点规则同步结果
     * @return 结果
     */
    int insertRunRuleSync(RunRuleSyncEntity runRuleSync);

    /**
     * 修改节点规则同步结果
     *
     * @param runRuleSync 节点规则同步结果
     * @return 结果
     */
    int updateRunRuleSync(RunRuleSyncEntity runRuleSync);

    /**
     * 删除节点规则同步结果
     *
     * @param id 节点规则同步结果主键
     * @return 结果
     */
    int deleteRunRuleSyncById(Long id);

    /**
     * 批量删除节点规则同步结果
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRunRuleSyncByIds(Long[] ids);
}
