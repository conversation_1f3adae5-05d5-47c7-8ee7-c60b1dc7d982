package com.ideal.envc.service;

import com.ideal.common.dto.R;
import com.ideal.envc.model.dto.ProjectDto;
import com.ideal.envc.model.dto.ProjectQueryDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.exception.ContrastBusinessException;

import java.util.List;

/**
 * 比对业务系统Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IProjectService {
    /**
     * 查询比对业务系统
     *
     * @param id 比对业务系统主键
     * @return 比对业务系统
     */
    ProjectDto selectProjectById(Long id) throws ContrastBusinessException;

    /**
     * 查询比对业务系统列表
     *
     * @param projectQueryDto 比对业务系统
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 比对业务系统集合
     */
    PageInfo<ProjectDto> selectProjectList(ProjectQueryDto projectQueryDto, UserDto userDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;

    /**
     * 查询待绑定比对业务系统列表
     *
     * @param projectQueryDto 比对业务系统
     * @param userDto 用户信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 比对业务系统
     */
    PageInfo<ProjectDto> getPendingSystemList(ProjectQueryDto projectQueryDto, UserDto userDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;

    /**
     * 新增比对业务系统
     *
     * @param projectDto 比对业务系统
     * @return 结果
     */
    int insertProject(ProjectDto projectDto) throws ContrastBusinessException;

    /**
     * 修改比对业务系统
     *
     * @param projectDto 比对业务系统
     * @return 结果
     */
    int updateProject(ProjectDto projectDto) throws ContrastBusinessException;

    /**
     * 批量删除比对业务系统
     *
     * @param ids 需要删除的比对业务系统主键集合
     * @return 结果
     */
    int deleteProjectByIds(Long[] ids) throws ContrastBusinessException;

    /**
     * 新增比对业务系统
     *
     * @param businessSystemIds 比对业务系统
     * @param  userDto 用户
     * @return 结果
     */
    int addProject(List<Long> businessSystemIds, UserDto userDto) throws ContrastBusinessException;
}
