package com.ideal.envc.component;

import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.exception.ContrastBusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RKeys;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TaskCounterComponent的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TaskCounterComponentTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RLock rLock;

    @Mock
    private RAtomicLong rAtomicLong;

    @Mock
    private RKeys rKeys;

    @InjectMocks
    private TaskCounterComponent taskCounterComponent;

    private Long instanceId;
    private Long instanceInfoId;
    private Integer initialValue;
    private String counterKey;
    private String lockKey;

    @BeforeEach
    void setUp() {
        instanceId = 1L;
        instanceInfoId = 100L;
        initialValue = 5;
        counterKey = ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX + instanceId;
        lockKey = ContrastConstants.RUN_INSTANCE_COUNTER_LOCK_PREFIX + instanceId;

        // 默认mock配置
        doReturn(rLock).when(redissonClient).getLock(anyString());
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(anyString());
    }

    @Test
    @DisplayName("测试初始化运行实例计数器 - 成功场景")
    void testInitInstanceCounter_Success() throws InterruptedException {
        // 准备测试数据
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));

        // 执行测试
        assertDoesNotThrow(() -> taskCounterComponent.initInstanceCounter(instanceId, initialValue));

        // 验证结果
        verify(redissonClient, atLeastOnce()).getLock(lockKey);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).set(initialValue);
        verify(rLock, times(1)).unlock();
    }

    @Test
    @DisplayName("测试初始化运行实例计数器 - 实例ID为空")
    void testInitInstanceCounter_NullInstanceId() {
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceCounter(null, initialValue));
        assertEquals("实例ID不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试初始化运行实例计数器 - 初始值为空")
    void testInitInstanceCounter_NullInitialValue() {
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceCounter(instanceId, null));
        assertEquals("初始值不能为空且必须大于等于0", exception.getMessage());
    }

    @Test
    @DisplayName("测试初始化运行实例计数器 - 初始值小于0")
    void testInitInstanceCounter_NegativeInitialValue() {
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceCounter(instanceId, -1));
        assertEquals("初始值不能为空且必须大于等于0", exception.getMessage());
    }

    @Test
    @DisplayName("测试初始化运行实例计数器 - 获取锁超时")
    void testInitInstanceCounter_LockTimeout() throws InterruptedException {
        // 准备测试数据
        doReturn(false).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceCounter(instanceId, initialValue));
        assertEquals("初始化计数器失败：初始化计数器时获取锁超时", exception.getMessage());

        // 验证结果
        verify(redissonClient, atLeastOnce()).getLock(lockKey);
        verify(redissonClient, never()).getAtomicLong(anyString());
        verify(rLock, never()).unlock();
    }

    @Test
    @DisplayName("测试初始化运行实例计数器 - 获取锁时被中断")
    void testInitInstanceCounter_Interrupted() throws InterruptedException {
        // 准备测试数据
        doThrow(new InterruptedException()).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceCounter(instanceId, initialValue));
        assertEquals("初始化计数器时被中断", exception.getMessage());

        // 验证结果
        verify(redissonClient, atLeastOnce()).getLock(lockKey);
        verify(redissonClient, never()).getAtomicLong(anyString());
        verify(rLock, never()).unlock();
    }

    @Test
    @DisplayName("测试初始化运行实例计数器 - 设置值时发生异常")
    void testInitInstanceCounter_Exception() throws InterruptedException {
        // 准备测试数据
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doThrow(new RuntimeException("模拟异常")).when(rAtomicLong).set(anyLong());

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceCounter(instanceId, initialValue));
        assertEquals("初始化计数器失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient, atLeastOnce()).getLock(lockKey);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).set(initialValue);
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试运行实例计数器减值 - 成功场景")
    void testDecrementInstanceCounter_Success() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(true).when(rAtomicLong).isExists();
        doReturn(1L).when(rAtomicLong).decrementAndGet();

        // 执行测试
        long result = taskCounterComponent.decrementInstanceCounter(instanceId);

        // 验证结果
        assertEquals(1L, result);
        verify(redissonClient).getLock(lockKey);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).decrementAndGet();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试运行实例计数器减值 - 减值操作时发生异常")
    void testDecrementInstanceCounter_Exception() throws InterruptedException {
        // 准备测试数据
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(true).when(rAtomicLong).isExists();
        doThrow(new RuntimeException("模拟异常")).when(rAtomicLong).decrementAndGet();

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.decrementInstanceCounter(instanceId));
        assertEquals("计数器减值失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient).getLock(lockKey);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong).decrementAndGet();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试运行实例计数器减值 - 计数器不存在")
    void testDecrementInstanceCounter_CounterNotExists() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(false).when(rAtomicLong).isExists();

        // 执行测试
        long result = taskCounterComponent.decrementInstanceCounter(instanceId);

        // 验证结果
        assertEquals(-1L, result);
        verify(redissonClient).getLock(lockKey);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong, never()).decrementAndGet();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试运行实例计数器减值 - 计数器值小于0")
    void testDecrementInstanceCounter_NegativeValue() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(true).when(rAtomicLong).isExists();
        doReturn(-1L).when(rAtomicLong).decrementAndGet();

        // 执行测试
        long result = taskCounterComponent.decrementInstanceCounter(instanceId);

        // 验证结果
        assertEquals(0L, result);
        verify(redissonClient).getLock(lockKey);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).decrementAndGet();
        verify(rAtomicLong).set(0L);
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试获取运行实例计数器值 - 成功场景")
    void testGetInstanceCounterValue_Success() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        doReturn(true).when(rAtomicLong).isExists();
        doReturn(5L).when(rAtomicLong).get();

        // 执行测试
        long result = taskCounterComponent.getInstanceCounterValue(instanceId);

        // 验证结果
        assertEquals(5L, result);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).get();
    }

    @Test
    @DisplayName("测试获取运行实例计数器值 - 计数器不存在")
    void testGetInstanceCounterValue_NotExists() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(false).when(rAtomicLong).isExists();

        // 执行测试
        long result = taskCounterComponent.getInstanceCounterValue(instanceId);

        // 验证结果
        assertEquals(-1L, result);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong, never()).get();
    }

    @Test
    @DisplayName("测试获取运行实例计数器值 - 发生异常")
    void testGetInstanceCounterValue_Exception() {
        // 准备测试数据
        doReturn(true).when(rAtomicLong).isExists();
        doThrow(new RuntimeException("模拟异常")).when(rAtomicLong).get();

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.getInstanceCounterValue(instanceId));
        assertEquals("获取计数器值失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong).get();
    }

    @Test
    @DisplayName("测试清理运行实例计数器 - 成功场景")
    void testClearInstanceCounter_Success() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(true).when(rAtomicLong).isExists();

        // 执行测试
        assertDoesNotThrow(() -> taskCounterComponent.clearInstanceCounter(instanceId));

        // 验证结果
        verify(redissonClient).getLock(lockKey);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong).delete();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试检查运行实例计数器是否存在 - 成功场景")
    void testIsInstanceCounterExists_Success() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(true).when(rAtomicLong).isExists();

        // 执行测试
        boolean result = taskCounterComponent.isInstanceCounterExists(instanceId);

        // 验证结果
        assertTrue(result);
        verify(redissonClient).getAtomicLong(counterKey);
        verify(rAtomicLong).isExists();
    }

    @Test
    @DisplayName("测试获取业务锁对象 - 成功场景")
    void testGetBusinessLock_Success() throws ContrastBusinessException {
        // 准备测试数据
        String businessLockKey = "test_lock";
        int timeoutSeconds = 30;
        doReturn(rLock).when(redissonClient).getLock(businessLockKey);

        // 执行测试
        RLock result = taskCounterComponent.getBusinessLock(businessLockKey, timeoutSeconds);

        // 验证结果
        assertNotNull(result);
        assertEquals(rLock, result);
        verify(redissonClient).getLock(businessLockKey);
    }

    @Test
    @DisplayName("测试获取业务锁对象 - 锁键为空")
    void testGetBusinessLock_NullKey() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.getBusinessLock(null, 30));
        assertEquals("锁键不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试获取业务锁对象 - 锁键为空白")
    void testGetBusinessLock_BlankKey() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.getBusinessLock("   ", 30));
        assertEquals("锁键不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试获取业务锁对象 - 发生异常")
    void testGetBusinessLock_Exception() {
        // 准备测试数据
        String businessLockKey = "test_lock";
        int timeoutSeconds = 30;
        doThrow(new RuntimeException("模拟异常")).when(redissonClient).getLock(businessLockKey);

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.getBusinessLock(businessLockKey, timeoutSeconds));
        assertEquals("获取业务处理锁失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient).getLock(businessLockKey);
    }

    @Test
    @DisplayName("测试释放业务锁 - 成功场景")
    void testReleaseBusinessLock_Success() throws ContrastBusinessException {
        // 准备测试数据
        String businessLockKey = "test_lock";
        doReturn(rLock).when(redissonClient).getLock(businessLockKey);
        doReturn(true).when(rLock).isHeldByCurrentThread();
        doReturn(true).when(rLock).isLocked();

        // 执行测试
        assertDoesNotThrow(() -> taskCounterComponent.releaseBusinessLock(businessLockKey));

        // 验证结果
        verify(redissonClient).getLock(businessLockKey);
        verify(rLock).isHeldByCurrentThread();
        verify(rLock).isLocked();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试释放业务锁 - 锁不存在")
    void testReleaseBusinessLock_LockNotExists() throws ContrastBusinessException {
        // 准备测试数据
        String businessLockKey = "test_lock";
        doReturn(rLock).when(redissonClient).getLock(businessLockKey);
        doReturn(false).when(rLock).isHeldByCurrentThread();

        // 执行测试
        assertDoesNotThrow(() -> taskCounterComponent.releaseBusinessLock(businessLockKey));

        // 验证结果
        verify(redissonClient).getLock(businessLockKey);
        verify(rLock).isHeldByCurrentThread();
        verify(rLock, never()).unlock();
    }

    @Test
    @DisplayName("测试释放业务锁 - 发生异常")
    void testReleaseBusinessLock_Exception() {
        // 准备测试数据
        String businessLockKey = "test_lock";
        doReturn(rLock).when(redissonClient).getLock(businessLockKey);
        doReturn(true).when(rLock).isHeldByCurrentThread();
        doReturn(true).when(rLock).isLocked();
        doThrow(new RuntimeException("模拟异常")).when(rLock).unlock();

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.releaseBusinessLock(businessLockKey));
        assertEquals("释放业务处理锁失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient).getLock(businessLockKey);
        verify(rLock).isHeldByCurrentThread();
        verify(rLock).isLocked();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试释放业务锁 - 锁键为空")
    void testReleaseBusinessLock_NullKey() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.releaseBusinessLock(null));
        assertEquals("锁键不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试释放业务锁 - 锁键为空白")
    void testReleaseBusinessLock_BlankKey() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.releaseBusinessLock("   "));
        assertEquals("锁键不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试批量清理指定前缀的计数器 - 成功")
    void testClearCountersByPrefix_Success() throws ContrastBusinessException {
        // 准备测试数据
        String prefix = "test:counter:";
        Collection<String> keys = Arrays.asList("test:counter:1", "test:counter:2", "test:counter:3");

        // Mock Keys对象
        when(redissonClient.getKeys()).thenReturn(rKeys);
        when(rKeys.getKeysByPattern(prefix + "*")).thenReturn(keys);
        when(rKeys.delete(any(String[].class))).thenReturn(3L);

        // 执行测试
        taskCounterComponent.clearCountersByPrefix(prefix);

        // 验证调用
        verify(redissonClient, times(2)).getKeys();
        verify(rKeys).getKeysByPattern(prefix + "*");
        verify(rKeys).delete("test:counter:1", "test:counter:2", "test:counter:3");
    }

    @Test
    @DisplayName("测试批量清理指定前缀的计数器 - 没有匹配的键")
    void testClearCountersByPrefix_NoKeys() throws ContrastBusinessException {
        String prefix = "test_prefix";
        Set<String> keys = new HashSet<>();
        when(redissonClient.getKeys()).thenReturn(rKeys);
        when(rKeys.getKeysByPattern(prefix + "*")).thenReturn(keys);
        taskCounterComponent.clearCountersByPrefix(prefix);
        verify(redissonClient).getKeys();
        verify(rKeys).getKeysByPattern(prefix + "*");
        verify(rKeys, never()).delete(any(String[].class));
    }

    @Test
    @DisplayName("测试批量清理指定前缀的计数器 - 前缀为空")
    void testClearCountersByPrefix_NullPrefix() {
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> taskCounterComponent.clearCountersByPrefix(null));
        assertEquals("计数器前缀不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试批量清理指定前缀的计数器 - 前缀为空白")
    void testClearCountersByPrefix_BlankPrefix() {
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> taskCounterComponent.clearCountersByPrefix("   "));
        assertEquals("计数器前缀不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试批量清理指定前缀的计数器 - 发生异常")
    void testClearCountersByPrefix_Exception() {
        // 准备测试数据
        String prefix = "test_prefix";
        when(redissonClient.getKeys()).thenReturn(rKeys);
        when(rKeys.getKeysByPattern(prefix + "*")).thenThrow(new RuntimeException("模拟异常"));

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.clearCountersByPrefix(prefix));
        assertEquals("批量清理计数器失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient).getKeys();
        verify(rKeys).getKeysByPattern(prefix + "*");
    }

    @Test
    @DisplayName("测试运行实例详情计数器减值 - 成功场景")
    void testDecrementInstanceInfoCounter_Success() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        String infoLockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        doReturn(rLock).when(redissonClient).getLock(infoLockKey);
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);
        doReturn(true).when(rAtomicLong).isExists();
        doReturn(1L).when(rAtomicLong).decrementAndGet();

        // 执行测试
        long result = taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId);

        // 验证结果
        assertEquals(1L, result);
        verify(redissonClient).getLock(infoLockKey);
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).decrementAndGet();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试运行实例详情计数器减值 - 实例详情ID为空")
    void testDecrementInstanceInfoCounter_NullId() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.decrementInstanceInfoCounter(null));
        assertEquals("实例详情ID不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试运行实例详情计数器减值 - 获取锁超时")
    void testDecrementInstanceInfoCounter_LockTimeout() throws InterruptedException {
        // 准备测试数据
        String infoLockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        doReturn(rLock).when(redissonClient).getLock(infoLockKey);
        doReturn(false).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId));
        assertEquals("计数器减值失败：计数器减值时获取锁超时", exception.getMessage());

        // 验证结果
        verify(redissonClient).getLock(infoLockKey);
        verify(rLock).tryLock(TaskCounterComponent.LOCK_WAIT_TIME, TaskCounterComponent.LOCK_TIMEOUT, TimeUnit.SECONDS);
        verify(redissonClient, never()).getAtomicLong(anyString());
    }

    @Test
    @DisplayName("测试运行实例详情计数器减值 - 获取锁时被中断")
    void testDecrementInstanceInfoCounter_Interrupted() throws InterruptedException {
        // 准备测试数据
        String infoLockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        doReturn(rLock).when(redissonClient).getLock(infoLockKey);
        doThrow(new InterruptedException()).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId));
        assertEquals("计数器减值时被中断", exception.getMessage());

        // 验证结果
        verify(redissonClient).getLock(infoLockKey);
        verify(rLock).tryLock(TaskCounterComponent.LOCK_WAIT_TIME, TaskCounterComponent.LOCK_TIMEOUT, TimeUnit.SECONDS);
        verify(redissonClient, never()).getAtomicLong(anyString());
    }

    @Test
    @DisplayName("测试获取运行实例详情计数器值 - 实例详情ID为空")
    void testGetInstanceInfoCounterValue_NullId() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.getInstanceInfoCounterValue(null));
        assertEquals("实例详情ID不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试获取运行实例详情计数器值 - 计数器不存在")
    void testGetInstanceInfoCounterValue_NotExists() throws ContrastBusinessException {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);
        doReturn(false).when(rAtomicLong).isExists();

        // 执行测试
        long result = taskCounterComponent.getInstanceInfoCounterValue(instanceInfoId);

        // 验证结果
        assertEquals(-1L, result);
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong, never()).get();
    }

    @Test
    @DisplayName("测试获取运行实例详情计数器值 - 发生异常")
    void testGetInstanceInfoCounterValue_Exception() {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);
        doReturn(true).when(rAtomicLong).isExists();
        doThrow(new RuntimeException("模拟异常")).when(rAtomicLong).get();

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.getInstanceInfoCounterValue(instanceInfoId));
        assertEquals("获取计数器值失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong).get();
    }

    @Test
    @DisplayName("测试获取运行实例详情计数器值 - 成功场景")
    void testGetInstanceInfoCounterValue_Success() throws ContrastBusinessException {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);
        doReturn(true).when(rAtomicLong).isExists();
        doReturn(5L).when(rAtomicLong).get();

        // 执行测试
        long result = taskCounterComponent.getInstanceInfoCounterValue(instanceInfoId);

        // 验证结果
        assertEquals(5L, result);
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong).get();
    }

    @Test
    @DisplayName("测试清理运行实例详情计数器 - 成功场景")
    void testClearInstanceInfoCounter_Success() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        String infoLockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        doReturn(rLock).when(redissonClient).getLock(infoLockKey);
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);
        doReturn(true).when(rAtomicLong).isExists();

        // 执行测试
        assertDoesNotThrow(() -> taskCounterComponent.clearInstanceInfoCounter(instanceInfoId));

        // 验证结果
        verify(redissonClient).getLock(infoLockKey);
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).isExists();
        verify(rAtomicLong).delete();
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试清理运行实例详情计数器 - 实例详情ID为空")
    void testClearInstanceInfoCounter_NullId() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.clearInstanceInfoCounter(null));
        assertEquals("实例详情ID不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试清理运行实例详情计数器 - 获取锁超时")
    void testClearInstanceInfoCounter_LockTimeout() throws InterruptedException {
        // 准备测试数据
        String infoLockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        doReturn(rLock).when(redissonClient).getLock(infoLockKey);
        doReturn(false).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.clearInstanceInfoCounter(instanceInfoId));
        assertEquals("清理计数器失败：清理计数器时获取锁超时", exception.getMessage());

        // 验证结果
        verify(redissonClient).getLock(infoLockKey);
        verify(rLock).tryLock(TaskCounterComponent.LOCK_WAIT_TIME, TaskCounterComponent.LOCK_TIMEOUT, TimeUnit.SECONDS);
        verify(redissonClient, never()).getAtomicLong(anyString());
    }

    @Test
    @DisplayName("测试检查运行实例详情计数器是否存在 - 成功场景")
    void testIsInstanceInfoCounterExists_Success() throws ContrastBusinessException {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);
        doReturn(true).when(rAtomicLong).isExists();

        // 执行测试
        boolean result = taskCounterComponent.isInstanceInfoCounterExists(instanceInfoId);

        // 验证结果
        assertTrue(result);
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).isExists();
    }

    @Test
    @DisplayName("测试检查运行实例详情计数器是否存在 - 实例详情ID为空")
    void testIsInstanceInfoCounterExists_NullId() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.isInstanceInfoCounterExists(null));
        assertEquals("实例详情ID不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试检查运行实例详情计数器是否存在 - 发生异常")
    void testIsInstanceInfoCounterExists_Exception() {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);
        doThrow(new RuntimeException("模拟异常")).when(rAtomicLong).isExists();

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.isInstanceInfoCounterExists(instanceInfoId));
        assertEquals("检查计数器是否存在失败：模拟异常", exception.getMessage());

        // 验证结果
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).isExists();
    }

    @Test
    @DisplayName("测试初始化运行实例详情计数器 - 成功场景")
    void testInitInstanceInfoCounter_Success() throws InterruptedException, ContrastBusinessException {
        // 准备测试数据
        String infoCounterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        String infoLockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        doReturn(rLock).when(redissonClient).getLock(infoLockKey);
        doReturn(true).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        doReturn(rAtomicLong).when(redissonClient).getAtomicLong(infoCounterKey);

        // 执行测试
        taskCounterComponent.initInstanceInfoCounter(instanceInfoId, initialValue);

        // 验证结果
        verify(redissonClient).getLock(infoLockKey);
        verify(redissonClient).getAtomicLong(infoCounterKey);
        verify(rAtomicLong).set(initialValue);
        verify(rLock).unlock();
    }

    @Test
    @DisplayName("测试初始化运行实例详情计数器 - 实例详情ID为空")
    void testInitInstanceInfoCounter_NullId() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceInfoCounter(null, initialValue));
        assertEquals("实例详情ID不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试初始化运行实例详情计数器 - 初始值为空")
    void testInitInstanceInfoCounter_NullInitialValue() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceInfoCounter(instanceInfoId, null));
        assertEquals("初始值不能为空且必须大于等于0", exception.getMessage());
    }

    @Test
    @DisplayName("测试初始化运行实例详情计数器 - 初始值小于0")
    void testInitInstanceInfoCounter_NegativeInitialValue() {
        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceInfoCounter(instanceInfoId, -1));
        assertEquals("初始值不能为空且必须大于等于0", exception.getMessage());
    }

    @Test
    @DisplayName("测试初始化运行实例详情计数器 - 获取锁超时")
    void testInitInstanceInfoCounter_LockTimeout() throws InterruptedException {
        // 准备测试数据
        String infoLockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        doReturn(rLock).when(redissonClient).getLock(infoLockKey);
        doReturn(false).when(rLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));

        // 执行测试
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
            () -> taskCounterComponent.initInstanceInfoCounter(instanceInfoId, initialValue));
        assertEquals("初始化计数器失败：初始化计数器时获取锁超时", exception.getMessage());

        // 验证结果
        verify(redissonClient).getLock(infoLockKey);
        verify(rLock).tryLock(TaskCounterComponent.LOCK_WAIT_TIME, TaskCounterComponent.LOCK_TIMEOUT, TimeUnit.SECONDS);
        verify(redissonClient, never()).getAtomicLong(anyString());
    }
}