package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.FlowOperateResultDto;
import com.ideal.envc.model.dto.OperateFlowStopRequestDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IFlowOperateService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Arrays;

/**
 * 比对结果
 * <AUTHOR>
 */
@RestController
@RequestMapping("/operateFlow")
@MethodPermission("@dp.hasBtnPermission('comparison-results')")
public class OperateFlowController {
    private static final Logger logger = LoggerFactory.getLogger(OperateFlowController.class);
    private final IFlowOperateService flowOperateService;
    private final UserinfoComponent userinfoComponent;

    public OperateFlowController(IFlowOperateService flowOperateService, UserinfoComponent userinfoComponent) {
        this.flowOperateService = flowOperateService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 终止流程
     *
     * @param operateFlowStopRequestDto 流程ID集合对象
     * @return 操作结果
     */
    @PostMapping("/stop")
    @MethodPermission("@dp.hasBtnPermission('stopFlowOfRule')")
    public R<FlowOperateResultDto> stopFlow(@RequestBody OperateFlowStopRequestDto operateFlowStopRequestDto) {
        UserDto userDto = userinfoComponent.getUser();
        if(operateFlowStopRequestDto==null || operateFlowStopRequestDto.getFlowIds()==null || operateFlowStopRequestDto.getFlowIds().isEmpty()){
            return R.fail(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getCode(), ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getDesc());
        }
        try {
            Long[]  flowIdsLong =  Arrays.stream(operateFlowStopRequestDto.getFlowIds().toArray(new String[0]))
                    .map(s -> {
                        try {
                            return Long.valueOf(s);
                        } catch (NumberFormatException e) {
                            return -1L; // 或其他默认值
                        }
                    })
                    .toArray(Long[]::new);
            FlowOperateResultDto flowOperateResultDto = flowOperateService.operateTerminatedFlow(flowIdsLong, userDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), flowOperateResultDto, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("终止流程系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 重试流程
     *
     * @param flowId 流程ID
     * @return 操作结果
     */
    @PostMapping("/retry")
    @MethodPermission("@dp.hasBtnPermission('retryFlowOfRule')")
    public R<FlowOperateResultDto> retryFlow(@RequestParam(value = "flowId") Long flowId) {
        UserDto userDto = userinfoComponent.getUser();
        try {
            FlowOperateResultDto flowOperateResultDto = flowOperateService.operateRetryFlow(flowId, userDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), flowOperateResultDto, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("重试流程系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
