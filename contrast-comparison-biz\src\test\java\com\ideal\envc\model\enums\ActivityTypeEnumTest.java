package com.ideal.envc.model.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;

import static org.junit.jupiter.api.Assertions.*;

class ActivityTypeEnumTest {

    @Test
    @DisplayName("测试所有枚举值的getCode和getName")
    void testEnumGetters() {
        assertEquals("shellcmd", ActivityTypeEnum.SHELL_CMD.getCode());
        assertEquals("脚本活动", ActivityTypeEnum.SHELL_CMD.getName());
        assertEquals("Compare Content", ActivityTypeEnum.COMPARE_CONTENT.getCode());
        assertEquals("文件比对活动", ActivityTypeEnum.COMPARE_CONTENT.getName());
        assertEquals("Sync Content", ActivityTypeEnum.SYNC_CONTENT.getCode());
        assertEquals("文件同步活动", ActivityTypeEnum.SYNC_CONTENT.getName());
    }

    @ParameterizedTest
    @CsvSource({
        "shellcmd, SHELL_CMD",
        "Compare Content, COMPARE_CONTENT",
        "Sync Content, SYNC_CONTENT"
    })
    @DisplayName("测试getByCode返回正确的枚举")
    void testGetByCodeValid(String code, String expectedEnumName) {
        ActivityTypeEnum result = ActivityTypeEnum.getByCode(code);
        assertNotNull(result);
        assertEquals(ActivityTypeEnum.valueOf(expectedEnumName), result);
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("测试getByCode传入null或空字符串返回null")
    void testGetByCodeNullOrEmpty(String code) {
        assertNull(ActivityTypeEnum.getByCode(code));
    }

    @Test
    @DisplayName("测试getByCode传入不存在的code返回null")
    void testGetByCodeInvalid() {
        assertNull(ActivityTypeEnum.getByCode("not-exist"));
    }

    @ParameterizedTest
    @CsvSource({
        "shellcmd, 脚本活动",
        "Compare Content, 文件比对活动",
        "Sync Content, 文件同步活动"
    })
    @DisplayName("测试getNameByCode返回正确的名称")
    void testGetNameByCodeValid(String code, String expectedName) {
        assertEquals(expectedName, ActivityTypeEnum.getNameByCode(code));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("测试getNameByCode传入null或空字符串返回未知活动类型")
    void testGetNameByCodeNullOrEmpty(String code) {
        assertEquals("未知活动类型", ActivityTypeEnum.getNameByCode(code));
    }

    @Test
    @DisplayName("测试getNameByCode传入不存在的code返回未知活动类型")
    void testGetNameByCodeInvalid() {
        assertEquals("未知活动类型", ActivityTypeEnum.getNameByCode("not-exist"));
    }

    @ParameterizedTest
    @CsvSource({
        "shellcmd, true",
        "Compare Content, true",
        "Sync Content, true",
        "not-exist, false"
    })
    @DisplayName("测试isValidCode各种情况")
    void testIsValidCode(String code, boolean expected) {
        assertEquals(expected, ActivityTypeEnum.isValidCode(code));
    }

    @Test
    @DisplayName("测试isValidCode传入null返回false")
    void testIsValidCodeNull() {
        assertFalse(ActivityTypeEnum.isValidCode(null));
    }
} 