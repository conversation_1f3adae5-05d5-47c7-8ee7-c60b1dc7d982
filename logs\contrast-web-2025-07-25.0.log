2025-07-25 00:01:32.566 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 00:21:32.564 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 00:51:32.576 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 01:21:32.572 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 01:41:32.588 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 02:21:32.584 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 02:31:32.593 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 03:21:32.596 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 03:21:32.600 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 04:11:32.708 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 04:12:18.551 [DubboMetadataReportTimer-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:434 -  [DUBBO] start to publish all metadata., dubbo version: 3.2.5, current host: **********
2025-07-25 04:12:18.553 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@581879fb; definition: {group=system, pid=25652, dubbo=2.0.2, application=contrast-dubbo, interface=com.ideal.system.api.IRoleProjectPermission, release=3.2.5, version=1.0.0, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getRoleButtonAuthority, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753348292508}, dubbo version: 3.2.5, current host: **********
2025-07-25 04:12:18.555 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@62ec8bd9; definition: {group=engine, pid=25652, dubbo=2.0.2, application=contrast-dubbo, interface=com.ideal.engine.api.IActivity, release=3.2.5, version=1.0.0, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753348293376}, dubbo version: 3.2.5, current host: **********
2025-07-25 04:12:18.556 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@473761; definition: {group=engine, pid=25652, dubbo=2.0.2, application=contrast-dubbo, interface=com.ideal.engine.api.IStartFlow, release=3.2.5, version=1.0.0, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=killFlow,pauseFlow,resumeFlow,startFlow, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753348292921}, dubbo version: 3.2.5, current host: **********
2025-07-25 04:12:18.557 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@2edefdb8; definition: {group=system, pid=25652, dubbo=2.0.2, application=contrast-dubbo, interface=com.ideal.system.api.IBusinessSystem, release=3.2.5, version=1.0.0, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753348293706}, dubbo version: 3.2.5, current host: **********
2025-07-25 04:12:18.558 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@35928e39; definition: {group=system, pid=25652, dubbo=2.0.2, application=contrast-dubbo, interface=com.ideal.system.api.IBusinessSystemCompuerList, release=3.2.5, version=1.0.0, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753348294065}, dubbo version: 3.2.5, current host: **********
2025-07-25 04:12:18.559 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@71ea5224; definition: {group=system, pid=25652, dubbo=2.0.2, application=contrast-dubbo, interface=com.ideal.system.api.ICenter, release=3.2.5, version=1.0.0, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753348289774}, dubbo version: 3.2.5, current host: **********
2025-07-25 04:21:32.726 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 05:01:32.847 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 05:21:32.868 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 05:51:32.926 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 06:21:32.953 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 06:41:32.985 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 07:21:33.026 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 07:31:33.048 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 08:21:33.094 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 08:21:33.100 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:32.784 [NettyClientWorker-9-1] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:58670 -> /************:20910 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.366 [nacos-grpc-client-executor-************-21611] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Receive server push request, request = NotifySubscriberRequest, requestId = 341085
2025-07-25 09:00:33.367 [nacos-grpc-client-executor-************-21613] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:00:33.367 [nacos-grpc-client-executor-************-21611] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"************#20910#null#dubbo-system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"6c4858cd33ca2117952c4d7f3a1033fc","dubbo.metadata.storage-type":"local","timestamp":"1753259089905"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:00:33.367 [nacos-grpc-client-executor-************-21611] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-system -> []
2025-07-25 09:00:33.367 [nacos-grpc-client-executor-************-21613] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753259088504"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:00:33.368 [nacos-grpc-client-executor-************-21613] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> []
2025-07-25 09:00:33.370 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.ICenter:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@3ca046a5
2025-07-25 09:00:33.388 [nacos-grpc-client-executor-************-21611] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Ack server push request, request = NotifySubscriberRequest, requestId = 341085
2025-07-25 09:00:33.388 [nacos-grpc-client-executor-************-21613] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:00:33.392 [nacos-grpc-client-executor-************-21614] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:00:33.393 [nacos-grpc-client-executor-************-21614] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753259089209"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:00:33.394 [nacos-grpc-client-executor-************-21614] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> []
2025-07-25 09:00:33.397 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-system to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@177c8d08
2025-07-25 09:00:33.398 [Dubbo-framework-registry-notification-0-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-25 09:00:33.399 [Dubbo-framework-registry-notification-0-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.ICenter?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.ICenter&methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348289774&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.401 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.401 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.412 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.419 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-25 09:00:33.422 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.425 [nacos-grpc-client-executor-************-21614] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:00:33.427 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.428 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.429 [nacos-grpc-client-executor-************-21615] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = 341099
2025-07-25 09:00:33.429 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IRoleProjectPermission:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.430 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-25 09:00:33.430 [nacos-grpc-client-executor-************-21615] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753259086642"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:00:33.431 [nacos-grpc-client-executor-************-21615] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> []
2025-07-25 09:00:33.431 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.432 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.432 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystem:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.432 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-25 09:00:33.433 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.433 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.434 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.434 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.434 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.ICenter:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.435 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-25 09:00:33.437 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.437 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.438 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystem:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@46de7d9
2025-07-25 09:00:33.440 [Dubbo-framework-registry-notification-4-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-25 09:00:33.441 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@c1d632f2
2025-07-25 09:00:33.441 [Dubbo-framework-registry-notification-4-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystem?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystem&methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348293706&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.445 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.445 [Dubbo-framework-registry-notification-5-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-25 09:00:33.446 [Dubbo-framework-registry-notification-5-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystemCompuerList?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystemCompuerList&methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348294065&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.452 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.458 [nacos-grpc-client-executor-************-21615] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = 341099
2025-07-25 09:00:33.461 [nacos-grpc-client-executor-************-21616] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:00:33.462 [nacos-grpc-client-executor-************-21616] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753259084250"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:00:33.462 [nacos-grpc-client-executor-************-21616] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> []
2025-07-25 09:00:33.462 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@7b5f731f
2025-07-25 09:00:33.462 [Dubbo-framework-registry-notification-1-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-25 09:00:33.462 [Dubbo-framework-registry-notification-1-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IRoleProjectPermission?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IRoleProjectPermission&methods=getRoleButtonAuthority&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348292508&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.468 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xd9924ef8, L:/**********:58670 ! R:/************:20910], dubbo version: 3.2.5, current host: **********
2025-07-25 09:00:33.479 [nacos-grpc-client-executor-************-21616] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:00:33.481 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.676 [nacos-grpc-client-executor-************-21687] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.677 [nacos-grpc-client-executor-************-21687] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405720694"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.678 [nacos-grpc-client-executor-************-21687] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405720694"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.678 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.ICenter:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@fc07d3e8
2025-07-25 09:03:42.688 [Dubbo-framework-registry-notification-0-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.ICenter?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.ICenter&methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348289774&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.696 [nacos-grpc-client-executor-************-21687] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.700 [nacos-grpc-client-executor-************-21688] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.703 [nacos-grpc-client-executor-************-21688] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405718840"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.706 [nacos-grpc-client-executor-************-21688] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405718840"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.706 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@485e6830
2025-07-25 09:03:42.706 [NettyClientWorker-9-3] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:57139 -> /*************:20910 is established., dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.707 [Dubbo-framework-registry-notification-5-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystemCompuerList?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystemCompuerList&methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348294065&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.706 [Dubbo-framework-registry-notification-0-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /*************:20910 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xdd2d1c27, L:/**********:57139 - R:/*************:20910]], dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.708 [Dubbo-framework-registry-notification-0-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /*************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.734 [nacos-grpc-client-executor-************-21688] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.735 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.735 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.737 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.737 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.739 [nacos-grpc-client-executor-************-21689] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.740 [nacos-grpc-client-executor-************-21689] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405721391"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.740 [nacos-grpc-client-executor-************-21689] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405721391"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.741 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystem:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@ca60d321
2025-07-25 09:03:42.742 [Dubbo-framework-registry-notification-4-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystem?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystem&methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348293706&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.755 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.756 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.769 [nacos-grpc-client-executor-************-21689] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.774 [nacos-grpc-client-executor-************-21690] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.775 [nacos-grpc-client-executor-************-21690] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405716431"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.776 [nacos-grpc-client-executor-************-21690] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405716431"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.777 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@54d37098
2025-07-25 09:03:42.778 [nacos-grpc-client-executor-************-21682] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Receive server push request, request = NotifySubscriberRequest, requestId = 341430
2025-07-25 09:03:42.779 [Dubbo-framework-registry-notification-1-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IRoleProjectPermission?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IRoleProjectPermission&methods=getRoleButtonAuthority&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348292508&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.779 [nacos-grpc-client-executor-************-21682] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"*************#20910#null#dubbo-system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"9d6e0f987f691e3d90818a77032e1f0a","dubbo.metadata.storage-type":"local","timestamp":"1753405722092"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.779 [nacos-grpc-client-executor-************-21682] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"*************#20910#null#dubbo-system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"9d6e0f987f691e3d90818a77032e1f0a","dubbo.metadata.storage-type":"local","timestamp":"1753405722092"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:03:42.779 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-system to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@177c8d08
2025-07-25 09:03:42.780 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.787 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: 9d6e0f987f691e3d90818a77032e1f0a , dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.788 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.788 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.789 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.792 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.793 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.793 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.793 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IRoleProjectPermission:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.795 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.796 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IRoleProjectPermission:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.796 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.796 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystem:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.798 [nacos-grpc-client-executor-************-21682] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Ack server push request, request = NotifySubscriberRequest, requestId = 341430
2025-07-25 09:03:42.798 [nacos-grpc-client-executor-************-21690] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:03:42.799 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.799 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystem:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.799 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.799 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.799 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: 9d6e0f987f691e3d90818a77032e1f0a , dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.799 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.ICenter:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.801 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.801 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.ICenter:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:03:42.801 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : *************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:11:33.150 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.037 [nacos-grpc-client-executor-************-21955] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.038 [nacos-grpc-client-executor-************-21955] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"***********49"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.039 [nacos-grpc-client-executor-************-21955] INFO  com.alibaba.nacos.client.naming:142 - current ips:(2) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405718840"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"***********49"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.040 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@38ea3e93
2025-07-25 09:15:14.041 [Dubbo-framework-registry-notification-5-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystemCompuerList?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystemCompuerList&methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348294065&unloadClusterRelated=false&version=1.0.0, url size: 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.064 [NettyClientWorker-9-4] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:58030 -> /************:20910 is established., dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.063 [Dubbo-framework-registry-notification-5-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /************:20910 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x35b50ed9, L:/**********:58030 - R:/************:20910]], dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.064 [Dubbo-framework-registry-notification-5-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.065 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0 Instance address size 1, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.065 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : ************:20910,*************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.071 [nacos-grpc-client-executor-************-21955] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.073 [nacos-grpc-client-executor-************-21956] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.076 [nacos-grpc-client-executor-************-21956] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406223460"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.076 [nacos-grpc-client-executor-************-21956] INFO  com.alibaba.nacos.client.naming:142 - current ips:(2) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405716431"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406223460"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.077 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@dfb821a9
2025-07-25 09:15:14.078 [Dubbo-framework-registry-notification-1-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IRoleProjectPermission?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IRoleProjectPermission&methods=getRoleButtonAuthority&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348292508&unloadClusterRelated=false&version=1.0.0, url size: 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.081 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IRoleProjectPermission:1.0.0 Instance address size 1, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.082 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : *************:20910,************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.100 [nacos-grpc-client-executor-************-21956] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.133 [nacos-grpc-client-executor-************-21957] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.134 [nacos-grpc-client-executor-************-21957] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406227741"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.134 [nacos-grpc-client-executor-************-21957] INFO  com.alibaba.nacos.client.naming:142 - current ips:(2) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406227741"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405720694"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.134 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.ICenter:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@f62b22c9
2025-07-25 09:15:14.134 [Dubbo-framework-registry-notification-0-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.ICenter?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.ICenter&methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348289774&unloadClusterRelated=false&version=1.0.0, url size: 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.136 [nacos-grpc-client-executor-************-21943] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.137 [nacos-grpc-client-executor-************-21943] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"************#20910#null#dubbo-system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"6c4858cd33ca2117952c4d7f3a1033fc","dubbo.metadata.storage-type":"local","timestamp":"1753406229138"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.137 [nacos-grpc-client-executor-************-21943] INFO  com.alibaba.nacos.client.naming:142 - current ips:(2) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"************#20910#null#dubbo-system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"6c4858cd33ca2117952c4d7f3a1033fc","dubbo.metadata.storage-type":"local","timestamp":"1753406229138"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"*************#20910#null#dubbo-system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"9d6e0f987f691e3d90818a77032e1f0a","dubbo.metadata.storage-type":"local","timestamp":"1753405722092"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.137 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-system to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@177c8d08
2025-07-25 09:15:14.138 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.138 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.ICenter:1.0.0 Instance address size 1, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.138 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : *************:20910,************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.138 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 2 unique working revisions: 9d6e0f987f691e3d90818a77032e1f0a 6c4858cd33ca2117952c4d7f3a1033fc , dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.138 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:null with urls 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.150 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.151 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0 Instance address size 2, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.151 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : *************:20910,************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.151 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IRoleProjectPermission:1.0.0:null with urls 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.152 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.153 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IRoleProjectPermission:1.0.0 Instance address size 2, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.153 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : *************:20910,************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.153 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystem:1.0.0:null with urls 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.156 [nacos-grpc-client-executor-************-21943] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.156 [nacos-grpc-client-executor-************-21957] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:15:14.156 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.157 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystem:1.0.0 Instance address size 2, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.157 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : ************:20910,*************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.157 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.159 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 2 unique working revisions: 9d6e0f987f691e3d90818a77032e1f0a 6c4858cd33ca2117952c4d7f3a1033fc , dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.159 [nacos-grpc-client-executor-************-21958] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = 341741
2025-07-25 09:15:14.159 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.ICenter:1.0.0:null with urls 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.159 [nacos-grpc-client-executor-************-21958] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406228431"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.160 [nacos-grpc-client-executor-************-21958] INFO  com.alibaba.nacos.client.naming:142 - current ips:(2) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405721391"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000},{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406228431"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:15:14.161 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.162 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.ICenter:1.0.0 Instance address size 2, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.162 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : ************:20910,*************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.163 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystem:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@7ebd12f1
2025-07-25 09:15:14.164 [Dubbo-framework-registry-notification-4-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystem?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystem&methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348293706&unloadClusterRelated=false&version=1.0.0, url size: 2, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.167 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystem:1.0.0 Instance address size 2, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.167 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 2. Invokers Size : 2. Available Size: 2. Available Invokers : *************:20910,************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:15:14.179 [nacos-grpc-client-executor-************-21958] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = 341741
2025-07-25 09:16:34.031 [NettyClientWorker-9-3] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:57139 -> /*************:20910 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.625 [nacos-grpc-client-executor-************-21972] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Receive server push request, request = NotifySubscriberRequest, requestId = 341877
2025-07-25 09:16:34.625 [nacos-grpc-client-executor-************-21987] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:16:34.628 [nacos-grpc-client-executor-************-21972] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"*************#20910#null#dubbo-system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"9d6e0f987f691e3d90818a77032e1f0a","dubbo.metadata.storage-type":"local","timestamp":"1753405722092"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.628 [nacos-grpc-client-executor-************-21987] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405720694"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.628 [nacos-grpc-client-executor-************-21972] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"************#20910#null#dubbo-system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"6c4858cd33ca2117952c4d7f3a1033fc","dubbo.metadata.storage-type":"local","timestamp":"1753406229138"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.628 [nacos-grpc-client-executor-************-21987] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406227741"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.628 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-system to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@177c8d08
2025-07-25 09:16:34.629 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.629 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: 6c4858cd33ca2117952c4d7f3a1033fc , dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.630 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.630 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.631 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:549 -  [DUBBO] 1 deprecated invokers deleted., dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0 Instance address size 1, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IRoleProjectPermission:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.633 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:549 -  [DUBBO] 1 deprecated invokers deleted., dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.634 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IRoleProjectPermission:1.0.0 Instance address size 1, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.634 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.635 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.IBusinessSystem:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.635 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.636 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:549 -  [DUBBO] 1 deprecated invokers deleted., dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.636 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystem:1.0.0 Instance address size 1, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.636 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.636 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.637 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: 6c4858cd33ca2117952c4d7f3a1033fc , dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.637 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service system/com.ideal.system.api.ICenter:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.637 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.638 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:549 -  [DUBBO] 1 deprecated invokers deleted., dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.638 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.ICenter:1.0.0 Instance address size 1, interface address size 2, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.638 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.639 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.ICenter:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@7a8316e4
2025-07-25 09:16:34.639 [Dubbo-framework-registry-notification-0-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.ICenter?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.ICenter&methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348289774&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.639 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:626 -  [DUBBO] New url total size, 1, destroyed total size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.640 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.ICenter:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.640 [Dubbo-framework-registry-notification-0-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.645 [nacos-grpc-client-executor-************-21972] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Ack server push request, request = NotifySubscriberRequest, requestId = 341877
2025-07-25 09:16:34.645 [nacos-grpc-client-executor-************-21987] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:16:34.647 [nacos-grpc-client-executor-************-21988] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:16:34.647 [nacos-grpc-client-executor-************-21988] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405721391"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.648 [nacos-grpc-client-executor-************-21988] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406228431"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.648 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystem:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@c25b09d
2025-07-25 09:16:34.648 [Dubbo-framework-registry-notification-4-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystem?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystem&methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348293706&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.648 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:626 -  [DUBBO] New url total size, 1, destroyed total size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.649 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystem:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.649 [Dubbo-framework-registry-notification-4-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.657 [nacos-grpc-client-executor-************-21988] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:16:34.659 [nacos-grpc-client-executor-************-21989] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:16:34.659 [nacos-grpc-client-executor-************-21989] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405718840"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.659 [nacos-grpc-client-executor-************-21989] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"***********49"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.660 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@2c954475
2025-07-25 09:16:34.660 [Dubbo-framework-registry-notification-5-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystemCompuerList?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystemCompuerList&methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348294065&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.661 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:626 -  [DUBBO] New url total size, 1, destroyed total size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.661 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.661 [Dubbo-framework-registry-notification-5-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.672 [nacos-grpc-client-executor-************-21989] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:16:34.674 [nacos-grpc-client-executor-************-21990] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:16:34.675 [nacos-grpc-client-executor-************-21990] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"*************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"*************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753405716431"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.676 [nacos-grpc-client-executor-************-21990] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406223460"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-25 09:16:34.677 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@13f24ccc
2025-07-25 09:16:34.678 [Dubbo-framework-registry-notification-1-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IRoleProjectPermission?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IRoleProjectPermission&methods=getRoleButtonAuthority&pid=25652&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753348292508&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.681 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xdd2d1c27, L:/**********:57139 ! R:/*************:20910], dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.683 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:626 -  [DUBBO] New url total size, 1, destroyed total size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.684 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IRoleProjectPermission:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.685 [Dubbo-framework-registry-notification-1-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:16:34.696 [nacos-grpc-client-executor-************-21990] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:21:33.165 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 09:23:16.474 [http-nio-8210-exec-1] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 09:23:16.477 [http-nio-8210-exec-1] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 09:24:01.786 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 09:24:01.806 [http-nio-8210-exec-5] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 09:24:53.703 [http-nio-8210-exec-6] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 09:25:12.203 [http-nio-8210-exec-6] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 09:25:25.768 [http-nio-8210-exec-7] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 09:25:27.683 [http-nio-8210-exec-7] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 09:26:31.277 [nacos-grpc-client-executor-************-22251] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 342206
2025-07-25 09:26:31.277 [nacos-grpc-client-executor-************-22248] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 342205
2025-07-25 09:26:31.278 [nacos-grpc-client-executor-************-22251] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 342206
2025-07-25 09:26:31.278 [nacos-grpc-client-executor-************-22248] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 342205
2025-07-25 09:26:31.278 [nacos-grpc-client-executor-************-22251] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753350661849_**********_62231]Request stream onCompleted, switch server
2025-07-25 09:26:31.278 [nacos-grpc-client-executor-************-22248] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753350661751_**********_62230]Request stream onCompleted, switch server
2025-07-25 09:26:31.353 [nacos-grpc-client-executor-************-22181] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Receive server push request, request = ClientDetectionRequest, requestId = 342204
2025-07-25 09:26:31.353 [nacos-grpc-client-executor-************-22159] INFO  com.alibaba.nacos.common.remote.client:63 - [8ad0970e-338a-4f36-9826-7e6c26e39062] Receive server push request, request = ClientDetectionRequest, requestId = 342207
2025-07-25 09:26:31.354 [nacos-grpc-client-executor-************-22181] INFO  com.alibaba.nacos.common.remote.client:63 - [c9ce9ed8-45dc-4121-a19b-bfe71b31ccdb] Ack server push request, request = ClientDetectionRequest, requestId = 342204
2025-07-25 09:26:31.354 [nacos-grpc-client-executor-************-22159] INFO  com.alibaba.nacos.common.remote.client:63 - [8ad0970e-338a-4f36-9826-7e6c26e39062] Ack server push request, request = ClientDetectionRequest, requestId = 342207
2025-07-25 09:26:31.355 [nacos-grpc-client-executor-************-22181] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753350661104_**********_62223]Request stream onCompleted, switch server
2025-07-25 09:26:31.355 [nacos-grpc-client-executor-************-22159] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753350661246_**********_62227]Request stream onCompleted, switch server
2025-07-25 09:26:31.369 [nacos-grpc-client-executor-************-22161] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Receive server push request, request = ClientDetectionRequest, requestId = 342209
2025-07-25 09:26:31.370 [nacos-grpc-client-executor-************-22161] INFO  com.alibaba.nacos.common.remote.client:63 - [0a61b75d-48ab-4b11-a59f-82372b4d183b] Ack server push request, request = ClientDetectionRequest, requestId = 342209
2025-07-25 09:26:31.369 [nacos-grpc-client-executor-************-22256] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 342208
2025-07-25 09:26:31.371 [nacos-grpc-client-executor-************-22256] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 342208
2025-07-25 09:26:31.371 [nacos-grpc-client-executor-************-22256] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753350662028_**********_62233]Request stream onCompleted, switch server
2025-07-25 09:26:31.371 [nacos-grpc-client-executor-************-22161] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753350661958_**********_62232]Request stream onCompleted, switch server
2025-07-25 09:26:31.402 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Server healthy check fail, currentConnection = 1753350661751_**********_62230
2025-07-25 09:26:31.409 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 09:26:31.416 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:26:31.423 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Server healthy check fail, currentConnection = 1753350662028_**********_62233
2025-07-25 09:26:31.424 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 09:26:31.426 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:26:31.459 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Server healthy check fail, currentConnection = 1753350661849_**********_62231
2025-07-25 09:26:31.465 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 09:26:31.465 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:26:31.487 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Success to connect a server [************:8848], connectionId = 1753406795291_**********_59628
2025-07-25 09:26:31.487 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Success to connect a server [************:8848], connectionId = 1753406795276_**********_59627
2025-07-25 09:26:31.488 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753350661751_**********_62230
2025-07-25 09:26:31.488 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753350661751_**********_62230
2025-07-25 09:26:31.489 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 09:26:31.489 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Notify disconnected event to listeners
2025-07-25 09:26:31.490 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] DisConnected,clear listen context...
2025-07-25 09:26:31.490 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Notify connected event to listeners.
2025-07-25 09:26:31.490 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [a98862b5-12ff-4419-a2d5-082d0492cfb3_config-0] Connected,notify listen context...
2025-07-25 09:26:31.490 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:26:31.488 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753350662028_**********_62233
2025-07-25 09:26:31.491 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753350662028_**********_62233
2025-07-25 09:26:31.492 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 09:26:31.493 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:26:31.493 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Notify disconnected event to listeners
2025-07-25 09:26:31.493 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] DisConnected,clear listen context...
2025-07-25 09:26:31.493 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Notify connected event to listeners.
2025-07-25 09:26:31.494 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [6c3188ee-ae0b-4609-a4a0-d9d2c4c4995f_config-0] Connected,notify listen context...
2025-07-25 09:26:31.503 [nacos.client.config.listener.task-0] ERROR com.alibaba.nacos.common.remote.client:102 - Send request fail, request = ConfigBatchListenRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=025d69441852157fd0d59c305fbfd6f5, Client-RequestTS=1753406791275, exConfigInfo=true}, requestId='null'}, retryTimes = 0, errorMessage = Connection is unregistered.
2025-07-25 09:26:31.515 [nacos.client.config.listener.task-0] ERROR com.alibaba.nacos.common.remote.client:102 - Send request fail, request = ConfigBatchListenRequest{headers={charset=UTF-8, Client-AppName=unknown, Client-RequestToken=025d69441852157fd0d59c305fbfd6f5, Client-RequestTS=1753406791275, exConfigInfo=true}, requestId='null'}, retryTimes = 0, errorMessage = Connection is unregistered.
2025-07-25 09:26:31.537 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Success to connect a server [************:8848], connectionId = 1753406795326_**********_59629
2025-07-25 09:26:31.538 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753350661849_**********_62231
2025-07-25 09:26:31.538 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753350661849_**********_62231
2025-07-25 09:26:31.539 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 09:26:31.539 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Notify disconnected event to listeners
2025-07-25 09:26:31.539 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:26:31.540 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] DisConnected,clear listen context...
2025-07-25 09:26:31.540 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Notify connected event to listeners.
2025-07-25 09:26:31.540 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [daf93d96-16c0-4848-bd9d-83d6f659fa8e_config-0] Connected,notify listen context...
2025-07-25 09:56:41.531 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-07-25 09:56:44.007 [main] INFO  com.alibaba.nacos.client.logging.NacosLogging:52 - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapterBuilder
2025-07-25 09:56:44.022 [main] INFO  com.alibaba.nacos.client.logging.NacosLogging:55 - Nacos Logging Adapter: com.alibaba.nacos.logger.adapter.logback12.LogbackNacosLoggingAdapter match ch.qos.logback.classic.Logger success.
2025-07-25 09:56:44.094 [main] INFO  com.alibaba.nacos.client.logging.NacosLogging:52 - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.logback14.LogbackNacosLoggingAdapterBuilder
2025-07-25 09:56:44.104 [main] INFO  com.alibaba.nacos.client.logging.NacosLogging:52 - Nacos Logging Adapter Builder: com.alibaba.nacos.logger.adapter.log4j2.Log4j2NacosLoggingAdapterBuilder
2025-07-25 09:56:44.473 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 09:56:44.475 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 09:56:45.349 [main] INFO  c.a.n.client.config.impl.LocalConfigInfoProcessor:65 - LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-25 09:56:45.379 [main] INFO  com.alibaba.nacos.common.remote.client:118 - [RpcClientFactory] create a new rpc client of 7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0
2025-07-25 09:56:45.640 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$390/386040589
2025-07-25 09:56:45.641 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$391/1800592689
2025-07-25 09:56:45.645 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-25 09:56:45.646 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-25 09:56:45.675 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Try to connect to server on start up, server: {serverIp = '************', server main port = 8848}
2025-07-25 09:56:45.862 [main] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:56:49.113 [main] INFO  c.a.n.common.ability.AbstractAbilityControlManager:61 - Ready to get current node abilities...
2025-07-25 09:56:49.145 [main] INFO  c.a.n.common.ability.AbstractAbilityControlManager:89 - Ready to initialize current node abilities, support modes: [SDK_CLIENT]
2025-07-25 09:56:49.151 [main] INFO  c.a.n.common.ability.AbstractAbilityControlManager:94 - Initialize current abilities finish...
2025-07-25 09:56:49.208 [main] INFO  c.a.n.c.ability.discover.NacosAbilityManagerHolder:85 - [AbilityControlManager] Successfully initialize AbilityControlManager
2025-07-25 09:56:49.369 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Success to connect to server [************:8848] on start up, connectionId = 1753408612159_**********_61926
2025-07-25 09:56:49.377 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Notify connected event to listeners.
2025-07-25 09:56:49.381 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Connected,notify listen context...
2025-07-25 09:56:49.384 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-25 09:56:49.390 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$403/486994287
2025-07-25 09:56:50.278 [main] INFO  com.alibaba.nacos.client.config.impl.Limiter:55 - limitTime:5.0
2025-07-25 09:56:50.488 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder:87 - Ignore the empty nacos configuration and get it based on dataId[contrast] & group[DEFAULT_GROUP]
2025-07-25 09:56:50.575 [main] WARN  c.a.cloud.nacos.client.NacosPropertySourceBuilder:87 - Ignore the empty nacos configuration and get it based on dataId[contrast.yml] & group[DEFAULT_GROUP]
2025-07-25 09:56:50.609 [main] INFO  com.alibaba.nacos.client.config.utils.JvmUtil:59 - isMultiInstance:false
2025-07-25 09:56:50.690 [main] INFO  o.s.c.b.c.PropertySourceBootstrapConfiguration:109 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-contrast-sit.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-contrast.yml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-contrast,DEFAULT_GROUP'}]
2025-07-25 09:56:50.758 [main] INFO  com.ideal.envc.Bootstrap:638 - The following 1 profile is active: "sit"
2025-07-25 09:56:55.997 [main] INFO  org.apache.dubbo.rpc.model.FrameworkModel:86 -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.134 [main] INFO  o.a.d.common.resource.GlobalResourcesRepository:96 -  [DUBBO] Creating global shared handler ..., dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.406 [main] INFO  org.apache.dubbo.rpc.model.ApplicationModel:108 -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.408 [main] INFO  org.apache.dubbo.rpc.model.ScopeModel:63 -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.538 [main] INFO  o.a.dubbo.config.context.AbstractConfigManager:141 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.543 [main] INFO  o.a.dubbo.config.context.AbstractConfigManager:141 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.603 [main] INFO  o.a.dubbo.common.utils.SerializeSecurityManager:106 -  [DUBBO] Serialize check serializable: true, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.605 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:126 -  [DUBBO] Read serialize allow list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.651 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:126 -  [DUBBO] Read serialize allow list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo-common/3.2.5/dubbo-common-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.654 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:145 -  [DUBBO] Read serialize blocked list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:56.705 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:145 -  [DUBBO] Read serialize blocked list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo-common/3.2.5/dubbo-common-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.272 [main] INFO  org.apache.dubbo.rpc.model.ApplicationModel:108 -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.274 [main] INFO  org.apache.dubbo.rpc.model.ScopeModel:63 -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.298 [main] INFO  o.a.dubbo.config.context.AbstractConfigManager:141 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.300 [main] INFO  o.a.dubbo.config.context.AbstractConfigManager:141 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.310 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:126 -  [DUBBO] Read serialize allow list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.310 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:126 -  [DUBBO] Read serialize allow list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo-common/3.2.5/dubbo-common-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.311 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:145 -  [DUBBO] Read serialize blocked list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.311 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:145 -  [DUBBO] Read serialize blocked list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo-common/3.2.5/dubbo-common-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.336 [main] INFO  o.a.d.config.spring.context.DubboSpringInitializer:115 -  [DUBBO] Use default application: Dubbo Application[1.1](unknown), dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.336 [main] INFO  org.apache.dubbo.rpc.model.ScopeModel:63 -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.343 [main] INFO  o.a.dubbo.config.context.AbstractConfigManager:141 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.357 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:126 -  [DUBBO] Read serialize allow list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.358 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:126 -  [DUBBO] Read serialize allow list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo-common/3.2.5/dubbo-common-3.2.5.jar!/security/serialize.allowlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.359 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:145 -  [DUBBO] Read serialize blocked list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo/3.2.5/dubbo-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.359 [main] INFO  o.a.d.common.utils.SerializeSecurityConfigurator:145 -  [DUBBO] Read serialize blocked list from jar:file:/F:/Maven/repository/org/apache/dubbo/dubbo-common/3.2.5/dubbo-common-3.2.5.jar!/security/serialize.blockedlist, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.367 [main] INFO  o.a.d.config.spring.context.DubboSpringInitializer:125 -  [DUBBO] Use default module model of target application: Dubbo Module[1.1.1], dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:57.368 [main] INFO  o.a.d.config.spring.context.DubboSpringInitializer:129 -  [DUBBO] Bind Dubbo Module[1.1.1] to spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@4c3de38e, dubbo version: 3.2.5, current host: **********
2025-07-25 09:56:59.707 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 09:56:59.727 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 09:57:00.044 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 58 ms. Found 0 Redis repository interfaces.
2025-07-25 09:57:01.041 [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor:276 -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:01.042 [main] INFO  o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor:278 -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:01.065 [main] WARN  o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor:? -  [DUBBO] No class annotated by Dubbo @Service was found under package [com.ideal.envc], ignore re-scanned classes: 0, dubbo version: 3.2.5, current host: **********, error code: 5-28. This may be caused by No annotations were found on the class, go to https://dubbo.apache.org/faq/5/28 to find instructions. 
2025-07-25 09:57:01.176 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner:44 - Skipping MapperFactoryBean with name 'actOutputMapper' and 'com.ideal.monitor.mapper.ActOutputMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 09:57:01.178 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner:44 - Skipping MapperFactoryBean with name 'callFlowMapper' and 'com.ideal.monitor.mapper.CallFlowMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 09:57:01.178 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner:44 - Skipping MapperFactoryBean with name 'flowActiveNodeMapper' and 'com.ideal.monitor.mapper.FlowActiveNodeMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 09:57:01.178 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner:44 - Skipping MapperFactoryBean with name 'flowMapper' and 'com.ideal.monitor.mapper.FlowMapper' mapperInterface. Bean already defined with the same name!
2025-07-25 09:57:01.180 [main] WARN  org.mybatis.spring.mapper.ClassPathMapperScanner:44 - No MyBatis mapper was found in '[com.ideal.monitor.mapper]' package. Please check your configuration.
2025-07-25 09:57:01.213 [main] INFO  o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor:292 - No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-25 09:57:01.282 [main] INFO  o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor:292 - No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-25 09:57:02.387 [main] INFO  o.springframework.cloud.context.scope.GenericScope:283 - BeanFactory id=447ae151-e8a0-3a11-8d1e-8011925bb33e
2025-07-25 09:57:02.799 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor:44 - Post-processing PropertySource instances
2025-07-25 09:57:02.810 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource bootstrapProperties-contrast-sit.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-25 09:57:02.811 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource bootstrapProperties-contrast.yml,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-25 09:57:02.811 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource bootstrapProperties-contrast,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-25 09:57:02.811 [main] INFO  c.u.j.EncryptablePropertySourceConverter:105 - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-25 09:57:02.812 [main] INFO  c.u.j.EncryptablePropertySourceConverter:105 - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-25 09:57:02.812 [main] INFO  c.u.j.EncryptablePropertySourceConverter:105 - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-25 09:57:02.813 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 09:57:02.814 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-25 09:57:02.815 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-25 09:57:02.815 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-25 09:57:02.815 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 09:57:02.816 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource spring.integration.poller [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 09:57:02.816 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 09:57:02.816 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-25 09:57:08.666 [main] INFO  o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor:245 -  [DUBBO] class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:08.748 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.alibaba.cloud.stream.binder.rocketmq.config.RocketMQComponent4BinderAutoConfiguration' of type [com.alibaba.cloud.stream.binder.rocketmq.config.RocketMQComponent4BinderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.758 [main] INFO  c.u.j.filter.DefaultLazyPropertyFilter:45 - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-25 09:57:08.782 [main] INFO  c.u.j.resolver.DefaultLazyPropertyResolver:46 - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-25 09:57:08.790 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jasyptConfig' of type [com.ideal.common.config.JasyptConfig$$EnhancerBySpringCGLIB$$dcb34ce3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.795 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'encryptablePropertyDetector' of type [com.ideal.common.util.jasypt.Sm4EncryptablePropertyDetector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.797 [main] INFO  c.u.j.detector.DefaultLazyPropertyDetector:39 - Found Custom Detector Bean com.ideal.common.util.jasypt.Sm4EncryptablePropertyDetector@20e9c165 with name: encryptablePropertyDetector
2025-07-25 09:57:08.972 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'defaultMQProducer' of type [org.apache.rocketmq.client.producer.DefaultMQProducer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.978 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.979 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.981 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.985 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.stream.config.BindersHealthIndicatorAutoConfiguration' of type [org.springframework.cloud.stream.config.BindersHealthIndicatorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.995 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'bindersHealthContributor' of type [org.springframework.cloud.stream.config.BindersHealthIndicatorAutoConfiguration$BindersHealthContributor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:08.999 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'bindersHealthIndicatorListener' of type [org.springframework.cloud.stream.config.BindersHealthIndicatorAutoConfiguration$BindersHealthIndicatorListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.006 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'BindingHandlerAdvise' of type [org.springframework.cloud.stream.config.BindingHandlerAdvise] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.007 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.009 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.011 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$713/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.012 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.028 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spelConverter' of type [org.springframework.cloud.stream.config.SpelExpressionConverterConfiguration$SpelConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.038 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.040 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.046 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.048 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.049 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.061 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.078 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jsonMixinModule' of type [org.springframework.boot.jackson.JsonMixinModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.079 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.089 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.093 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.120 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.775 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'rocketMQTemplate' of type [org.apache.rocketmq.spring.core.RocketMQTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:09.779 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'transactionHandlerRegistry' of type [org.apache.rocketmq.spring.config.TransactionHandlerRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:10.857 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration$IntegrationJmxConfiguration' of type [org.springframework.boot.autoconfigure.integration.IntegrationAutoConfiguration$IntegrationJmxConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:10.872 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties' of type [org.springframework.boot.autoconfigure.jmx.JmxProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:10.892 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration' of type [org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:10.910 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'mbeanServer' of type [com.sun.jmx.mbeanserver.JmxMBeanServer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 09:57:12.099 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8210 (http)
2025-07-25 09:57:12.184 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Initializing ProtocolHandler ["http-nio-8210"]
2025-07-25 09:57:12.206 [main] INFO  org.apache.catalina.core.StandardService:168 - Starting service [Tomcat]
2025-07-25 09:57:12.207 [main] INFO  org.apache.catalina.core.StandardEngine:168 - Starting Servlet engine: [Apache Tomcat/9.0.104]
2025-07-25 09:57:12.317 [main] WARN  org.apache.catalina.webresources.DirResourceSet:168 - Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8210.3480698828741895215] which is part of the web application []
2025-07-25 09:57:13.125 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]:168 - Initializing Spring embedded WebApplicationContext
2025-07-25 09:57:13.126 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 22257 ms
2025-07-25 09:57:13.676 [main] INFO  c.u.j.encryptor.DefaultLazyEncryptor:41 - Found Custom Encryptor Bean com.ideal.common.util.jasypt.Sm4Encryptor@40e7aea9 with name: jasyptStringEncryptor
2025-07-25 09:57:17.490 [main] INFO  o.a.d.c.spring.context.DubboConfigBeanInitializer:102 -  [DUBBO] loading dubbo config beans ..., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:17.513 [main] INFO  o.a.d.c.spring.context.DubboConfigBeanInitializer:128 -  [DUBBO] dubbo config beans are loaded., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:17.888 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:544 -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : nacos] supports as the config center, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:17.895 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:550 -  [DUBBO] The registry[<dubbo:registry address="nacos://************:8848?namespace=123456" protocol="nacos" port="8848" parameters="{namespace=123456}" />] will be used as the config center, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:17.932 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:362 -  [DUBBO] use registry as config-center: <dubbo:config-center highestPriority="false" id="config-center-nacos-************-8848" address="nacos://************:8848?namespace=123456" protocol="nacos" port="8848" parameters="{namespace=123456, client=null}" />, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.137 [main] INFO  com.alibaba.nacos.client.config.NacosConfigService:78 - Nacos client key init properties: 
	serverAddr=************:8848
	namespace=123456

2025-07-25 09:57:18.144 [main] INFO  com.alibaba.nacos.common.labels:48 - DefaultLabelsCollectorManager get labels.....
2025-07-25 09:57:18.145 [main] INFO  com.alibaba.nacos.common.labels:62 - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-25 09:57:18.145 [main] INFO  com.alibaba.nacos.common.labels:64 - default nacos collect properties raw labels: null
2025-07-25 09:57:18.146 [main] INFO  com.alibaba.nacos.common.labels:71 - default nacos collect properties labels: {}
2025-07-25 09:57:18.146 [main] INFO  com.alibaba.nacos.common.labels:74 - default nacos collect jvm raw labels: null
2025-07-25 09:57:18.146 [main] INFO  com.alibaba.nacos.common.labels:80 - default nacos collect jvm labels: {}
2025-07-25 09:57:18.146 [main] INFO  com.alibaba.nacos.common.labels:83 - default nacos collect env raw labels: null
2025-07-25 09:57:18.147 [main] INFO  com.alibaba.nacos.common.labels:91 - default nacos collect env labels: {}
2025-07-25 09:57:18.147 [main] INFO  com.alibaba.nacos.common.labels:50 - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-25 09:57:18.149 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 09:57:18.149 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 09:57:18.151 [main] INFO  com.alibaba.nacos.common.remote.client:118 - [RpcClientFactory] create a new rpc client of 9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0
2025-07-25 09:57:18.152 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$390/386040589
2025-07-25 09:57:18.152 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$391/1800592689
2025-07-25 09:57:18.152 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-25 09:57:18.152 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-25 09:57:18.153 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Try to connect to server on start up, server: {serverIp = '************', server main port = 8848}
2025-07-25 09:57:18.153 [main] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:57:18.198 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Success to connect to server [************:8848] on start up, connectionId = 1753408642007_**********_62003
2025-07-25 09:57:18.199 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-25 09:57:18.200 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Notify connected event to listeners.
2025-07-25 09:57:18.201 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$403/486994287
2025-07-25 09:57:18.202 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Connected,notify listen context...
2025-07-25 09:57:18.247 [main] INFO  org.apache.dubbo.common.config.ConfigurationUtils:205 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.247 [main] INFO  org.apache.dubbo.common.config.ConfigurationUtils:205 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.401 [main] INFO  org.apache.dubbo.config.context.ConfigManager:314 -  [DUBBO] The current configurations or effective configurations are as follows:, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.407 [main] INFO  org.apache.dubbo.config.context.ConfigManager:316 -  [DUBBO] <dubbo:application enableFileCache="true" qosPort="21518" executorManagementMode="isolation" parameters="{}" name="contrast-dubbo" protocol="dubbo" />, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.409 [main] INFO  org.apache.dubbo.config.context.ConfigManager:316 -  [DUBBO] <dubbo:protocol preferSerialization="fastjson2,hessian2" port="21510" name="dubbo" />, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.410 [main] INFO  org.apache.dubbo.config.context.ConfigManager:316 -  [DUBBO] <dubbo:registry address="nacos://************:8848?namespace=123456" protocol="nacos" port="8848" parameters="{namespace=123456}" />, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.410 [main] INFO  org.apache.dubbo.config.context.ConfigManager:316 -  [DUBBO] <dubbo:ssl />, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.542 [main] INFO  o.apache.dubbo.config.deploy.DefaultModuleDeployer:138 -  [DUBBO] Dubbo Module[1.1.0] has been initialized!, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:18.549 [main] INFO  o.apache.dubbo.config.deploy.DefaultModuleDeployer:138 -  [DUBBO] Dubbo Module[1.1.1] has been initialized!, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:19.671 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:544 -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : nacos] supports as the metadata center, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:19.672 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:550 -  [DUBBO] The registry[<dubbo:registry address="nacos://************:8848?namespace=123456" protocol="nacos" port="8848" parameters="{namespace=123456}" />] will be used as the metadata center, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:19.701 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:514 -  [DUBBO] use registry as metadata-center: <dubbo:metadata-report address="nacos://************:8848?namespace=123456" protocol="nacos" port="8848" parameters="{namespace=123456, client=null}" />, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:19.812 [main] INFO  com.alibaba.nacos.client.config.NacosConfigService:78 - Nacos client key init properties: 
	serverAddr=************:8848
	namespace=123456

2025-07-25 09:57:19.818 [main] INFO  com.alibaba.nacos.common.labels:48 - DefaultLabelsCollectorManager get labels.....
2025-07-25 09:57:19.818 [main] INFO  com.alibaba.nacos.common.labels:62 - Process LabelsCollector with [name:defaultNacosLabelsCollector]
2025-07-25 09:57:19.819 [main] INFO  com.alibaba.nacos.common.labels:64 - default nacos collect properties raw labels: null
2025-07-25 09:57:19.819 [main] INFO  com.alibaba.nacos.common.labels:71 - default nacos collect properties labels: {}
2025-07-25 09:57:19.819 [main] INFO  com.alibaba.nacos.common.labels:74 - default nacos collect jvm raw labels: null
2025-07-25 09:57:19.819 [main] INFO  com.alibaba.nacos.common.labels:80 - default nacos collect jvm labels: {}
2025-07-25 09:57:19.820 [main] INFO  com.alibaba.nacos.common.labels:83 - default nacos collect env raw labels: null
2025-07-25 09:57:19.820 [main] INFO  com.alibaba.nacos.common.labels:91 - default nacos collect env labels: {}
2025-07-25 09:57:19.823 [main] INFO  com.alibaba.nacos.common.labels:50 - DefaultLabelsCollectorManager get labels finished,labels :{}
2025-07-25 09:57:19.824 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 09:57:19.828 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 09:57:19.838 [main] INFO  com.alibaba.nacos.common.remote.client:118 - [RpcClientFactory] create a new rpc client of 30feca63-c1af-472d-8f0d-b7df8745bf85_config-0
2025-07-25 09:57:19.838 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$390/386040589
2025-07-25 09:57:19.839 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$391/1800592689
2025-07-25 09:57:19.841 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-25 09:57:19.841 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-25 09:57:19.842 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Try to connect to server on start up, server: {serverIp = '************', server main port = 8848}
2025-07-25 09:57:19.842 [main] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:57:19.934 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Success to connect to server [************:8848] on start up, connectionId = 1753408643745_**********_62008
2025-07-25 09:57:19.936 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Notify connected event to listeners.
2025-07-25 09:57:19.937 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Connected,notify listen context...
2025-07-25 09:57:19.939 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-25 09:57:19.939 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$403/486994287
2025-07-25 09:57:19.956 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:234 -  [DUBBO] Dubbo Application[1.1](contrast-dubbo) has been initialized!, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:21.808 [main] INFO  com.alibaba.druid.pool.DruidDataSource:1010 - {dataSource-1} inited
2025-07-25 09:57:25.008 [main] INFO  liquibase.changelog:37 - Reading from contrast.databasechangelog
2025-07-25 09:57:25.278 [main] INFO  liquibase.ui:37 - Database is up to date, no changesets to execute
2025-07-25 09:57:25.285 [main] INFO  liquibase.changelog:37 - Reading from contrast.databasechangelog
2025-07-25 09:57:25.368 [main] INFO  liquibase.util:37 - UPDATE SUMMARY
2025-07-25 09:57:25.369 [main] INFO  liquibase.util:37 - Run:                          0
2025-07-25 09:57:25.369 [main] INFO  liquibase.util:37 - Previously run:              35
2025-07-25 09:57:25.369 [main] INFO  liquibase.util:37 - Filtered out:                 0
2025-07-25 09:57:25.369 [main] INFO  liquibase.util:37 - -------------------------------
2025-07-25 09:57:25.369 [main] INFO  liquibase.util:37 - Total change sets:           35
2025-07-25 09:57:25.376 [main] INFO  liquibase.util:37 - Update summary generated
2025-07-25 09:57:25.518 [main] INFO  liquibase.lockservice:37 - Successfully released change log lock
2025-07-25 09:57:25.533 [main] INFO  liquibase.command:37 - Command execution complete
2025-07-25 09:57:25.993 [main] INFO  org.redisson.Version:41 - Redisson 3.17.7
2025-07-25 09:57:26.490 [main] INFO  org.redisson.cluster.ClusterConnectionManager:106 - Redis cluster nodes configuration got from ************/************:27001:
728b2be681bc7094f0fcb03aea9e72ba968e1b99 ************:27002@37002 master - 0 1753408648000 7 connected 0-5460
2e5bbc9c916e2712c456747b9add41679722cfb8 ************:27001@37001 master - 0 1753408648384 3 connected 5461-10922
493ee93c767e8618983d0bf65a9ecc74ecf64442 ************:27001@37001 myself,slave 728b2be681bc7094f0fcb03aea9e72ba968e1b99 0 1753408646000 7 connected
c1870020b8d2bea7c5a56db523e14e2cb5bd4681 ************:27002@37002 master - 0 1753408649389 10 connected 10923-16383
f9a6897deb39bc120be23b56590e998b28c75734 192.168.2.97:27001@37001 master,fail - 1750635357141 1750635351116 9 connected
ff39d7dfc8c03ad1836e05e315d1bc3840776ad7 192.168.2.97:27002@37002 slave,fail 2e5bbc9c916e2712c456747b9add41679722cfb8 1750635355133 1750635352000 3 connected

2025-07-25 09:57:26.646 [redisson-netty-2-21] INFO  o.r.connection.pool.MasterPubSubConnectionPool:161 - 1 connections initialized for ************/************:27002
2025-07-25 09:57:26.648 [redisson-netty-2-19] INFO  o.r.connection.pool.MasterPubSubConnectionPool:161 - 1 connections initialized for ************/************:27001
2025-07-25 09:57:26.648 [redisson-netty-2-20] INFO  o.r.connection.pool.MasterPubSubConnectionPool:161 - 1 connections initialized for ************/************:27002
2025-07-25 09:57:26.730 [redisson-netty-2-12] INFO  org.redisson.connection.pool.PubSubConnectionPool:161 - 1 connections initialized for ************/************:27001
2025-07-25 09:57:26.732 [redisson-netty-2-26] INFO  org.redisson.connection.pool.MasterConnectionPool:161 - 24 connections initialized for ************/************:27002
2025-07-25 09:57:26.736 [redisson-netty-2-1] INFO  org.redisson.connection.pool.PubSubConnectionPool:161 - 1 connections initialized for ************/************:27002
2025-07-25 09:57:26.738 [redisson-netty-2-5] INFO  org.redisson.cluster.ClusterConnectionManager:322 - slaves: [redis://192.168.2.97:27002] added for slot ranges: [[5461-10922]]
2025-07-25 09:57:26.739 [redisson-netty-2-5] WARN  org.redisson.cluster.ClusterConnectionManager:324 - slaves: [redis://192.168.2.97:27002] are down for slot ranges: [[5461-10922]]
2025-07-25 09:57:26.742 [redisson-netty-2-2] INFO  org.redisson.cluster.ClusterConnectionManager:329 - master: redis://************:27002 added for slot ranges: [[10923-16383]]
2025-07-25 09:57:26.742 [redisson-netty-2-5] INFO  org.redisson.cluster.ClusterConnectionManager:329 - master: redis://************:27001 added for slot ranges: [[5461-10922]]
2025-07-25 09:57:26.742 [redisson-netty-2-5] INFO  org.redisson.connection.pool.MasterConnectionPool:161 - 24 connections initialized for ************/************:27001
2025-07-25 09:57:26.742 [redisson-netty-2-2] INFO  org.redisson.connection.pool.MasterConnectionPool:161 - 24 connections initialized for ************/************:27002
2025-07-25 09:57:26.745 [redisson-netty-2-4] INFO  org.redisson.connection.pool.PubSubConnectionPool:161 - 1 connections initialized for ************/************:27001
2025-07-25 09:57:26.773 [redisson-netty-2-17] INFO  org.redisson.connection.pool.SlaveConnectionPool:161 - 24 connections initialized for ************/************:27001
2025-07-25 09:57:26.775 [redisson-netty-2-21] INFO  org.redisson.cluster.ClusterConnectionManager:322 - slaves: [redis://************:27001] added for slot ranges: [[0-5460]]
2025-07-25 09:57:26.775 [redisson-netty-2-1] INFO  org.redisson.connection.pool.SlaveConnectionPool:161 - 24 connections initialized for ************/************:27002
2025-07-25 09:57:26.775 [redisson-netty-2-21] INFO  org.redisson.cluster.ClusterConnectionManager:329 - master: redis://************:27002 added for slot ranges: [[0-5460]]
2025-07-25 09:57:26.775 [redisson-netty-2-21] INFO  org.redisson.connection.pool.SlaveConnectionPool:161 - 24 connections initialized for ************/************:27001
2025-07-25 09:57:27.130 [main] INFO  o.a.dubbo.config.spring.reference.ReferenceCreator:96 -  [DUBBO] The configBean[type:ReferenceConfig<com.ideal.system.api.IBusinessSystem>] has been built., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:27.350 [main] INFO  o.a.dubbo.config.spring.reference.ReferenceCreator:96 -  [DUBBO] The configBean[type:ReferenceConfig<com.ideal.system.api.IBusinessSystemCompuerList>] has been built., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:27.378 [main] INFO  o.a.dubbo.config.spring.reference.ReferenceCreator:96 -  [DUBBO] The configBean[type:ReferenceConfig<com.ideal.system.api.ICenter>] has been built., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:27.632 [main] INFO  o.a.dubbo.config.spring.reference.ReferenceCreator:96 -  [DUBBO] The configBean[type:ReferenceConfig<com.ideal.engine.api.IStartFlow>] has been built., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:28.845 [main] INFO  o.a.dubbo.config.spring.reference.ReferenceCreator:96 -  [DUBBO] The configBean[type:ReferenceConfig<com.ideal.system.api.IRoleProjectPermission>] has been built., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:29.019 [main] INFO  c.i.s.service.impl.RedisSnowflakeRecordServiceImpl:125 - >>> workerId: 13, datacenterId: 26
2025-07-25 09:57:29.460 [main] INFO  o.a.dubbo.config.spring.reference.ReferenceCreator:96 -  [DUBBO] The configBean[type:ReferenceConfig<com.ideal.engine.api.IActivity>] has been built., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:31.370 [main] INFO  com.alibaba.nacos.client.naming:102 - Nacos client key init properties: 
	serverAddr=************:8848
	namespace=123456

2025-07-25 09:57:31.378 [main] INFO  com.alibaba.nacos.client.naming:62 - initializer namespace from ans.namespace attribute : null
2025-07-25 09:57:31.382 [main] INFO  com.alibaba.nacos.client.naming:66 - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-25 09:57:31.386 [main] INFO  com.alibaba.nacos.client.naming:73 - initializer namespace from namespace attribute :null
2025-07-25 09:57:31.445 [main] INFO  com.alibaba.nacos.client.naming:75 - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-25 09:57:31.481 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 09:57:31.482 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 09:57:31.503 [main] INFO  com.alibaba.nacos.common.remote.client:118 - [RpcClientFactory] create a new rpc client of 8e56f62e-778d-4e19-a133-2b1b52bd1515
2025-07-25 09:57:31.519 [main] INFO  com.alibaba.nacos.client.naming:109 - Create naming rpc client for uuid->8e56f62e-778d-4e19-a133-2b1b52bd1515
2025-07-25 09:57:31.521 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-25 09:57:31.522 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-25 09:57:31.526 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-25 09:57:31.527 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Try to connect to server on start up, server: {serverIp = '************', server main port = 8848}
2025-07-25 09:57:31.527 [main] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:57:31.555 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Success to connect to server [************:8848] on start up, connectionId = 1753408655381_**********_62257
2025-07-25 09:57:31.556 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-25 09:57:31.556 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Notify connected event to listeners.
2025-07-25 09:57:31.556 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$403/486994287
2025-07-25 09:57:31.556 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 09:57:32.934 [main] INFO  o.s.c.stream.messaging.DirectWithAttributesChannel:174 - Channel 'contrast-1.contrastReceiveTaskSendResult-in-0' has 1 subscriber(s).
2025-07-25 09:57:34.249 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 23 endpoint(s) beneath base path '/actuator'
2025-07-25 09:57:34.545 [main] INFO  com.xxl.job.core.executor.XxlJobExecutor:187 - >>>>>>>>>>> xxl-job register jobhandler success, name:contrastJobHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4da6fd47[class com.ideal.envc.xxjob.ScheduleJobHandler$$EnhancerBySpringCGLIB$$1bbc7038#contrastJobHandler]
2025-07-25 09:57:34.885 [Thread-126] INFO  com.xxl.job.core.server.EmbedServer:82 - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 8211
2025-07-25 09:57:35.153 [main] INFO  o.s.integration.monitor.IntegrationMBeanExporter:681 - Registering MessageChannel contrastReceiveTaskSendResult-in-0
2025-07-25 09:57:35.343 [main] INFO  o.s.integration.monitor.IntegrationMBeanExporter:681 - Registering MessageChannel errorChannel
2025-07-25 09:57:35.384 [main] INFO  o.s.integration.monitor.IntegrationMBeanExporter:681 - Registering MessageChannel nullChannel
2025-07-25 09:57:35.412 [main] INFO  o.s.integration.monitor.IntegrationMBeanExporter:703 - Registering MessageHandler _org.springframework.integration.errorLogger
2025-07-25 09:57:35.590 [main] INFO  o.s.integration.endpoint.EventDrivenConsumer:174 - Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-25 09:57:35.591 [main] INFO  o.s.integration.channel.PublishSubscribeChannel:174 - Channel 'contrast-1.errorChannel' has 1 subscriber(s).
2025-07-25 09:57:35.593 [main] INFO  o.s.integration.endpoint.EventDrivenConsumer:292 - started bean '_org.springframework.integration.errorLogger'
2025-07-25 09:57:35.651 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:contrast, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:35.653 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:contrast, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:35.702 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(0) service: DEFAULT_GROUP@@contrast -> []
2025-07-25 09:57:35.808 [main] INFO  o.s.cloud.stream.binder.DefaultBinderFactory:251 - Creating binder: rocketmq
2025-07-25 09:57:36.006 [main] INFO  o.s.cloud.stream.binder.DefaultBinderFactory:294 - Caching the binder: rocketmq
2025-07-25 09:57:36.007 [main] INFO  o.s.cloud.stream.binder.DefaultBinderFactory:298 - Retrieving cached binder: rocketmq
2025-07-25 09:57:36.205 [nacos-grpc-client-executor-************-10] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Receive server push request, request = NotifySubscriberRequest, requestId = 343225
2025-07-25 09:57:36.207 [nacos-grpc-client-executor-************-10] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Ack server push request, request = NotifySubscriberRequest, requestId = 343225
2025-07-25 09:57:36.703 [main] INFO  o.s.cloud.stream.binder.BinderErrorChannel:174 - Channel 'contrastReceiveTaskSendResult.contrastReceiveTaskSendResultGroup.errors' has 1 subscriber(s).
2025-07-25 09:57:36.707 [main] INFO  o.s.cloud.stream.binder.BinderErrorChannel:174 - Channel 'contrastReceiveTaskSendResult.contrastReceiveTaskSendResultGroup.errors' has 0 subscriber(s).
2025-07-25 09:57:36.709 [main] INFO  o.s.cloud.stream.binder.BinderErrorChannel:174 - Channel 'contrastReceiveTaskSendResult.contrastReceiveTaskSendResultGroup.errors' has 1 subscriber(s).
2025-07-25 09:57:36.709 [main] INFO  o.s.cloud.stream.binder.BinderErrorChannel:174 - Channel 'contrastReceiveTaskSendResult.contrastReceiveTaskSendResultGroup.errors' has 2 subscriber(s).
2025-07-25 09:57:36.998 [main] INFO  c.a.c.s.b.r.c.RocketMQListenerBindingContainer:186 - running container: RocketMQListenerBindingContainer{consumerGroup='contrastReceiveTaskSendResultGroup', nameServer='[192.168.1.152:9876]', topic='contrastReceiveTaskSendResult', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='null', messageModel=CLUSTERING}
2025-07-25 09:57:36.998 [main] INFO  c.a.c.s.b.r.i.RocketMQInboundChannelAdapter:292 - started com.alibaba.cloud.stream.binder.rocketmq.integration.RocketMQInboundChannelAdapter@16704703
2025-07-25 09:57:37.008 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:168 - Starting ProtocolHandler ["http-nio-8210"]
2025-07-25 09:57:37.055 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8210 (http) with context path ''
2025-07-25 09:57:37.059 [main] INFO  c.u.j.caching.RefreshScopeRefreshedEventListener:70 - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-25 09:57:37.061 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source bootstrapProperties-contrast-sit.yml,DEFAULT_GROUP refreshed
2025-07-25 09:57:37.061 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source bootstrapProperties-contrast.yml,DEFAULT_GROUP refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source bootstrapProperties-contrast,DEFAULT_GROUP refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source systemProperties refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source systemEnvironment refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source random refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source cachedrandom refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source springCloudClientHostInfo refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source spring.integration.poller refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source Config resource 'class path resource [bootstrap.yml]' via location 'optional:classpath:/' refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource:92 - Property Source springCloudDefaultProperties refreshed
2025-07-25 09:57:37.062 [main] INFO  c.u.j.EncryptablePropertySourceConverter:105 - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-25 09:57:37.062 [main] INFO  c.u.j.EncryptablePropertySourceConverter:105 - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-25 09:57:37.062 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-25 09:57:37.062 [main] INFO  c.u.j.EncryptablePropertySourceConverter:110 - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-25 09:57:37.168 [main] INFO  com.alibaba.nacos.client.naming:133 - [REGISTER-SERVICE] 123456 registering service contrast with instance Instance{instanceId='null', ip='**********', port=8210, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-25 09:57:37.186 [main] INFO  c.a.cloud.nacos.registry.NacosServiceRegistry:75 - nacos registry, DEFAULT_GROUP contrast **********:8210 register finished
2025-07-25 09:57:37.309 [main] INFO  o.apache.dubbo.config.deploy.DefaultModuleDeployer:324 -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.313 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:1193 -  [DUBBO] Dubbo Application[1.1](contrast-dubbo) is starting., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.314 [main] INFO  o.apache.dubbo.config.deploy.DefaultModuleDeployer:324 -  [DUBBO] Dubbo Module[1.1.0] is starting., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.316 [main] INFO  o.apache.dubbo.config.deploy.DefaultModuleDeployer:331 -  [DUBBO] Dubbo Module[1.1.0] has started., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.493 [main] INFO  org.apache.dubbo.qos.server.Server:125 -  [DUBBO] qos-server bind localhost:21518, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.623 [main] INFO  com.alibaba.nacos.client.naming:102 - Nacos client key init properties: 
	serverAddr=************:8848
	namespace=123456

2025-07-25 09:57:37.625 [main] INFO  com.alibaba.nacos.client.naming:62 - initializer namespace from ans.namespace attribute : null
2025-07-25 09:57:37.626 [main] INFO  com.alibaba.nacos.client.naming:66 - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-25 09:57:37.626 [main] INFO  com.alibaba.nacos.client.naming:73 - initializer namespace from namespace attribute :null
2025-07-25 09:57:37.627 [main] INFO  com.alibaba.nacos.client.naming:75 - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-25 09:57:37.629 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 09:57:37.630 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 09:57:37.632 [main] INFO  com.alibaba.nacos.common.remote.client:118 - [RpcClientFactory] create a new rpc client of 723c9d0c-b4d5-4785-8f50-ca8b52674713
2025-07-25 09:57:37.634 [main] INFO  com.alibaba.nacos.client.naming:109 - Create naming rpc client for uuid->723c9d0c-b4d5-4785-8f50-ca8b52674713
2025-07-25 09:57:37.635 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-25 09:57:37.635 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-25 09:57:37.635 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-25 09:57:37.635 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Try to connect to server on start up, server: {serverIp = '************', server main port = 8848}
2025-07-25 09:57:37.636 [main] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:57:37.668 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Success to connect to server [************:8848] on start up, connectionId = 1753408661487_**********_62470
2025-07-25 09:57:37.669 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-25 09:57:37.669 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Notify connected event to listeners.
2025-07-25 09:57:37.669 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$403/486994287
2025-07-25 09:57:37.670 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 09:57:37.698 [main] INFO  o.a.dubbo.registry.nacos.NacosNamingServiceWrapper:67 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.713 [nacos-grpc-client-executor-************-13] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:37.731 [nacos-grpc-client-executor-************-13] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@contrast -> [{"instanceId":"**********#8210#DEFAULT#DEFAULT_GROUP@@contrast","ip":"**********","port":8210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@contrast","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:37.735 [nacos-grpc-client-executor-************-13] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@contrast -> [{"instanceId":"**********#8210#DEFAULT#DEFAULT_GROUP@@contrast","ip":"**********","port":8210,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@contrast","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:37.737 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:315 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-contrast-dubbo-************-8848.cache, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.759 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: contrast to Listener: com.alibaba.cloud.nacos.discovery.NacosWatch$1@683721ce
2025-07-25 09:57:37.802 [nacos-grpc-client-executor-************-13] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:37.866 [main] INFO  o.a.d.r.client.migration.MigrationRuleListener:103 -  [DUBBO] Listening for migration rules on dataId contrast-dubbo.migration, group DUBBO_SERVICEDISCOVERY_MIGRATION, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:37.892 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:72 - config listener notify warn timeout millis use default 60000 millis 
2025-07-25 09:57:37.893 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:99 - nacos.cache.data.init.snapshot = true 
2025-07-25 09:57:37.895 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] contrast-dubbo.migration+DUBBO_SERVICEDISCOVERY_MIGRATION+***********-07-25 09:57:37.904 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=contrast-dubbo.migration, group=DUBBO_SERVICEDISCOVERY_MIGRATION, cnt=1
2025-07-25 09:57:38.095 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] contrast-dubbo.configurators+dubbo+***********-07-25 09:57:38.096 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=contrast-dubbo.configurators, group=dubbo, cnt=1
2025-07-25 09:57:38.106 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:189 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:38.212 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.ICenter:1.0.0:system.condition-router+dubbo+***********-07-25 09:57:38.212 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.ICenter:1.0.0:system.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:38.229 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] contrast-dubbo.condition-router+dubbo+***********-07-25 09:57:38.230 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=contrast-dubbo.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:38.585 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:415 -  [DUBBO] Subscribe: consumer://**********/com.ideal.system.api.ICenter?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.ICenter&methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408657372&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:38.610 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.ICenter:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:38.611 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:providers:com.ideal.system.api.ICenter:1.0.0:system, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:38.618 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406227741"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:38.619 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getCenterListForApi,getCenterListForUserId,getCenterPageListForApi","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.ICenter","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.ICenter","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406227741"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:38.665 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.ICenter?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.ICenter&methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408657372&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:39.219 [nacos-grpc-client-executor-************-14] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 343228
2025-07-25 09:57:39.221 [nacos-grpc-client-executor-************-14] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 343228
2025-07-25 09:57:39.383 [main] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /************:20910 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xb7ae27f8, L:/**********:62515 - R:/************:20910]], dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:39.385 [main] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:39.390 [NettyClientWorker-9-1] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:62515 -> /************:20910 is established., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:39.740 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-system.MESHAPPRULE+dubbo+***********-07-25 09:57:39.741 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-system.MESHAPPRULE, group=dubbo, cnt=1
2025-07-25 09:57:39.746 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-system.tag-router+dubbo+***********-07-25 09:57:39.747 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-system.tag-router, group=dubbo, cnt=1
2025-07-25 09:57:39.779 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-system.condition-router+dubbo+***********-07-25 09:57:39.780 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-system.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:39.793 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-system.script-router+dubbo+***********-07-25 09:57:39.794 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-system.script-router, group=dubbo, cnt=1
2025-07-25 09:57:39.828 [main] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:39.865 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.ICenter:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:39.878 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.ICenter:1.0.0:system.configurators+dubbo+***********-07-25 09:57:39.880 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.ICenter:1.0.0:system.configurators, group=dubbo, cnt=1
2025-07-25 09:57:40.085 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:315 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-contrast-dubbo-************-8848.cache, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:40.216 [main] INFO  o.a.d.r.client.metadata.store.MetaCacheManager:54 -  [DUBBO] Successfully loaded meta cache from file .metadata.contrast-dubbo.nacos.************:8848, entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:40.850 [main] INFO  com.alibaba.nacos.client.naming:102 - Nacos client key init properties: 
	serverAddr=************:8848
	namespace=123456

2025-07-25 09:57:40.851 [main] INFO  com.alibaba.nacos.client.naming:62 - initializer namespace from ans.namespace attribute : null
2025-07-25 09:57:40.851 [main] INFO  com.alibaba.nacos.client.naming:66 - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
2025-07-25 09:57:40.851 [main] INFO  com.alibaba.nacos.client.naming:73 - initializer namespace from namespace attribute :null
2025-07-25 09:57:40.851 [main] INFO  com.alibaba.nacos.client.naming:75 - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
2025-07-25 09:57:40.853 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 09:57:40.853 [main] INFO  c.a.n.p.auth.spi.client.ClientAuthPluginManager:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 09:57:40.854 [main] INFO  com.alibaba.nacos.common.remote.client:118 - [RpcClientFactory] create a new rpc client of f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33
2025-07-25 09:57:40.857 [main] INFO  com.alibaba.nacos.client.naming:109 - Create naming rpc client for uuid->f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33
2025-07-25 09:57:40.857 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-25 09:57:40.857 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
2025-07-25 09:57:40.858 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-25 09:57:40.858 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Try to connect to server on start up, server: {serverIp = '************', server main port = 8848}
2025-07-25 09:57:40.859 [main] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 09:57:40.896 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Success to connect to server [************:8848] on start up, connectionId = 1753408664708_**********_62519
2025-07-25 09:57:40.898 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Notify connected event to listeners.
2025-07-25 09:57:40.898 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-25 09:57:40.898 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 09:57:40.899 [main] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$403/486994287
2025-07-25 09:57:40.903 [main] INFO  o.a.dubbo.registry.nacos.NacosNamingServiceWrapper:67 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:40.953 [main] INFO  org.apache.dubbo.metadata.MappingCacheManager:54 -  [DUBBO] Successfully loaded mapping cache from file .mapping.contrast-dubbo, entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.047 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:310 -  [DUBBO] Trying to subscribe from apps dubbo-system for service key system/com.ideal.system.api.ICenter:1.0.0, , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.075 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:dubbo-system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:41.076 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:dubbo-system, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:41.083 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"************#20910#null#dubbo-system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"6c4858cd33ca2117952c4d7f3a1033fc","dubbo.metadata.storage-type":"local","timestamp":"1753406229138"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:41.084 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-system -> [{"instanceId":"************#20910#null#dubbo-system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-system","metadata":{"dubbo.metadata-service.url-params":"{\"serialization\":\"hessian2\",\"prefer.serialization\":\"hessian2\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20910\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20910,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"6c4858cd33ca2117952c4d7f3a1033fc","dubbo.metadata.storage-type":"local","timestamp":"1753406229138"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:41.096 [Dubbo-framework-mapping-refreshing-scheduler-thread-1] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.ICenter+mapping+***********-07-25 09:57:41.101 [Dubbo-framework-mapping-refreshing-scheduler-thread-1] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.ICenter, group=mapping, cnt=1
2025-07-25 09:57:41.254 [Dubbo-framework-mapping-refreshing-scheduler-thread-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener:379 -  [DUBBO] Received mapping notification from meta server, {serviceKey: com.ideal.system.api.ICenter, apps: [dubbo-system]}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.258 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.289 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: 6c4858cd33ca2117952c4d7f3a1033fc , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.352 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:235 -  [DUBBO] Notify serviceKey: system/com.ideal.system.api.ICenter:1.0.0:null, listener: Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.540 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.546 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.ICenter:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.561 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:dubbo-system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:41.615 [nacos-grpc-client-executor-************-13] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = 343229
2025-07-25 09:57:41.616 [nacos-grpc-client-executor-************-13] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = 343229
2025-07-25 09:57:41.637 [main] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.ICenter:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.638 [main] INFO  o.a.d.r.client.migration.MigrationRuleHandler:88 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: com.ideal.system.api.ICenter:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.641 [main] INFO  org.apache.dubbo.config.ReferenceConfig:464 -  [DUBBO] Referred dubbo service: [com.ideal.system.api.ICenter]. it's not GenericService reference, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.708 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@132e736d; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.ICenter, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408657372}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.817 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:189 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.818 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IRoleProjectPermission:1.0.0:system.condition-router+dubbo+***********-07-25 09:57:41.819 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IRoleProjectPermission:1.0.0:system.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:41.884 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:415 -  [DUBBO] Subscribe: consumer://**********/com.ideal.system.api.IRoleProjectPermission?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IRoleProjectPermission&methods=getRoleButtonAuthority&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408661802&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.885 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:41.885 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:41.891 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406223460"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:41.892 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getRoleButtonAuthority","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IRoleProjectPermission","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IRoleProjectPermission","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406223460"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:41.915 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IRoleProjectPermission?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IRoleProjectPermission&methods=getRoleButtonAuthority&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408661802&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.938 [main] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:41.940 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:41.943 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IRoleProjectPermission:1.0.0:system.configurators+dubbo+***********-07-25 09:57:41.943 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IRoleProjectPermission:1.0.0:system.configurators, group=dubbo, cnt=1
2025-07-25 09:57:42.159 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:310 -  [DUBBO] Trying to subscribe from apps dubbo-system,dubbo-system-lei for service key system/com.ideal.system.api.IRoleProjectPermission:1.0.0, , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.168 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-system, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.170 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: 6c4858cd33ca2117952c4d7f3a1033fc , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.172 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:dubbo-system-lei, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.174 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:dubbo-system-lei, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:42.185 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(0) service: DEFAULT_GROUP@@dubbo-system-lei -> []
2025-07-25 09:57:42.189 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:235 -  [DUBBO] Notify serviceKey: system/com.ideal.system.api.IRoleProjectPermission:1.0.0:null, listener: Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.192 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.196 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IRoleProjectPermission:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.197 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:dubbo-system-lei, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.200 [main] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IRoleProjectPermission:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.201 [main] INFO  o.a.d.r.client.migration.MigrationRuleHandler:88 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: com.ideal.system.api.IRoleProjectPermission:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.202 [main] INFO  org.apache.dubbo.config.ReferenceConfig:464 -  [DUBBO] Referred dubbo service: [com.ideal.system.api.IRoleProjectPermission]. it's not GenericService reference, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.203 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@6fb6039a; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.IRoleProjectPermission, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getRoleButtonAuthority, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408661802}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.207 [Dubbo-framework-mapping-refreshing-scheduler-thread-2] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IRoleProjectPermission+mapping+***********-07-25 09:57:42.207 [Dubbo-framework-mapping-refreshing-scheduler-thread-2] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IRoleProjectPermission, group=mapping, cnt=1
2025-07-25 09:57:42.261 [Dubbo-framework-mapping-refreshing-scheduler-thread-2] INFO  o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener:379 -  [DUBBO] Received mapping notification from meta server, {serviceKey: com.ideal.system.api.IRoleProjectPermission, apps: [dubbo-system, dubbo-system-lei]}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.277 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:189 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.279 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.engine.api.IStartFlow:1.0.0:engine.condition-router+dubbo+***********-07-25 09:57:42.280 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.engine.api.IStartFlow:1.0.0:engine.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:42.312 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:415 -  [DUBBO] Subscribe: consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662241&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.313 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.engine.api.IStartFlow:1.0.0:engine, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.313 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:providers:com.ideal.engine.api.IStartFlow:1.0.0:engine, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:42.352 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753343330105"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:42.356 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753343330105"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:42.410 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662241&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.413 [nacos-grpc-client-executor-************-19] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 343230
2025-07-25 09:57:42.415 [nacos-grpc-client-executor-************-19] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 343230
2025-07-25 09:57:42.422 [NettyClientWorker-9-2] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:62524 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.422 [main] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xc0c88572, L:/**********:62524 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.424 [main] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.435 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-engine.MESHAPPRULE+dubbo+***********-07-25 09:57:42.436 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-engine.MESHAPPRULE, group=dubbo, cnt=1
2025-07-25 09:57:42.439 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-engine.tag-router+dubbo+***********-07-25 09:57:42.440 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-engine.tag-router, group=dubbo, cnt=1
2025-07-25 09:57:42.447 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-engine.condition-router+dubbo+***********-07-25 09:57:42.451 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-engine.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:42.460 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] dubbo-engine.script-router+dubbo+***********-07-25 09:57:42.462 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=dubbo-engine.script-router, group=dubbo, cnt=1
2025-07-25 09:57:42.475 [main] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.477 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.engine.api.IStartFlow:1.0.0:engine, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.481 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.engine.api.IStartFlow:1.0.0:engine.configurators+dubbo+***********-07-25 09:57:42.482 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.engine.api.IStartFlow:1.0.0:engine.configurators, group=dubbo, cnt=1
2025-07-25 09:57:42.702 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:310 -  [DUBBO] Trying to subscribe from apps dubbo-engine for service key engine/com.ideal.engine.api.IStartFlow:1.0.0, , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.703 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:dubbo-engine, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.703 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:dubbo-engine, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:42.709 [nacos-grpc-client-executor-************-16] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:42.711 [nacos-grpc-client-executor-************-16] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:42.749 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753343331504"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:42.750 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1753343331504"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:42.779 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.780 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.781 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:235 -  [DUBBO] Notify serviceKey: engine/com.ideal.engine.api.IStartFlow:1.0.0:null, listener: Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.783 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.784 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.785 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:dubbo-engine, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.786 [main] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.786 [main] INFO  o.a.d.r.client.migration.MigrationRuleHandler:88 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: com.ideal.engine.api.IStartFlow:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.791 [main] INFO  org.apache.dubbo.config.ReferenceConfig:464 -  [DUBBO] Referred dubbo service: [com.ideal.engine.api.IStartFlow]. it's not GenericService reference, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.792 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@560298a6; definition: {group=engine, release=3.2.5, side=consumer, interface=com.ideal.engine.api.IStartFlow, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=killFlow,pauseFlow,resumeFlow,startFlow, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753408662241}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.859 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:189 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.860 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.engine.api.IActivity:1.0.0:engine.condition-router+dubbo+***********-07-25 09:57:42.861 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.engine.api.IActivity:1.0.0:engine.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:42.880 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:415 -  [DUBBO] Subscribe: consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662798&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.882 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.engine.api.IActivity:1.0.0:engine, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.882 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:providers:com.ideal.engine.api.IActivity:1.0.0:engine, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:42.913 [nacos-grpc-client-executor-************-22] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:42.913 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753343331504"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:42.914 [nacos-grpc-client-executor-************-22] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:42.914 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1753343331504"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:42.955 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=31248&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1753408662798&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.984 [main] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:42.986 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.engine.api.IActivity:1.0.0:engine, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:42.989 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.engine.api.IActivity:1.0.0:engine.configurators+dubbo+***********-07-25 09:57:42.990 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.engine.api.IActivity:1.0.0:engine.configurators, group=dubbo, cnt=1
2025-07-25 09:57:43.203 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:310 -  [DUBBO] Trying to subscribe from apps dubbo-engine for service key engine/com.ideal.engine.api.IActivity:1.0.0, , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.205 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:235 -  [DUBBO] Notify serviceKey: engine/com.ideal.engine.api.IActivity:1.0.0:null, listener: Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.207 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.208 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.209 [main] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.209 [main] INFO  o.a.d.r.client.migration.MigrationRuleHandler:88 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: com.ideal.engine.api.IActivity:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.209 [main] INFO  org.apache.dubbo.config.ReferenceConfig:464 -  [DUBBO] Referred dubbo service: [com.ideal.engine.api.IActivity]. it's not GenericService reference, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.209 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@5e474cee; definition: {group=engine, release=3.2.5, side=consumer, interface=com.ideal.engine.api.IActivity, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1753408662798}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.279 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:189 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.282 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IBusinessSystem:1.0.0:system.condition-router+dubbo+***********-07-25 09:57:43.283 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IBusinessSystem:1.0.0:system.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:43.296 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:415 -  [DUBBO] Subscribe: consumer://**********/com.ideal.system.api.IBusinessSystem?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystem&methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408663213&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.296 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.IBusinessSystem:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:43.296 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:providers:com.ideal.system.api.IBusinessSystem:1.0.0:system, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:43.301 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406228431"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:43.308 [nacos-grpc-client-executor-************-19] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:43.313 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem,takeOverBusinessSystem","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystem","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystem","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"1753406228431"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:43.314 [nacos-grpc-client-executor-************-19] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-25 09:57:43.341 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystem?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystem&methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408663213&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.376 [main] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.377 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.IBusinessSystem:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:43.378 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IBusinessSystem:1.0.0:system.configurators+dubbo+***********-07-25 09:57:43.379 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IBusinessSystem:1.0.0:system.configurators, group=dubbo, cnt=1
2025-07-25 09:57:43.412 [nacos-grpc-client-executor-************-25] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 343234
2025-07-25 09:57:43.414 [nacos-grpc-client-executor-************-25] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 343234
2025-07-25 09:57:43.602 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:310 -  [DUBBO] Trying to subscribe from apps dubbo-system,dubbo-system-lei for service key system/com.ideal.system.api.IBusinessSystem:1.0.0, , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.603 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:235 -  [DUBBO] Notify serviceKey: system/com.ideal.system.api.IBusinessSystem:1.0.0:null, listener: Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.608 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.608 [Dubbo-framework-mapping-refreshing-scheduler-thread-3] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IBusinessSystem+mapping+***********-07-25 09:57:43.609 [Dubbo-framework-mapping-refreshing-scheduler-thread-3] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IBusinessSystem, group=mapping, cnt=1
2025-07-25 09:57:43.628 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystem:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.635 [main] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystem:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.635 [main] INFO  o.a.d.r.client.migration.MigrationRuleHandler:88 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: com.ideal.system.api.IBusinessSystem:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.636 [main] INFO  org.apache.dubbo.config.ReferenceConfig:464 -  [DUBBO] Referred dubbo service: [com.ideal.system.api.IBusinessSystem]. it's not GenericService reference, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.636 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@7c686e64; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.IBusinessSystem, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408663213}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.645 [Dubbo-framework-mapping-refreshing-scheduler-thread-3] INFO  o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener:379 -  [DUBBO] Received mapping notification from meta server, {serviceKey: com.ideal.system.api.IBusinessSystem, apps: [dubbo-system, dubbo-system-lei]}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.746 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:189 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.748 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system.condition-router+dubbo+***********-07-25 09:57:43.749 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system.condition-router, group=dubbo, cnt=1
2025-07-25 09:57:43.770 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:415 -  [DUBBO] Subscribe: consumer://**********/com.ideal.system.api.IBusinessSystemCompuerList?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystemCompuerList&methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408663672&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.771 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:43.772 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:43.779 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"***********49"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:43.779 [main] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system -> [{"instanceId":"************#20910#null#DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","ip":"************","port":20910,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system","metadata":{"side":"provider","release":"3.2.5","methods":"getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.system.api.IBusinessSystemCompuerList","service-name-mapping":"true","version":"1.0.0","generic":"false","revision":"1.0.0","serialization":"hessian2","path":"com.ideal.system.api.IBusinessSystemCompuerList","protocol":"dubbo","application":"dubbo-system","prefer.serialization":"hessian2","dynamic":"true","category":"providers","group":"system","timestamp":"***********49"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}]
2025-07-25 09:57:43.814 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.system.api.IBusinessSystemCompuerList?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=system&interface=com.ideal.system.api.IBusinessSystemCompuerList&methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll&pid=31248&qos.port=21518&release=3.2.5&retries=5&revision=1.6.2-20250417.011711-9&side=consumer&sticky=false&timeout=200000&timestamp=1753408663672&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.843 [nacos-grpc-client-executor-************-28] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 343235
2025-07-25 09:57:43.845 [main] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:43.845 [nacos-grpc-client-executor-************-28] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 343235
2025-07-25 09:57:43.847 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:43.850 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system.configurators+dubbo+***********-07-25 09:57:43.850 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system.configurators, group=dubbo, cnt=1
2025-07-25 09:57:44.063 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:310 -  [DUBBO] Trying to subscribe from apps dubbo-system,dubbo-system-lei for service key system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0, , dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.064 [main] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:235 -  [DUBBO] Notify serviceKey: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:null, listener: Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.070 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.073 [Dubbo-framework-mapping-refreshing-scheduler-thread-4] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] com.ideal.system.api.IBusinessSystemCompuerList+mapping+***********-07-25 09:57:44.074 [main] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ************:20910, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.074 [Dubbo-framework-mapping-refreshing-scheduler-thread-4] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=com.ideal.system.api.IBusinessSystemCompuerList, group=mapping, cnt=1
2025-07-25 09:57:44.077 [main] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:system/com.ideal.system.api.IBusinessSystemCompuerList:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.077 [main] INFO  o.a.d.r.client.migration.MigrationRuleHandler:88 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: com.ideal.system.api.IBusinessSystemCompuerList:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.078 [main] INFO  org.apache.dubbo.config.ReferenceConfig:464 -  [DUBBO] Referred dubbo service: [com.ideal.system.api.IBusinessSystemCompuerList]. it's not GenericService reference, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.078 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@231d18a0; definition: {group=system, release=3.2.5, side=consumer, interface=com.ideal.system.api.IBusinessSystemCompuerList, version=1.0.0, application=contrast-dubbo, dubbo=2.0.2, pid=31248, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1753408663672}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.078 [main] INFO  o.apache.dubbo.config.deploy.DefaultModuleDeployer:331 -  [DUBBO] Dubbo Module[1.1.1] has started., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.081 [main] INFO  o.a.d.config.deploy.DefaultMetricsServiceExporter:101 -  [DUBBO] The MetricsConfig not exist, will not export metrics service., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.086 [Dubbo-framework-mapping-refreshing-scheduler-thread-4] INFO  o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener:379 -  [DUBBO] Received mapping notification from meta server, {serviceKey: com.ideal.system.api.IBusinessSystemCompuerList, apps: [dubbo-system, dubbo-system-lei]}, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.134 [main] INFO  o.a.d.c.b.builders.InternalServiceConfigBuilder:206 -  [DUBBO] org.apache.dubbo.metadata.MetadataServiceService Port hasn't been set will use default protocol defined in protocols., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.149 [main] INFO  o.a.d.c.b.builders.InternalServiceConfigBuilder:254 -  [DUBBO] Using dubbo protocol to export org.apache.dubbo.metadata.MetadataService service on port 21510, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.312 [nacos-grpc-client-executor-************-29] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 343236
2025-07-25 09:57:44.315 [nacos-grpc-client-executor-************-29] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 343236
2025-07-25 09:57:44.641 [main] INFO  org.apache.dubbo.config.ServiceConfig:858 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to local registry url : injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=contrast-dubbo&background=false&bind.ip=**********&bind.port=21510&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=contrast-dubbo&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=31248&prefer.serialization=fastjson2,hessian2&qos.port=21518&register=false&release=3.2.5&revision=3.2.5&service.filter=-default&side=provider&timeout=200000&timestamp=1753408664303&validation=true&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.643 [main] INFO  org.apache.dubbo.config.ServiceConfig:805 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to url dubbo://**********:21510/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=contrast-dubbo&background=false&bind.ip=**********&bind.port=21510&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=contrast-dubbo&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=31248&prefer.serialization=fastjson2,hessian2&qos.port=21518&register=false&release=3.2.5&revision=3.2.5&service-name-mapping=true&service.filter=-default&side=provider&timeout=200000&timestamp=1753408664303&validation=true&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:44.696 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo.configurators+dubbo+***********-07-25 09:57:44.697 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo.configurators, group=dubbo, cnt=1
2025-07-25 09:57:44.945 [main] INFO  org.apache.dubbo.remoting.transport.AbstractServer:71 -  [DUBBO] Start NettyServer bind /0.0.0.0:21510, export /**********:21510, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.028 [main] INFO  o.a.dubbo.registry.client.ServiceDiscoveryRegistry:315 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-contrast-dubbo-************-8848.cache, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.052 [main] INFO  o.a.d.r.client.migration.MigrationRuleListener:103 -  [DUBBO] Listening for migration rules on dataId contrast-dubbo.migration, group DUBBO_SERVICEDISCOVERY_MIGRATION, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.076 [main] INFO  org.apache.dubbo.config.ServiceConfig:805 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to url dubbo://**********:21510/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=contrast-dubbo&background=false&bind.ip=**********&bind.port=21510&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=contrast-dubbo&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=31248&prefer.serialization=fastjson2,hessian2&qos.port=21518&register=false&release=3.2.5&revision=3.2.5&service-name-mapping=true&service.filter=-default&side=provider&timeout=200000&timestamp=1753408664303&validation=true&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.090 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:415 -  [DUBBO] Subscribe: provider://**********:21510/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=contrast-dubbo&background=false&bind.ip=**********&bind.port=21510&category=configurators&check=false&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=contrast-dubbo&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=31248&prefer.serialization=fastjson2,hessian2&qos.port=21518&register=false&release=3.2.5&revision=3.2.5&service-name-mapping=true&service.filter=-default&side=provider&timeout=200000&timestamp=1753408664303&validation=true&version=1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.093 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:45.093 [main] INFO  com.alibaba.nacos.client.naming:377 - [GRPC-SUBSCRIBE] service:providers:org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo, group:DEFAULT_GROUP, cluster: 
2025-07-25 09:57:45.100 [main] INFO  com.alibaba.nacos.client.naming:51 - init new ips(0) service: DEFAULT_GROUP@@providers:org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo -> []
2025-07-25 09:57:45.101 [main] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-25 09:57:45.102 [main] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url provider://**********:21510/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=contrast-dubbo&background=false&bind.ip=**********&bind.port=21510&category=configurators&check=false&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=contrast-dubbo&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=31248&prefer.serialization=fastjson2,hessian2&qos.port=21518&register=false&release=3.2.5&revision=3.2.5&service-name-mapping=true&service.filter=-default&side=provider&timeout=200000&timestamp=1753408664303&validation=true&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.103 [main] INFO  com.alibaba.nacos.client.naming:164 - [SUBSCRIBE-SERVICE] service:providers:org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo, group:DEFAULT_GROUP, clusters: 
2025-07-25 09:57:45.108 [main] INFO  org.apache.dubbo.config.ServiceConfig:375 -  [DUBBO] Try to register interface application mapping for service contrast-dubbo/org.apache.dubbo.metadata.MetadataService:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.108 [main] INFO  org.apache.dubbo.config.ServiceConfig:380 -  [DUBBO] Successfully registered interface application mapping for service contrast-dubbo/org.apache.dubbo.metadata.MetadataService:1.0.0, dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.109 [main] INFO  o.a.d.c.m.ConfigurableMetadataServiceExporter:76 -  [DUBBO] The MetadataService exports urls : [dubbo://**********:21510/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=contrast-dubbo&background=false&bind.ip=**********&bind.port=21510&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=contrast-dubbo&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=31248&prefer.serialization=fastjson2,hessian2&qos.port=21518&register=false&release=3.2.5&revision=3.2.5&service-name-mapping=true&service.filter=-default&side=provider&timeout=200000&timestamp=1753408664303&validation=true&version=1.0.0], dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.109 [main] INFO  o.a.d.r.c.metadata.ServiceInstanceMetadataUtils:206 -  [DUBBO] Start registering instance address to registry., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.180 [main] INFO  o.a.dubbo.config.deploy.DefaultApplicationDeployer:1206 -  [DUBBO] Dubbo Application[1.1](contrast-dubbo) is ready., dubbo version: 3.2.5, current host: **********
2025-07-25 09:57:45.252 [main] INFO  o.s.integration.monitor.IntegrationMBeanExporter:681 - Registering MessageChannel contrastReceiveTaskSendResult.contrastReceiveTaskSendResultGroup.errors
2025-07-25 09:57:45.610 [nacos-grpc-client-executor-************-0] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 343237
2025-07-25 09:57:45.611 [nacos-grpc-client-executor-************-0] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 343237
2025-07-25 09:57:46.009 [main] INFO  com.ideal.envc.Bootstrap:61 - Started Bootstrap in 65.521 seconds (JVM running for 91.997)
2025-07-25 09:57:46.086 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] contrast+DEFAULT_GROUP+***********-07-25 09:57:46.087 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=contrast, group=DEFAULT_GROUP, cnt=1
2025-07-25 09:57:46.131 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] contrast-sit.yml+DEFAULT_GROUP+***********-07-25 09:57:46.132 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=contrast-sit.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 09:57:46.133 [main] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:420 - [fixed-123456-************_8848] [subscribe] contrast.yml+DEFAULT_GROUP+***********-07-25 09:57:46.133 [main] INFO  com.alibaba.nacos.client.config.impl.CacheData:236 - [fixed-123456-************_8848] [add-listener] ok, tenant=123456, dataId=contrast.yml, group=DEFAULT_GROUP, cnt=1
2025-07-25 09:57:46.135 [main] INFO  com.ideal.envc.Bootstrap:26 -  Contrast-comparison Server is already start successfully
2025-07-25 09:57:47.875 [RMI TCP Connection(40)-**********] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]:168 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 09:57:47.875 [RMI TCP Connection(40)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-07-25 09:57:47.885 [RMI TCP Connection(40)-**********] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 9 ms
2025-07-25 09:59:46.769 [http-nio-8210-exec-3] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 09:59:50.270 [http-nio-8210-exec-3] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 10:00:14.388 [http-nio-8210-exec-1] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 10:00:14.389 [http-nio-8210-exec-1] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 10:02:10.291 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 10:02:10.310 [http-nio-8210-exec-5] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 10:02:13.341 [http-nio-8210-exec-4] INFO  com.ideal.envc.controller.ResultMonitorController:93 - 导出比对报表，flowId：1107818608473399296
2025-07-25 10:02:13.343 [http-nio-8210-exec-4] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:252 - 根据流程ID导出比对报表，flowId：1107818608473399296
2025-07-25 10:02:13.463 [http-nio-8210-exec-4] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:345 - 成功解析JSON格式的content，sourceContent长度：36212，targetContent长度：35077
2025-07-25 10:02:13.464 [http-nio-8210-exec-4] INFO  c.i.envc.service.impl.FileComparisonServiceImpl:72 - 开始比较文件内容，基线服务器：长春数据中心，目标服务器：测试环境171
2025-07-25 10:02:13.473 [http-nio-8210-exec-4] INFO  c.i.envc.service.impl.FileComparisonServiceImpl:90 - 解析完成，源文件数量：316，目标文件数量：305
2025-07-25 10:02:13.477 [http-nio-8210-exec-4] INFO  c.i.envc.service.impl.FileComparisonServiceImpl:142 - 文件比较完成，结果：FileComparisonResultDto{baselineServer='长春数据中心', targetServer='测试环境171', description='null', totalSourceFiles=316, totalTargetFiles=305, consistentCount=303, inconsistentCount=0, missingCount=13, extraCount=2, consistentRate=95.89, inconsistentRate=0.00, missingRate=4.11, extraRate=0.66}
2025-07-25 10:02:13.487 [http-nio-8210-exec-4] INFO  c.i.envc.service.impl.FileComparisonServiceImpl:313 - 使用POI导出比对报表，数据行数：2
2025-07-25 10:02:16.086 [http-nio-8210-exec-4] INFO  c.i.envc.service.impl.FileComparisonServiceImpl:359 - POI Excel导出完成，包含比对结果和一致文件列表两个Sheet
2025-07-25 10:02:16.087 [http-nio-8210-exec-4] INFO  c.i.envc.service.impl.FileComparisonServiceImpl:174 - 文件比较结果导出完成，共导出2条记录
2025-07-25 10:02:16.087 [http-nio-8210-exec-4] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:273 - 导出比对报表成功，flowId：1107818608473399296
2025-07-25 10:02:16.090 [http-nio-8210-exec-4] INFO  com.ideal.envc.controller.ResultMonitorController:97 - 导出比对报表成功，flowId：1107818608473399296
2025-07-25 10:07:40.916 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 10:07:40.980 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 10:08:02.908 [http-nio-8210-exec-2] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为1
2025-07-25 10:08:10.782 [http-nio-8210-exec-8] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:08:10.962 [http-nio-8210-exec-8] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:08:11.897 [http-nio-8210-exec-9] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 2144183173,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:08:11.898 [http-nio-8210-exec-10] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:08:11.898 [http-nio-8210-exec-10] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:08:11.920 [http-nio-8210-exec-9] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 2144183173,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:08:11.929 [http-nio-8210-exec-10] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:08:14.413 [http-nio-8210-exec-3] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:08:14.497 [http-nio-8210-exec-3] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:08:15.261 [http-nio-8210-exec-1] INFO  com.ideal.envc.controller.ResultMonitorController:48 - 查询比对结果列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}}
2025-07-25 10:08:15.278 [http-nio-8210-exec-1] INFO  c.ideal.envc.service.impl.ResultMonitorServiceImpl:72 - 查询比对结果列表，查询条件：ResultMonitorQueryDto{businessSystemName='null', model=null, result=null, from=null}
2025-07-25 10:09:36.917 [http-nio-8210-exec-2] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:09:37.024 [http-nio-8210-exec-2] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:09:37.499 [http-nio-8210-exec-6] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:09:37.499 [http-nio-8210-exec-7] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 1335835063,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:09:37.500 [http-nio-8210-exec-6] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:09:37.500 [http-nio-8210-exec-7] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 1335835063,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:09:37.515 [http-nio-8210-exec-6] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:09:39.717 [http-nio-8210-exec-10] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 811508875,id=null,businessSystemId=1076665888441950208,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:09:39.721 [http-nio-8210-exec-10] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 811508875,id=null,businessSystemId=1076665888441950208,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:09:39.717 [http-nio-8210-exec-8] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:09:39.724 [http-nio-8210-exec-8] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:09:39.751 [http-nio-8210-exec-8] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:09:40.218 [http-nio-8210-exec-9] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 906493205,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:09:40.218 [http-nio-8210-exec-3] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:09:40.218 [http-nio-8210-exec-9] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 906493205,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:09:40.218 [http-nio-8210-exec-3] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:09:40.264 [http-nio-8210-exec-3] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:09:41.365 [http-nio-8210-exec-1] INFO  c.ideal.envc.service.impl.NodeRelationServiceImpl:71 - 查询节点关系规则列表，查询条件：NodeRelationQueryDto [Hash = 396565278,id=null,envcSystemComputerNodeId=1092596022002225152,model=null,type=null,path=null,sourcePath=null,encode=null,way=null,ruleType=null,enabled=null,childLevel=null,creatorId=null,creatorName=null,createTime=null,updatorId=null,updatorName=null,updateTime=null]
2025-07-25 10:09:43.610 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.CharsetController:38 - 获取字符集列表
2025-07-25 10:10:17.419 [http-nio-8210-exec-4] INFO  com.ideal.envc.controller.CharsetController:38 - 获取字符集列表
2025-07-25 10:10:20.484 [http-nio-8210-exec-2] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:10:20.484 [http-nio-8210-exec-2] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:10:20.499 [http-nio-8210-exec-2] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:10:28.148 [http-nio-8210-exec-6] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:10:28.149 [http-nio-8210-exec-6] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:10:28.164 [http-nio-8210-exec-6] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:10:31.402 [http-nio-8210-exec-7] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:10:31.407 [http-nio-8210-exec-7] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:10:41.965 [http-nio-8210-exec-8] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:10:41.967 [http-nio-8210-exec-8] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:10:42.030 [http-nio-8210-exec-8] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:10:51.251 [http-nio-8210-exec-10] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:10:51.354 [http-nio-8210-exec-10] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:11:07.468 [http-nio-8210-exec-9] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:11:07.554 [http-nio-8210-exec-9] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:11:08.282 [http-nio-8210-exec-1] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:11:08.282 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 722566911,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:11:08.282 [http-nio-8210-exec-1] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:11:08.282 [http-nio-8210-exec-5] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 722566911,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:11:08.307 [http-nio-8210-exec-1] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:11:21.426 [http-nio-8210-exec-4] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:11:21.556 [http-nio-8210-exec-4] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:11:24.786 [http-nio-8210-exec-6] INFO  com.ideal.envc.interaction.sysm.SystemInteract:357 - getBusinessSystemComputerListOfPageInfo()--systemComputerListQueryDto:{"businessSystemId":1084726198363185152,"pageNum":1,"pageSize":10,"queryParam":{"agentBusinessType":"一致性比对","businessSystemId":1084726198363185152,"computerType":0,"excludeComputerIds":[1060034655318372352,1092595899385688064],"resultBusinessSystem":true,"resultComputerUser":true}}
2025-07-25 10:11:24.862 [http-nio-8210-exec-6] INFO  com.ideal.envc.interaction.sysm.SystemInteract:360 - 平台管理queryBusinessSystemComputerList接口返回:{"endRow":0,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[],"navigateFirstPage":0,"navigateLastPage":0,"navigatePages":8,"navigatepageNums":[],"nextPage":0,"pageNum":1,"pageSize":10,"pages":0,"prePage":0,"size":0,"startRow":0,"total":0}
2025-07-25 10:12:31.950 [http-nio-8210-exec-9] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:12:32.026 [http-nio-8210-exec-9] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:12:32.484 [http-nio-8210-exec-1] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:12:32.485 [http-nio-8210-exec-1] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:12:32.486 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 316679951,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:12:32.487 [http-nio-8210-exec-5] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 316679951,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:12:32.501 [http-nio-8210-exec-1] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:12:34.464 [http-nio-8210-exec-4] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:12:34.554 [http-nio-8210-exec-4] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:12:36.350 [http-nio-8210-exec-6] INFO  com.ideal.envc.interaction.sysm.SystemInteract:357 - getBusinessSystemComputerListOfPageInfo()--systemComputerListQueryDto:{"businessSystemId":1084726198363185152,"pageNum":1,"pageSize":10,"queryParam":{"agentBusinessType":"一致性比对","businessSystemId":1084726198363185152,"computerType":0,"excludeComputerIds":[1060034655318372352,1092595899385688064],"resultBusinessSystem":true,"resultComputerUser":true}}
2025-07-25 10:12:36.519 [http-nio-8210-exec-6] INFO  com.ideal.envc.interaction.sysm.SystemInteract:360 - 平台管理queryBusinessSystemComputerList接口返回:{"endRow":1,"hasNextPage":false,"hasPreviousPage":false,"isFirstPage":true,"isLastPage":true,"list":[{"agentGroupList":[],"agentId":1106024534002622464,"agentIp":"*********","agentName":"*********","agentPort":"15000","businessSystemApiDtoList":[{"code":"lch000","description":"一致性比对系统","id":1084726198363185152,"instanceId":"ieai-1084726198363185152","name":"lch_test_compare_business","type":978067655522234360}],"centerId":1022009652215046144,"centerName":"长春数据中心","computerId":1106024533864210432,"computerIp":"*********","computerName":"*********","computerType":0,"computerUserList":[],"description":"","highPowerUser":"","instanceId":"ieai-1106024533864210432","loginAgreement":"","lowPowerUser":"","machineBox":"","machineRoom":"","osName":"linux","osVersion":"1.0","serialNumber":"","startU":"","startUnit":"","tag":"","tagList":[],"warehouse":""}],"navigateFirstPage":1,"navigateLastPage":1,"navigatePages":8,"navigatepageNums":[1],"nextPage":0,"pageNum":1,"pageSize":10,"pages":1,"prePage":0,"size":1,"startRow":1,"total":1}
2025-07-25 10:12:39.947 [http-nio-8210-exec-7] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:12:42.404 [http-nio-8210-exec-10] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:12:42.504 [http-nio-8210-exec-10] INFO  com.ideal.envc.interaction.sysm.SystemInteract:184 - getBusinessSystemIdList 本次获取平台管理配置下用户ID为1082170050173710336,业务系统总数为471
2025-07-25 10:12:43.757 [http-nio-8210-exec-9] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:12:43.762 [http-nio-8210-exec-9] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:12:43.765 [http-nio-8210-exec-3] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 212862424,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:12:43.767 [http-nio-8210-exec-3] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 212862424,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:12:43.780 [http-nio-8210-exec-9] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:12:45.224 [http-nio-8210-exec-1] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:12:45.225 [http-nio-8210-exec-1] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:12:45.289 [http-nio-8210-exec-1] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:13:00.248 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:13:00.249 [http-nio-8210-exec-5] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:13:05.217 [http-nio-8210-exec-4] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=944816362427318272, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:13:05.217 [http-nio-8210-exec-4] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：944816362427318272，排除设备ID集合：null
2025-07-25 10:13:18.343 [http-nio-8210-exec-2] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:13:18.344 [http-nio-8210-exec-2] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:13:18.357 [http-nio-8210-exec-2] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:13:25.848 [http-nio-8210-exec-6] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=944816362427318272, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:13:25.848 [http-nio-8210-exec-6] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：944816362427318272，排除设备ID集合：null
2025-07-25 10:13:41.620 [http-nio-8210-exec-7] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:13:41.620 [http-nio-8210-exec-7] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:13:58.552 [http-nio-8210-exec-8] INFO  com.ideal.envc.controller.NodeIndexController:187 - 查询系统设备列表（分页），查询条件：com.ideal.envc.model.dto.SystemComputerQueryPageDto@30d22f52
2025-07-25 10:14:12.049 [http-nio-8210-exec-10] INFO  com.ideal.envc.controller.NodeIndexController:187 - 查询系统设备列表（分页），查询条件：com.ideal.envc.model.dto.SystemComputerQueryPageDto@7f682b54
2025-07-25 10:14:15.209 [http-nio-8210-exec-9] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:14:15.226 [http-nio-8210-exec-9] WARN  c.i.e.service.impl.SystemComputerNodeServiceImpl:300 - 源设备和目标设备不能相同，跳过：1060034655318372352
2025-07-25 10:14:15.232 [http-nio-8210-exec-9] WARN  com.ideal.envc.controller.NodeIndexController:128 - 批量绑定系统设备节点失败：130101：没有有效的目标设备可以绑定
2025-07-25 10:14:47.804 [http-nio-8210-exec-3] INFO  c.ideal.envc.service.impl.NodeRelationServiceImpl:71 - 查询节点关系规则列表，查询条件：NodeRelationQueryDto [Hash = 291387036,id=null,envcSystemComputerNodeId=1092596022002225152,model=null,type=null,path=null,sourcePath=null,encode=null,way=null,ruleType=null,enabled=null,childLevel=null,creatorId=null,creatorName=null,createTime=null,updatorId=null,updatorName=null,updateTime=null]
2025-07-25 10:14:50.210 [http-nio-8210-exec-1] INFO  com.ideal.envc.controller.CharsetController:38 - 获取字符集列表
2025-07-25 10:14:54.434 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:14:54.435 [http-nio-8210-exec-5] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:14:54.449 [http-nio-8210-exec-5] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:14:58.507 [http-nio-8210-exec-4] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:14:58.517 [http-nio-8210-exec-4] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:15:02.569 [http-nio-8210-exec-2] INFO  com.ideal.envc.controller.NodeIndexController:187 - 查询系统设备列表（分页），查询条件：com.ideal.envc.model.dto.SystemComputerQueryPageDto@2638ad33
2025-07-25 10:15:09.481 [http-nio-8210-exec-6] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:15:09.514 [http-nio-8210-exec-6] WARN  c.i.e.service.impl.SystemComputerNodeServiceImpl:300 - 源设备和目标设备不能相同，跳过：1060034655318372352
2025-07-25 10:15:09.523 [http-nio-8210-exec-6] WARN  com.ideal.envc.controller.NodeIndexController:128 - 批量绑定系统设备节点失败：130101：没有有效的目标设备可以绑定
2025-07-25 10:19:18.856 [http-nio-8210-exec-10] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:19:18.875 [http-nio-8210-exec-10] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:19:19.634 [http-nio-8210-exec-10] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:19:24.242 [http-nio-8210-exec-9] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:19:24.253 [http-nio-8210-exec-9] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:19:32.485 [http-nio-8210-exec-3] INFO  com.ideal.envc.controller.NodeIndexController:187 - 查询系统设备列表（分页），查询条件：com.ideal.envc.model.dto.SystemComputerQueryPageDto@3be0fdc7
2025-07-25 10:22:52.716 [http-nio-8210-exec-5] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:22:52.741 [http-nio-8210-exec-5] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:22:53.662 [http-nio-8210-exec-5] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:22:56.789 [http-nio-8210-exec-4] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:22:56.791 [http-nio-8210-exec-4] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:23:02.717 [http-nio-8210-exec-2] INFO  com.ideal.envc.controller.NodeIndexController:187 - 查询系统设备列表（分页），查询条件：com.ideal.envc.model.dto.SystemComputerQueryPageDto@34d8687
2025-07-25 10:23:11.314 [http-nio-8210-exec-6] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:23:11.316 [http-nio-8210-exec-6] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:23:11.581 [http-nio-8210-exec-6] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:23:13.622 [http-nio-8210-exec-7] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:23:13.623 [http-nio-8210-exec-7] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:23:17.804 [http-nio-8210-exec-8] INFO  com.ideal.envc.controller.NodeIndexController:187 - 查询系统设备列表（分页），查询条件：com.ideal.envc.model.dto.SystemComputerQueryPageDto@39c97a33
2025-07-25 10:23:41.712 [http-nio-8210-exec-10] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:23:41.719 [http-nio-8210-exec-10] WARN  c.i.e.service.impl.SystemComputerNodeServiceImpl:300 - 源设备和目标设备不能相同，跳过：1060034655318372352
2025-07-25 10:23:41.724 [http-nio-8210-exec-10] WARN  com.ideal.envc.controller.NodeIndexController:128 - 批量绑定系统设备节点失败：130101：没有有效的目标设备可以绑定
2025-07-25 10:24:15.834 [http-nio-8210-exec-9] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:24:22.246 [http-nio-8210-exec-9] WARN  c.i.e.service.impl.SystemComputerNodeServiceImpl:300 - 源设备和目标设备不能相同，跳过：1060034655318372352
2025-07-25 10:24:22.249 [http-nio-8210-exec-9] WARN  com.ideal.envc.controller.NodeIndexController:128 - 批量绑定系统设备节点失败：130101：没有有效的目标设备可以绑定
2025-07-25 10:24:36.817 [http-nio-8210-exec-3] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:24:36.878 [http-nio-8210-exec-3] WARN  c.i.e.service.impl.SystemComputerNodeServiceImpl:300 - 源设备和目标设备不能相同，跳过：1060034655318372352
2025-07-25 10:24:36.880 [http-nio-8210-exec-3] WARN  com.ideal.envc.controller.NodeIndexController:128 - 批量绑定系统设备节点失败：130101：没有有效的目标设备可以绑定
2025-07-25 10:24:55.759 [http-nio-8210-exec-1] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:25:19.712 [http-nio-8210-exec-1] WARN  c.i.e.service.impl.SystemComputerNodeServiceImpl:300 - 源设备和目标设备不能相同，跳过：1060034655318372352
2025-07-25 10:26:05.917 [nacos-grpc-client-executor-************-660] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 344326
2025-07-25 10:26:05.918 [nacos-grpc-client-executor-************-660] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 344326
2025-07-25 10:26:05.918 [nacos-grpc-client-executor-************-626] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Receive server push request, request = ClientDetectionRequest, requestId = 344324
2025-07-25 10:26:05.918 [nacos-grpc-client-executor-************-626] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Ack server push request, request = ClientDetectionRequest, requestId = 344324
2025-07-25 10:26:05.920 [nacos-grpc-client-executor-************-648] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 344327
2025-07-25 10:26:05.925 [nacos-grpc-client-executor-************-648] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 344327
2025-07-25 10:26:05.937 [http-nio-8210-exec-1] WARN  com.ideal.envc.controller.NodeIndexController:128 - 批量绑定系统设备节点失败：130101：没有有效的目标设备可以绑定
2025-07-25 10:26:05.946 [nacos-grpc-client-executor-************-663] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753408642007_**********_62003]Request stream onCompleted, switch server
2025-07-25 10:26:05.946 [nacos-grpc-client-executor-************-651] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753408643745_**********_62008]Request stream onCompleted, switch server
2025-07-25 10:26:05.946 [nacos-grpc-client-executor-************-629] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753408655381_**********_62257]Request stream onCompleted, switch server
2025-07-25 10:26:05.949 [nacos-grpc-client-executor-************-632] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = ClientDetectionRequest, requestId = 344325
2025-07-25 10:26:05.949 [nacos-grpc-client-executor-************-632] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = ClientDetectionRequest, requestId = 344325
2025-07-25 10:26:05.950 [nacos-grpc-client-executor-************-656] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Receive server push request, request = ClientDetectionRequest, requestId = 344328
2025-07-25 10:26:05.950 [nacos-grpc-client-executor-************-656] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Ack server push request, request = ClientDetectionRequest, requestId = 344328
2025-07-25 10:26:05.950 [nacos-grpc-client-executor-************-644] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = ClientDetectionRequest, requestId = 344330
2025-07-25 10:26:05.950 [nacos-grpc-client-executor-************-644] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = ClientDetectionRequest, requestId = 344330
2025-07-25 10:26:05.956 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:05.957 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:05.981 [nacos-grpc-client-executor-************-632] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753408664708_**********_62519]Request stream onCompleted, switch server
2025-07-25 10:26:05.982 [nacos-grpc-client-executor-************-644] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753408661487_**********_62470]Request stream onCompleted, switch server
2025-07-25 10:26:05.985 [nacos-grpc-client-executor-************-656] ERROR c.a.nacos.common.remote.client.grpc.GrpcClient:102 - [1753408612159_**********_61926]Request stream onCompleted, switch server
2025-07-25 10:26:06.005 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Success to connect a server [************:8848], connectionId = 1753410369826_**********_64909
2025-07-25 10:26:06.006 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753408643745_**********_62008
2025-07-25 10:26:06.006 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753408643745_**********_62008
2025-07-25 10:26:06.117 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Notify disconnected event to listeners
2025-07-25 10:26:06.119 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] DisConnected,clear listen context...
2025-07-25 10:26:06.121 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Notify connected event to listeners.
2025-07-25 10:26:06.121 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [30feca63-c1af-472d-8f0d-b7df8745bf85_config-0] Connected,notify listen context...
2025-07-25 10:26:06.122 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Server healthy check fail, currentConnection = 1753408664708_**********_62519
2025-07-25 10:26:06.122 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Server healthy check fail, currentConnection = 1753408655381_**********_62257
2025-07-25 10:26:06.122 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:06.122 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:06.122 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:06.122 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:06.123 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Server healthy check fail, currentConnection = 1753408661487_**********_62470
2025-07-25 10:26:06.123 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:06.123 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:06.483 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Server healthy check fail, currentConnection = 1753408612159_**********_61926
2025-07-25 10:26:06.484 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:06.484 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:06.913 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Success to connect a server [************:8848], connectionId = 1753410369986_**********_64913
2025-07-25 10:26:06.914 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Abandon prev connection, server is ************:8848, connectionId is 1753408655381_**********_62257
2025-07-25 10:26:06.914 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753408655381_**********_62257
2025-07-25 10:26:07.094 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Success to connect a server [************:8848], connectionId = 1753410370109_**********_64915
2025-07-25 10:26:07.102 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Abandon prev connection, server is ************:8848, connectionId is 1753408664708_**********_62519
2025-07-25 10:26:07.103 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753408664708_**********_62519
2025-07-25 10:26:07.104 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:07.104 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Notify disconnected event to listeners
2025-07-25 10:26:07.104 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Notify disconnected event to listeners
2025-07-25 10:26:07.104 [com.alibaba.nacos.client.remote.worker.1] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-25 10:26:07.104 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:07.104 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:07.104 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-25 10:26:07.108 [com.alibaba.nacos.client.remote.worker.0] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:07.111 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Success to connect a server [************:8848], connectionId = 1753410369998_**********_64914
2025-07-25 10:26:07.111 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Abandon prev connection, server is ************:8848, connectionId is 1753408661487_**********_62470
2025-07-25 10:26:07.109 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Server healthy check fail, currentConnection = 1753408642007_**********_62003
2025-07-25 10:26:07.111 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753408661487_**********_62470
2025-07-25 10:26:07.112 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:07.112 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:07.112 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:07.112 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Notify disconnected event to listeners
2025-07-25 10:26:07.113 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-25 10:26:07.113 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:07.123 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Success to connect a server [************:8848], connectionId = 1753410370944_**********_64919
2025-07-25 10:26:07.124 [com.alibaba.nacos.client.remote.worker.1] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-25 10:26:07.124 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753408612159_**********_61926
2025-07-25 10:26:07.124 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Notify connected event to listeners.
2025-07-25 10:26:07.124 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 10:26:07.124 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753408612159_**********_61926
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Notify connected event to listeners.
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Notify disconnected event to listeners
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] DisConnected,clear listen context...
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Notify connected event to listeners.
2025-07-25 10:26:07.125 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Connected,notify listen context...
2025-07-25 10:26:07.129 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-25 10:26:07.132 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Notify connected event to listeners.
2025-07-25 10:26:07.132 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 10:26:07.166 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Success to connect a server [************:8848], connectionId = 1753410370964_**********_64920
2025-07-25 10:26:07.169 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Abandon prev connection, server is ************:8848, connectionId is 1753410369986_**********_64913
2025-07-25 10:26:07.171 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753410369986_**********_64913
2025-07-25 10:26:07.171 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Success to connect a server [************:8848], connectionId = 1753410370972_**********_64921
2025-07-25 10:26:07.171 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Abandon prev connection, server is ************:8848, connectionId is 1753410369998_**********_64914
2025-07-25 10:26:07.171 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753410369998_**********_64914
2025-07-25 10:26:07.171 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Notify disconnected event to listeners
2025-07-25 10:26:07.171 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-25 10:26:07.171 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-25 10:26:07.172 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Notify connected event to listeners.
2025-07-25 10:26:07.172 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Notify disconnected event to listeners
2025-07-25 10:26:07.172 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 10:26:07.172 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-25 10:26:07.172 [com.alibaba.nacos.client.remote.worker.0] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-25 10:26:07.172 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Notify connected event to listeners.
2025-07-25 10:26:07.172 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 10:26:07.182 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Success to connect a server [************:8848], connectionId = 1753410370992_**********_64923
2025-07-25 10:26:07.183 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Abandon prev connection, server is ************:8848, connectionId is 1753410370109_**********_64915
2025-07-25 10:26:07.183 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753410370109_**********_64915
2025-07-25 10:26:07.183 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Notify disconnected event to listeners
2025-07-25 10:26:07.183 [com.alibaba.nacos.client.remote.worker.1] WARN  com.alibaba.nacos.client.naming:96 - Grpc connection disconnect, mark to redo
2025-07-25 10:26:07.184 [com.alibaba.nacos.client.remote.worker.1] WARN  com.alibaba.nacos.client.naming:103 - mark to redo completed
2025-07-25 10:26:07.184 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Notify connected event to listeners.
2025-07-25 10:26:07.184 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.client.naming:90 - Grpc connection connect
2025-07-25 10:26:07.188 [nacos-grpc-client-executor-************-648] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753410369986_**********_64913]Ignore error event,isRunning:true,isAbandon=true
2025-07-25 10:26:07.188 [nacos-grpc-client-executor-************-653] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753410370109_**********_64915]Ignore error event,isRunning:true,isAbandon=true
2025-07-25 10:26:07.188 [nacos-grpc-client-executor-************-664] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753410369998_**********_64914]Ignore error event,isRunning:true,isAbandon=true
2025-07-25 10:26:07.192 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Success to connect a server [************:8848], connectionId = 1753410370992_**********_64922
2025-07-25 10:26:07.192 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753408642007_**********_62003
2025-07-25 10:26:07.192 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753408642007_**********_62003
2025-07-25 10:26:07.193 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
2025-07-25 10:26:07.193 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Notify disconnected event to listeners
2025-07-25 10:26:07.193 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] DisConnected,clear listen context...
2025-07-25 10:26:07.193 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Notify connected event to listeners.
2025-07-25 10:26:07.193 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Connected,notify listen context...
2025-07-25 10:26:07.196 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:07.198 [com.alibaba.nacos.client.remote.worker.1] INFO  c.a.nacos.common.remote.client.grpc.GrpcClient:201 - grpc client connection server:************ ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Success to connect a server [************:8848], connectionId = 1753410371061_**********_64925
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Success to connect a server [************:8848], connectionId = 1753410371052_**********_64924
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753410370992_**********_64922
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Abandon prev connection, server is ************:8848, connectionId is 1753410370944_**********_64919
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753410370992_**********_64922
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.1] INFO  com.alibaba.nacos.common.remote.client:585 - Close current connection 1753410370944_**********_64919
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Notify disconnected event to listeners
2025-07-25 10:26:07.259 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Notify disconnected event to listeners
2025-07-25 10:26:07.261 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] DisConnected,clear listen context...
2025-07-25 10:26:07.261 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:724 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] DisConnected,clear listen context...
2025-07-25 10:26:07.261 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Notify connected event to listeners.
2025-07-25 10:26:07.261 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [7cfdcee4-f6b4-4668-b7bd-7bce60b65944_config-0] Connected,notify listen context...
2025-07-25 10:26:07.261 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.common.remote.client:63 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Notify connected event to listeners.
2025-07-25 10:26:07.261 [com.alibaba.nacos.client.remote.worker.0] INFO  com.alibaba.nacos.client.config.impl.ClientWorker:717 - [9b0da77b-f4d0-4b9c-88c4-717da1b82afe_config-0] Connected,notify listen context...
2025-07-25 10:26:07.265 [nacos-grpc-client-executor-************-685] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753410370992_**********_64922]Ignore error event,isRunning:true,isAbandon=true
2025-07-25 10:26:07.265 [nacos-grpc-client-executor-************-680] WARN  c.a.nacos.common.remote.client.grpc.GrpcClient:89 - [1753410370944_**********_64919]Ignore error event,isRunning:true,isAbandon=true
2025-07-25 10:26:08.921 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:73 - Redo instance operation REGISTER for DEFAULT_GROUP@@contrast
2025-07-25 10:26:08.921 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:org.apache.dubbo.metadata.MetadataService:1.0.0:contrast-dubbo#
2025-07-25 10:26:08.921 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@dubbo-system#
2025-07-25 10:26:08.955 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@dubbo-engine#
2025-07-25 10:26:08.955 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@contrast#
2025-07-25 10:26:08.954 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystemCompuerList:1.0.0:system#
2025-07-25 10:26:08.958 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@dubbo-system-lei#
2025-07-25 10:26:08.959 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine#
2025-07-25 10:26:08.963 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.ICenter:1.0.0:system#
2025-07-25 10:26:08.966 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.IRoleProjectPermission:1.0.0:system#
2025-07-25 10:26:08.971 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine#
2025-07-25 10:26:08.973 [com.alibaba.nacos.client.naming.grpc.redo.0] INFO  com.alibaba.nacos.client.naming:121 - Redo subscriber operation REGISTER for DEFAULT_GROUP@@providers:com.ideal.system.api.IBusinessSystem:1.0.0:system#
2025-07-25 10:26:09.513 [nacos-grpc-client-executor-************-660] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = 344331
2025-07-25 10:26:09.513 [nacos-grpc-client-executor-************-648] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 344333
2025-07-25 10:26:09.514 [nacos-grpc-client-executor-************-660] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = 344331
2025-07-25 10:26:09.515 [nacos-grpc-client-executor-************-648] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 344333
2025-07-25 10:26:09.515 [nacos-grpc-client-executor-************-653] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Receive server push request, request = NotifySubscriberRequest, requestId = 344338
2025-07-25 10:26:09.517 [nacos-grpc-client-executor-************-653] INFO  com.alibaba.nacos.common.remote.client:63 - [8e56f62e-778d-4e19-a133-2b1b52bd1515] Ack server push request, request = NotifySubscriberRequest, requestId = 344338
2025-07-25 10:26:09.519 [nacos-grpc-client-executor-************-661] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = 344335
2025-07-25 10:26:09.519 [nacos-grpc-client-executor-************-649] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 344334
2025-07-25 10:26:09.519 [nacos-grpc-client-executor-************-649] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 344334
2025-07-25 10:26:09.519 [nacos-grpc-client-executor-************-661] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = 344335
2025-07-25 10:26:09.522 [nacos-grpc-client-executor-************-650] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 344336
2025-07-25 10:26:09.522 [nacos-grpc-client-executor-************-650] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 344336
2025-07-25 10:26:09.522 [nacos-grpc-client-executor-************-662] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Receive server push request, request = NotifySubscriberRequest, requestId = 344337
2025-07-25 10:26:09.523 [nacos-grpc-client-executor-************-662] INFO  com.alibaba.nacos.common.remote.client:63 - [f3bb0e29-99d0-4d47-86ec-bf3dcfe17c33] Ack server push request, request = NotifySubscriberRequest, requestId = 344337
2025-07-25 10:26:09.523 [nacos-grpc-client-executor-************-651] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 344339
2025-07-25 10:26:09.523 [nacos-grpc-client-executor-************-651] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 344339
2025-07-25 10:26:09.524 [nacos-grpc-client-executor-************-652] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 344340
2025-07-25 10:26:09.524 [nacos-grpc-client-executor-************-652] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 344340
2025-07-25 10:26:09.525 [nacos-grpc-client-executor-************-653] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 344341
2025-07-25 10:26:09.525 [nacos-grpc-client-executor-************-653] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 344341
2025-07-25 10:26:09.527 [nacos-grpc-client-executor-************-654] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Receive server push request, request = NotifySubscriberRequest, requestId = 344342
2025-07-25 10:26:09.527 [nacos-grpc-client-executor-************-654] INFO  com.alibaba.nacos.common.remote.client:63 - [723c9d0c-b4d5-4785-8f50-ca8b52674713] Ack server push request, request = NotifySubscriberRequest, requestId = 344342
2025-07-25 10:26:19.663 [http-nio-8210-exec-5] INFO  com.ideal.envc.component.UserinfoComponent:25 - 登录用户信息:UserDto{id=1082170050173710336, loginName='lch', fullName='卢长海', orgCode='994442464299859968#956709609688858624#'}
2025-07-25 10:26:49.827 [http-nio-8210-exec-4] INFO  com.ideal.envc.controller.NodeIndexController:101 - 查询系统已绑定源目标设备列表，查询条件：TableQueryDTO{page=1, pageSize=10, queryParam=SystemComputerNodeQueryDto [Hash = 917876664,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]}
2025-07-25 10:26:49.829 [http-nio-8210-exec-4] INFO  c.i.e.service.impl.SystemComputerNodeServiceImpl:168 - 查询系统已绑定源目标设备列表，查询条件：SystemComputerNodeQueryDto [Hash = 917876664,id=null,businessSystemId=1084726198363185152,sourceCenterId=null,targetCenterId=null,sourceComputerId=null,sourceComputerIp=null,targetComputerId=null,targetComputerIp=null,creatorId=null,creatorName=null,createTime=null]
2025-07-25 10:26:59.726 [http-nio-8210-exec-2] INFO  com.ideal.envc.controller.CenterController:41 - 获取中心列表
2025-07-25 10:26:59.727 [http-nio-8210-exec-2] INFO  com.ideal.envc.service.impl.CenterServiceImpl:35 - 获取中心列表
2025-07-25 10:26:59.834 [http-nio-8210-exec-2] INFO  com.ideal.envc.interaction.sysm.SystemInteract:529 - 本次获取平台管理中心列表总数为51
2025-07-25 10:27:02.329 [http-nio-8210-exec-6] INFO  com.ideal.envc.controller.NodeIndexController:229 - 查询系统源设备列表（不分页），查询条件：SystemComputerQueryDto{id=null, businessSystemId=1084726198363185152, computerId=null, computerIp='null', computerName='null', centerId=1022009652215046144, centerName='null', createTime=null, creatorId=null, creatorName='null', excludeComputerIds=null, appointComputerIds=null}
2025-07-25 10:27:02.339 [http-nio-8210-exec-6] INFO  com.ideal.envc.service.impl.NodeIndexServiceImpl:351 - 查询系统设备列表（不分页），业务系统ID：1084726198363185152，中心ID：1022009652215046144，排除设备ID集合：null
2025-07-25 10:27:05.878 [http-nio-8210-exec-7] INFO  com.ideal.envc.controller.NodeIndexController:187 - 查询系统设备列表（分页），查询条件：com.ideal.envc.model.dto.SystemComputerQueryPageDto@1e9b6165
2025-07-25 10:57:41.036 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 11:07:40.987 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 11:47:41.101 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 12:07:41.064 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 12:37:41.152 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 13:07:41.096 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-25 13:27:41.149 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-25 14:07:41.130 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
