package com.ideal.envc.service;

import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.bean.StartPlanBean;
import com.ideal.envc.model.dto.ComputerInfoDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;

import java.util.List;
import java.util.Map;

/**
 * 对比基础服务接口
 *
 * <AUTHOR>
 */
public interface IStartContrastBaseService {

    /**
     * 根据实例ID查询层次化实例信息
     *
     * @param instanceId 实例ID
     * @return 层次化实例信息
     */
    HierarchicalRunInstanceBean getHierarchicalRunInstanceById(Long instanceId);

    /**
     * 根据方案ID查询层次化实例信息列表
     *
     * @param planId 方案ID
     * @return 层次化实例信息列表
     */
    List<HierarchicalRunInstanceBean> getHierarchicalRunInstancesByPlanId(Long planId);

    /**
     * 根据任务ID查询层次化实例信息列表
     *
     * @param taskId 任务ID
     * @return 层次化实例信息列表
     */
    List<HierarchicalRunInstanceBean> getHierarchicalRunInstancesByTaskId(Long taskId);

    /**
     * 存储运行实例相关表数据
     *
     * @param startPlanBeanList 方案信息列表
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源（1：周期触发，2：手动触发，3：重试）
     * @param taskId 任务ID（方案启动和重试无任务ID）
     * @return 层次化运行实例列表
     */
    List<HierarchicalRunInstanceBean> saveRunInstanceData(List<StartPlanBean> startPlanBeanList, Long userId, String userName, Integer from, Long taskId,Map<Long, String> centerMap,Map<Long, ComputerInfoDto>computerInfoDtoMap);


    /**
     * 初始下发更新状态
     * 更改运行实例及下属子表中数据状态，将状态重置为初始状态
     * 主要是修改ieai_envc_run_instance表iresult=-1，istate=0
     * ieai_envc_run_instance_info表的iresult=-1，istate=0
     * ieai_envc_run_rule的iresult=-1，istate=0
     * 表ieai_envc_run_flow的istate=0，iresult=-1
     *
     * @param instanceId 实例ID
     * @return 更新结果，true表示成功，false表示失败
     */
    boolean updateInstanceStatusToInitial(Long instanceId);
}
