# 导出比对报表接口文档

## 概述

本文档描述了新增的导出比对报表功能，该功能通过flowId查询比对结果，解析其中的sourceContent和targetContent，然后生成Excel格式的比对报表。

## 接口信息

### 基本信息
- **接口路径**: `/result/exportReport`
- **请求方法**: `POST`
- **权限要求**: `comparison-results`
- **响应类型**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flowId | Long | 是 | 流程ID，用于查询比对结果 |
| sourceServer | String | 否 | 基线服务器名称，默认为"基线服务器" |
| targetServer | String | 否 | 目标服务器名称，默认为"目标服务器" |

### 请求示例

```bash
curl -X POST "http://localhost:8080/result/exportReport" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "flowId=1&sourceServer=生产服务器&targetServer=测试服务器"
```

### 响应说明

#### 成功响应
- **状态码**: 200
- **响应头**: 
  - `Content-Type`: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
  - `Content-Disposition`: `attachment;filename*=utf-8''文件比较结果_yyyyMMdd_HHmmss.xlsx`
- **响应体**: Excel文件二进制数据

#### 错误响应

##### 业务异常 (400)
```
状态码: 400 Bad Request
Content-Type: text/plain;charset=utf-8
响应体: 导出失败：具体错误信息
```

##### 系统异常 (500)
```
状态码: 500 Internal Server Error
Content-Type: text/plain;charset=utf-8
响应体: 系统异常，导出失败
```

## 数据处理流程

### 1. 数据查询
通过flowId查询`RunFlowResultEntity`表，获取content字段内容。

### 2. 内容解析
支持三种格式的content解析：

#### 格式1：JSON格式（推荐）
```json
{
  "sourceContent": "基线采集内容",
  "targetContent": "目标采集内容", 
  "content": "比对结果",
  "ret": true
}
```

#### 格式2：分隔符格式
```
基线采集内容@$@目标采集内容
```

#### 格式3：纯文本格式
```
整个内容作为基线内容处理，目标内容为空
```

### 3. 比对分析
调用`FileComparisonComponent`组件对解析出的sourceContent和targetContent进行比对分析。

### 4. Excel生成
生成包含以下信息的Excel报表：
- 文件路径对比
- 状态分类（一致、不一致、缺失、多出）
- 文件大小对比
- MD5值对比
- 权限信息
- 备注说明

## Excel报表格式（严格按照提供图片格式）

### 文件信息
- **文件名**: `文件比较结果_yyyyMMdd_HHmmss.xlsx`
- **工作表名**: `文件比较结果`

### 报表结构

#### 1. 说明文字部分（位于表格上方）
```
环境一致性比对结果报告
比对对象：[基线服务器] vs [目标服务器]
比对时间：yyyy-MM-dd HH:mm:ss
比对说明：基于采集的配置信息进行环境一致性比对，识别配置差异和不一致项
不一致项：基于采集的配置信息进行环境一致性比对，识别配置差异和不一致项，输出差异数据的详细信息
```

#### 2. 表格部分

| 列名 | 说明 | 宽度 |
|------|------|------|
| 序号 | 行序号 | 8 |
| hostname | 服务器名称或文件路径 | 20 |
| 主机 | 主机类型（基线/目标/不一致/缺失/多出） | 15 |
| 数量 | 文件数量 | 10 |
| 备注 | 详细说明信息 | 20 |
| 不一致 | 不一致文件数量 | 10 |
| 一致 | 一致文件数量 | 10 |

#### 3. 数据内容
- **汇总行**: 基线服务器和目标服务器的统计信息
- **详细行**: 具体的文件差异信息，包括不一致、缺失、多出的文件

### 样式说明
- **表头**: 居中对齐，加粗，灰色背景
- **数据**: 左对齐，垂直居中
- **说明文字**: 普通格式，位于表格上方

## 异常处理

### 业务异常
| 异常情况 | 错误信息 |
|----------|----------|
| flowId为空 | flowId不能为空 |
| 未找到流程结果 | 未查询到对应的流程结果数据 |
| 内容为空 | 流程结果内容为空，无法导出比对报表 |
| 解析后内容为空 | 解析后的比对内容为空，无法导出比对报表 |

### 系统异常
所有系统级异常都会被包装为`ContrastBusinessException`，并记录详细的错误日志。

## 使用示例

### Java代码示例
```java
@Autowired
private IResultMonitorService resultMonitorService;

public void exportReport(Long flowId, HttpServletResponse response) {
    try {
        resultMonitorService.exportComparisonReportByFlowId(
            flowId, 
            "生产服务器", 
            "测试服务器", 
            response
        );
    } catch (ContrastBusinessException e) {
        // 处理业务异常
        log.error("导出报表失败：{}", e.getMessage());
    }
}
```

### 前端调用示例
```javascript
// 使用表单提交方式下载文件
function exportReport(flowId, sourceServer, targetServer) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/result/exportReport';
    
    const params = {
        flowId: flowId,
        sourceServer: sourceServer || '',
        targetServer: targetServer || ''
    };
    
    for (const key in params) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = params[key];
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
```

## 注意事项

1. **权限控制**: 需要确保用户具有`comparison-results`权限
2. **文件大小**: 大量数据可能导致Excel文件较大，建议合理控制数据量
3. **超时处理**: 大数据量处理可能耗时较长，建议设置合适的超时时间
4. **内存使用**: Excel生成过程会占用一定内存，建议监控服务器资源使用情况
5. **并发控制**: 大量并发导出请求可能影响系统性能，建议实施适当的限流措施

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0 | 2025-07-22 | 初始版本，支持基本的导出比对报表功能 |
