package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.PlanEntity;

/**
 * 方案信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface PlanMapper {
    /**
     * 查询方案信息
     *
     * @param id 方案信息主键
     * @return 方案信息
     */
    PlanEntity selectPlanById(Long id);

    /**
     * 查询方案信息列表
     *
     * @param plan 方案信息
     * @return 方案信息集合
     */
    List<PlanEntity> selectPlanList(PlanEntity plan);

    /**
     * 新增方案信息
     *
     * @param plan 方案信息
     * @return 结果
     */
    int insertPlan(PlanEntity plan);

    /**
     * 修改方案信息
     *
     * @param plan 方案信息
     * @return 结果
     */
    int updatePlan(PlanEntity plan);

    /**
     * 删除方案信息
     *
     * @param id 方案信息主键
     * @return 结果
     */
    int deletePlanById(Long id);

    /**
     * 批量删除方案信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePlanByIds(Long[] ids);

    /**
     * 检查方案名称是否已存在
     *
     * @param name 方案名称
     * @return 存在的方案数量
     */
    int checkPlanNameExists(String name);

    /**
     * 检查除指定ID外的方案名称是否已存在
     *
     * @param name 方案名称
     * @param id 方案ID
     * @return 存在的方案数量
     */
    int checkPlanNameExistsExcludeId(String name, Long id);
}
