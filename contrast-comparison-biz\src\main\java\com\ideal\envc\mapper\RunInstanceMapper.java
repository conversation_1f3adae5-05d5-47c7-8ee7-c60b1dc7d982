package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.bean.RetryRuleBean;
import org.apache.ibatis.annotations.Param;

/**
 * 实例Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface RunInstanceMapper {
    /**
     * 查询实例
     *
     * @param id 实例主键
     * @return 实例
     */
    RunInstanceEntity selectRunInstanceById(Long id);

    /**
     * 查询实例列表
     *
     * @param runInstance 实例
     * @return 实例集合
     */
    List<RunInstanceEntity> selectRunInstanceList(RunInstanceEntity runInstance);

    /**
     * 新增实例
     *
     * @param runInstance 实例
     * @return 结果
     */
    int insertRunInstance(RunInstanceEntity runInstance);

    /**
     * 修改实例
     *
     * @param runInstance 实例
     * @return 结果
     */
    int updateRunInstance(RunInstanceEntity runInstance);

    /**
     * 删除实例
     *
     * @param id 实例主键
     * @return 结果
     */
    int deleteRunInstanceById(Long id);

    /**
     * 批量删除实例
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRunInstanceByIds(Long[] ids);

    /**
     * 根据流程ID查询重试规则信息
     *
     * @param flowId 流程ID
     * @return 重试规则信息
     */
    RetryRuleBean selectRetryRuleByFlowId(Long flowId);

    /**
     * 根据ID和时间戳更新运行实例的状态和结果
     * 使用乐观锁机制防止并发更新冲突
     *
     * @param id 运行实例ID
     * @param state 状态（0：运行中，1：已完成，2：终止）
     * @param result 结果（-1：运行中，0：一致/成功，1：不一致/失败）
     * @return 更新的行数，0表示并发冲突或记录不存在
     */
    int updateStateAndResultByIdAndTimestamp(@Param("id") Long id, 
                                           @Param("state") int state, 
                                           @Param("result") int result);
}
