package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class UserDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String loginName;
    private String fullName;
    private String orgCode;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Override
    public String toString() {
        return "UserDto{" +
                "id=" + id +
                ", loginName='" + loginName + '\'' +
                ", fullName='" + fullName + '\'' +
                ", orgCode='" + orgCode + '\'' +
                '}';
    }
}
