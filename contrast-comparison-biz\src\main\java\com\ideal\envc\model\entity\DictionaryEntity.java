package com.ideal.envc.model.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.ideal.snowflake.annotion.IdGenerator;

/**
 * 字典码对象 ieai_envc_dictionary
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class DictionaryEntity implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @IdGenerator
    private Long id;
    /** 字典码 */
    private String code;
    /** 字典描述 */
    private String description;
    /** 是否初始化 0：否，1：是 */
    private Integer nit;
    /** 删除标识 0：否，1：是 */
    private Integer deleted;
    /** 创建人名称 */
    private String creatorName;
    /** 创建人ID */
    private Long creatorId;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setCode(String code){
        this.code = code;
    }

    public String getCode(){
        return code;
    }

    public void setDescription(String description){
        this.description = description;
    }

    public String getDescription(){
        return description;
    }

    public void setNit(Integer nit){
        this.nit = nit;
    }

    public Integer getNit(){
        return nit;
    }

    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    public Integer getDeleted(){
        return deleted;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",code="+getCode()+
                    ",description="+getDescription()+
                    ",nit="+getNit()+
                    ",deleted="+getDeleted()+
                    ",creatorName="+getCreatorName()+
                    ",creatorId="+getCreatorId()+
                    ",createTime="+getCreateTime()+
                    ",updatorId="+getUpdatorId()+
                    ",updatorName="+getUpdatorName()+
                    ",updateTime="+getUpdateTime()+
                "]";
    }
}

