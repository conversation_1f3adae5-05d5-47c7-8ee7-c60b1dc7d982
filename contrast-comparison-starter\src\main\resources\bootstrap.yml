spring:
  profiles:
    active: sit
  application:
    name: contrast
  cloud:
    nacos:
      config:
        serverAddr: ************:8848
        file-extension: yml
        group: DEFAULT_GROUP
        namespace: 123456
      discovery:
        server-addr: ************:8848
        namespace: 123456
        group: DEFAULT_GROUP
logging:
  config: http://${spring.cloud.nacos.discovery.server-addr}/nacos/v1/cs/configs?tenant=123456&group=${spring.cloud.nacos.discovery.group}&dataId=contrast-logback-spring.xml

xxl:
  job:
    admin:
      #xxljob地址
      addresses: http://*************:8899/xxl-job-admin
    executor:
      appname: contrast-executor
      #脚本服务化xxljob执行器端口
      port: 9421
      logpath: ${user.dir}/logs
      logretentiondays: 30
    accessToken: default_token