<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.EngineActOutputMapper">

    <resultMap id="engineActOutputResult" type="com.ideal.envc.model.bean.EngineActOutputBean">
        <result property="id" column="iid" />
        <result property="reqId" column="ireq_id" />
        <result property="type" column="itype" />
        <result property="output" column="ioutput" />
    </resultMap>



    <select id="queryOutputByReqId" resultType="java.lang.Integer" resultMap="engineActOutputResult">
        SELECT iid, ireq_id,itype,ioutput FROM ieai_monitor_act_output WHERE ireq_id = #{reqId}
    </select>
</mapper>