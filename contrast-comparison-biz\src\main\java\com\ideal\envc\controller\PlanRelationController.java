package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.PlanRelationDto;
import com.ideal.envc.model.dto.PlanRelationQueryDto;
import com.ideal.envc.model.dto.PlanRelationBatchDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.PlanSystemListDto;
import com.ideal.envc.model.dto.PlanSystemQueryDto;
import com.ideal.envc.model.dto.SystemComputerNodeListDto;
import com.ideal.envc.model.dto.SystemComputerNodeQueryDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IPlanRelationService;
import com.ideal.envc.service.ISystemComputerNodeService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 方案管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/planRelation")
@MethodPermission("@dp.hasBtnPermission('programme-management')")
public class PlanRelationController {
    private final Logger logger = LoggerFactory.getLogger(PlanRelationController.class);

    private final IPlanRelationService planRelationService;
    private final ISystemComputerNodeService systemComputerNodeService;
    private final UserinfoComponent userinfoComponent;

    public PlanRelationController(IPlanRelationService planRelationService, 
                                ISystemComputerNodeService systemComputerNodeService,
                                UserinfoComponent userinfoComponent) {
        this.planRelationService = planRelationService;
        this.systemComputerNodeService = systemComputerNodeService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 查询方案与系统关系列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<PlanRelationDto>> list(@RequestBody @Validated TableQueryDto<PlanRelationQueryDto> tableQueryDto) {
        try {
            PageInfo<PlanRelationDto> list = planRelationService.selectPlanRelationList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询方案与系统关系列表失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询方案与系统关系列表系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询方案信息详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<PlanRelationDto> getPlanRelationDtoById(@RequestParam(value = "id") Long id) {
        try {
            if (id == null) {
                return R.fail(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "ID不能为空");
            }
            PlanRelationDto dto = planRelationService.selectPlanRelationById(id);
            if (dto == null) {
                return R.fail(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), ResponseCodeEnum.DATA_NOT_FOUND.getDesc());
            }
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), dto, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询方案详细信息失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询方案详细信息系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 方案绑定系统信息
     *
     * @param planRelationBatchDto 批量添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('saveSystemOfPlan')")
    public R<Void> save(@RequestBody @Validated PlanRelationBatchDto planRelationBatchDto) {
        try {
            // 获取当前登录用户信息
            UserDto userDto = userinfoComponent.getUser();
            if (planRelationService.batchInsertPlanRelation(planRelationBatchDto, userDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("方案绑定系统失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("方案绑定系统系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存方案信息
     *
     * @param planRelationDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody @Validated PlanRelationDto planRelationDto) {
        try {
            // 获取当前登录用户信息
            UserDto userDto = userinfoComponent.getUser();
            planRelationService.updatePlanRelation(planRelationDto, userDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("修改方案信息失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("修改方案信息系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 方案解绑系统
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('removeSystemOfPlan')")
    public R<Void> remove(@RequestBody Long[] ids) {
        try {
            if (ids == null || ids.length == 0) {
                return R.fail(ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), "删除ID不能为空");
            }
            // 获取当前登录用户信息
            UserDto userDto = userinfoComponent.getUser();
            planRelationService.deletePlanRelationByIds(ids, userDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("方案解绑系统失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.DELETE_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("方案解绑系统系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询方案已绑定系统列表
     *
     * @param tableQueryDto 查询条件
     * @return 方案绑定系统列表
     */
    @PostMapping("/systemList")
    public R<PageInfo<PlanSystemListDto>> systemList(@RequestBody @Validated TableQueryDto<PlanSystemQueryDto> tableQueryDto) {
        try {
            // 参数验证
            if (tableQueryDto.getQueryParam() == null || tableQueryDto.getQueryParam().getPlanId() == null) {
                return R.fail(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空");
            }

            PageInfo<PlanSystemListDto> list = planRelationService.selectPlanSystemList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询方案绑定系统列表失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询方案绑定系统列表系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询方案待绑定系统列表
     *
     * @param tableQueryDto 查询条件
     * @return 方案待绑定系统列表
     */
    @PostMapping("/pendingSystemList")
    public R<PageInfo<PlanSystemListDto>> pendingSystemList(@RequestBody @Validated TableQueryDto<PlanSystemQueryDto> tableQueryDto) {
        try {
            // 参数验证
            if (tableQueryDto.getQueryParam() == null || tableQueryDto.getQueryParam().getPlanId() == null) {
                return R.fail(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空");
            }

            PageInfo<PlanSystemListDto> list = planRelationService.selectAvailablePlanSystemList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询方案待绑定系统列表失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询方案绑绑定系统列表系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询方案系统下源目标设备信息
     *
     * @param tableQueryDto 查询条件
     * @return 源目标设备信息列表
     */
    @PostMapping("/systemNodeList")
    public R<PageInfo<SystemComputerNodeListDto>> systemNodeList(@RequestBody @Validated TableQueryDto<SystemComputerNodeQueryDto> tableQueryDto) {
        try {
            // 参数验证
            if (tableQueryDto.getQueryParam() == null || tableQueryDto.getQueryParam().getBusinessSystemId() == null) {
                return R.fail(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "业务系统ID不能为空");
            }

            PageInfo<SystemComputerNodeListDto> list = systemComputerNodeService.selectSystemComputerNodeBeanList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("查询方案系统下源目标设备信息系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询方案可绑定系统列表
     *
     * @param tableQueryDto 查询条件
     * @return 方案可绑定系统列表
     */
    @PostMapping("/availableSystemList")
    public R<PageInfo<PlanSystemListDto>> availableSystemList(@RequestBody @Validated TableQueryDto<PlanSystemQueryDto> tableQueryDto) {
        try {
            // 参数验证
            if (tableQueryDto.getQueryParam() == null || tableQueryDto.getQueryParam().getPlanId() == null) {
                return R.fail(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空");
            }

            PageInfo<PlanSystemListDto> list = planRelationService.selectAvailablePlanSystemList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询方案可绑定系统列表失败：{}", e.getMessage());
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询方案可绑定系统列表系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
