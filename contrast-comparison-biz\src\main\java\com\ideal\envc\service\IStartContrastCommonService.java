package com.ideal.envc.service;

import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.bean.StartPlanBean;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.model.dto.UserDto;

import java.util.List;

/**
 * 一致性比对启动公共服务接口
 *
 * <AUTHOR>
 */
public interface IStartContrastCommonService {

    /**
     * 通用启动处理方法
     *
     * @param startPlanList 方案信息列表
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源
     * @param startType 启动类型名称（用于日志）
     * @param userDto 用户信息
     * @return 启动结果
     */
    StartResult processStart(List<StartPlanBean> startPlanList, Long userId, String userName, Integer from, String startType, UserDto userDto) throws EngineServiceException;
}
