package com.ideal.envc.component;

import com.ideal.common.util.BeanUtils;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.system.common.component.config.AuthContext;
import com.ideal.system.common.component.model.CurrentUser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 用户信息组件单元测试
 */
@ExtendWith(MockitoExtension.class)
public class UserinfoComponentTest {

    @InjectMocks
    private UserinfoComponent userinfoComponent;

    private CurrentUser currentUser;
    private UserDto userDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setFullName("测试用户");
        
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        userDto.setLoginName("testuser");
    }

    @Test
    @DisplayName("测试获取当前用户信息 - 成功场景")
    void testGetUser_Success() {
        try (MockedStatic<AuthContext> mockedAuthContext = mockStatic(AuthContext.class);
             MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            
            // 设置Mock行为
            mockedAuthContext.when(AuthContext::getUser).thenReturn(currentUser);
            mockedBeanUtils.when(() -> BeanUtils.copy(any(CurrentUser.class), eq(UserDto.class))).thenReturn(userDto);
            
            // 执行测试方法
            UserDto result = userinfoComponent.getUser();
            
            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals("测试用户", result.getFullName());
            assertEquals("testuser", result.getLoginName());
            
            // 验证方法调用
            mockedAuthContext.verify(AuthContext::getUser);
            mockedBeanUtils.verify(() -> BeanUtils.copy(currentUser, UserDto.class));
        }
    }

    @Test
    @DisplayName("测试获取当前用户信息 - 未登录场景")
    void testGetUser_NotLoggedIn() {
        try (MockedStatic<AuthContext> mockedAuthContext = mockStatic(AuthContext.class);
             MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            
            // 设置Mock行为
            mockedAuthContext.when(AuthContext::getUser).thenReturn(null);
            
            // 执行测试方法并验证异常
            assertThrows(IllegalStateException.class, () -> userinfoComponent.getUser());
            
            // 验证方法调用
            mockedAuthContext.verify(AuthContext::getUser);
            mockedBeanUtils.verify(() -> BeanUtils.copy(any(), any()), never());
        }
    }

    @Test
    @DisplayName("测试获取当前用户信息 - BeanUtils复制失败场景")
    void testGetUser_CopyFailure() {
        try (MockedStatic<AuthContext> mockedAuthContext = mockStatic(AuthContext.class);
             MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            
            // 设置Mock行为
            mockedAuthContext.when(AuthContext::getUser).thenReturn(currentUser);
            mockedBeanUtils.when(() -> BeanUtils.copy(any(CurrentUser.class), eq(UserDto.class)))
                    .thenThrow(new RuntimeException("Bean复制失败"));
            
            // 执行测试方法并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, 
                    () -> userinfoComponent.getUser());
            
            // 验证异常信息
            assertEquals("Bean复制失败", exception.getMessage());
            
            // 验证方法调用
            mockedAuthContext.verify(AuthContext::getUser);
            mockedBeanUtils.verify(() -> BeanUtils.copy(currentUser, UserDto.class));
        }
    }

    @Test
    @DisplayName("测试获取当前用户信息 - 最小用户信息场景")
    void testGetUser_MinimalUserInfo() {
        try (MockedStatic<AuthContext> mockedAuthContext = mockStatic(AuthContext.class);
             MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            
            // 准备最小用户数据
            CurrentUser minimalUser = new CurrentUser();
            minimalUser.setId(1L);
            
            UserDto minimalDto = new UserDto();
            minimalDto.setId(1L);
            
            // 设置Mock行为
            mockedAuthContext.when(AuthContext::getUser).thenReturn(minimalUser);
            mockedBeanUtils.when(() -> BeanUtils.copy(any(CurrentUser.class), eq(UserDto.class))).thenReturn(minimalDto);
            
            // 执行测试方法
            UserDto result = userinfoComponent.getUser();
            
            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertNull(result.getFullName());
            assertNull(result.getLoginName());
            
            // 验证方法调用
            mockedAuthContext.verify(AuthContext::getUser);
            mockedBeanUtils.verify(() -> BeanUtils.copy(minimalUser, UserDto.class));
        }
    }

    @Test
    @DisplayName("测试获取当前用户信息 - AuthContext异常场景")
    void testGetUser_AuthContextException() {
        try (MockedStatic<AuthContext> mockedAuthContext = mockStatic(AuthContext.class);
             MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            
            // 设置Mock行为
            mockedAuthContext.when(AuthContext::getUser).thenThrow(new RuntimeException("认证上下文异常"));
            
            // 执行测试方法并验证异常
            RuntimeException exception = assertThrows(RuntimeException.class, 
                    () -> userinfoComponent.getUser());
            
            // 验证异常信息
            assertEquals("认证上下文异常", exception.getMessage());
            
            // 验证方法调用
            mockedAuthContext.verify(AuthContext::getUser);
            mockedBeanUtils.verify(() -> BeanUtils.copy(any(), any()), never());
        }
    }
} 