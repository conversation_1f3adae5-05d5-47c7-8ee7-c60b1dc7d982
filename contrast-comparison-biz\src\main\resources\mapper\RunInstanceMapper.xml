<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.RunInstanceMapper">

    <resultMap type="com.ideal.envc.model.entity.RunInstanceEntity" id="RunInstanceResult">
            <result property="id" column="iid"/>
            <result property="envcPlanId" column="ienvc_plan_id"/>
            <result property="envcTaskId" column="ienvc_task_id"/>
            <result property="result" column="iresult"/>
            <result property="state" column="istate"/>
            <result property="from" column="ifrom"/>
            <result property="starterName" column="istarter_name"/>
            <result property="starterId" column="istarter_id"/>
            <result property="startTime" column="istart_time"/>
            <result property="endTime" column="iend_time"/>
            <result property="elapsedTime" column="ielapsed_time"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <resultMap type="com.ideal.envc.model.bean.RetryRuleBean" id="RetryRuleResult">
        <result property="runRuleId" column="run_rule_id"/>
        <result property="runInstanceInfoId" column="run_instance_info_id"/>
        <result property="runInstanceId" column="run_instance_id"/>
        <result property="ruleContent" column="rule_content"/>
    </resultMap>

    <sql id="selectRunInstance">
        select iid, ienvc_plan_id, ienvc_task_id, iresult, istate, ifrom, istarter_name, istarter_id, istart_time, iend_time, ielapsed_time, iupdate_time
        from ieai_envc_run_instance
    </sql>

    <select id="selectRunInstanceList" parameterType="com.ideal.envc.model.entity.RunInstanceEntity" resultMap="RunInstanceResult">
        <include refid="selectRunInstance"/>
        <where>
                        <if test="envcPlanId != null ">
                            and ienvc_plan_id = #{envcPlanId}
                        </if>
                        <if test="envcTaskId != null ">
                            and ienvc_task_id = #{envcTaskId}
                        </if>
                        <if test="result != null ">
                            and iresult = #{result}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="from != null ">
                            and ifrom = #{from}
                        </if>
                        <if test="starterName != null  and starterName != ''">
                            and istarter_name like concat('%', #{starterName}, '%')
                        </if>
                        <if test="starterId != null ">
                            and istarter_id = #{starterId}
                        </if>
                        <if test="startTime != null ">
                            and istart_time = #{startTime}
                        </if>
                        <if test="endTime != null ">
                            and iend_time = #{endTime}
                        </if>
                        <if test="elapsedTime != null ">
                            and ielapsed_time = #{elapsedTime}
                        </if>
        </where>
    </select>

    <select id="selectRunInstanceById" parameterType="Long"
            resultMap="RunInstanceResult">
            <include refid="selectRunInstance"/>
            where iid = #{id}
    </select>

    <insert id="insertRunInstance" parameterType="com.ideal.envc.model.entity.RunInstanceEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_run_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,</if>
                    <if test="envcPlanId != null">ienvc_plan_id,</if>
                    <if test="envcTaskId != null">ienvc_task_id,</if>
                    <if test="result != null">iresult,</if>
                    <if test="state != null">istate,</if>
                    <if test="from != null">ifrom,</if>
                    <if test="starterName != null">istarter_name,</if>
                    <if test="starterId != null">istarter_id,</if>
                    istart_time,
                    <if test="endTime != null">iend_time,</if>
                    <if test="elapsedTime != null">ielapsed_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="envcPlanId != null">#{envcPlanId},</if>
                    <if test="envcTaskId != null">#{envcTaskId},</if>
                    <if test="result != null">#{result},</if>
                    <if test="state != null">#{state},</if>
                    <if test="from != null">#{from},</if>
                    <if test="starterName != null">#{starterName},</if>
                    <if test="starterId != null">#{starterId},</if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="endTime != null">#{endTime},</if>
                    <if test="elapsedTime != null">#{elapsedTime},</if>
        </trim>
    </insert>

    <update id="updateRunInstance" parameterType="com.ideal.envc.model.entity.RunInstanceEntity">
        update ieai_envc_run_instance
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcPlanId != null">ienvc_plan_id = #{envcPlanId},</if>
                    <if test="envcTaskId != null">ienvc_task_id = #{envcTaskId},</if>
                    <if test="result != null">iresult = #{result},</if>
                    <if test="state != null">istate = #{state},</if>
                    <if test="from != null">ifrom = #{from},</if>
                    iend_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="elapsedTime != null">ielapsed_time = #{elapsedTime},</if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteRunInstanceById" parameterType="Long">
        delete
        from ieai_envc_run_instance where iid = #{id}
    </delete>

    <delete id="deleteRunInstanceByIds" parameterType="String">
        delete from ieai_envc_run_instance where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectRetryRuleByFlowId" parameterType="Long" resultMap="RetryRuleResult">
        SELECT 
            rr.iid as run_rule_id,
            rii.iid as run_instance_info_id,
            ri.iid as run_instance_id,
            rc.icontent as rule_content
        FROM ieai_envc_run_rule rr
         JOIN ieai_envc_run_flow rf on rr.iid = rf.irun_biz_id
         JOIN ieai_envc_run_instance_info rii ON rr.ienvc_run_instance_info_id = rii.iid
        JOIN ieai_envc_run_instance ri ON rii.ienvc_run_instance_id = ri.iid
        LEFT JOIN ieai_envc_run_rule_content rc ON rc.ienvc_run_rule_id = rr.iid
        WHERE rf.iflowid =  #{flowId}
    </select>

    <!-- 根据ID和时间戳更新运行实例的状态和结果 -->
    <update id="updateStateAndResultByIdAndTimestamp">
        UPDATE ieai_envc_run_instance
        SET istate = #{state},
            iresult = #{result},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        WHERE iid = #{id}
    </update>

</mapper>