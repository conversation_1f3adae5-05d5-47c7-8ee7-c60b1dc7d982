package com.ideal.envc.model.dto;

import java.util.List;

/**
 * 系统级启动DTO
 *
 * <AUTHOR>
 */
public class SystemLevelStartDto extends StartContrastBaseDto {
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 业务系统ID列表
     */
    private List<Long> systemIds;

    /** 源中心ID */
    private Long sourceCenterId;
    /** 目标中心ID */
    private Long targetCenterId;

    /** 触发来源 */
    private Integer from;

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public List<Long> getSystemIds() {
        return systemIds;
    }

    public void setSystemIds(List<Long> systemIds) {
        this.systemIds = systemIds;
    }

    @Override
    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    @Override
    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    @Override
    public Long getTargetCenterId() {
        return targetCenterId;
    }

    @Override
    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "SystemLevelStartDto{" +
                "planId=" + planId +
                ", systemIds=" + systemIds +
                ", sourceCenterId=" + sourceCenterId +
                ", targetCenterId=" + targetCenterId +
                ", from=" + from +
                '}';
    }
}
