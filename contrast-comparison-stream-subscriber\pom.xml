<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>ieai-envcontrast-comparison</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>contrast-comparison-stream-subscriber</artifactId>
    <packaging>jar</packaging>
    <name>contrast-comparison-stream-subscriber</name>
    <version>${revision}</version>
    <dependencies>
        <!-- 消息队列的封装工程 -->
        <dependency>
            <groupId>com.ideal</groupId>
            <artifactId>ideal-message-spring-cloud-stream-publisher</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
