package com.ideal.envc.service.impl;

import com.ideal.envc.dto
        .ComputerInfoDto;
import com.ideal.envc.dto.StartComputerNodeBean;
import com.ideal.envc.dto.StartPlanBean;
import com.ideal.envc.dto.StartSystemBean;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * StartContrastBaseServiceImpl计算机信息验证功能测试
 * 测试计算机信息验证时机优化后的功能
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class StartContrastBaseServiceImplComputerValidationTest {

    @InjectMocks
    private StartContrastBaseServiceImpl startContrastBaseService;

    @Mock
    private Logger log;

    private Method validateComputerInfoBeforeProcessingMethod;

    @BeforeEach
    public void setUp() throws Exception {
        // 获取私有方法用于测试
        validateComputerInfoBeforeProcessingMethod = StartContrastBaseServiceImpl.class
            .getDeclaredMethod("validateComputerInfoBeforeProcessing", List.class, Map.class);
        validateComputerInfoBeforeProcessingMethod.setAccessible(true);
    }

    @Test
    @DisplayName("测试计算机信息预验证成功")
    public void testValidateComputerInfoBeforeProcessing_Success() throws Exception {
        // 准备测试数据
        List<StartPlanBean> startPlanBeanList = createTestPlanList();
        Map<Long, ComputerInfoDto> computerInfoDtoMap = createTestComputerInfoMap();

        // 执行测试
        assertDoesNotThrow(() -> {
            try {
                validateComputerInfoBeforeProcessingMethod.invoke(startContrastBaseService, startPlanBeanList, computerInfoDtoMap);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        // 验证日志记录
        verify(log).info("计算机信息预验证通过，共验证{}台计算机", 2);
    }

    @Test
    @DisplayName("测试计算机信息预验证失败-缺少源计算机")
    public void testValidateComputerInfoBeforeProcessing_MissingSourceComputer() throws Exception {
        // 准备测试数据
        List<StartPlanBean> startPlanBeanList = createTestPlanList();
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<Long, ComputerInfoDto>();
        // 只添加目标计算机信息，缺少源计算机信息
        computerInfoDtoMap.put(2L, createComputerInfo(2L, "target-computer"));

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            try {
                validateComputerInfoBeforeProcessingMethod.invoke(startContrastBaseService, startPlanBeanList, computerInfoDtoMap);
            } catch (Exception e) {
                if (e.getCause() instanceof IllegalArgumentException) {
                    throw (IllegalArgumentException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });

        assertTrue(exception.getMessage().contains("预验证失败，未找到计算机信息，计算机ID：1"));
        verify(log).error("预验证失败，未找到计算机信息，计算机ID：{}", 1L);
    }

    @Test
    @DisplayName("测试计算机信息预验证失败-缺少目标计算机")
    public void testValidateComputerInfoBeforeProcessing_MissingTargetComputer() throws Exception {
        // 准备测试数据
        List<StartPlanBean> startPlanBeanList = createTestPlanList();
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<Long, ComputerInfoDto>();
        // 只添加源计算机信息，缺少目标计算机信息
        computerInfoDtoMap.put(1L, createComputerInfo(1L, "source-computer"));

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            try {
                validateComputerInfoBeforeProcessingMethod.invoke(startContrastBaseService, startPlanBeanList, computerInfoDtoMap);
            } catch (Exception e) {
                if (e.getCause() instanceof IllegalArgumentException) {
                    throw (IllegalArgumentException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });

        assertTrue(exception.getMessage().contains("预验证失败，未找到计算机信息，计算机ID：2"));
        verify(log).error("预验证失败，未找到计算机信息，计算机ID：{}", 2L);
    }

    @Test
    @DisplayName("测试计算机信息预验证失败-缺少多台计算机")
    public void testValidateComputerInfoBeforeProcessing_MissingMultipleComputers() throws Exception {
        // 准备测试数据
        List<StartPlanBean> startPlanBeanList = createTestPlanList();
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<Long, ComputerInfoDto>();
        // 不添加任何计算机信息

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            try {
                validateComputerInfoBeforeProcessingMethod.invoke(startContrastBaseService, startPlanBeanList, computerInfoDtoMap);
            } catch (Exception e) {
                if (e.getCause() instanceof IllegalArgumentException) {
                    throw (IllegalArgumentException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });

        assertTrue(exception.getMessage().contains("预验证失败，未找到计算机信息"));
        assertTrue(exception.getMessage().contains("1") && exception.getMessage().contains("2"));
    }

    @Test
    @DisplayName("测试空方案列表的预验证")
    public void testValidateComputerInfoBeforeProcessing_EmptyPlanList() throws Exception {
        // 准备测试数据
        List<StartPlanBean> startPlanBeanList = new ArrayList<StartPlanBean>();
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<Long, ComputerInfoDto>();

        // 执行测试
        assertDoesNotThrow(() -> {
            try {
                validateComputerInfoBeforeProcessingMethod.invoke(startContrastBaseService, startPlanBeanList, computerInfoDtoMap);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        // 验证日志记录
        verify(log).info("计算机信息预验证通过，共验证{}台计算机", 0);
    }

    @Test
    @DisplayName("测试null节点的预验证")
    public void testValidateComputerInfoBeforeProcessing_NullNodes() throws Exception {
        // 准备测试数据 - 包含null节点
        List<StartPlanBean> startPlanBeanList = new ArrayList<StartPlanBean>();
        StartPlanBean planBean = new StartPlanBean();
        List<StartSystemBean> systems = new ArrayList<StartSystemBean>();
        StartSystemBean systemBean = new StartSystemBean();
        List<StartComputerNodeBean> nodes = new ArrayList<StartComputerNodeBean>();
        nodes.add(null); // 添加null节点
        systemBean.setComputerNodeBeans(nodes);
        systems.add(systemBean);
        planBean.setSystems(systems);
        startPlanBeanList.add(planBean);

        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<Long, ComputerInfoDto>();

        // 执行测试
        assertDoesNotThrow(() -> {
            try {
                validateComputerInfoBeforeProcessingMethod.invoke(startContrastBaseService, startPlanBeanList, computerInfoDtoMap);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        // 验证日志记录
        verify(log).info("计算机信息预验证通过，共验证{}台计算机", 0);
    }

    /**
     * 创建测试用的方案列表
     */
    private List<StartPlanBean> createTestPlanList() {
        List<StartPlanBean> planList = new ArrayList<StartPlanBean>();
        StartPlanBean planBean = new StartPlanBean();
        
        List<StartSystemBean> systems = new ArrayList<StartSystemBean>();
        StartSystemBean systemBean = new StartSystemBean();
        
        List<StartComputerNodeBean> nodes = new ArrayList<StartComputerNodeBean>();
        StartComputerNodeBean nodeBean = new StartComputerNodeBean();
        nodeBean.setSourceComputerId(1L);
        nodeBean.setTargetComputerId(2L);
        nodes.add(nodeBean);
        
        systemBean.setComputerNodeBeans(nodes);
        systems.add(systemBean);
        planBean.setSystems(systems);
        planList.add(planBean);
        
        return planList;
    }

    /**
     * 创建测试用的计算机信息映射
     */
    private Map<Long, ComputerInfoDto> createTestComputerInfoMap() {
        Map<Long, ComputerInfoDto> computerInfoMap = new HashMap<Long, ComputerInfoDto>();
        computerInfoMap.put(1L, createComputerInfo(1L, "source-computer"));
        computerInfoMap.put(2L, createComputerInfo(2L, "target-computer"));
        return computerInfoMap;
    }

    /**
     * 创建测试用的计算机信息
     */
    private ComputerInfoDto createComputerInfo(Long id, String name) {
        ComputerInfoDto computerInfo = new ComputerInfoDto();
        computerInfo.setId(id);
        computerInfo.setComputerName(name);
        computerInfo.setAgentPort(8080);
        computerInfo.setOsName("Linux");
        return computerInfo;
    }
}
