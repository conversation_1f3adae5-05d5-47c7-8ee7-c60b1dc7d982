package com.ideal.envc.startup;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.event.ApplicationStartingEvent;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * StartupMonitor单元测试
 * 测试启动监控器的各种事件处理功能
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class StartupMonitorTest {
    
    @InjectMocks
    private StartupMonitor startupMonitor;
    
    @Mock
    private ApplicationStartingEvent applicationStartingEvent;
    
    @Mock
    private ApplicationReadyEvent applicationReadyEvent;
    
    @Mock
    private ApplicationFailedEvent applicationFailedEvent;
    
    @Mock
    private ConfigurableApplicationContext applicationContext;
    
    @Mock
    private Environment environment;
    
    @BeforeEach
    public void setUp() {
        // 初始化测试环境
    }
    
    @Test
    @DisplayName("测试应用开始启动事件处理")
    public void testHandleApplicationStarting() {
        // 执行测试
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        
        // 验证状态
        assertEquals(StartupMonitor.StartupStatus.STARTING, startupMonitor.getStartupStatus());
    }
    
    @Test
    @DisplayName("测试应用就绪事件处理")
    public void testHandleApplicationReady() {
        // 准备测试数据
        when(applicationReadyEvent.getApplicationContext()).thenReturn(applicationContext);
        when(applicationContext.getApplicationName()).thenReturn("test-app");
        when(applicationContext.getBeanDefinitionCount()).thenReturn(100);
        when(applicationContext.getEnvironment()).thenReturn(environment);
        when(environment.getActiveProfiles()).thenReturn(new String[]{"test"});
        
        // 先触发启动事件
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        
        // 等待一小段时间以确保有启动耗时
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 触发就绪事件
        startupMonitor.onApplicationEvent(applicationReadyEvent);
        
        // 验证状态
        assertEquals(StartupMonitor.StartupStatus.READY, startupMonitor.getStartupStatus());
        assertTrue(startupMonitor.getStartupDuration() > 0);
    }
    
    @Test
    @DisplayName("测试应用启动失败事件处理")
    public void testHandleApplicationFailed() {
        // 准备测试数据
        RuntimeException testException = new RuntimeException("Test startup failure");
        when(applicationFailedEvent.getException()).thenReturn(testException);
        
        // 先触发启动事件
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        
        // 触发失败事件
        startupMonitor.onApplicationEvent(applicationFailedEvent);
        
        // 验证状态
        assertEquals(StartupMonitor.StartupStatus.FAILED, startupMonitor.getStartupStatus());
    }
    
    @Test
    @DisplayName("测试获取启动状态-未开始")
    public void testGetStartupStatus_NotStarted() {
        // 验证初始状态
        assertEquals(StartupMonitor.StartupStatus.NOT_STARTED, startupMonitor.getStartupStatus());
    }
    
    @Test
    @DisplayName("测试获取启动耗时-未完成启动")
    public void testGetStartupDuration_NotCompleted() {
        // 只触发启动事件，不触发就绪事件
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        
        // 验证耗时为0（因为未完成启动）
        assertEquals(0, startupMonitor.getStartupDuration());
    }
    
    @Test
    @DisplayName("测试启动状态枚举描述")
    public void testStartupStatusDescriptions() {
        assertEquals("未开始", StartupMonitor.StartupStatus.NOT_STARTED.getDescription());
        assertEquals("启动中", StartupMonitor.StartupStatus.STARTING.getDescription());
        assertEquals("已就绪", StartupMonitor.StartupStatus.READY.getDescription());
        assertEquals("启动失败", StartupMonitor.StartupStatus.FAILED.getDescription());
    }
    
    @Test
    @DisplayName("测试事件处理的时序")
    public void testEventSequence() {
        // 1. 应用开始启动
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        assertEquals(StartupMonitor.StartupStatus.STARTING, startupMonitor.getStartupStatus());
        
        // 2. 应用就绪
        when(applicationReadyEvent.getApplicationContext()).thenReturn(applicationContext);
        when(applicationContext.getApplicationName()).thenReturn("test-app");
        when(applicationContext.getBeanDefinitionCount()).thenReturn(50);
        when(applicationContext.getEnvironment()).thenReturn(environment);
        when(environment.getActiveProfiles()).thenReturn(new String[]{});
        
        startupMonitor.onApplicationEvent(applicationReadyEvent);
        assertEquals(StartupMonitor.StartupStatus.READY, startupMonitor.getStartupStatus());
    }
    
    @Test
    @DisplayName("测试启动失败后的状态")
    public void testFailureAfterStarting() {
        // 1. 应用开始启动
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        assertEquals(StartupMonitor.StartupStatus.STARTING, startupMonitor.getStartupStatus());
        
        // 2. 应用启动失败
        RuntimeException testException = new RuntimeException("Database connection failed");
        when(applicationFailedEvent.getException()).thenReturn(testException);
        
        startupMonitor.onApplicationEvent(applicationFailedEvent);
        assertEquals(StartupMonitor.StartupStatus.FAILED, startupMonitor.getStartupStatus());
    }
    
    @Test
    @DisplayName("测试多次启动事件的处理")
    public void testMultipleStartingEvents() {
        // 触发多次启动事件
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        StartupMonitor.StartupStatus firstStatus = startupMonitor.getStartupStatus();
        
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        StartupMonitor.StartupStatus secondStatus = startupMonitor.getStartupStatus();
        
        // 状态应该保持一致
        assertEquals(firstStatus, secondStatus);
        assertEquals(StartupMonitor.StartupStatus.STARTING, secondStatus);
    }
    
    @Test
    @DisplayName("测试空配置文件的处理")
    public void testEmptyActiveProfiles() {
        // 准备测试数据 - 空的活跃配置文件
        when(applicationReadyEvent.getApplicationContext()).thenReturn(applicationContext);
        when(applicationContext.getApplicationName()).thenReturn("test-app");
        when(applicationContext.getBeanDefinitionCount()).thenReturn(25);
        when(applicationContext.getEnvironment()).thenReturn(environment);
        when(environment.getActiveProfiles()).thenReturn(new String[]{});
        
        // 先触发启动事件
        startupMonitor.onApplicationEvent(applicationStartingEvent);
        
        // 触发就绪事件
        startupMonitor.onApplicationEvent(applicationReadyEvent);
        
        // 验证状态正常
        assertEquals(StartupMonitor.StartupStatus.READY, startupMonitor.getStartupStatus());
    }
}
