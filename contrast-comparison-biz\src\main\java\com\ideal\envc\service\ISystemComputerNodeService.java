package com.ideal.envc.service;

import com.ideal.envc.model.dto.SystemComputerNodeBatchDto;
import com.ideal.envc.model.dto.SystemComputerNodeDto;
import com.ideal.envc.model.dto.SystemComputerNodeListDto;
import com.ideal.envc.model.dto.SystemComputerNodeQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.exception.ContrastBusinessException;

/**
 * 系统与设备节点关系Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface ISystemComputerNodeService {
    /**
     * 查询系统与设备节点关系
     *
     * @param id 系统与设备节点关系主键
     * @return 系统与设备节点关系
     */
    SystemComputerNodeDto selectSystemComputerNodeById(Long id);

    /**
     * 查询系统与设备节点关系列表
     *
     * @param systemComputerNodeQueryDto 系统与设备节点关系
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 系统与设备节点关系集合
     */
    PageInfo<SystemComputerNodeDto> selectSystemComputerNodeList(SystemComputerNodeQueryDto systemComputerNodeQueryDto, Integer pageNum, Integer pageSize);



    /**
     * 新增系统与设备节点关系
     *
     * @param systemComputerNodeDto 系统与设备节点关系
     * @return 结果
     */
    int insertSystemComputerNode(SystemComputerNodeDto systemComputerNodeDto);

    /**
     * 修改系统与设备节点关系
     *
     * @param systemComputerNodeDto 系统与设备节点关系
     * @return 结果
     */
    int updateSystemComputerNode(SystemComputerNodeDto systemComputerNodeDto);

    /**
     * 批量删除系统与设备节点关系
     *
     * @param ids 需要删除的系统与设备节点关系主键集合
     * @return 结果
     */
    int deleteSystemComputerNodeByIds(Long[] ids);



    /**
     * 检查节点是否已存在
     *
     * @param systemComputerNodeDto 系统与设备节点关系
     * @return 是否存在
     */
    boolean checkNodeExists(SystemComputerNodeDto systemComputerNodeDto);

    /**
     * 查询系统已绑定源目标设备列表
     *
     * @param systemComputerNodeQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 系统已绑定源目标设备列表
     */
    PageInfo<SystemComputerNodeListDto> selectSystemComputerNodeBeanList(SystemComputerNodeQueryDto systemComputerNodeQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 批量绑定系统设备节点
     *
     * @param batchDto 批量绑定参数
     * @param userDto 用户信息
     * @return 绑定结果
     * @throws ContrastBusinessException 业务异常
     */
    int batchBindSystemComputerNode(SystemComputerNodeBatchDto batchDto, UserDto userDto) throws ContrastBusinessException;
}
