package com.ideal.envc.service.impl;

import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.mapper.StartContrastBaseMapper;
import com.ideal.envc.mapper.StartContrastMapper;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunRuleContentMapper;
import com.ideal.envc.model.bean.HierarchicalRunFlowBean;
import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.bean.HierarchicalRunInstanceInfoBean;
import com.ideal.envc.model.bean.HierarchicalRunRuleBean;
import com.ideal.envc.model.bean.StartComputerNodeBean;
import com.ideal.envc.model.bean.StartPlanBean;
import com.ideal.envc.model.bean.StartRuleBean;
import com.ideal.envc.model.bean.StartSystemBean;
import com.ideal.envc.model.dto.ComputerInfoDto;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleContentEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.service.IStartContrastBaseService;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import com.ideal.common.util.batch.BatchHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * 对比基础服务实现类
 *
 * <AUTHOR>
 */
@Service
public class StartContrastBaseServiceImpl implements IStartContrastBaseService {
    private final Logger log = LoggerFactory.getLogger(StartContrastBaseServiceImpl.class);
    private final StartContrastBaseMapper startContrastBaseMapper;
    private final StartContrastMapper startContrastMapper;
    private final BatchHandler batchHandler;
    private final RunInstanceMapper runInstanceMapper;
    private final RunInstanceInfoMapper runInstanceInfoMapper;
    private final RunRuleMapper runRuleMapper;
    private final RunFlowMapper runFlowMapper;
    private final RunRuleContentMapper runRuleContentMapper;

    public StartContrastBaseServiceImpl(
            StartContrastBaseMapper startContrastBaseMapper, 
            StartContrastMapper startContrastMapper, 
            BatchHandler batchHandler,
            RunInstanceMapper runInstanceMapper,
            RunInstanceInfoMapper runInstanceInfoMapper,
            RunRuleMapper runRuleMapper,
            RunFlowMapper runFlowMapper,
            RunRuleContentMapper runRuleContentMapper) {
        this.startContrastBaseMapper = startContrastBaseMapper;
        this.startContrastMapper = startContrastMapper;
        this.batchHandler = batchHandler;
        this.runInstanceMapper = runInstanceMapper;
        this.runInstanceInfoMapper = runInstanceInfoMapper;
        this.runRuleMapper = runRuleMapper;
        this.runFlowMapper = runFlowMapper;
        this.runRuleContentMapper = runRuleContentMapper;
    }

    /**
     * 根据实例ID查询层次化实例信息
     *
     * @param instanceId 实例ID
     * @return 层次化实例信息
     */
    @Override
    public HierarchicalRunInstanceBean getHierarchicalRunInstanceById(Long instanceId) {
        if (instanceId == null) {
            log.warn("实例ID不能为空");
            return null;
        }

        // 使用关联查询直接获取层次化实例信息
        HierarchicalRunInstanceBean instanceBean = startContrastBaseMapper.selectHierarchicalRunInstanceById(instanceId);
        if (instanceBean == null) {
            log.warn("未找到实例ID为{}的实例信息", instanceId);
            return null;
        }

        return instanceBean;
    }

    /**
     * 根据方案ID查询层次化实例信息列表
     *
     * @param planId 方案ID
     * @return 层次化实例信息列表
     */
    @Override
    public List<HierarchicalRunInstanceBean> getHierarchicalRunInstancesByPlanId(Long planId) {
        if (planId == null) {
            log.warn("方案ID不能为空");
            return new ArrayList<>();
        }

        // 使用关联查询直接获取层次化实例信息列表
        List<HierarchicalRunInstanceBean> instanceBeans = startContrastBaseMapper.selectHierarchicalRunInstancesByPlanId(planId);
        if (instanceBeans == null || instanceBeans.isEmpty()) {
            log.warn("未找到方案ID为{}的实例信息", planId);
            return new ArrayList<>();
        }

        return instanceBeans;
    }

    /**
     * 根据任务ID查询层次化实例信息列表
     *
     * @param taskId 任务ID
     * @return 层次化实例信息列表
     */
    @Override
    public List<HierarchicalRunInstanceBean> getHierarchicalRunInstancesByTaskId(Long taskId) {
        if (taskId == null) {
            log.warn("任务ID不能为空");
            return new ArrayList<>();
        }

        // 使用关联查询直接获取层次化实例信息列表
        List<HierarchicalRunInstanceBean> instanceBeans = startContrastBaseMapper.selectHierarchicalRunInstancesByTaskId(taskId);
        if (instanceBeans == null || instanceBeans.isEmpty()) {
            log.warn("未找到任务ID为{}的实例信息", taskId);
            return new ArrayList<>();
        }

        return instanceBeans;
    }

    /**
     * 存储运行实例相关表数据
     *
     * @param startPlanBeanList 方案信息列表
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源（1：周期触发，2：手动触发，3：重试）
     * @param taskId 任务ID（方案启动和重试无任务ID）
     * @param centerMap 中心映射
     * @param computerInfoDtoMap 计算机信息映射
     * @return 运行实例列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<HierarchicalRunInstanceBean> saveRunInstanceData(List<StartPlanBean> startPlanBeanList, Long userId, String userName, Integer from, Long taskId, Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap) {
        // 参数校验
        if (CollectionUtils.isEmpty(startPlanBeanList)) {
            log.warn("方案信息列表为空，无法存储运行实例数据");
            return new ArrayList<>();
        }
        
        validateSaveRunInstanceDataParams(userId, userName, from, centerMap, computerInfoDtoMap);
        
        log.info("开始存储运行实例相关表数据，方案数量：{}", startPlanBeanList.size());

        // 初始化数据容器
        RunInstanceDataContainer dataContainer = new RunInstanceDataContainer();
        List<HierarchicalRunInstanceBean> hierarchicalInstanceList = new ArrayList<>();

        // 处理方案数据
        for (StartPlanBean planBean : startPlanBeanList) {
            if (planBean == null) {
                log.warn("方案为空，跳过处理");
                continue;
            }
            processPlanData(planBean, userId, userName, from, taskId, centerMap, computerInfoDtoMap, dataContainer, hierarchicalInstanceList);
        }

        // 批量保存数据
        batchSaveRunInstanceData(dataContainer);

        log.info("存储运行实例相关表数据完成，运行实例数量：{}", dataContainer.getRunInstanceList().size());
        return hierarchicalInstanceList;
    }

    /**
     * 验证保存运行实例数据的参数
     *
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源
     * @param centerMap 中心映射
     * @param computerInfoDtoMap 计算机信息映射
     */
    private void validateSaveRunInstanceDataParams(Long userId, String userName, Integer from, Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap) {
        if (userId == null) {
            log.error("用户ID不能为空");
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(userName)) {
            log.error("用户名称不能为空");
            throw new IllegalArgumentException("用户名称不能为空");
        }
        if (from == null) {
            log.error("触发来源不能为空");
            throw new IllegalArgumentException("触发来源不能为空");
        }
        if (centerMap == null) {
            log.error("中心映射不能为空");
            throw new IllegalArgumentException("中心映射不能为空");
        }
        if (computerInfoDtoMap == null) {
            log.error("计算机信息映射不能为空");
            throw new IllegalArgumentException("计算机信息映射不能为空");
        }
    }

    /**
     * 处理方案数据
     *
     * @param planBean 方案信息
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源
     * @param taskId 任务ID
     * @param centerMap 中心映射
     * @param computerInfoDtoMap 计算机信息映射
     * @param dataContainer 数据容器
     * @param hierarchicalInstanceList 层次化实例列表
     */
    private void processPlanData(StartPlanBean planBean, Long userId, String userName, Integer from, Long taskId, Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap, RunInstanceDataContainer dataContainer, List<HierarchicalRunInstanceBean> hierarchicalInstanceList) {
        // 创建运行实例
        RunInstanceEntity runInstance = createRunInstance(planBean, userId, userName, from, taskId);
        dataContainer.addRunInstance(runInstance);

        // 创建层次化实例对象
        HierarchicalRunInstanceBean hierarchicalInstance = createHierarchicalInstance(runInstance);
        if (hierarchicalInstance == null) {
            return;
        }
        hierarchicalInstanceList.add(hierarchicalInstance);

        // 处理系统数据
        if (CollectionUtils.isEmpty(planBean.getSystems())) {
            log.warn("方案{}未关联系统，跳过创建实例详情", planBean.getId());
            return;
        }

        for (StartSystemBean systemBean : planBean.getSystems()) {
            if (systemBean == null) {
                log.warn("系统为空，跳过处理");
                continue;
            }
            processSystemData(systemBean, planBean, runInstance, centerMap, computerInfoDtoMap, dataContainer, hierarchicalInstance, userId, userName);
        }
    }

    /**
     * 创建运行实例
     *
     * @param planBean 方案信息
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源
     * @param taskId 任务ID
     * @return 运行实例
     */
    private RunInstanceEntity createRunInstance(StartPlanBean planBean, Long userId, String userName, Integer from, Long taskId) {
        RunInstanceEntity runInstance = new RunInstanceEntity();
        runInstance.setId(SnowflakeIdWorker.generateId());
        runInstance.setEnvcPlanId(planBean.getId());
        runInstance.setEnvcTaskId(taskId);
        runInstance.setResult(-1);
        runInstance.setState(0);
        runInstance.setFrom(from);
        runInstance.setStarterName(userName);
        runInstance.setStarterId(userId);
        runInstance.setStartTime(new Date());
        return runInstance;
    }

    /**
     * 创建层次化实例对象
     *
     * @param runInstance 运行实例
     * @return 层次化实例对象
     */
    private HierarchicalRunInstanceBean createHierarchicalInstance(RunInstanceEntity runInstance) {
        HierarchicalRunInstanceBean hierarchicalInstance = HierarchicalRunInstanceBean.fromEntity(runInstance);
        if (hierarchicalInstance == null) {
            log.error("创建层次化实例对象失败，跳过处理");
            return null;
        }
        List<HierarchicalRunInstanceInfoBean> hierarchicalInfoList = new ArrayList<>();
        hierarchicalInstance.setInstanceInfoList(hierarchicalInfoList);
        return hierarchicalInstance;
    }

    /**
     * 处理系统数据
     *
     * @param systemBean 系统信息
     * @param planBean 方案信息
     * @param runInstance 运行实例
     * @param centerMap 中心映射
     * @param computerInfoDtoMap 计算机信息映射
     * @param dataContainer 数据容器
     * @param hierarchicalInstance 层次化实例
     * @param userId 用户ID
     * @param userName 用户名称
     */
    private void processSystemData(StartSystemBean systemBean, StartPlanBean planBean, RunInstanceEntity runInstance, Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap, RunInstanceDataContainer dataContainer, HierarchicalRunInstanceBean hierarchicalInstance, Long userId, String userName) {
        if (CollectionUtils.isEmpty(systemBean.getComputerNodeBeans())) {
            log.warn("系统{}未关联节点，跳过创建实例详情", systemBean.getBusinessSystemId());
            return;
        }

        for (StartComputerNodeBean nodeBean : systemBean.getComputerNodeBeans()) {
            if (nodeBean == null) {
                log.warn("节点为空，跳过处理");
                continue;
            }
            processNodeData(nodeBean, systemBean, planBean, runInstance, centerMap, computerInfoDtoMap, dataContainer, hierarchicalInstance, userId, userName);
        }
    }

    /**
     * 处理节点数据
     *
     * @param nodeBean 节点信息
     * @param systemBean 系统信息
     * @param planBean 方案信息
     * @param runInstance 运行实例
     * @param centerMap 中心映射
     * @param computerInfoDtoMap 计算机信息映射
     * @param dataContainer 数据容器
     * @param hierarchicalInstance 层次化实例
     * @param userId 用户ID
     * @param userName 用户名称
     */
    private void processNodeData(StartComputerNodeBean nodeBean, StartSystemBean systemBean, StartPlanBean planBean, RunInstanceEntity runInstance, Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap, RunInstanceDataContainer dataContainer, HierarchicalRunInstanceBean hierarchicalInstance, Long userId, String userName) {
        // 验证节点必要字段
        if (!validateNodeRequiredFields(nodeBean)) {
            return;
        }

        // 创建实例详情
        RunInstanceInfoEntity runInstanceInfo = createRunInstanceInfo(nodeBean, systemBean, planBean, runInstance, centerMap, computerInfoDtoMap);
        dataContainer.addRunInstanceInfo(runInstanceInfo);

        // 创建层次化实例详情对象
        HierarchicalRunInstanceInfoBean hierarchicalInfo = createHierarchicalInstanceInfo(runInstanceInfo);
        if (hierarchicalInfo == null) {
            return;
        }
        hierarchicalInstance.getInstanceInfoList().add(hierarchicalInfo);

        // 处理规则数据
        if (CollectionUtils.isEmpty(nodeBean.getRules())) {
            log.warn("节点{}未关联规则，跳过创建规则运行", nodeBean.getId());
            return;
        }

        for (StartRuleBean ruleBean : nodeBean.getRules()) {
            if (ruleBean == null) {
                log.warn("规则为空，跳过处理");
                continue;
            }
            processRuleData(ruleBean, runInstanceInfo, dataContainer, hierarchicalInfo, userId, userName);
        }
    }

    /**
     * 验证节点必要字段
     *
     * @param nodeBean 节点信息
     * @return 验证结果
     */
    private boolean validateNodeRequiredFields(StartComputerNodeBean nodeBean) {
        if (nodeBean.getSourceCenterId() == null || nodeBean.getTargetCenterId() == null ||
            nodeBean.getSourceComputerId() == null || nodeBean.getTargetComputerId() == null) {
            log.warn("节点必要字段为空，跳过处理：{}", nodeBean);
            return false;
        }
        return true;
    }

    /**
     * 创建实例详情
     *
     * @param nodeBean 节点信息
     * @param systemBean 系统信息
     * @param planBean 方案信息
     * @param runInstance 运行实例
     * @param centerMap 中心映射
     * @param computerInfoDtoMap 计算机信息映射
     * @return 实例详情
     */
    private RunInstanceInfoEntity createRunInstanceInfo(StartComputerNodeBean nodeBean, StartSystemBean systemBean, StartPlanBean planBean, RunInstanceEntity runInstance, Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap) {
        // 获取中心名称
        String sourceCenterName = getCenterName(nodeBean.getSourceCenterId(), centerMap);
        String targetCenterName = getCenterName(nodeBean.getTargetCenterId(), centerMap);

        // 创建实例详情
        RunInstanceInfoEntity runInstanceInfo = new RunInstanceInfoEntity();
        runInstanceInfo.setId(SnowflakeIdWorker.generateId());
        runInstanceInfo.setEnvcRunInstanceId(runInstance.getId());
        runInstanceInfo.setEnvcPlanId(planBean.getId());
        runInstanceInfo.setBusinessSystemId(systemBean.getBusinessSystemId());
        runInstanceInfo.setSourceCenterId(nodeBean.getSourceCenterId());
        runInstanceInfo.setSourceCenterName(sourceCenterName);
        runInstanceInfo.setTargetCenterId(nodeBean.getTargetCenterId());
        runInstanceInfo.setTargetCenterName(targetCenterName);
        runInstanceInfo.setSourceComputerId(nodeBean.getSourceComputerId());
        runInstanceInfo.setSourceComputerIp(nodeBean.getSourceComputerIp());
        runInstanceInfo.setTargetComputerId(nodeBean.getTargetComputerId());
        runInstanceInfo.setTargetComputerIp(nodeBean.getTargetComputerIp());

        // 设置计算机信息
        setComputerInfo(runInstanceInfo, nodeBean, computerInfoDtoMap);

        runInstanceInfo.setResult(-1);
        runInstanceInfo.setState(0);
        runInstanceInfo.setStoreTime(new Date());
        return runInstanceInfo;
    }

    /**
     * 获取中心名称
     *
     * @param centerId 中心ID
     * @param centerMap 中心映射
     * @return 中心名称
     */
    private String getCenterName(Long centerId, Map<Long, String> centerMap) {
        String centerName = centerMap.get(centerId);
        if (StringUtils.isBlank(centerName)) {
            log.warn("未找到中心名称，中心ID：{}", centerId);
            centerName = "未知中心";
        }
        return centerName;
    }

    /**
     * 设置计算机信息
     *
     * @param runInstanceInfo 实例详情
     * @param nodeBean 节点信息
     * @param computerInfoDtoMap 计算机信息映射
     */
    private void setComputerInfo(RunInstanceInfoEntity runInstanceInfo, StartComputerNodeBean nodeBean, Map<Long, ComputerInfoDto> computerInfoDtoMap) {
        // 获取源计算机信息
        ComputerInfoDto sourceComputerInfo = computerInfoDtoMap.get(nodeBean.getSourceComputerId());
        if (sourceComputerInfo == null) {
            log.error("未找到源计算机信息，计算机ID：{}", nodeBean.getSourceComputerId());
            throw new IllegalArgumentException("未找到源计算机信息，计算机ID：" + nodeBean.getSourceComputerId());
        }
        runInstanceInfo.setSourceComputerPort(sourceComputerInfo.getAgentPort());
        runInstanceInfo.setSourceComputerOs(sourceComputerInfo.getOsName());

        // 获取目标计算机信息
        ComputerInfoDto targetComputerInfo = computerInfoDtoMap.get(nodeBean.getTargetComputerId());
        if (targetComputerInfo == null) {
            log.error("未找到目标计算机信息，计算机ID：{}", nodeBean.getTargetComputerId());
            throw new IllegalArgumentException("未找到目标计算机信息，计算机ID：" + nodeBean.getTargetComputerId());
        }
        runInstanceInfo.setTargetComputerPort(targetComputerInfo.getAgentPort());
        runInstanceInfo.setTargetComputerOs(targetComputerInfo.getOsName());
    }

    /**
     * 创建层次化实例详情对象
     *
     * @param runInstanceInfo 实例详情
     * @return 层次化实例详情对象
     */
    private HierarchicalRunInstanceInfoBean createHierarchicalInstanceInfo(RunInstanceInfoEntity runInstanceInfo) {
        HierarchicalRunInstanceInfoBean hierarchicalInfo = HierarchicalRunInstanceInfoBean.fromEntity(runInstanceInfo);
        if (hierarchicalInfo == null) {
            log.error("创建层次化实例详情对象失败，跳过处理");
            return null;
        }
        List<HierarchicalRunRuleBean> hierarchicalRuleList = new ArrayList<>();
        hierarchicalInfo.setRuleList(hierarchicalRuleList);
        return hierarchicalInfo;
    }

    /**
     * 处理规则数据
     *
     * @param ruleBean 规则信息
     * @param runInstanceInfo 实例详情
     * @param dataContainer 数据容器
     * @param hierarchicalInfo 层次化实例详情
     * @param userId 用户ID
     * @param userName 用户名称
     */
    private void processRuleData(StartRuleBean ruleBean, RunInstanceInfoEntity runInstanceInfo, RunInstanceDataContainer dataContainer, HierarchicalRunInstanceInfoBean hierarchicalInfo, Long userId, String userName) {
        // 验证规则必要字段
        if (!validateRuleRequiredFields(ruleBean)) {
            return;
        }

        // 创建节点规则运行
        RunRuleEntity runRule = createRunRule(ruleBean, runInstanceInfo, userId, userName);
        dataContainer.addRunRule(runRule);

        // 创建节点规则流程
        RunFlowEntity flow = createRunFlow(ruleBean, runRule, userId, userName);
        dataContainer.addRunFlow(flow);

        // 创建层次化规则对象
        HierarchicalRunRuleBean hierarchicalRule = createHierarchicalRule(runRule, flow);
        if (hierarchicalRule != null) {
            hierarchicalInfo.getRuleList().add(hierarchicalRule);
        }

        // 如果有规则内容，创建节点规则内容
        if (ruleBean.getContentBean() != null && StringUtils.isNotBlank(ruleBean.getContentBean().getContent())) {
            RunRuleContentEntity ruleContent = createRunRuleContent(ruleBean, runRule);
            dataContainer.addRunRuleContent(ruleContent);
        }
    }

    /**
     * 验证规则必要字段
     *
     * @param ruleBean 规则信息
     * @return 验证结果
     */
    private boolean validateRuleRequiredFields(StartRuleBean ruleBean) {
        if (ruleBean.getModel() == null || ruleBean.getType() == null || 
            StringUtils.isBlank(ruleBean.getPath()) || StringUtils.isBlank(ruleBean.getSourcePath())) {
            log.warn("规则必要字段为空，跳过处理：{}", ruleBean);
            return false;
        }
        return true;
    }

    /**
     * 创建节点规则运行
     *
     * @param ruleBean 规则信息
     * @param runInstanceInfo 实例详情
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 节点规则运行
     */
    private RunRuleEntity createRunRule(StartRuleBean ruleBean, RunInstanceInfoEntity runInstanceInfo, Long userId, String userName) {
        RunRuleEntity runRule = new RunRuleEntity();
        runRule.setId(SnowflakeIdWorker.generateId());
        runRule.setEnvcRunInstanceInfoId(runInstanceInfo.getId());
        runRule.setModel(ruleBean.getModel());
        runRule.setType(ruleBean.getType());
        runRule.setPath(ruleBean.getPath());
        runRule.setSourcePath(ruleBean.getSourcePath());
        runRule.setEncode(ruleBean.getEncode());
        runRule.setWay(ruleBean.getWay());
        runRule.setRuleType(ruleBean.getRuleType());
        runRule.setEnabled(ruleBean.getEnabled());
        runRule.setChildLevel(ruleBean.getChildLevel());
        runRule.setCreatorId(userId);
        runRule.setCreatorName(userName);
        runRule.setResult(-1);
        runRule.setState(0);
        runRule.setCreateTime(new Date());
        return runRule;
    }

    /**
     * 创建节点规则流程
     *
     * @param ruleBean 规则信息
     * @param runRule 节点规则运行
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 节点规则流程
     */
    private RunFlowEntity createRunFlow(StartRuleBean ruleBean, RunRuleEntity runRule, Long userId, String userName) {
        RunFlowEntity flow = new RunFlowEntity();
        flow.setId(SnowflakeIdWorker.generateId());
        flow.setRunBizId(runRule.getId());
        flow.setModel(ruleBean.getModel());
        flow.setCreatorId(userId);
        flow.setCreatorName(userName);
        flow.setState(0);
        flow.setElapsedTime(null);
        flow.setRet(null);
        flow.setUpdateOrderTime(0L);
        return flow;
    }

    /**
     * 创建层次化规则对象
     *
     * @param runRule 节点规则运行
     * @param flow 节点规则流程
     * @return 层次化规则对象
     */
    private HierarchicalRunRuleBean createHierarchicalRule(RunRuleEntity runRule, RunFlowEntity flow) {
        HierarchicalRunRuleBean hierarchicalRule = HierarchicalRunRuleBean.fromEntity(runRule);
        if (hierarchicalRule == null) {
            log.error("创建层次化规则对象失败，跳过处理");
            return null;
        }
        HierarchicalRunFlowBean hierarchicalFlow = HierarchicalRunFlowBean.fromEntity(flow);
        if (hierarchicalFlow == null) {
            log.error("创建层次化流程对象失败，跳过处理");
            return null;
        }
        hierarchicalRule.setRuleFlow(hierarchicalFlow);
        return hierarchicalRule;
    }

    /**
     * 创建节点规则内容
     *
     * @param ruleBean 规则信息
     * @param runRule 节点规则运行
     * @return 节点规则内容
     */
    private RunRuleContentEntity createRunRuleContent(StartRuleBean ruleBean, RunRuleEntity runRule) {
        RunRuleContentEntity ruleContent = new RunRuleContentEntity();
        ruleContent.setId(SnowflakeIdWorker.generateId());
        ruleContent.setEnvcRunRuleId(runRule.getId());
        ruleContent.setContent(ruleBean.getContentBean().getContent());
        return ruleContent;
    }

    /**
     * 批量保存运行实例数据
     *
     * @param dataContainer 数据容器
     */
    private void batchSaveRunInstanceData(RunInstanceDataContainer dataContainer) {
        int batchSize = 500;

        // 批量处理运行实例
        if (!dataContainer.getRunInstanceList().isEmpty()) {
            log.info("批量插入运行实例，数量：{}", dataContainer.getRunInstanceList().size());
            batchHandler.batchData(dataContainer.getRunInstanceList(), runInstanceMapper::insertRunInstance, batchSize);
        }

        // 批量处理运行实例详情
        if (!dataContainer.getRunInstanceInfoList().isEmpty()) {
            log.info("批量插入运行实例详情，数量：{}", dataContainer.getRunInstanceInfoList().size());
            batchHandler.batchData(dataContainer.getRunInstanceInfoList(), runInstanceInfoMapper::insertRunInstanceInfo, batchSize);
        }

        // 批量处理运行规则
        if (!dataContainer.getRunRuleList().isEmpty()) {
            log.info("批量插入运行规则，数量：{}", dataContainer.getRunRuleList().size());
            batchHandler.batchData(dataContainer.getRunRuleList(), runRuleMapper::insertRunRule, batchSize);
        }

        // 批量处理运行规则内容
        if (!dataContainer.getRunRuleContentList().isEmpty()) {
            log.info("批量插入运行规则内容，数量：{}", dataContainer.getRunRuleContentList().size());
            batchHandler.batchData(dataContainer.getRunRuleContentList(), runRuleContentMapper::insertRunRuleContent, batchSize);
        }

        // 批量处理运行流程
        if (!dataContainer.getRunFlowList().isEmpty()) {
            log.info("批量插入运行流程，数量：{}", dataContainer.getRunFlowList().size());
            batchHandler.batchData(dataContainer.getRunFlowList(), runFlowMapper::insertRunFlow, batchSize);
        }
    }

    /**
     * 运行实例数据容器，用于存储批量处理的数据
     */
    private static class RunInstanceDataContainer {
        private final List<RunInstanceEntity> runInstanceList = new ArrayList<>();
        private final List<RunInstanceInfoEntity> runInstanceInfoList = new ArrayList<>();
        private final List<RunRuleEntity> runRuleList = new ArrayList<>();
        private final List<RunRuleContentEntity> runRuleContentList = new ArrayList<>();
        private final List<RunFlowEntity> runFlowList = new ArrayList<>();

        public void addRunInstance(RunInstanceEntity runInstance) {
            runInstanceList.add(runInstance);
        }

        public void addRunInstanceInfo(RunInstanceInfoEntity runInstanceInfo) {
            runInstanceInfoList.add(runInstanceInfo);
        }

        public void addRunRule(RunRuleEntity runRule) {
            runRuleList.add(runRule);
        }

        public void addRunRuleContent(RunRuleContentEntity runRuleContent) {
            runRuleContentList.add(runRuleContent);
        }

        public void addRunFlow(RunFlowEntity runFlow) {
            runFlowList.add(runFlow);
        }

        public List<RunInstanceEntity> getRunInstanceList() {
            return runInstanceList;
        }

        public List<RunInstanceInfoEntity> getRunInstanceInfoList() {
            return runInstanceInfoList;
        }

        public List<RunRuleEntity> getRunRuleList() {
            return runRuleList;
        }

        public List<RunRuleContentEntity> getRunRuleContentList() {
            return runRuleContentList;
        }

        public List<RunFlowEntity> getRunFlowList() {
            return runFlowList;
        }
    }

    /**
     * 初始下发更新状态
     * 更改运行实例及下属子表中数据状态，将状态重置为初始状态
     * 主要是修改ieai_envc_run_instance表iresult=-1，istate=0
     * ieai_envc_run_instance_info表的iresult=-1，istate=0
     * ieai_envc_run_rule的iresult=-1，istate=0
     * 表ieai_envc_run_flow的istate=0
     *
     * @param instanceId 实例ID
     * @return 更新结果，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInstanceStatusToInitial(Long instanceId) {
        if (instanceId == null) {
            log.error("实例ID不能为空");
            return false;
        }

        // 针对死锁异常的简单重试机制
        int maxRetries = 3;
        for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
                return executeUpdateInstanceStatusToInitial(instanceId);
            } catch (org.springframework.dao.DeadlockLoserDataAccessException e) {
                log.warn("发生数据库死锁，实例ID：{}，重试次数：{}/{}", instanceId, attempt + 1, maxRetries);
                if (attempt == maxRetries - 1) {
                    log.error("达到最大重试次数，更新实例状态失败，实例ID：{}", instanceId, e);
                    throw e;
                }
                // 简单延迟后重试
                try {
                    // 使用指数退避策略：50ms, 100ms, 200ms
                    Thread.sleep(50 * (1 << attempt));
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试等待被中断", ie);
                }
            }
        }
        return false;
    }
    
    /**
     * 执行实际的状态更新操作
     *
     * @param instanceId 实例ID
     * @return 更新结果
     */
    private boolean executeUpdateInstanceStatusToInitial(Long instanceId) {
        log.info("开始更新实例状态为初始状态，实例ID：{}", instanceId);

        try {
            // 1. 更新实例表状态
            int instanceRows = startContrastBaseMapper.updateRunInstanceStatusToInitial(instanceId);
            if (instanceRows <= 0) {
                log.warn("未找到实例ID为{}的记录", instanceId);
                return false;
            }

            // 2. 更新实例详情表状态
            int instanceInfoRows = startContrastBaseMapper.updateRunInstanceInfoStatusToInitial(instanceId);
            log.info("更新实例详情表状态，影响行数：{}", instanceInfoRows);

            // 3. 更新规则表状态（已优化为JOIN更新）
            int ruleRows = startContrastBaseMapper.updateRunRuleStatusToInitial(instanceId);
            log.info("更新规则表状态，影响行数：{}", ruleRows);

            // 4. 更新流程表状态（已优化为JOIN更新）
            int flowRows = startContrastBaseMapper.updateRunFlowStatusToInitial(instanceId);
            log.info("更新流程表状态，影响行数：{}", flowRows);

            log.info("实例ID为{}的状态已成功更新为初始状态", instanceId);
            return true;
        } catch (Exception e) {
            log.error("更新实例状态异常，实例ID：{}", instanceId, e);
            throw e;
        }
    }
}
