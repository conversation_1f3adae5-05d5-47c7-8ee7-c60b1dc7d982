package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.NodeRuleContentEntity;

/**
 * 节点关系规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface NodeRuleContentMapper {
    /**
     * 查询节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 节点关系规则
     */
    NodeRuleContentEntity selectNodeRuleContentById(Long id);

    /**
     * 查询节点关系规则列表
     *
     * @param nodeRuleContent 节点关系规则
     * @return 节点关系规则集合
     */
    List<NodeRuleContentEntity> selectNodeRuleContentList(NodeRuleContentEntity nodeRuleContent);

    /**
     * 新增节点关系规则
     *
     * @param nodeRuleContentEntity 节点关系规则
     * @return 结果
     */
    int insertNodeRuleContent(NodeRuleContentEntity nodeRuleContentEntity);

    /**
     * 修改节点关系规则
     *
     * @param nodeRuleContent 节点关系规则
     * @return 结果
     */
    int updateNodeRuleContent(NodeRuleContentEntity nodeRuleContent);

    /**
     * 删除节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 结果
     */
    int deleteNodeRuleContentById(Long id);

    /**
     * 批量删除节点关系规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteNodeRuleContentByIds(Long[] ids);

    /**
     * 根据节点关系 ID 集合批量删除节点关系规则
     *
     * @param envcNodeRelationIds 节点关系 ID 集合
     * @return 结果
     */
    int deleteNodeRuleContentByNodeRelationIds(Long[] envcNodeRelationIds);

    /**
     * 根据系统设备节点 ID 集合批量删除节点关系规则
     *
     * @param systemComputerNodeIds 系统设备节点 ID 集合
     * @return 结果
     */
    int deleteNodeRuleContentBySystemComputerNodeIds(Long[] systemComputerNodeIds);
}
