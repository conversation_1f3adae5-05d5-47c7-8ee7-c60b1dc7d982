<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.RunRuleMapper">

    <resultMap type="com.ideal.envc.model.entity.RunRuleEntity" id="RunRuleResult">
            <result property="id" column="iid"/>
            <result property="envcRunInstanceInfoId" column="ienvc_run_instance_info_id"/>
            <result property="model" column="imodel"/>
            <result property="type" column="itype"/>
            <result property="path" column="ipath"/>
            <result property="sourcePath" column="isource_path"/>
            <result property="encode" column="iencode"/>
            <result property="way" column="iway"/>
            <result property="ruleType" column="irule_type"/>
            <result property="enabled" column="ienabled"/>
            <result property="childLevel" column="ichild_level"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="endTime" column="iend_time"/>
            <result property="result" column="iresult"/>
            <result property="state" column="istate"/>
            <result property="elapsedTime" column="ielapsed_time"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectRunRule">
        select iid, ienvc_run_instance_info_id, imodel, itype, ipath, isource_path, iencode, iway, irule_type, ienabled, ichild_level, icreator_id, icreator_name, icreate_time, iend_time, iresult, istate, ielapsed_time, iupdate_time
        from ieai_envc_run_rule
    </sql>

    <select id="selectRunRuleList" parameterType="com.ideal.envc.model.entity.RunRuleEntity" resultMap="RunRuleResult">
        <include refid="selectRunRule"/>
        <where>
                        <if test="envcRunInstanceInfoId != null ">
                            and ienvc_run_instance_info_id = #{envcRunInstanceInfoId}
                        </if>
                        <if test="model != null ">
                            and imodel = #{model}
                        </if>
                        <if test="type != null ">
                            and itype = #{type}
                        </if>
                        <if test="path != null  and path != ''">
                            and ipath = #{path}
                        </if>
                        <if test="sourcePath != null  and sourcePath != ''">
                            and isource_path = #{sourcePath}
                        </if>
                        <if test="encode != null  and encode != ''">
                            and iencode = #{encode}
                        </if>
                        <if test="way != null ">
                            and iway = #{way}
                        </if>
                        <if test="ruleType != null">
                            and irule_type = #{ruleType}
                        </if>
                        <if test="enabled != null ">
                            and ienabled = #{enabled}
                        </if>
                        <if test="childLevel != null ">
                            and ichild_level = #{childLevel}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="endTime != null ">
                            and iend_time = #{endTime}
                        </if>
                        <if test="result != null ">
                            and iresult = #{result}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="elapsedTime != null ">
                            and ielapsed_time = #{elapsedTime}
                        </if>
        </where>
    </select>

    <select id="selectRunRuleById" parameterType="Long"
            resultMap="RunRuleResult">
            <include refid="selectRunRule"/>
            where iid = #{id}
    </select>

    <!-- 根据ID列表批量查询节点规则结果 -->
    <select id="selectRunRuleByIds" resultMap="RunRuleResult">
        <include refid="selectRunRule"/>
        where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertRunRule" parameterType="com.ideal.envc.model.entity.RunRuleEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_run_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,</if>
                    <if test="envcRunInstanceInfoId != null">ienvc_run_instance_info_id,</if>
                    <if test="model != null">imodel,</if>
                    <if test="type != null">itype,</if>
                    <if test="path != null">ipath,</if>
                    <if test="sourcePath != null">isource_path,</if>
                    <if test="encode != null">iencode,</if>
                    <if test="way != null">iway,</if>
                    <if test="ruleType != null">irule_type,</if>
                    <if test="enabled != null">ienabled,</if>
                    <if test="childLevel != null">ichild_level,</if>
                    <if test="creatorId != null">icreator_id,</if>
                    <if test="creatorName != null">icreator_name,</if>
                    icreate_time,
                    iupdate_time,
                    <if test="endTime != null">iend_time,</if>
                    <if test="result != null">iresult,</if>
                    <if test="state != null">istate,</if>
                    <if test="elapsedTime != null">ielapsed_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="envcRunInstanceInfoId != null">#{envcRunInstanceInfoId},</if>
                    <if test="model != null">#{model},</if>
                    <if test="type != null">#{type},</if>
                    <if test="path != null">#{path},</if>
                    <if test="sourcePath != null">#{sourcePath},</if>
                    <if test="encode != null">#{encode},</if>
                    <if test="way != null">#{way},</if>
                    <if test="ruleType != null">#{ruleType},</if>
                    <if test="enabled != null">#{enabled},</if>
                    <if test="childLevel != null">#{childLevel},</if>
                    <if test="creatorId != null">#{creatorId},</if>
                    <if test="creatorName != null">#{creatorName},</if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="endTime != null">#{endTime},</if>
                    <if test="result != null">#{result},</if>
                    <if test="state != null">#{state},</if>
                    <if test="elapsedTime != null">#{elapsedTime},</if>
        </trim>
    </insert>

    <update id="updateRunRule" parameterType="com.ideal.envc.model.entity.RunRuleEntity">
        update ieai_envc_run_rule
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcRunInstanceInfoId != null">ienvc_run_instance_info_id =
                        #{envcRunInstanceInfoId},
                    </if>
                    <if test="model != null">imodel =
                        #{model},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
                    <if test="path != null">ipath =
                        #{path},
                    </if>
                    <if test="sourcePath != null">isource_path =
                        #{sourcePath},
                    </if>
                    <if test="encode != null">iencode =
                        #{encode},
                    </if>
                    <if test="way != null">iway =
                        #{way},
                    </if>
                    <if test="ruleType != null">irule_type =
                        #{ruleType},
                    </if>
                    <if test="enabled != null">ienabled =
                        #{enabled},
                    </if>
                    <if test="childLevel != null">ichild_level =
                        #{childLevel},
                    </if>
                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="createTime != null">icreate_time =
                        #{createTime},
                    </if>
                    <if test="endTime != null">iend_time =
                        #{endTime},
                    </if>
                    <if test="result != null">iresult =
                        #{result},
                    </if>
                    <if test="state != null">istate =
                        #{state},
                    </if>
                    <if test="elapsedTime != null">ielapsed_time =
                        #{elapsedTime},
                    </if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <update id="updateRunRuleOfResult" >
        update ieai_envc_run_rule set iresult = #{result},iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        where iid = #{id}
    </update>

    <delete id="deleteRunRuleById" parameterType="Long">
        delete
        from ieai_envc_run_rule where iid = #{id}
    </delete>

    <delete id="deleteRunRuleByIds" parameterType="String">
        delete from ieai_envc_run_rule where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据运行实例信息ID查询关联的运行规则列表 -->
    <select id="selectRulesByInstanceInfoId" parameterType="Long" resultMap="RunRuleResult">
        <include refid="selectRunRule"/>
        where ienvc_run_instance_info_id = #{instanceInfoId}
    </select>

    <!-- 运行规则流程详细信息结果映射 -->
    <resultMap type="com.ideal.envc.model.bean.RunFlowDetailBean" id="RunFlowDetailResult">
        <result property="sourcePath" column="isource_path"/>
        <result property="path" column="ipath"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="sourceComputerId" column="isource_computer_id"/>
        <result property="targetComputerId" column="itarget_computer_id"/>
        <result property="targetCenterName" column="itarget_center_name"/>
        <result property="sourceCenterName" column="isource_center_name"/>
        <result property="sourceComputerIp" column="isource_computer_ip"/>
        <result property="targetComputerIp" column="itarget_computer_ip"/>
        <result property="sourceComputerName" column="isource_computer_name"/>
        <result property="targetComputerName" column="itarget_computer_name"/>
        <result property="businessSystemName" column="ibusiness_system_name"/>
    </resultMap>

    <!-- 根据流程ID查询运行规则详细信息 -->
    <select id="selectRunRuleDetailByFlowId" parameterType="Long" resultMap="RunFlowDetailResult">
        SELECT
            ru.isource_path,
            ru.ipath,
            sc.ibusiness_system_id,
            ri.isource_computer_id,
            ri.itarget_computer_id,
            ri.itarget_center_name,
            ri.isource_center_name,
            sc.icomputer_ip as isource_computer_ip,
            st.icomputer_ip as itarget_computer_ip,
            sc.icomputer_name as isource_computer_name,
            st.icomputer_name as itarget_computer_name,
            p.ibusiness_system_name
        FROM
            ieai_envc_run_flow rf
            INNER JOIN ieai_envc_run_rule ru ON rf.irun_biz_id = ru.iid
            INNER JOIN ieai_envc_run_instance_info ri on ru.ienvc_run_instance_info_id = ri.iid
            INNER JOIN ieai_envc_system_computer sc on ri.ibusiness_system_id = sc.ibusiness_system_id and ri.isource_computer_id=sc.icomputer_id
            INNER JOIN ieai_envc_system_computer st on ri.ibusiness_system_id = st.ibusiness_system_id and ri.itarget_computer_id=st.icomputer_id
            INNER JOIN ieai_envc_project p on sc.ibusiness_system_id = p.ibusiness_system_id and sc.ibusiness_system_id=st.ibusiness_system_id
        WHERE rf.iflowid = #{flowId}
    </select>

    <!-- 批量更新节点规则结果状态和结果 -->
    <update id="batchUpdateRunRuleStateAndResult">
        update ieai_envc_run_rule
        set istate = #{state}, iresult = #{result}
        where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>