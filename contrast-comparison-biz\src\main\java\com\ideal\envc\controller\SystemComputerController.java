package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.interaction.model.ComputerJo;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryDto;
import com.ideal.envc.service.ISystemComputerService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.enums.ResponseCodeEnum;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 系统配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sysmComputer")
@MethodPermission("@dp.hasBtnPermission('system-configuration')")
public class SystemComputerController {
    private final Logger logger = LoggerFactory.getLogger(SystemComputerController.class);

    private final ISystemComputerService systemComputerService;
    private final UserinfoComponent userinfoComponent;

    public SystemComputerController(ISystemComputerService systemComputerService, UserinfoComponent userinfoComponent) {
        this.systemComputerService = systemComputerService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 已绑定设备列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<SystemComputerDto>> list(@RequestBody TableQueryDto<SystemComputerQueryDto> tableQueryDto) {
        PageInfo<SystemComputerDto> list = systemComputerService.selectSystemComputerList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 待绑定设备列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/pendingList")
    @MethodPermission("@dp.hasBtnPermission('removeSystem')")
    public R<PageInfo<ComputerJo>> pendingList(@RequestBody TableQueryDto<SystemComputerQueryDto> tableQueryDto) {
        return  systemComputerService.pendingSystemComputerList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    /**
     * 根据系统与设备表主键查询绑定设备信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<SystemComputerDto> selectSystemComputerById(@RequestParam(value = "id")Long id) {
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), systemComputerService.selectSystemComputerById(id), ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 单个设备绑定--暂无使用
     *
     * @param systemComputerDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody SystemComputerDto systemComputerDto) {
        try {
            if (systemComputerService.insertSystemComputer(systemComputerDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("新增业务系统下设备系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 系统绑定设备-批量
     *
     * @param systemComputerDtoList 添加数据
     * @return 添加结果
     */
    @PostMapping("/add")
    @MethodPermission("@dp.hasBtnPermission('removeSystem')")
    public R<Void> add(@RequestBody List<SystemComputerDto> systemComputerDtoList) {
        UserDto userDto = userinfoComponent.getUser();
        try {
            if (systemComputerService.addSystemComputer(systemComputerDtoList,userDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (ContrastBusinessException e) {
            logger.warn("批量添加设备失败: {}", e.getMessage());
            return R.fail(ResponseCodeEnum.BATCH_ADD_DEVICE_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("批量添加设备发生未知异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 更新绑定设备--暂无使用
     *
     * @param systemComputerDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody SystemComputerDto systemComputerDto) {
        systemComputerService.updateSystemComputer(systemComputerDto);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
    }


    /**
     * 解绑设备
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('removeComputer')")
    public R<Void> remove(@RequestBody Long[] ids) {
        try {
            systemComputerService.deleteSystemComputerByIds(ids);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("解绑设备失败", e);
            return R.fail(ResponseCodeEnum.DELETE_FAIL.getCode(), e.getMessage());
        }
    }


}
