涉及表如下：
ieai_envc_project
ieai_envc_system_computer
ieai_envc_system_computer_node
ieai_envc_node_relation
ieai_envc_node_rule_content
ieai_envc_plan
ieai_envc_plan_relation
ieai_envc_task
ieai_envc_run_instance
ieai_envc_run_instance_info
ieai_envc_run_rule
ieai_envc_run_rule_sync
ieai_envc_run_flow
ieai_envc_run_flow_result
ieai_envc_dictionary
ieai_envc_dictionary_detail

创建语句：
create table ieai_envc_project
(
   iid                  bigint not null comment '主键ID',
   ibusiness_system_id  bigint comment '系统ID',
   ibusiness_system_code VARCHAR(100) comment '系统编码',
   ibusiness_system_name VARCHAR(100) comment '系统名称',
   ibusiness_system_unique VARCHAR(100) comment '系统唯一标识',
   ibusiness_system_desc VARCHAR(150) comment '系统描述',
   icreator_id          bigint comment '创建人ID',
   icreator_name        VARCHAR(50) comment '创建人',
   icreate_time         timestamp comment '添加时间',
   istatus              smallint default 1 comment '是否有效（1：有效，0：失效）',
   iupdator_id          bigint comment '更新人ID',
   iupdator_name        varchar(50) comment '更新人名称',
   iupdate_time         timestamp comment '更新时间',
   primary key (iid)
);
alter table ieai_envc_project comment '比对业务系统表';
并创建如下索引：
索引名字：idx_envc_project_system_01，对应字段ibusiness_system_id,ibusiness_system_name
索引名字：idx_envc_project_system_02，对应字段ibusiness_system_name
索引名字：idx_envc_project_system_03，对应字段ibusiness_system_desc

创建语句：
create table ieai_envc_system_computer
(
   iid                  bigint not null comment '主键ID',
   ibusiness_system_id  bigint comment '系统ID',
   icomputer_id         bigint comment '设备ID',
   icomputer_ip         varchar(255) comment '代理IP',
   icomputer_name       varchar(150) comment '代理名称',
   icenter_id           bigint default -1 comment '中心ID',
   icenter_name         varchar(100) comment '中心名称',
   icreate_time         timestamp comment '存储时间',
   icreator_id          bigint comment '添加人ID',
   icreator_name        varchar(50) comment '创建人名称',
   primary key (iid)
);
alter table ieai_envc_system_agent comment '系统设备关系表';
并创建如下索引：
索引名字：idx_system_computer_01,对应字段：ibusiness_system_id,computer_id
索引名字：idx_system_computer_02,对应字段：icomputer_id


创建语句：
create table ieai_envc_system_computer_node
(
   iid                  bigint not null comment '主键ID',
   ibusiness_system_id  bigint comment '系统ID',
   isource_center_id    bigint comment '源中心ID',
   itarget_center_id    bigint comment '目标中心ID',
   isource_computer_id  bigint comment '源设备ID',
   isource_computer_ip  varchar(255) comment '源设备IP',
   itarget_computer_id  bigint comment '目标设备ID',
   itarget_computer_ip  varchar(255) comment '目标设备IP',
   icreator_id          bigint comment '创建人ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreate_time         timestamp comment '创建时间',
   primary key (iid)
);
alter table ieai_envc_system_computer_node comment '系统与设备节点关系表';
并创建如下索引：
索引名字：idx_system_computer_node_01,对应字段：ibusiness_system_id,isource_center_id
索引名字：idx_system_computer_node_02,对应字段：ibusiness_system_id,itarget_center_id
索引名字：idx_system_computer_node_03,对应字段：isource_computer_id
索引名字：idx_system_computer_node_04,对应字段：itarget_computer_id


创建语句：
create table ieai_envc_node_relation
(
   iid                  bigint not null comment '主键ID',
   ienvc_system_computer_node_id bigint comment '节点关系ID',
   imodel               smallint comment '模式（0：比对，1：同步，2：比对后同步）',
   itype                bigint default 0 comment '模块类型（0：目录，1;文件，2：脚本）',
   ipath                varchar(255) comment '路径',
   isource_path         varchar(255) comment '原路径',
   iencode              varchar(32) comment '字符集',
   iway                 smallint default 0 comment '方式（0：全部:1：部分）',
   irule_type           smallint comment '规则类型（0：匹配，1：排除）',
   ienabled             smallint default 0 comment '是否有效（0：有效，1：无效）',
   ichild_level         smallint default 1 comment '是否子集（0:是，1：否）',
   icreator_id          bigint comment '创建人ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreate_time         timestamp comment '创建时间',
   iupdator_id          bigint comment '更新人ID',
   iupdator_name        varchar(50) comment '更新人名称',
   iupdate_time         timestamp comment '更新时间',
   primary key (iid)
);
alter table ieai_envc_node_relation comment '节点关系规则';

并创建如下索引：
索引名字：idx_node_relation_01,对应字段：ienvc_system_computer_node_id


创建语句：
create table ieai_envc_node_rule_content
(
   iid                  bigint not null comment '主键ID',
   ienvc_node_relation_id bigint comment '信息配置ID',
   irule_content        longtext comment '规则内容',
   primary key (iid)
);
alter table ieai_envc_node_rule_content comment '节点关系规则';

并创建如下索引：
索引名字：idx_node_rule_content_01,对应字段：ienvc_node_relation_id


创建语句：
create table ieai_envc_plan
(
   iid                  bigint not null comment '主键',
   iname                varchar(100) comment '方案名称',
   iplan_desc           varchar(150) comment '方案描述',
   icreator_name        varchar(50) comment '创建人名称',
   icreator_id          bigint comment '创建人ID',
   icreate_time         timestamp comment '创建时间',
   iupdator_id          bigint comment '更新人ID',
   iupdator_name        varchar(50) comment '更新人名称',
   iupdate_time         timestamp,
   primary key (iid)
);
alter table ieai_envc_plan comment '方案信息';

并创建如下索引：
索引名字：idx_envc_plan_01,对应字段：iname



创建语句：
create table ieai_envc_plan_relation
(
   iid                  bigint not null comment '主键',
   ienvc_plan_id        bigint comment '方案ID',
   ibusiness_system_id  bigint comment '业务系统ID',
   icreator_id          bigint comment '创建人ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreate_time         timestamp comment '创建时间',
   primary key (iid)
);
alter table ieai_envc_plan_relation comment '方案信息';

并创建如下索引：
索引名字：idx_plan_relation_01,对应字段：ienvc_plan_id,ibusiness_system_id,并且要求该索引为唯一索引。
索引名字：idx_plan_relation_02,对应字段：ibusiness_system_id


创建语句：
create table ieai_envc_task
(
   iid                  bigint not null comment '主键',
   ienvc_plan_id        bigint comment '方案ID',
   icron                varchar(300) comment '周期表达式',
   ienabled             smallint default 1 comment '是否启用（1:启用，0：禁用）',
   istate               smallint comment '启停状态（0:启动，1：停止）',
   isource_center_id    bigint comment '源中心ID',
   itarget_center_id    bigint comment '目标中心ID',
   ischeduled_id        bigint comment '定时ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreator_id          bigint comment '创建人ID',
   icreate_time         timestamp comment '创建时间',
   iupdator_id          bigint comment '更新人ID',
   iupdator_name        varchar(50) comment '更新人名称',
   iupdate_time         timestamp,
   primary key (iid)
);
alter table ieai_envc_task comment '任务表';

并创建如下索引：
索引名字：idx_envc_task_01,对应字段：ienvc_plan_id
索引名字：idx_envc_task_02,对应字段：istate
索引名字：idx_task_center_01,对应字段：isource_center_id,itarget_center_id


创建语句：
create table ieai_envc_run_instance
(
   iid                  bigint not null comment '主键',
   ienvc_plan_id        bigint comment '方案ID',
   ienvc_task_id        bigint default -1 comment '周期任务ID（方案启动和重试无任务id）',
   iresult              smallint default -1 comment '结果状态（-1:运行中，0:一致/成功，1：不一致/失败）',
   istate               smallint default 0 comment '启停状态（0：运行中，1：已完成，2：终止）',
   ifrom                smallint comment '触发来源：（1：周期触发，2：手动触发，3：重试）',
   istarter_name        varchar(50) comment '启动人名称',
   istarter_id          bigint comment '启动人ID',
   istart_time          timestamp comment '启动时间',
   iend_time            timestamp comment '结束时间',
   ielapsed_time        bigint comment '耗时',
   iupdate_time         timestamp comment '更新时间',
   primary key (iid)
);
alter table ieai_envc_run_instance comment '实例表';


创建语句：
create table ieai_envc_run_instance_info
(
   iid                  bigint not null comment '主键ID',
   ienvc_run_instance_id bigint comment '实例ID',
   ienvc_plan_id        bigint comment '方案ID',
   ibusiness_system_id  bigint comment '系统ID',
   isource_center_id    bigint comment '源中心ID',
   isource_center_name  varchar(150) comment '源中心名称',
   itarget_center_id    bigint comment '目标中心ID',
   itarget_center_name  varchar(150) comment '目标中心名称',
   isource_computer_id  bigint comment '源设备ID',
   isource_computer_ip  varchar(255) comment '源设备IP',
   isource_computer_port integer comment '源设备端口',
   isource_computer_os  varchar(150) comment '源设备操作系统',
   itarget_computer_id  bigint comment '目标设备ID',
   itarget_computer_ip  varchar(255) comment '目标设备IP',
   itarget_computer_port integer comment '目标设备端口',
   itarget_computer_os  varchar(150) comment '目标设备操作系统',
   istore_time          timestamp comment '存储时间',
   iresult              smallint comment '结果状态（-1:运行中，0:一致/成功，1：不一致/失败）',
   istate               smallint comment '启停状态（0：运行中，1：已完成，2：终止）',
   iupdate_time         timestamp comment '更新时间',
   primary key (iid)
);

alter table ieai_envc_run_instance_info comment '实例详情';

并创建如下索引：
索引名字：idx_run_instance_01,对应字段：ienvc_plan_id,ienvc_task_id
索引名字：idx_run_instance_02,对应字段：ienvc_task_id



创建语句：
create table ieai_envc_run_rule
(
   iid                  bigint not null comment '主键ID',
   ienvc_run_instance_info_id bigint comment '实例详情ID',
   imodel               smallint default -1 comment '模式（0：比对，1：同步，2：比对后同步）',
   itype                bigint default 0 comment '模块类型（0：目录，1;文件，2：脚本）',
   ipath                varchar(255) comment '路径',
   iencode              varchar(32) comment '字符集',
   iway                 smallint default 0 comment '方式（0：全部:1：部分）',
   irule_type           smallint comment '规则类型（0：匹配，1：排除）',
   ienabled             smallint default 0 comment '是否有效（0：有效，1：无效）',
   ichild_level         smallint default 1 comment '是否子集（0:是，1：否）',
   icreator_id          bigint comment '创建人ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreate_time         timestamp comment '创建时间',
   iend_time            timestamp comment '更新时间',
   iresult              smallint comment '结果状态（-1:运行中，0:一致/成功，1：不一致/失败）',
   istate               smallint comment '启停状态（0：运行中，1：已完成，2：终止）',
   ielapsed_time        bigint comment '耗时',
   iupdate_time         timestamp comment '更新时间',
   primary key (iid)
);
alter table ieai_envc_run_rule comment '节点规则结果表';

并创建如下索引：
索引名字：idx_run_rule_01,对应字段：ienvc_run_instance_info_id


创建语句：
create table ieai_envc_run_rule_sync
(
   iid                  bigint not null comment '主键ID',
   ienvc_system_agent_node_id bigint comment '节点关系ID',
   ienvc_run_rule_id    bigint comment '节点规则结果ID',
   icreator_id          bigint comment '创建人ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreate_time         timestamp comment '创建时间',
   iend_time            timestamp comment '更新时间',
   iresult              smallint comment '结果状态（-1:运行中，0:一致/成功，1：不一致/失败）',
   istate               smallint comment '启停状态（0：运行中，1：已完成，2：终止）',
   ielapsed_time        bigint comment '耗时',
   primary key (iid)
);
alter table ieai_envc_run_rule_sync comment '节点规则同步结果';

并创建如下索引：
索引名字：idx_run_rule_sync_01,对应字段：ienvc_run_rule_id


创建语句：
create table ieai_envc_run_rule_content
(
   iid                  bigint not null comment '主键ID',
   ienvc_run_rule_id    bigint comment '节点规则结果ID',
   icontent_type        smallint comment '内容类型（0：源内容，1：目标内容，2：差异内容）',
   icontent             text comment '内容',
   icreator_id          bigint comment '创建人ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreate_time         timestamp comment '创建时间',
   iupdate_time         timestamp comment '更新时间',
   primary key (iid)
);
alter table ieai_envc_run_rule_content comment '节点规则内容表';

并创建如下索引：
索引名字：idx_run_rule_content_01,对应字段：ienvc_run_rule_id


创建语句：
create table ieai_envc_run_flow
(
   iid                  bigint not null comment '主键ID',
   iflowid              bigint comment '流程ID',
   irun_biz_id          bigint comment '比对规则ID或者比对规则对应的同步id',
   imodel               smallint comment '来源标识（0：比对，1：同步）',
   icreator_id          bigint comment '创建人ID',
   icreator_name        varchar(50) comment '创建人名称',
   icreate_time         timestamp comment '创建时间',
   iend_time            timestamp comment '更新时间',
   istate               smallint comment '启停状态（0：运行中，1：已完成，2：终止）',
   ielapsed_time        bigint comment '耗时',
   iret                 varchar(10) comment '执行结束码',
   iupdate_order_time   bigint comment '引擎消息时间字段',
   primary key (iid)
);
alter table ieai_envc_run_flow comment '节点规则流程表';

并创建如下索引：
索引名字：idx_run_flow_01,对应字段：irun_biz_id,iflowid
索引名字：idx_run_flow_02,对应字段：iflowid



创建语句：
create table ieai_envc_run_flow_result
(
   iid                  bigint not null auto_increment comment '主键ID',
   ienvc_run_flow_id    bigint comment '节点规则流程主键',
   iflowid              bigint comment '流程ID',
   icontent             longtext comment '输出内容',
   istderr              longtext comment '错误输出内容',
   istore_time          timestamp comment '结果入库时间',
   iupdate_order_time   bigint comment '引擎消息时间字段',
   primary key (iid)
);
alter table ieai_envc_run_flow_result comment '流程输出结果表';

并创建如下索引：
索引名字：idx_run_flow_result_01,对应字段：ienvc_run_flow_id
索引名字：idx_run_flow_result_02,对应字段：iflowid



创建语句：
create table ieai_envc_dictionary
(
   iid                  bigint not null comment '主键',
   icode                varchar(50) comment '字典码',
   idescription         varchar(150) comment '字典描述',
   init                 smallint comment '是否初始化 0：否，1：是',
   ideleted             smallint default 0 comment '删除标识 0：否，1：是',
   icreator_name        varchar(50) comment '创建人名称',
   icreator_id          bigint comment '创建人ID',
   icreate_time         timestamp comment '创建时间',
   iupdator_id          bigint comment '更新人ID',
   iupdator_name        varchar(50) comment '更新人名称',
   iupdate_time         timestamp,
   primary key (iid)
);
alter table ieai_envc_dictionary comment '字典码表';

并创建如下索引：
索引名字：idx_envc_dictionary_01,对应字段：icode 唯一索引。



创建语句：
create table ieai_envc_dictionary_detail
(
   iid                  bigint not null comment '主键',
   ienvc_dictionary_id  bigint comment '码表主键',
   icode                varchar(50) comment '字典码',
   ilable               varchar(100) comment '显示名称',
   ivalue               varchar(200) comment '显示值',
   isort                bigint comment '排序序号',
   ideleted             smallint default 0 comment '删除标识 0：否，1：是',
   iarray_flag          smallint default 0 comment '数组标识 0：否，1：是',
   ivalue_type          varchar(20) comment '字典值类型（四类八种+String）',
   icreator_name        varchar(50) comment '创建人名称',
   icreator_id          bigint comment '创建人ID',
   icreate_time         timestamp comment '创建时间',
   iupdator_id          bigint comment '更新人ID',
   iupdator_name        varchar(50) comment '更新人名称',
   iupdate_time         timestamp,
   primary key (iid)
);
alter table ieai_envc_dictionary_detail comment '字典详情表';

并创建如下索引：
索引名字：idx_dictionary_detail_01,对应字段：icode
索引名字：idx_dictionary_detail_02,对应字段：ienvc_dictionary_id