<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ideal</groupId>
    <artifactId>ieai-envcontrast-comparison</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <groupId>com.ideal</groupId>
  <artifactId>contrast-comparison-starter</artifactId>
  <version>1.0-SNAPSHOT</version>
  <properties>
    <project.start.class>com.ideal.envc.Bootstrap</project.start.class>
    <maven.source.skip>true</maven.source.skip>
    <maven.javadoc.skip>true</maven.javadoc.skip>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.ideal</groupId>
      <artifactId>contrast-comparison-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ideal</groupId>
      <artifactId>contrast-comparison-biz</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-bootstrap</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba.nacos</groupId>
          <artifactId>nacos-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.alibaba.nacos</groupId>
          <artifactId>nacos-client</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.dubbo</groupId>
      <artifactId>dubbo-registry-nacos</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.nacos</groupId>
      <artifactId>nacos-spring-context</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid</artifactId>
    </dependency>
    <dependency>
      <groupId>org.liquibase</groupId>
      <artifactId>liquibase-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.internetitem</groupId>
      <artifactId>logback-elasticsearch-appender</artifactId>
    </dependency>
    <dependency>
      <groupId>net.logstash.logback</groupId>
      <artifactId>logstash-logback-encoder</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.skywalking</groupId>
      <artifactId>apm-toolkit-logback-1.x</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.skywalking</groupId>
      <artifactId>apm-toolkit-trace</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>com.ideal</groupId>
        <artifactId>ideal-file-generator-plugin</artifactId>
        <version>1.0.0</version>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>file-generator</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <fileConfigs>
            <fileConfig>
              <output>${project.basedir}/../build/bootstrap.yml</output>
              <templatePath>${project.basedir}/../build/bootstrap.yml.ftl</templatePath>
              <parameters>
                <parameter>
                  <name>appActive</name>
                  <type>java.lang.String</type>
                  <value>sit</value>
                </parameter>
                <parameter>
                  <name>appName</name>
                  <type>java.lang.String</type>
                  <value>contrast</value>
                </parameter>
                <parameter>
                  <name>configActive</name>
                  <type>java.lang.String</type>
                  <value>${config-center}</value>
                </parameter>
                <parameter>
                  <name>discoverActive</name>
                  <type>java.lang.String</type>
                  <value>${discover-center}</value>
                </parameter>
              </parameters>
            </fileConfig>
            <fileConfig>
              <output>${project.basedir}/../build/application.yml</output>
              <templatePath>${project.basedir}/../build/application.yml.ftl</templatePath>
              <parameters>
                <parameter>
                  <name>configActive</name>
                  <type>java.lang.String</type>
                  <value>${config-center}</value>
                </parameter>
                <parameter>
                  <name>discoverActive</name>
                  <type>java.lang.String</type>
                  <value>${discover-center}</value>
                </parameter>
              </parameters>
            </fileConfig>
            <fileConfig>
              <output>${project.basedir}/../build/start.sh</output>
              <templatePath>${project.basedir}/../build/start.sh.ftl</templatePath>
              <parameters>
                <parameter>
                  <name>jvmOption</name>
                  <type>java.lang.String</type>
                  <value>-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8212 -Xms1024m -Xmx1024m -Xmn500m  -XX:+UseG1GC  -XX:MaxGCPauseMillis=50  -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-OmitStackTraceInFastThrow</value>
                </parameter>
                <parameter>
                  <name>appName</name>
                  <type>java.lang.String</type>
                  <value>${project.build.finalName}.jar</value>
                </parameter>
              </parameters>
            </fileConfig>
          </fileConfigs>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.7.13</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>timestamp-property</id>
            <goals>
              <goal>timestamp-property</goal>
            </goals>
            <configuration>
              <name>current.time</name>
              <pattern>yyyyMMddHHmmss</pattern>
              <locale>zh_CN</locale>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>2.5.1</version>
        <executions>
          <execution>
            <id>assemble</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <appendAssemblyId>false</appendAssemblyId>
          <attach>false</attach>
          <archive>
            <manifest>
              <mainClass>${project.start.class}</mainClass>
            </manifest>
          </archive>
          <descriptors>
            <descriptor>${project.basedir}/../build/assembly.xml</descriptor>
          </descriptors>
          <outputDirectory>${project.build.directory}</outputDirectory>
          <finalName>${project.artifactId}-${project.version}-${current.time}</finalName>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
