package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 启动级别枚举
 * <AUTHOR>
 */
public enum StartLevelEnums {

    /**
     * 方案级启动
     */
    PLAN_LEVEL(1, "方案级启动"),

    /**
     * 系统级启动
     */
    SYSTEM_LEVEL(2, "系统级启动"),

    /**
     * 节点级启动
     */
    NODE_LEVEL(3, "节点级启动"),

    /**
     * 规则级启动
     */
    RULE_LEVEL(4, "规则级启动"),

    /**
     * 任务级启动
     */
    TASK_LEVEL(5, "任务级启动");

    /**
     * 启动级别代码
     */
    private final Integer code;

    /**
     * 启动级别名称
     */
    private final String name;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<Integer, StartLevelEnums> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(StartLevelEnums::getCode, Function.identity()));

    /**
     * 构造函数
     * @param code 启动级别代码
     * @param name 启动级别名称
     */
    StartLevelEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取启动级别代码
     * @return 启动级别代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取启动级别名称
     * @return 启动级别名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据启动级别代码获取枚举实例
     * @param code 启动级别代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static StartLevelEnums getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据启动级别代码获取启动级别名称
     * @param code 启动级别代码
     * @return 启动级别名称，如果不存在则返回"未知启动级别"
     */
    public static String getNameByCode(Integer code) {
        return Optional.ofNullable(getByCode(code))
                .map(StartLevelEnums::getName)
                .orElse("未知启动级别");
    }

    /**
     * 判断给定的启动级别代码是否在枚举允许的范围内
     * @param code 启动级别代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return code != null && CODE_MAP.containsKey(code);
    }
}
