package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.interaction.model.ComputerJo;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.service.ISystemComputerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.doThrow;

/**
 * 系统配置控制器测试
 */
@ExtendWith(MockitoExtension.class)
public class SystemComputerControllerTest {

    @Mock
    private ISystemComputerService systemComputerService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private SystemComputerController systemComputerController;

    private TableQueryDto<SystemComputerQueryDto> tableQueryDto;
    private SystemComputerQueryDto queryDto;
    private SystemComputerDto systemComputerDto;
    private PageInfo<SystemComputerDto> pageInfo;
    private List<SystemComputerDto> systemComputerDtoList;
    private UserDto userDto;
    private PageInfo<ComputerJo> pendingPageInfo;

    @BeforeEach
    void setUp() {
        // 初始化查询参数
        queryDto = new SystemComputerQueryDto();
        queryDto.setBusinessSystemId(1L);
        
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        // 初始化DTO对象
        systemComputerDto = new SystemComputerDto();
        systemComputerDto.setId(1L);
        systemComputerDto.setBusinessSystemId(1L);
        systemComputerDto.setComputerId(1L);
        systemComputerDto.setComputerIp("***********");
        systemComputerDto.setComputerName("测试计算机");
        systemComputerDto.setCenterName("测试中心");

        systemComputerDtoList = new ArrayList<>();
        systemComputerDtoList.add(systemComputerDto);

        // 初始化分页对象
        pageInfo = new PageInfo<>(systemComputerDtoList);
        pageInfo.setTotal(1);

        // 初始化用户DTO
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setLoginName("testuser");
        
        // 初始化待绑定设备分页对象
        List<ComputerJo> computerJoList = new ArrayList<>();
        ComputerJo computerJo = new ComputerJo();
        computerJo.setComputerId(456L);
        computerJo.setComputerIp("***********");
        computerJo.setComputerName("待绑定计算机");
        computerJoList.add(computerJo);
        
        pendingPageInfo = new PageInfo<>(computerJoList);
        pendingPageInfo.setTotal(1);
    }

    @Test
    @DisplayName("测试查询已绑定设备列表")
    void testList() throws ContrastBusinessException {
        // 设置Mock行为
        when(systemComputerService.selectSystemComputerList(
                any(SystemComputerQueryDto.class), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试方法
        R<PageInfo<SystemComputerDto>> result = systemComputerController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());
        
        // 验证服务调用
        verify(systemComputerService).selectSystemComputerList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询待绑定设备列表")
    void testPendingList() throws ContrastBusinessException {
        // 设置Mock行为
        when(systemComputerService.pendingSystemComputerList(
                any(SystemComputerQueryDto.class), anyInt(), anyInt()))
                .thenReturn(R.ok(pendingPageInfo));

        // 执行测试方法
        R<PageInfo<ComputerJo>> result = systemComputerController.pendingList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());
        
        // 验证服务调用
        verify(systemComputerService).pendingSystemComputerList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试根据ID查询绑定设备信息")
    void testSelectSystemComputerById() throws ContrastBusinessException {
        // 设置Mock行为
        when(systemComputerService.selectSystemComputerById(anyLong())).thenReturn(systemComputerDto);

        // 执行测试方法
        R<SystemComputerDto> result = systemComputerController.selectSystemComputerById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());
        assertEquals("测试中心", result.getData().getCenterName());
        
        // 验证服务调用
        verify(systemComputerService).selectSystemComputerById(1L);
    }

    @Test
    @DisplayName("测试单个设备绑定")
    void testSave() throws ContrastBusinessException {
        // 设置Mock行为
        when(systemComputerService.insertSystemComputer(any(SystemComputerDto.class))).thenReturn(1);

        // 执行测试方法
        R<Void> result = systemComputerController.save(systemComputerDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(systemComputerService).insertSystemComputer(systemComputerDto);
    }

    @Test
    @DisplayName("测试单个设备绑定失败")
    void testSaveFail() throws ContrastBusinessException {
        // 设置Mock行为
        when(systemComputerService.insertSystemComputer(any(SystemComputerDto.class))).thenReturn(0);

        // 执行测试方法
        R<Void> result = systemComputerController.save(systemComputerDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());
        
        // 验证服务调用
        verify(systemComputerService).insertSystemComputer(systemComputerDto);
    }

    @Test
    @DisplayName("测试批量绑定设备")
    void testAdd() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerService.addSystemComputer(any(), any(UserDto.class))).thenReturn(1);

        // 执行测试方法
        R<Void> result = systemComputerController.add(systemComputerDtoList);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(systemComputerService).addSystemComputer(systemComputerDtoList, userDto);
    }

    @Test
    @DisplayName("测试批量绑定设备失败")
    void testAddFail() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerService.addSystemComputer(any(), any(UserDto.class))).thenReturn(0);

        // 执行测试方法
        R<Void> result = systemComputerController.add(systemComputerDtoList);

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());
        
        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(systemComputerService).addSystemComputer(systemComputerDtoList, userDto);
    }

    @Test
    @DisplayName("测试更新绑定设备")
    void testUpdate() throws ContrastBusinessException {
        // 设置Mock行为
        doReturn(1).when(systemComputerService).updateSystemComputer(any(SystemComputerDto.class));

        // 执行测试方法
        R<Void> result = systemComputerController.update(systemComputerDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(systemComputerService).updateSystemComputer(systemComputerDto);
    }

    @Test
    @DisplayName("测试解绑设备")
    void testRemove() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 设置Mock行为
        doReturn(2).when(systemComputerService).deleteSystemComputerByIds(any());

        // 执行测试方法
        R<Void> result = systemComputerController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(systemComputerService).deleteSystemComputerByIds(ids);
    }

    @Test
    @DisplayName("测试单个设备绑定_系统异常")
    void testSave_SystemException() throws ContrastBusinessException {
        // 设置Mock行为 - 抛出系统异常
        when(systemComputerService.insertSystemComputer(any(SystemComputerDto.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行测试方法
        R<Void> result = systemComputerController.save(systemComputerDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("139900", result.getCode());
        
        // 验证服务调用
        verify(systemComputerService).insertSystemComputer(systemComputerDto);
    }

    @Test
    @DisplayName("测试批量绑定设备_业务异常")
    void testAdd_BusinessException() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerService.addSystemComputer(any(), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("设备已存在"));

        // 执行测试方法
        R<Void> result = systemComputerController.add(systemComputerDtoList);

        // 验证结果
        assertNotNull(result);
        assertEquals("13001", result.getCode());
        assertEquals("设备已存在", result.getMessage());
        
        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(systemComputerService).addSystemComputer(systemComputerDtoList, userDto);
    }

    @Test
    @DisplayName("测试批量绑定设备_系统异常")
    void testAdd_SystemException() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerService.addSystemComputer(any(), any(UserDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // 执行测试方法
        R<Void> result = systemComputerController.add(systemComputerDtoList);

        // 验证结果
        assertNotNull(result);
        assertEquals("139900", result.getCode());
        
        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(systemComputerService).addSystemComputer(systemComputerDtoList, userDto);
    }

    @Test
    @DisplayName("测试解绑设备_业务异常")
    void testRemove_BusinessException() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 设置Mock行为 - 抛出业务异常
        when(systemComputerService.deleteSystemComputerByIds(any()))
                .thenThrow(new ContrastBusinessException("设备不存在"));

        // 执行测试方法
        R<Void> result = systemComputerController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("130400", result.getCode());
        assertEquals("设备不存在", result.getMessage());
        
        // 验证服务调用
        verify(systemComputerService).deleteSystemComputerByIds(ids);
    }

    @Test
    @DisplayName("测试查询已绑定设备列表_服务异常")
    void testList_ServiceException() throws ContrastBusinessException {
        // 设置Mock行为 - 抛出异常
        when(systemComputerService.selectSystemComputerList(
                any(SystemComputerQueryDto.class), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("查询异常"));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            systemComputerController.list(tableQueryDto);
        });
        
        // 验证异常信息
        assertEquals("查询异常", exception.getMessage());
        
        // 验证服务调用
        verify(systemComputerService).selectSystemComputerList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询待绑定设备列表_服务异常")
    void testPendingList_ServiceException() throws ContrastBusinessException {
        // 设置Mock行为 - 抛出异常
        when(systemComputerService.pendingSystemComputerList(
                any(SystemComputerQueryDto.class), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("查询异常"));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            systemComputerController.pendingList(tableQueryDto);
        });
        
        // 验证异常信息
        assertEquals("查询异常", exception.getMessage());
        
        // 验证服务调用
        verify(systemComputerService).pendingSystemComputerList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试根据ID查询绑定设备信息_服务异常")
    void testSelectSystemComputerById_ServiceException() throws ContrastBusinessException {
        // 设置Mock行为 - 抛出异常
        when(systemComputerService.selectSystemComputerById(anyLong()))
                .thenThrow(new RuntimeException("查询异常"));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            systemComputerController.selectSystemComputerById(1L);
        });
        
        // 验证异常信息
        assertEquals("查询异常", exception.getMessage());
        
        // 验证服务调用
        verify(systemComputerService).selectSystemComputerById(1L);
    }

    @Test
    @DisplayName("测试更新绑定设备_服务异常")
    void testUpdate_ServiceException() throws ContrastBusinessException {
        // 设置Mock行为 - 抛出异常
        when(systemComputerService.updateSystemComputer(any(SystemComputerDto.class)))
                .thenThrow(new RuntimeException("更新异常"));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            systemComputerController.update(systemComputerDto);
        });
        
        // 验证异常信息
        assertEquals("更新异常", exception.getMessage());
        
        // 验证服务调用
        verify(systemComputerService).updateSystemComputer(systemComputerDto);
    }

    @Test
    @DisplayName("测试查询待绑定设备列表_返回失败结果")
    void testPendingList_FailResult() throws ContrastBusinessException {
        // 设置Mock行为 - 返回失败结果
        when(systemComputerService.pendingSystemComputerList(
                any(SystemComputerQueryDto.class), anyInt(), anyInt()))
                .thenReturn(R.fail("13000", "查询失败"));

        // 执行测试方法
        R<PageInfo<ComputerJo>> result = systemComputerController.pendingList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("13000", result.getCode());
        assertEquals("查询失败", result.getMessage());
        
        // 验证服务调用
        verify(systemComputerService).pendingSystemComputerList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试根据ID查询绑定设备信息_返回null")
    void testSelectSystemComputerById_ReturnNull() throws ContrastBusinessException {
        // 设置Mock行为 - 返回null
        when(systemComputerService.selectSystemComputerById(anyLong())).thenReturn(null);

        // 执行测试方法
        R<SystemComputerDto> result = systemComputerController.selectSystemComputerById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        // 数据为null但响应码仍为成功
        
        // 验证服务调用
        verify(systemComputerService).selectSystemComputerById(1L);
    }

    @Test
    @DisplayName("测试批量绑定设备_空列表")
    void testAdd_EmptyList() throws ContrastBusinessException {
        // 准备空列表
        List<SystemComputerDto> emptyList = new ArrayList<>();
        
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerService.addSystemComputer(any(), any(UserDto.class))).thenReturn(0);

        // 执行测试方法
        R<Void> result = systemComputerController.add(emptyList);

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());
        
        // 验证服务调用
        verify(userinfoComponent).getUser();
        verify(systemComputerService).addSystemComputer(emptyList, userDto);
    }

    @Test
    @DisplayName("测试解绑设备_空ID数组")
    void testRemove_EmptyIds() throws ContrastBusinessException {
        // 准备空ID数组
        Long[] emptyIds = {};

        // 设置Mock行为
        when(systemComputerService.deleteSystemComputerByIds(any()))
                .thenThrow(new ContrastBusinessException("删除失败，主键集合为空"));

        // 执行测试方法
        R<Void> result = systemComputerController.remove(emptyIds);

        // 验证结果
        assertNotNull(result);
        assertEquals("130400", result.getCode());
        assertEquals("删除失败，主键集合为空", result.getMessage());
        
        // 验证服务调用
        verify(systemComputerService).deleteSystemComputerByIds(emptyIds);
    }
} 