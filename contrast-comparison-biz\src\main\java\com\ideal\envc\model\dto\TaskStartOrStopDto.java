package com.ideal.envc.model.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 任务启停参数
 * <AUTHOR>
 */
public class TaskStartOrStopDto {

    /**
     * 操作类型（0：启动，1：停止）
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operateType;

    /**
     * 任务ID集合
     */
    @NotEmpty(message = "任务ID列表不能为空")
    private List<Long> taskIdList;

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public List<Long> getTaskIdList() {
        return taskIdList;
    }

    public void setTaskIdList(List<Long> taskIdList) {
        this.taskIdList = taskIdList;
    }
}
