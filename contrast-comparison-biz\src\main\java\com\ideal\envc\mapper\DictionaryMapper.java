package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.DictionaryEntity;

/**
 * 字典码Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface DictionaryMapper {
    /**
     * 查询字典码
     *
     * @param id 字典码主键
     * @return 字典码
     */
    DictionaryEntity selectDictionaryById(Long id);

    /**
     * 查询字典码列表
     *
     * @param dictionary 字典码
     * @return 字典码集合
     */
    List<DictionaryEntity> selectDictionaryList(DictionaryEntity dictionary);

    /**
     * 新增字典码
     *
     * @param dictionary 字典码
     * @return 结果
     */
    int insertDictionary(DictionaryEntity dictionary);

    /**
     * 修改字典码
     *
     * @param dictionary 字典码
     * @return 结果
     */
    int updateDictionary(DictionaryEntity dictionary);

    /**
     * 删除字典码
     *
     * @param id 字典码主键
     * @return 结果
     */
    int deleteDictionaryById(Long id);

    /**
     * 批量删除字典码
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDictionaryByIds(Long[] ids);
}
