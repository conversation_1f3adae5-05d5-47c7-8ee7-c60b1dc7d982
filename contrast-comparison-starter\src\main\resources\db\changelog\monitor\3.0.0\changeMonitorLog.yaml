databaseChangeLog:
- changeSet:
    id: ieai_monitor_callflow
    author: engine
    changes:
    - createTable:
        columns:
        - column:
            constraints:
              nullable: false
              primaryKey: true
            name: iid
            remarks: 主键
            type: BIGINT
        - column:
            name: icallflowactid
            remarks: callFlow的ActID
            type: BIGINT
        - column:
            name: icallflowid
            remarks: callFlow的ID
            type: BIGINT
        - column:
            name: imainflowid
            remarks: 主流程的ID
            type: BIGINT
        - column:
            name: iexecactid
            remarks: execActID
            type: BIGINT
        tableName: ieai_monitor_callflow

- changeSet:
    id: callFlowUnique
    author: entegor
    changes:
    - addUniqueConstraint:
        columnNames: icallflowactid, icallflowid, imainflowid, iexecactid
        constraintName: callFlowUnique
        tableName: ieai_monitor_callflow