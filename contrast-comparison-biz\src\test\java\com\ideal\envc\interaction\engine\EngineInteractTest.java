package com.ideal.envc.interaction.engine;

import com.ideal.engine.api.IStartFlow;
import com.ideal.engine.dto.FlowApiDto;
import com.ideal.engine.dto.FlowResultApiDto;
import com.ideal.engine.dto.WorkFlowOperationApiDto;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.enums.TaskFlowOperationTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EngineInteractTest {

    @Mock
    private IStartFlow startFlow;

    @InjectMocks
    private EngineInteract engineInteract;

    private FlowResultApiDto successFlowResultApiDto;
    private FlowResultApiDto failureFlowResultApiDto;
    private Long[] flowIds;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        flowIds = new Long[]{1L, 2L, 3L};

        // 成功的返回结果
        successFlowResultApiDto = new FlowResultApiDto();
        successFlowResultApiDto.setResultFlag(true);
        List<FlowApiDto> successFlowList = new ArrayList<>();
        for (Long flowId : flowIds) {
            FlowApiDto flowApiDto = new FlowApiDto();
            flowApiDto.setFlowId(flowId);
            flowApiDto.setSuccessFlag(true);
            successFlowList.add(flowApiDto);
        }
        successFlowResultApiDto.setListFlowDto(successFlowList);

        // 部分失败的返回结果
        failureFlowResultApiDto = new FlowResultApiDto();
        failureFlowResultApiDto.setResultFlag(false);
        List<FlowApiDto> failureFlowList = new ArrayList<>();
        for (Long flowId : flowIds) {
            FlowApiDto flowApiDto = new FlowApiDto();
            flowApiDto.setFlowId(flowId);
            flowApiDto.setSuccessFlag(flowId != 2L); // 让第二个流程失败
            failureFlowList.add(flowApiDto);
        }
        failureFlowResultApiDto.setListFlowDto(failureFlowList);
    }

    @Test
    void engineKillPauseResumeFlow_KillFlow_Success() throws Exception {
        // 设置模拟行为
        when(startFlow.killFlow(any(WorkFlowOperationApiDto.class))).thenReturn(successFlowResultApiDto);

        // 执行测试
        String result = engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode());

        // 验证结果
        assertTrue(result.isEmpty());
        verify(startFlow, times(1)).killFlow(any(WorkFlowOperationApiDto.class));
    }

    @Test
    void engineKillPauseResumeFlow_KillFlow_PartialFailure() throws Exception {
        // 设置模拟行为
        when(startFlow.killFlow(any(WorkFlowOperationApiDto.class))).thenReturn(failureFlowResultApiDto);

        // 执行测试
        String result = engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode());

        // 验证结果
        assertEquals("2,", result);
        verify(startFlow, times(1)).killFlow(any(WorkFlowOperationApiDto.class));
    }

    @Test
    void engineKillPauseResumeFlow_PauseFlow_Success() throws Exception {
        // 设置模拟行为
        when(startFlow.pauseFlow(any(WorkFlowOperationApiDto.class))).thenReturn(successFlowResultApiDto);

        // 执行测试
        String result = engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_CHANGE.getCode());

        // 验证结果
        assertTrue(result.isEmpty());
        verify(startFlow, times(1)).pauseFlow(any(WorkFlowOperationApiDto.class));
    }

    @Test
    void engineKillPauseResumeFlow_ResumeFlow_Success() throws Exception {
        // 设置模拟行为
        when(startFlow.resumeFlow(any(WorkFlowOperationApiDto.class))).thenReturn(successFlowResultApiDto);

        // 执行测试
        String result = engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_MAINTENANCE.getCode());

        // 验证结果
        assertTrue(result.isEmpty());
        verify(startFlow, times(1)).resumeFlow(any(WorkFlowOperationApiDto.class));
    }

    @Test
    void engineKillPauseResumeFlow_EmptyFlowIds() {
        // 执行测试并验证异常
        Exception exception = assertThrows(EngineServiceException.class, () ->
            engineInteract.engineKillPauseResumeFlow(new Long[]{}, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode())
        );
        assertTrue(exception.getMessage().contains("工作流操作flowIds为空"));
    }

    @Test
    void engineKillPauseResumeFlow_NullFlowIds() {
        // 执行测试并验证异常
        Exception exception = assertThrows(EngineServiceException.class, () ->
            engineInteract.engineKillPauseResumeFlow(null, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode())
        );
        assertTrue(exception.getMessage().contains("工作流操作flowIds为空"));
    }

    @Test
    void engineKillPauseResumeFlow_EngineException() throws Exception {
        // 设置模拟行为抛出异常
        when(startFlow.killFlow(any(WorkFlowOperationApiDto.class))).thenThrow(new RuntimeException("Engine error"));

        // 执行测试并验证异常
        Exception exception = assertThrows(EngineServiceException.class, () ->
            engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode())
        );
        assertTrue(exception.getCause().getMessage().contains("Engine error"));
    }

    @Test
    void engineKillPauseResumeFlow_NullResponse() throws Exception {
        // 设置模拟行为返回null
        when(startFlow.killFlow(any(WorkFlowOperationApiDto.class))).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(EngineServiceException.class, () ->
            engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode())
        );
        assertTrue(exception.getMessage().contains("响应为空"));
    }
} 