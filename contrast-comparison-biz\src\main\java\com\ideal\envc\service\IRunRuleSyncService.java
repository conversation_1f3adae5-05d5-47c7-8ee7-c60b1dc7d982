package com.ideal.envc.service;

import com.ideal.envc.model.dto.RunRuleSyncDto;
import com.ideal.envc.model.dto.RunRuleSyncQueryDto;
import com.github.pagehelper.PageInfo;

/**
 * 节点规则同步结果Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IRunRuleSyncService {
    /**
     * 查询节点规则同步结果
     *
     * @param id 节点规则同步结果主键
     * @return 节点规则同步结果
     */
    RunRuleSyncDto selectRunRuleSyncById(Long id);

    /**
     * 查询节点规则同步结果列表
     *
     * @param runRuleSyncQueryDto 节点规则同步结果
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点规则同步结果集合
     */
    PageInfo<RunRuleSyncDto> selectRunRuleSyncList(RunRuleSyncQueryDto runRuleSyncQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增节点规则同步结果
     *
     * @param runRuleSyncDto 节点规则同步结果
     * @return 结果
     */
    int insertRunRuleSync(RunRuleSyncDto runRuleSyncDto);

    /**
     * 修改节点规则同步结果
     *
     * @param runRuleSyncDto 节点规则同步结果
     * @return 结果
     */
    int updateRunRuleSync(RunRuleSyncDto runRuleSyncDto);

    /**
     * 批量删除节点规则同步结果
     *
     * @param ids 需要删除的节点规则同步结果主键集合
     * @return 结果
     */
    int deleteRunRuleSyncByIds(Long[] ids);
}
