package com.ideal.envc.strategy;

import com.ideal.envc.model.enums.RuleModelEnum;
import com.ideal.envc.model.enums.RuleITypeEnums;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 输出解析策略工厂
 * <AUTHOR>
 */
@Component
public class OutputParseStrategyFactory {
    
    private final Map<String, OutputParseStrategy> strategyMap = new ConcurrentHashMap<>();
    
    public OutputParseStrategyFactory(List<OutputParseStrategy> strategies) {
        for (OutputParseStrategy strategy : strategies) {
            strategyMap.put(strategy.getType(), strategy);
        }
    }
    
    /**
     * 获取策略
     * @param model 模式
     * @param type 类型
     * @return 策略实现
     */
    public OutputParseStrategy getStrategy(Integer model, Long type) {
        String key = generateKey(model, type);
        if (key == null) {
            return null;
        }
        return strategyMap.get(key);
    }
    
    /**
     * 生成策略key
     * @param model 模式
     * @param type 类型
     * @return 策略key
     */
    private String generateKey(Integer model, Long type) {
        // 边界检查
        if (model == null || type == null) {
            return null;
        }
        
        RuleModelEnum[] modelEnums = RuleModelEnum.values();
        if (model < 0 || model >= modelEnums.length) {
            return null;
        }
        
        RuleModelEnum modelEnum = modelEnums[model];
        RuleITypeEnums typeEnum = RuleITypeEnums.getByCode(type);
        
        if (typeEnum == null) {
            return null;
        }
        
        return modelEnum.name() + "_" + typeEnum.name();
    }
} 