package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.RunRuleSyncMapper;
import com.ideal.envc.model.entity.RunRuleSyncEntity;
import com.ideal.envc.service.IRunRuleSyncService;
import com.ideal.envc.model.dto.RunRuleSyncDto;
import com.ideal.envc.model.dto.RunRuleSyncQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 节点规则同步结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class RunRuleSyncServiceImpl implements IRunRuleSyncService {
    private final Logger logger = LoggerFactory.getLogger(RunRuleSyncServiceImpl.class);

    private final RunRuleSyncMapper runRuleSyncMapper;

    public RunRuleSyncServiceImpl(RunRuleSyncMapper runRuleSyncMapper) {
        this.runRuleSyncMapper = runRuleSyncMapper;
    }

    /**
     * 查询节点规则同步结果
     *
     * @param id 节点规则同步结果主键
     * @return 节点规则同步结果
     */
    @Override
    public RunRuleSyncDto selectRunRuleSyncById(Long id) {
        RunRuleSyncEntity runRuleSync = runRuleSyncMapper.selectRunRuleSyncById(id);
        return BeanUtils.copy(runRuleSync, RunRuleSyncDto.class);
    }

    /**
     * 查询节点规则同步结果列表
     *
     * @param runRuleSyncQueryDto 节点规则同步结果
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点规则同步结果
     */
    @Override
    public PageInfo<RunRuleSyncDto> selectRunRuleSyncList(RunRuleSyncQueryDto runRuleSyncQueryDto, Integer pageNum, Integer pageSize) {
        RunRuleSyncEntity query = BeanUtils.copy(runRuleSyncQueryDto, RunRuleSyncEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<RunRuleSyncEntity> runRuleSyncList = runRuleSyncMapper.selectRunRuleSyncList(query);
        return PageDataUtil.toDtoPage(runRuleSyncList, RunRuleSyncDto.class);
    }

    /**
     * 新增节点规则同步结果
     *
     * @param runRuleSyncDto 节点规则同步结果
     * @return 结果
     */
    @Override
    public int insertRunRuleSync(RunRuleSyncDto runRuleSyncDto) {
        RunRuleSyncEntity runRuleSync = BeanUtils.copy(runRuleSyncDto, RunRuleSyncEntity.class);
        return runRuleSyncMapper.insertRunRuleSync(runRuleSync);
    }

    /**
     * 修改节点规则同步结果
     *
     * @param runRuleSyncDto 节点规则同步结果
     * @return 结果
     */
    @Override
    public int updateRunRuleSync(RunRuleSyncDto runRuleSyncDto) {
        RunRuleSyncEntity runRuleSync = BeanUtils.copy(runRuleSyncDto, RunRuleSyncEntity.class);
        return runRuleSyncMapper.updateRunRuleSync(runRuleSync);
    }

    /**
     * 批量删除节点规则同步结果
     *
     * @param ids 需要删除的节点规则同步结果主键
     * @return 结果
     */
    @Override
    public int deleteRunRuleSyncByIds(Long[] ids) {
        return runRuleSyncMapper.deleteRunRuleSyncByIds(ids);
    }
}
