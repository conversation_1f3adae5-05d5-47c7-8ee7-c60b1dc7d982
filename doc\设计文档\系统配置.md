# 系统配置功能设计文档

## 1. 概述

系统配置模块是一致性比对微服务的基础功能模块，用于管理业务系统和设备的配置信息。该模块包含业务系统管理、设备管理和节点关系管理三个主要部分，提供了业务系统和设备的绑定、解绑等基本功能。

## 2. 数据模型

### 2.1 数据库表结构

#### 2.1.1 比对业务系统表 (ieai_envc_project)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ibusiness_system_id | bigint | | 是 | 系统ID |
| ibusiness_system_code | VARCHAR(100) | | 是 | 系统编码 |
| ibusiness_system_name | VARCHAR(100) | | 是 | 系统名称 |
| ibusiness_system_unique | VARCHAR(100) | | 是 | 系统唯一标识 |
| ibusiness_system_desc | VARCHAR(150) | | 是 | 系统描述 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | VARCHAR(50) | | 是 | 创建人 |
| icreate_time | timestamp | | 是 | 添加时间 |
| istatus | smallint | 1 | 是 | 是否有效（1：有效，0：失效） |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- idx_envc_project_system_01：ibusiness_system_id, ibusiness_system_name
- idx_envc_project_system_02：ibusiness_system_name
- idx_envc_project_system_03：ibusiness_system_desc

#### 2.1.2 系统设备关系表 (ieai_envc_system_computer)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ibusiness_system_id | bigint | | 是 | 系统ID |
| icomputer_id | bigint | | 是 | 设备ID |
| icomputer_ip | varchar(255) | | 是 | 代理IP |
| icomputer_name | varchar(150) | | 是 | 代理名称 |
| icenter_id | bigint | -1 | 是 | 中心ID |
| icenter_name | varchar(100) | | 是 | 中心名称 |
| icreate_time | timestamp | | 是 | 存储时间 |
| icreator_id | bigint | | 是 | 添加人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |

**索引：**
- 主键：iid
- idx_system_computer_01：ibusiness_system_id, icomputer_id
- idx_system_computer_02：icomputer_id

#### 2.1.3 系统与设备节点关系表 (ieai_envc_system_computer_node)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ibusiness_system_id | bigint | | 是 | 系统ID |
| isource_center_id | bigint | | 是 | 源中心ID |
| itarget_center_id | bigint | | 是 | 目标中心ID |
| isource_computer_id | bigint | | 是 | 源设备ID |
| isource_computer_ip | varchar(255) | | 是 | 源设备IP |
| itarget_computer_id | bigint | | 是 | 目标设备ID |
| itarget_computer_ip | varchar(255) | | 是 | 目标设备IP |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreate_time | timestamp | | 是 | 创建时间 |

**索引：**
- 主键：iid
- idx_system_computer_node_01：ibusiness_system_id, isource_center_id
- idx_system_computer_node_02：ibusiness_system_id, itarget_center_id
- idx_system_computer_node_03：isource_computer_id
- idx_system_computer_node_04：itarget_computer_id

### 2.2 实体类

#### 2.2.1 比对业务系统实体类 (ProjectEntity)

```java
public class ProjectEntity implements Serializable {
    private static final long serialVersionUID=1L;

    @IdGenerator
    private Long id;                // 主键ID
    private Long businessSystemId;  // 系统ID
    private String businessSystemCode; // 系统编码
    private String businessSystemName; // 系统名称
    private String businessSystemUnique; // 系统唯一标识
    private String businessSystemDesc; // 系统描述
    private Long creatorId;         // 创建人ID
    private String creatorName;     // 创建人名称
    private Date createTime;        // 创建时间
    private Integer status;         // 是否有效（1：有效，0：失效）
    private Long updatorId;         // 更新人ID
    private String updatorName;     // 更新人名称
    private Date updateTime;        // 更新时间

    // getter和setter方法
}
```

#### 2.2.2 系统设备关系实体类 (SystemComputerEntity)

```java
public class SystemComputerEntity implements Serializable {
    private static final long serialVersionUID=1L;

    @IdGenerator
    private Long id;                // 主键ID
    private Long businessSystemId;  // 系统ID
    private Long computerId;        // 设备ID
    private String computerIp;      // 代理IP
    private String computerName;    // 代理名称
    private Long centerId;          // 中心ID
    private String centerName;      // 中心名称
    private Date createTime;        // 存储时间
    private Long creatorId;         // 添加人ID
    private String creatorName;     // 创建人名称

    // getter和setter方法
}
```

#### 2.2.3 系统与设备节点关系实体类 (SystemComputerNodeEntity)

```java
public class SystemComputerNodeEntity implements Serializable {
    private static final long serialVersionUID=1L;

    @IdGenerator
    private Long id;                // 主键ID
    private Long businessSystemId;  // 系统ID
    private Long sourceCenterId;    // 源中心ID
    private Long targetCenterId;    // 目标中心ID
    private Long sourceComputerId;  // 源设备ID
    private String sourceComputerIp; // 源设备IP
    private Long targetComputerId;  // 目标设备ID
    private String targetComputerIp; // 目标设备IP
    private Long creatorId;         // 创建人ID
    private String creatorName;     // 创建人名称
    private Date createTime;        // 创建时间

    // getter和setter方法
}
```

### 2.3 DTO类

#### 2.3.1 比对业务系统DTO (ProjectDto)

与ProjectEntity结构相同，用于前后端数据传输。

#### 2.3.2 比对业务系统查询DTO (ProjectQueryDto)

与ProjectDto结构相同，用于查询条件传输。

#### 2.3.3 系统设备关系DTO (SystemComputerDto)

与SystemComputerEntity结构相同，用于前后端数据传输。

#### 2.3.4 系统设备关系查询DTO (SystemComputerQueryDto)

与SystemComputerDto结构相同，并增加了以下字段：
- excludeComputerIds：排除设备ID集合
- appointComputerIds：指定设备ID集合

#### 2.3.5 系统与设备节点关系DTO (SystemComputerNodeDto)

与SystemComputerNodeEntity结构相同，用于前后端数据传输。

#### 2.3.6 系统与设备节点关系查询DTO (SystemComputerNodeQueryDto)

与SystemComputerNodeDto结构相同，用于查询条件传输。