<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.SystemComputerNodeMapper">

    <resultMap type="com.ideal.envc.model.entity.SystemComputerNodeEntity" id="SystemComputerNodeResult">
            <result property="id" column="iid"/>
            <result property="businessSystemId" column="ibusiness_system_id"/>
            <result property="sourceCenterId" column="isource_center_id"/>
            <result property="targetCenterId" column="itarget_center_id"/>
            <result property="sourceComputerId" column="isource_computer_id"/>
            <result property="sourceComputerIp" column="isource_computer_ip"/>
            <result property="targetComputerId" column="itarget_computer_id"/>
            <result property="targetComputerIp" column="itarget_computer_ip"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>

    <sql id="selectSystemComputerNode">
        select iid, ibusiness_system_id, isource_center_id, itarget_center_id, isource_computer_id, isource_computer_ip, itarget_computer_id, itarget_computer_ip, icreator_id, icreator_name, icreate_time
        from ieai_envc_system_computer_node
    </sql>

    <select id="selectSystemComputerNodeList" parameterType="com.ideal.envc.model.entity.SystemComputerNodeEntity" resultMap="SystemComputerNodeResult">
        <include refid="selectSystemComputerNode"/>
        <where>
                        <if test="businessSystemId != null ">
                            and ibusiness_system_id = #{businessSystemId}
                        </if>
                        <if test="sourceCenterId != null ">
                            and isource_center_id = #{sourceCenterId}
                        </if>
                        <if test="targetCenterId != null ">
                            and itarget_center_id = #{targetCenterId}
                        </if>
                        <if test="sourceComputerId != null ">
                            and isource_computer_id = #{sourceComputerId}
                        </if>
                        <if test="sourceComputerIp != null  and sourceComputerIp != ''">
                            and isource_computer_ip = #{sourceComputerIp}
                        </if>
                        <if test="targetComputerId != null ">
                            and itarget_computer_id = #{targetComputerId}
                        </if>
                        <if test="targetComputerIp != null  and targetComputerIp != ''">
                            and itarget_computer_ip = #{targetComputerIp}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
        </where>
    </select>

    <select id="selectSystemComputerNodeById" parameterType="Long"
            resultMap="SystemComputerNodeResult">
            <include refid="selectSystemComputerNode"/>
            where iid = #{id}
    </select>

    <insert id="insertSystemComputerNode" parameterType="com.ideal.envc.model.entity.SystemComputerNodeEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_system_computer_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="businessSystemId != null">ibusiness_system_id,
                    </if>
                    <if test="sourceCenterId != null">isource_center_id,
                    </if>
                    <if test="targetCenterId != null">itarget_center_id,
                    </if>
                    <if test="sourceComputerId != null">isource_computer_id,
                    </if>
                    <if test="sourceComputerIp != null">isource_computer_ip,
                    </if>
                    <if test="targetComputerId != null">itarget_computer_id,
                    </if>
                    <if test="targetComputerIp != null">itarget_computer_ip,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="businessSystemId != null">#{businessSystemId},
                    </if>
                    <if test="sourceCenterId != null">#{sourceCenterId},
                    </if>
                    <if test="targetCenterId != null">#{targetCenterId},
                    </if>
                    <if test="sourceComputerId != null">#{sourceComputerId},
                    </if>
                    <if test="sourceComputerIp != null">#{sourceComputerIp},
                    </if>
                    <if test="targetComputerId != null">#{targetComputerId},
                    </if>
                    <if test="targetComputerIp != null">#{targetComputerIp},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateSystemComputerNode" parameterType="com.ideal.envc.model.entity.SystemComputerNodeEntity">
        update ieai_envc_system_computer_node
        <trim prefix="SET" suffixOverrides=",">
                    <if test="businessSystemId != null">ibusiness_system_id = #{businessSystemId},</if>
                    <if test="sourceCenterId != null">isource_center_id = #{sourceCenterId},</if>
                    <if test="targetCenterId != null">itarget_center_id = #{targetCenterId},</if>
                    <if test="sourceComputerId != null">isource_computer_id = #{sourceComputerId},</if>
                    <if test="sourceComputerIp != null">isource_computer_ip = #{sourceComputerIp},</if>
                    <if test="targetComputerId != null">itarget_computer_id = #{targetComputerId},</if>
                    <if test="targetComputerIp != null">itarget_computer_ip = #{targetComputerIp},</if>
                    <if test="updatorId != null">iupdator_id = #{updatorId},</if>
                    <if test="updatorName != null">iupdator_name = #{updatorName},</if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteSystemComputerNodeById" parameterType="Long">
        delete
        from ieai_envc_system_computer_node where iid = #{id}
    </delete>

    <delete id="deleteSystemComputerNodeByIds" parameterType="java.lang.Long">
        delete from ieai_envc_system_computer_node where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询系统已绑定源目标设备列表 -->
    <resultMap id="SystemComputerNodeListResult" type="com.ideal.envc.model.bean.SystemComputerNodeListBean">
        <result property="id" column="iid" />
        <result property="businessSystemId" column="ibusiness_system_id" />
        <result property="sourceCenterId" column="isource_center_id" />
        <result property="sourceCenterName" column="source_center_name" />
        <result property="sourceComputerIp" column="isource_computer_ip" />
        <result property="targetCenterId" column="itarget_center_id" />
        <result property="targetCenterName" column="target_center_name" />
        <result property="targetComputerIp" column="itarget_computer_ip" />
    </resultMap>

    <select id="selectSystemComputerNodeListByCondition" resultMap="SystemComputerNodeListResult">
        SELECT
            n.iid,
            n.ibusiness_system_id,
            n.isource_center_id,
            sc.icenter_name AS source_center_name,
            n.isource_computer_ip,
            n.itarget_center_id,
            tc.icenter_name AS target_center_name,
            n.itarget_computer_ip
        FROM
            ieai_envc_system_computer_node n
        INNER JOIN
            ieai_envc_system_computer sc ON n.isource_computer_id = sc.icomputer_id and n.ibusiness_system_id = sc.ibusiness_system_id
        INNER JOIN
            ieai_envc_system_computer tc ON n.itarget_computer_id = tc.icomputer_id and n.ibusiness_system_id = tc.ibusiness_system_id
        WHERE
            n.ibusiness_system_id = #{businessSystemId}
        <if test="sourceCenterId != null">
            AND n.isource_center_id = #{sourceCenterId}
        </if>
        <if test="targetCenterId != null">
            AND n.itarget_center_id = #{targetCenterId}
        </if>
        <if test="sourceComputerIp != null and sourceComputerIp != ''">
            AND n.isource_computer_ip LIKE CONCAT('%', #{sourceComputerIp}, '%')
        </if>
        <if test="targetComputerIp != null and targetComputerIp != ''">
            AND n.itarget_computer_ip LIKE CONCAT('%', #{targetComputerIp}, '%')
        </if>
        ORDER BY n.icreate_time DESC
    </select>

    <select id="selectSystemComputerNodeByBusinessSystemIds" parameterType="java.util.List" resultMap="SystemComputerNodeResult">
        select * from ieai_envc_system_computer_node
        where ibusiness_system_id in
        <foreach collection="businessSystemIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据业务系统ID和设备ID列表查询需要删除的节点记录 -->
    <select id="selectSystemComputerNodeByBusinessSystemIdAndComputerIds" resultMap="SystemComputerNodeResult">
        <include refid="selectSystemComputerNode"/>
        where ibusiness_system_id = #{businessSystemId}
        and (
            <foreach collection="computerIds" index="index" item="id" separator=" OR ">
                <if test="index % 999 == 0">
                    isource_computer_id IN
                    <foreach collection="computerIds" item="computerId" index="computerIdIndex" open="(" separator="," close=")">
                        <if test="computerIdIndex >= index and computerIdIndex &lt; index + 999">
                            #{computerId}
                        </if>
                    </foreach>
                </if>
            </foreach>
            or
            <foreach collection="computerIds" index="index" item="id" separator=" OR ">
                <if test="index % 999 == 0">
                    itarget_computer_id IN
                    <foreach collection="computerIds" item="computerId" index="computerIdIndex" open="(" separator="," close=")">
                        <if test="computerIdIndex >= index and computerIdIndex &lt; index + 999">
                            #{computerId}
                        </if>
                    </foreach>
                </if>
            </foreach>
        )
    </select>

    <!-- 根据系统计算机ID集合查询系统计算机节点 -->
    <select id="selectSystemComputerNodeBySystemComputerIds" parameterType="java.util.List" resultMap="SystemComputerNodeResult">
        <include refid="selectSystemComputerNode"/>
        where ibusiness_system_id in
        <foreach collection="systemComputerIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>