package com.ideal.envc.interaction.model;

import java.io.Serializable;
import java.util.List;

/**
 * 中心数据传输对象
 *
 * <AUTHOR>
 */
public class CenterQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键，中心ID
     */
    private Long id;
    
    /**
     * 中心名称
     */
    private String name;

    /**
     * 指定中心ID集合
     */
    private List<Long> appointIds;

    /**
     * 排除中心ID集合
     */
    private List<Long> excludeIds;
    


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Long> getAppointIds() {
        return appointIds;
    }

    public void setAppointIds(List<Long> appointIds) {
        this.appointIds = appointIds;
    }

    public List<Long> getExcludeIds() {
        return excludeIds;
    }

    public void setExcludeIds(List<Long> excludeIds) {
        this.excludeIds = excludeIds;
    }

    @Override
    public String toString() {
        return "CenterQueryDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", appointIds=" + appointIds +
                ", excludeIds=" + excludeIds +
                '}';
    }
}
