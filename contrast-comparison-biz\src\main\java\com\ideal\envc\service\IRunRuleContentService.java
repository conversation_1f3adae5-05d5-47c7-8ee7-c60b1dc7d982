package com.ideal.envc.service;

import com.ideal.envc.model.dto.RunRuleContentDto;
import com.ideal.envc.model.dto.RunRuleContentQueryDto;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 节点规则内容Service接口
 *
 * <AUTHOR>
 */
public interface IRunRuleContentService {

    /**
     * 查询节点规则内容
     *
     * @param id 节点规则内容ID
     * @return 节点规则内容
     */
    RunRuleContentDto selectRunRuleContentById(Long id);

    /**
     * 查询节点规则内容列表
     *
     * @param queryDto 节点规则内容查询条件
     * @return 节点规则内容集合
     */
    List<RunRuleContentDto> selectRunRuleContentList(RunRuleContentQueryDto queryDto);

    /**
     * 查询节点规则内容分页列表
     *
     * @param queryDto 节点规则内容查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 节点规则内容分页列表
     */
    PageInfo<RunRuleContentDto> selectRunRuleContentPage(RunRuleContentQueryDto queryDto, Integer pageNum, Integer pageSize);

    /**
     * 根据规则ID查询节点规则内容列表
     *
     * @param envcRunRuleId 规则ID
     * @return 节点规则内容集合
     */
    List<RunRuleContentDto> selectRunRuleContentListByRuleId(Long envcRunRuleId);

    /**
     * 新增节点规则内容
     *
     * @param runRuleContentDto 节点规则内容
     * @return 结果
     */
    int insertRunRuleContent(RunRuleContentDto runRuleContentDto);

    /**
     * 修改节点规则内容
     *
     * @param runRuleContentDto 节点规则内容
     * @return 结果
     */
    int updateRunRuleContent(RunRuleContentDto runRuleContentDto);

    /**
     * 批量删除节点规则内容
     *
     * @param ids 需要删除的节点规则内容ID
     * @return 结果
     */
    int deleteRunRuleContentByIds(Long[] ids);

    /**
     * 删除节点规则内容信息
     *
     * @param id 节点规则内容ID
     * @return 结果
     */
    int deleteRunRuleContentById(Long id);

    /**
     * 根据规则ID删除节点规则内容
     *
     * @param envcRunRuleId 规则ID
     * @return 结果
     */
    int deleteRunRuleContentByRuleId(Long envcRunRuleId);
}
