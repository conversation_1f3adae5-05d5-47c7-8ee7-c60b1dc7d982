package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import java.util.List;
import com.ideal.envc.model.dto.DictionaryDetailDto;
import com.ideal.envc.model.dto.DictionaryDetailQueryDto;
import com.ideal.envc.service.IDictionaryDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 字典管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dictionary/detail")
public class DictionaryDetailController {
    private final Logger logger = LoggerFactory.getLogger(DictionaryDetailController.class);

    private final IDictionaryDetailService dictionaryDetailService;

    public DictionaryDetailController(IDictionaryDetailService dictionaryDetailService) {
        this.dictionaryDetailService = dictionaryDetailService;
    }

    /**
     * 查询字典详情列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<DictionaryDetailDto>> list(@RequestBody TableQueryDto<DictionaryDetailQueryDto> tableQueryDto) {
        PageInfo<DictionaryDetailDto> list = dictionaryDetailService.selectDictionaryDetailList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 查询字典详情详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<DictionaryDetailDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(dictionaryDetailService.selectDictionaryDetailById(id));
    }

    /**
     * 新增保存字典详情
     *
     * @param dictionaryDetailDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody DictionaryDetailDto dictionaryDetailDto) {
        if (dictionaryDetailService.insertDictionaryDetail(dictionaryDetailDto) > 0) {
            return R.ok();
        }
        return R.fail();
    }

    /**
     * 修改保存字典详情
     *
     * @param dictionaryDetailDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody DictionaryDetailDto dictionaryDetailDto) {
        dictionaryDetailService.updateDictionaryDetail(dictionaryDetailDto);
        return R.ok();
    }


    /**
     * 删除字典详情
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody Long[] ids) {
        dictionaryDetailService.deleteDictionaryDetailByIds(ids);
        return R.ok();
    }

    /**
     * 根据字典码值获取字典详情信息列表
     *
     * @param code 字典码
     * @return 字典详情列表
     */
    @GetMapping("/findDictionaryDeatailList")
    public R<List<DictionaryDetailDto>> findDictionaryDeatailList(@RequestParam(value = "code") String code) {
        logger.info("根据字典码获取字典详情列表: {}", code);
        List<DictionaryDetailDto> list = dictionaryDetailService.findDictionaryDetailListByCode(code);
        return R.ok(list);
    }
}
