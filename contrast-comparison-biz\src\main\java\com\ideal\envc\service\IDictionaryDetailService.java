package com.ideal.envc.service;

import com.ideal.envc.model.dto.DictionaryDetailDto;
import com.ideal.envc.model.dto.DictionaryDetailQueryDto;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 字典详情Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IDictionaryDetailService {
    /**
     * 查询字典详情
     *
     * @param id 字典详情主键
     * @return 字典详情
     */
    DictionaryDetailDto selectDictionaryDetailById(Long id);

    /**
     * 查询字典详情列表
     *
     * @param dictionaryDetailQueryDto 字典详情
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 字典详情集合
     */
    PageInfo<DictionaryDetailDto> selectDictionaryDetailList(DictionaryDetailQueryDto dictionaryDetailQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增字典详情
     *
     * @param dictionaryDetailDto 字典详情
     * @return 结果
     */
    int insertDictionaryDetail(DictionaryDetailDto dictionaryDetailDto);

    /**
     * 修改字典详情
     *
     * @param dictionaryDetailDto 字典详情
     * @return 结果
     */
    int updateDictionaryDetail(DictionaryDetailDto dictionaryDetailDto);

    /**
     * 批量删除字典详情
     *
     * @param ids 需要删除的字典详情主键集合
     * @return 结果
     */
    int deleteDictionaryDetailByIds(Long[] ids);

    /**
     * 根据字典码值获取字典详情信息列表
     *
     * @param code 字典码
     * @return 字典详情列表
     */
    List<DictionaryDetailDto> findDictionaryDetailListByCode(String code);
}
