package com.ideal.envc.startup;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.HashMap;
import java.util.Map;

/**
 * 启动状态健康检查指示器
 * 提供应用启动状态和系统资源使用情况的健康检查
 * 
 * <AUTHOR>
 */
@Component
public class StartupHealthIndicator implements HealthIndicator {
    
    @Autowired(required = false)
    private StartupMonitor startupMonitor;
    
    @Override
    public Health health() {
        Map<String, Object> details = new HashMap<String, Object>();
        
        // 添加启动状态信息
        addStartupStatusDetails(details);
        
        // 添加系统资源信息
        addSystemResourceDetails(details);
        
        // 添加JVM信息
        addJvmDetails(details);
        
        // 根据启动状态决定健康状态
        if (startupMonitor != null) {
            StartupMonitor.StartupStatus status = startupMonitor.getStartupStatus();
            switch (status) {
                case READY:
                    return Health.up().withDetails(details).build();
                case STARTING:
                    return Health.up().withDetail("status", "应用正在启动中").withDetails(details).build();
                case FAILED:
                    return Health.down().withDetail("status", "应用启动失败").withDetails(details).build();
                default:
                    return Health.unknown().withDetail("status", "应用状态未知").withDetails(details).build();
            }
        } else {
            return Health.up().withDetail("status", "启动监控器未可用").withDetails(details).build();
        }
    }
    
    /**
     * 添加启动状态详细信息
     */
    private void addStartupStatusDetails(Map<String, Object> details) {
        if (startupMonitor != null) {
            StartupMonitor.StartupStatus status = startupMonitor.getStartupStatus();
            details.put("startupStatus", status.getDescription());
            
            long duration = startupMonitor.getStartupDuration();
            if (duration > 0) {
                details.put("startupDuration", duration + "ms");
            }
        } else {
            details.put("startupStatus", "监控器未可用");
        }
    }
    
    /**
     * 添加系统资源详细信息
     */
    private void addSystemResourceDetails(Map<String, Object> details) {
        Runtime runtime = Runtime.getRuntime();
        
        // 内存信息
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        Map<String, Object> memoryInfo = new HashMap<String, Object>();
        memoryInfo.put("max", formatBytes(maxMemory));
        memoryInfo.put("total", formatBytes(totalMemory));
        memoryInfo.put("used", formatBytes(usedMemory));
        memoryInfo.put("free", formatBytes(freeMemory));
        memoryInfo.put("usagePercent", String.format("%.2f%%", (double) usedMemory / totalMemory * 100));
        
        details.put("memory", memoryInfo);
        
        // CPU信息
        details.put("availableProcessors", runtime.availableProcessors());
    }
    
    /**
     * 添加JVM详细信息
     */
    private void addJvmDetails(Map<String, Object> details) {
        Map<String, Object> jvmInfo = new HashMap<String, Object>();
        
        // 基本JVM信息
        jvmInfo.put("version", System.getProperty("java.version"));
        jvmInfo.put("vendor", System.getProperty("java.vendor"));
        jvmInfo.put("home", System.getProperty("java.home"));
        
        // 运行时信息
        jvmInfo.put("uptime", ManagementFactory.getRuntimeMXBean().getUptime() + "ms");
        jvmInfo.put("startTime", ManagementFactory.getRuntimeMXBean().getStartTime());
        
        // 内存详细信息
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        Map<String, Object> heapInfo = new HashMap<String, Object>();
        heapInfo.put("init", formatBytes(heapUsage.getInit()));
        heapInfo.put("used", formatBytes(heapUsage.getUsed()));
        heapInfo.put("committed", formatBytes(heapUsage.getCommitted()));
        heapInfo.put("max", formatBytes(heapUsage.getMax()));
        
        Map<String, Object> nonHeapInfo = new HashMap<String, Object>();
        nonHeapInfo.put("init", formatBytes(nonHeapUsage.getInit()));
        nonHeapInfo.put("used", formatBytes(nonHeapUsage.getUsed()));
        nonHeapInfo.put("committed", formatBytes(nonHeapUsage.getCommitted()));
        nonHeapInfo.put("max", formatBytes(nonHeapUsage.getMax()));
        
        jvmInfo.put("heapMemory", heapInfo);
        jvmInfo.put("nonHeapMemory", nonHeapInfo);
        
        details.put("jvm", jvmInfo);
        
        // 操作系统信息
        Map<String, Object> osInfo = new HashMap<String, Object>();
        osInfo.put("name", System.getProperty("os.name"));
        osInfo.put("version", System.getProperty("os.version"));
        osInfo.put("arch", System.getProperty("os.arch"));
        
        details.put("os", osInfo);
    }
    
    /**
     * 格式化字节数为可读格式
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) {
            return "N/A";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
}
