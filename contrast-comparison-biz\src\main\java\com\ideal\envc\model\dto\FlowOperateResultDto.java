package com.ideal.envc.model.dto;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class FlowOperateResultDto {
    /**
     *任务id
     */
    private Long  taskId;
    /**
     * 发送操作失败的工作流id集合逗号分隔
     */
    private String failFlowIds;
    /**
     *所有需要操作的工作流id集合
     */
    private Long [] flowIds;


    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getFailFlowIds() {
        return failFlowIds;
    }

    public void setFailFlowIds(String failFlowIds) {
        this.failFlowIds = failFlowIds;
    }

    public Long[] getFlowIds() {
        return flowIds;
    }

    public void setFlowIds(Long[] flowIds) {
        this.flowIds = flowIds;
    }

    @Override
    public String toString() {
        return "FlowOperateResultDto{" +
                "taskId=" + taskId +
                ", failFlowIds='" + failFlowIds + '\'' +
                ", flowIds=" + Arrays.toString(flowIds) +
                '}';
    }
}
