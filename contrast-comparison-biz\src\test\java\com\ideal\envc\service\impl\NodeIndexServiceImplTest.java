package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryPageDto;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.model.dto.*;
import com.ideal.envc.model.dto.NodeRelationContentDto;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * NodeIndexServiceImpl单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class NodeIndexServiceImplTest {

    @Mock
    private SystemComputerNodeMapper systemComputerNodeMapper;

    @Mock
    private NodeRelationMapper nodeRelationMapper;

    @Mock
    private NodeRuleContentMapper nodeRuleContentMapper;

    @Mock
    private SystemComputerMapper systemComputerMapper;

    @Mock
    private BatchHandler batchHandler;

    @InjectMocks
    private NodeIndexServiceImpl nodeIndexService;

    private NodeBatchSaveRequestDto nodeBatchSaveRequestDto;
    private UserDto userDto;
    private List<Long> targetComputerIds;
    private Map<Long, SystemComputerEntity> computerMap;
    private List<NodeRelationContentDto> nodeRelationContentDtoList;
    private List<SystemComputerEntity> systemComputerEntityList;

    @BeforeEach
    void setUp() {
        // 初始化通用测试数据
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");

        targetComputerIds = new ArrayList<>();
        targetComputerIds.add(101L);
        targetComputerIds.add(102L);

        // 初始化计算机实体和映射
        computerMap = new HashMap<>();
        SystemComputerEntity computer1 = new SystemComputerEntity();
        computer1.setId(101L);
        computer1.setComputerIp("*************");
        computerMap.put(101L, computer1);

        SystemComputerEntity computer2 = new SystemComputerEntity();
        computer2.setId(102L);
        computer2.setComputerIp("*************");
        computerMap.put(102L, computer2);

        // 初始化节点关系内容列表
        nodeRelationContentDtoList = new ArrayList<>();
        NodeRelationContentDto contentDto = new NodeRelationContentDto();
        contentDto.setModel(0);
        contentDto.setType(1L);
        contentDto.setPath("/test/path");
        contentDto.setEncode("UTF-8");
        contentDto.setWay(0);
        contentDto.setRuleType(0);
        contentDto.setEnabled(0);
        contentDto.setChildLevel(0);
        contentDto.setContent("测试内容");
        nodeRelationContentDtoList.add(contentDto);

        // 初始化系统计算机实体列表
        systemComputerEntityList = new ArrayList<>();
        systemComputerEntityList.add(computer1);
        systemComputerEntityList.add(computer2);

        // 初始化批量保存请求数据
        nodeBatchSaveRequestDto = new NodeBatchSaveRequestDto();
        nodeBatchSaveRequestDto.setBusinessSystemId(1L);
        nodeBatchSaveRequestDto.setSourceCenterId(10L);
        nodeBatchSaveRequestDto.setSourceComputerId(100L);
        nodeBatchSaveRequestDto.setSourceIp("*************");
        nodeBatchSaveRequestDto.setTargetCenterId(20L);
        nodeBatchSaveRequestDto.setTargetComputerIdList(targetComputerIds);
        nodeBatchSaveRequestDto.setNodeRelationContentDtoList(nodeRelationContentDtoList);
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_成功场景")
    void testBatchSaveNodeRelation_Success() {
        // 设置模拟行为
        when(systemComputerMapper.selectComputerIpMapByIdsAndSystemId(anyList(), anyLong()))
                .thenReturn(computerMap);

        // 使用静态模拟来处理SnowflakeIdWorker.generateId()调用
        try (MockedStatic<SnowflakeIdWorker> mockedSnowflake = mockStatic(SnowflakeIdWorker.class)) {
            
            mockedSnowflake.when(SnowflakeIdWorker::generateId).thenReturn(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L);

            // 执行测试方法
            boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

            // 验证结果和调用
            assertTrue(result);
            verify(systemComputerMapper).selectComputerIpMapByIdsAndSystemId(eq(targetComputerIds), eq(1L));
            verify(batchHandler, times(3)).batchData(anyList(), any(), eq(1000));
        }
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_无节点关系内容")
    void testBatchSaveNodeRelation_NoNodeRelationContent() {
        // 设置无节点关系内容的请求
        nodeBatchSaveRequestDto.setNodeRelationContentDtoList(null);
        
        // 设置模拟行为
        when(systemComputerMapper.selectComputerIpMapByIdsAndSystemId(anyList(), anyLong()))
                .thenReturn(computerMap);

        // 使用静态模拟来处理SnowflakeIdWorker.generateId()调用
        try (MockedStatic<SnowflakeIdWorker> mockedSnowflake = mockStatic(SnowflakeIdWorker.class)) {
            
            mockedSnowflake.when(SnowflakeIdWorker::generateId).thenReturn(1L, 2L);

            // 执行测试方法
            boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

            // 验证结果和调用
            assertTrue(result);
            verify(systemComputerMapper).selectComputerIpMapByIdsAndSystemId(eq(targetComputerIds), eq(1L));
            // 只有系统计算机节点会被保存，节点关系和内容列表为空
            verify(batchHandler, times(3)).batchData(anyList(), any(), eq(1000));
        }
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_空节点关系内容列表")
    void testBatchSaveNodeRelation_EmptyNodeRelationContentList() {
        // 设置空的节点关系内容列表
        nodeBatchSaveRequestDto.setNodeRelationContentDtoList(new ArrayList<>());
        
        // 设置模拟行为
        when(systemComputerMapper.selectComputerIpMapByIdsAndSystemId(anyList(), anyLong()))
                .thenReturn(computerMap);

        // 使用静态模拟来处理SnowflakeIdWorker.generateId()调用
        try (MockedStatic<SnowflakeIdWorker> mockedSnowflake = mockStatic(SnowflakeIdWorker.class)) {
            
            mockedSnowflake.when(SnowflakeIdWorker::generateId).thenReturn(1L, 2L);

            // 执行测试方法
            boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

            // 验证结果和调用
            assertTrue(result);
            verify(systemComputerMapper).selectComputerIpMapByIdsAndSystemId(eq(targetComputerIds), eq(1L));
            // 只有系统计算机节点会被保存，节点关系和内容列表为空
            verify(batchHandler, times(3)).batchData(anyList(), any(), eq(1000));
        }
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_请求对象为空")
    void testBatchSaveNodeRelation_NullRequest() {
        // 执行测试方法
        boolean result = nodeIndexService.batchSaveNodeRelation(null, userDto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerMapper, never()).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        verify(batchHandler, never()).batchData(anyList(), any(), anyInt());
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_源设备ID为空")
    void testBatchSaveNodeRelation_NullSourceComputer() {
        // 设置源设备ID为空
        nodeBatchSaveRequestDto.setSourceComputerId(null);

        // 执行测试方法
        boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerMapper, never()).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        verify(batchHandler, never()).batchData(anyList(), any(), anyInt());
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_目标设备ID列表为空")
    void testBatchSaveNodeRelation_EmptyTargetComputers() {
        // 设置目标设备ID列表为空
        nodeBatchSaveRequestDto.setTargetComputerIdList(new ArrayList<>());

        // 执行测试方法
        boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerMapper, never()).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        verify(batchHandler, never()).batchData(anyList(), any(), anyInt());
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_源中心ID为空")
    void testBatchSaveNodeRelation_NullSourceCenter() {
        // 设置源中心ID为空
        nodeBatchSaveRequestDto.setSourceCenterId(null);

        // 执行测试方法
        boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerMapper, never()).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        verify(batchHandler, never()).batchData(anyList(), any(), anyInt());
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_目标中心ID为空")
    void testBatchSaveNodeRelation_NullTargetCenter() {
        // 设置目标中心ID为空
        nodeBatchSaveRequestDto.setTargetCenterId(null);

        // 执行测试方法
        boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerMapper, never()).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        verify(batchHandler, never()).batchData(anyList(), any(), anyInt());
    }

    @Test
    @DisplayName("测试批量保存节点规则信息关系_业务系统ID为空")
    void testBatchSaveNodeRelation_NullBusinessSystem() {
        // 设置业务系统ID为空
        nodeBatchSaveRequestDto.setBusinessSystemId(null);

        // 执行测试方法
        boolean result = nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto, userDto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerMapper, never()).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        verify(batchHandler, never()).batchData(anyList(), any(), anyInt());
    }

    @Test
    @DisplayName("测试查询系统设备列表分页_正常场景")
    void testSelectSystemComputerListPage_Success() throws ContrastBusinessException {
        // 设置模拟行为
        when(systemComputerMapper.selectComputerListByCondition(anyLong(), anyLong(), anyList()))
                .thenReturn(systemComputerEntityList);
                
        List<SystemComputerDto> dtoList = new ArrayList<>();
        SystemComputerDto dto1 = new SystemComputerDto();
        dto1.setId(101L);
        SystemComputerDto dto2 = new SystemComputerDto();
        dto2.setId(102L);
        dtoList.add(dto1);
        dtoList.add(dto2);
        
        // 创建查询条件
        SystemComputerQueryPageDto queryPageDto = new SystemComputerQueryPageDto();
        queryPageDto.setBusinessSystemId(1L);
        queryPageDto.setSourceCenterId(10L);
        queryPageDto.setSourceComputerId(101L);
        queryPageDto.setTargetCenterId(20L);
        
        TableQueryDto<SystemComputerQueryPageDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryPageDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
                
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(anyList(), eq(SystemComputerDto.class)))
                    .thenReturn(dtoList);
            
            // 执行测试方法
            PageInfo<SystemComputerDto> result = nodeIndexService.selectSystemComputerListPage(tableQueryDto);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(dtoList, result.getList());
            verify(systemComputerMapper).selectComputerListByCondition(eq(1L), eq(20L), anyList());
        }
    }

    @Test
    @DisplayName("测试查询系统设备列表分页_业务系统ID为空")
    void testSelectSystemComputerListPage_NullBusinessSystemId() throws ContrastBusinessException {
        // 创建查询条件
        SystemComputerQueryPageDto queryPageDto = new SystemComputerQueryPageDto();
        queryPageDto.setSourceCenterId(10L);
        
        TableQueryDto<SystemComputerQueryPageDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryPageDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        
        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            nodeIndexService.selectSystemComputerListPage(tableQueryDto);
        });
        
        assertEquals(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode() + "：业务系统ID、源中心ID、目标中心ID和源设备ID不能为空", exception.getMessage());
        verify(systemComputerMapper, never()).selectComputerListByCondition(anyLong(), anyLong(), anyList());
    }

    @Test
    @DisplayName("测试查询系统设备列表_正常场景")
    void testSelectSystemComputerList_Success() {
        // 设置模拟行为
        when(systemComputerMapper.selectComputerListByCondition(anyLong(), anyLong(), anyList()))
                .thenReturn(systemComputerEntityList);
                
        List<SystemComputerDto> dtoList = new ArrayList<>();
        SystemComputerDto dto1 = new SystemComputerDto();
        dto1.setId(101L);
        SystemComputerDto dto2 = new SystemComputerDto();
        dto2.setId(102L);
        dtoList.add(dto1);
        dtoList.add(dto2);
        
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(anyList(), eq(SystemComputerDto.class)))
                    .thenReturn(dtoList);
            
            // 执行测试方法
            List<SystemComputerDto> result = nodeIndexService.selectSystemComputerList(1L, 20L, Collections.singletonList(200L));
            
            // 验证结果
            assertNotNull(result);
            assertEquals(dtoList, result);
            verify(systemComputerMapper).selectComputerListByCondition(eq(1L), eq(20L), anyList());
        }
    }

    @Test
    @DisplayName("测试查询系统设备列表_业务系统ID为空")
    void testSelectSystemComputerList_NullBusinessSystemId() {
        // 执行测试方法
        List<SystemComputerDto> result = nodeIndexService.selectSystemComputerList(null, 20L, Collections.singletonList(200L));
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(systemComputerMapper, never()).selectComputerListByCondition(anyLong(), anyLong(), anyList());
    }
} 