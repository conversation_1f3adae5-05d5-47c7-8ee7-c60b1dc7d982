package com.ideal.envc.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.model.bean.SystemComputerNodeListBean;
import com.ideal.envc.model.dto.SystemComputerNodeBatchDto;
import com.ideal.envc.model.dto.SystemComputerNodeDto;
import com.ideal.envc.model.dto.SystemComputerNodeListDto;
import com.ideal.envc.model.dto.SystemComputerNodeQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import com.ideal.common.util.batch.BatchHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SystemComputerNodeServiceImpl单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SystemComputerNodeServiceImpl单元测试")
class SystemComputerNodeServiceImplTest {

    @Mock
    private SystemComputerNodeMapper systemComputerNodeMapper;

    @Mock
    private NodeRelationMapper nodeRelationMapper;

    @Mock
    private NodeRuleContentMapper nodeRuleContentMapper;

    @Mock
    private SystemComputerMapper systemComputerMapper;

    @Mock
    private BatchHandler batchHandler;

    @InjectMocks
    private SystemComputerNodeServiceImpl systemComputerNodeService;

    private SystemComputerNodeDto systemComputerNodeDto;
    private SystemComputerNodeEntity systemComputerNodeEntity;
    private SystemComputerNodeQueryDto systemComputerNodeQueryDto;
    private List<SystemComputerNodeEntity> systemComputerNodeEntityList;
    private Long[] ids;
    private List<SystemComputerNodeListBean> systemComputerNodeListBeans;
    private List<SystemComputerNodeListDto> systemComputerNodeListDtos;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        systemComputerNodeDto = new SystemComputerNodeDto();
        systemComputerNodeDto.setId(1L);
        systemComputerNodeDto.setBusinessSystemId(100L);
        systemComputerNodeDto.setSourceCenterId(10L);
        systemComputerNodeDto.setTargetCenterId(20L);
        systemComputerNodeDto.setSourceComputerId(1001L);
        systemComputerNodeDto.setSourceComputerIp("*************");
        systemComputerNodeDto.setTargetComputerId(2001L);
        systemComputerNodeDto.setTargetComputerIp("*************");
        systemComputerNodeDto.setCreatorId(1L);
        systemComputerNodeDto.setCreatorName("测试用户");
        systemComputerNodeDto.setCreateTime(new Date());

        systemComputerNodeEntity = new SystemComputerNodeEntity();
        systemComputerNodeEntity.setId(1L);
        systemComputerNodeEntity.setBusinessSystemId(100L);
        systemComputerNodeEntity.setSourceCenterId(10L);
        systemComputerNodeEntity.setTargetCenterId(20L);
        systemComputerNodeEntity.setSourceComputerId(1001L);
        systemComputerNodeEntity.setSourceComputerIp("*************");
        systemComputerNodeEntity.setTargetComputerId(2001L);
        systemComputerNodeEntity.setTargetComputerIp("*************");
        systemComputerNodeEntity.setCreatorId(1L);
        systemComputerNodeEntity.setCreatorName("测试用户");
        systemComputerNodeEntity.setCreateTime(new Date());

        systemComputerNodeQueryDto = new SystemComputerNodeQueryDto();
        systemComputerNodeQueryDto.setBusinessSystemId(100L);
        systemComputerNodeQueryDto.setSourceCenterId(10L);
        systemComputerNodeQueryDto.setTargetCenterId(20L);
        systemComputerNodeQueryDto.setSourceComputerIp("*************");
        systemComputerNodeQueryDto.setTargetComputerIp("*************");

        systemComputerNodeEntityList = new ArrayList<>();
        systemComputerNodeEntityList.add(systemComputerNodeEntity);

        // 初始化SystemComputerNodeListBean和SystemComputerNodeListDto
        systemComputerNodeListBeans = new ArrayList<>();
        SystemComputerNodeListBean bean = new SystemComputerNodeListBean();
        bean.setId(1L);
        bean.setBusinessSystemId(100L);
        bean.setSourceCenterId(10L);
        bean.setSourceCenterName("源中心");
        bean.setSourceComputerIp("*************");
        bean.setTargetCenterId(20L);
        bean.setTargetCenterName("目标中心");
        bean.setTargetComputerIp("*************");
        systemComputerNodeListBeans.add(bean);

        systemComputerNodeListDtos = new ArrayList<>();
        SystemComputerNodeListDto dto = new SystemComputerNodeListDto();
        dto.setId(1L);
        dto.setBusinessSystemId(100L);
        dto.setSourceCenterId(10L);
        dto.setSourceCenterName("源中心");
        dto.setSourceComputerIp("*************");
        dto.setTargetCenterId(20L);
        dto.setTargetCenterName("目标中心");
        dto.setTargetComputerIp("*************");
        systemComputerNodeListDtos.add(dto);

        ids = new Long[]{1L, 2L};
    }

    @Test
    @DisplayName("根据ID查询系统与设备节点关系 - 成功场景")
    void selectSystemComputerNodeById_Success() {
        // 准备测试数据
        Long id = 1L;
        SystemComputerNodeEntity entity = createSystemComputerNodeEntity(id);

        // Mock依赖
        when(systemComputerNodeMapper.selectSystemComputerNodeById(id)).thenReturn(entity);

        // 执行测试
        SystemComputerNodeDto result = systemComputerNodeService.selectSystemComputerNodeById(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(id, result.getId());
        verify(systemComputerNodeMapper).selectSystemComputerNodeById(id);
    }

    @Test
    @DisplayName("查询系统与设备节点关系列表 - 成功场景")
    void selectSystemComputerNodeList_Success() {
        // 准备测试数据
        systemComputerNodeQueryDto = new SystemComputerNodeQueryDto();
        List<SystemComputerNodeEntity> entityList = Arrays.asList(
            createSystemComputerNodeEntity(1L),
            createSystemComputerNodeEntity(2L)
        );
        
        // 创建Page对象包装实体列表
        Page<SystemComputerNodeEntity> page = new Page<>(1, 10);
        page.addAll(entityList);
        page.setTotal(entityList.size());

        // 准备DTO列表
        List<SystemComputerNodeDto> dtoList = Arrays.asList(
            createSystemComputerNodeDto(1L),
            createSystemComputerNodeDto(2L)
        );

        // Mock依赖
        doReturn(page).when(systemComputerNodeMapper).selectSystemComputerNodeList(any());

        // 执行测试
        PageInfo<SystemComputerNodeDto> result = systemComputerNodeService.selectSystemComputerNodeList(systemComputerNodeQueryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        verify(systemComputerNodeMapper).selectSystemComputerNodeList(any());
    }

    @Test
    @DisplayName("新增系统与设备节点关系 - 成功场景")
    void insertSystemComputerNode_Success() {
        // 准备测试数据
        SystemComputerNodeDto dto = createSystemComputerNodeDto(null);

        // Mock依赖
        doReturn(1).when(systemComputerNodeMapper).insertSystemComputerNode(any());

        // 执行测试
        int result = systemComputerNodeService.insertSystemComputerNode(dto);

        // 验证结果
        assertEquals(1, result);
        verify(systemComputerNodeMapper).insertSystemComputerNode(any());
    }

    @Test
    @DisplayName("修改系统与设备节点关系 - 成功场景")
    void updateSystemComputerNode_Success() {
        // 准备测试数据
        SystemComputerNodeDto dto = createSystemComputerNodeDto(1L);

        // Mock依赖
        doReturn(1).when(systemComputerNodeMapper).updateSystemComputerNode(any());

        // 执行测试
        int result = systemComputerNodeService.updateSystemComputerNode(dto);

        // 验证结果
        assertEquals(1, result);
        verify(systemComputerNodeMapper).updateSystemComputerNode(any());
    }

    @Test
    @DisplayName("批量删除系统与设备节点关系 - 成功场景")
    void deleteSystemComputerNodeByIds_Success() {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // Mock依赖
        doReturn(2).when(systemComputerNodeMapper).deleteSystemComputerNodeByIds(ids);

        // 执行测试
        int result = systemComputerNodeService.deleteSystemComputerNodeByIds(ids);

        // 验证结果
        assertEquals(2, result);
        verify(nodeRuleContentMapper).deleteNodeRuleContentBySystemComputerNodeIds(ids);
        verify(nodeRelationMapper).deleteNodeRelationBySystemComputerNodeIds(ids);
        verify(systemComputerNodeMapper).deleteSystemComputerNodeByIds(ids);
    }

    @Test
    @DisplayName("检查节点是否存在 - 存在场景")
    void checkNodeExists_Exists() {
        // 准备测试数据
        SystemComputerNodeDto dto = createSystemComputerNodeDto(1L);
        List<SystemComputerNodeEntity> existingNodes = Collections.singletonList(createSystemComputerNodeEntity(1L));

        // Mock依赖
        doReturn(existingNodes).when(systemComputerNodeMapper).selectSystemComputerNodeList(any());

        // 执行测试
        boolean result = systemComputerNodeService.checkNodeExists(dto);

        // 验证结果
        assertTrue(result);
        verify(systemComputerNodeMapper).selectSystemComputerNodeList(any());
    }

    @Test
    @DisplayName("检查节点是否存在 - 不存在场景")
    void checkNodeExists_NotExists() {
        // 准备测试数据
        SystemComputerNodeDto dto = createSystemComputerNodeDto(1L);

        // Mock依赖
        doReturn(Collections.emptyList()).when(systemComputerNodeMapper).selectSystemComputerNodeList(any());

        // 执行测试
        boolean result = systemComputerNodeService.checkNodeExists(dto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerNodeMapper).selectSystemComputerNodeList(any());
    }

    @Test
    @DisplayName("检查节点是否存在 - 参数不完整")
    void checkNodeExists_InvalidParams() {
        // 准备测试数据
        SystemComputerNodeDto dto = new SystemComputerNodeDto();

        // 执行测试
        boolean result = systemComputerNodeService.checkNodeExists(dto);

        // 验证结果
        assertFalse(result);
        verify(systemComputerNodeMapper, never()).selectSystemComputerNodeList(any());
    }

    @Test
    @DisplayName("查询系统已绑定源目标设备列表 - 成功场景")
    void selectSystemComputerNodeBeanList_Success() {
        // 准备测试数据
        SystemComputerNodeQueryDto queryDto = new SystemComputerNodeQueryDto();
        queryDto.setBusinessSystemId(100L);
        List<SystemComputerNodeListBean> beans = Collections.singletonList(createSystemComputerNodeListBean(1L));

        // Mock依赖
        doReturn(beans).when(systemComputerNodeMapper).selectSystemComputerNodeListByCondition(
            anyLong(), any(), any(), any(), any()
        );

        // 执行测试
        PageInfo<SystemComputerNodeListDto> result = systemComputerNodeService.selectSystemComputerNodeBeanList(queryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        verify(systemComputerNodeMapper).selectSystemComputerNodeListByCondition(
            anyLong(), any(), any(), any(), any()
        );
    }

    @Test
    @DisplayName("查询系统已绑定源目标设备列表 - 参数不完整")
    void selectSystemComputerNodeBeanList_InvalidParams() {
        // 准备测试数据
        SystemComputerNodeQueryDto queryDto = new SystemComputerNodeQueryDto();

        // 执行测试
        PageInfo<SystemComputerNodeListDto> result = systemComputerNodeService.selectSystemComputerNodeBeanList(queryDto, 1, 10);

        // 验证结果
        assertTrue(result.getList().isEmpty());
        verify(systemComputerNodeMapper, never()).selectSystemComputerNodeListByCondition(
            anyLong(), anyLong(), anyLong(), anyString(), anyString()
        );
    }

    @Test
    @DisplayName("批量绑定系统与设备节点关系 - 成功场景")
    void batchBindSystemComputerNode_Success() throws ContrastBusinessException {
        // 准备测试数据
        SystemComputerNodeBatchDto batchDto = createSystemComputerNodeBatchDto();
        UserDto userDto = createUserDto(1L);
        
        // 创建计算机实体
        SystemComputerEntity sourceComputer = createSystemComputerEntity(1001L);
        sourceComputer.setComputerId(1001L);
        sourceComputer.setComputerIp("*************");
        
        SystemComputerEntity targetComputer = createSystemComputerEntity(2001L);
        targetComputer.setComputerId(2001L);
        targetComputer.setComputerIp("*************");
        
        // 创建计算机映射
        Map<Long, SystemComputerEntity> computerMap = new HashMap<>();
        computerMap.put(1001L, sourceComputer);
        computerMap.put(2001L, targetComputer);

        // Mock依赖
        doReturn(computerMap).when(systemComputerMapper).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        doReturn(Collections.emptyList()).when(systemComputerNodeMapper).selectSystemComputerNodeList(any());
        
        // 正确模拟batchHandler.batchData方法
        doAnswer(invocation -> {
            List<?> dataList = invocation.getArgument(0);
            return null; // 方法返回值为void
        }).when(batchHandler).batchData(anyList(), any(), anyInt());
        
        // 执行测试
        int result = systemComputerNodeService.batchBindSystemComputerNode(batchDto, userDto);

        // 验证结果
        assertEquals(1, result); // 只有一个目标计算机能成功绑定
        verify(systemComputerMapper).selectComputerIpMapByIdsAndSystemId(anyList(), anyLong());
        verify(systemComputerNodeMapper).selectSystemComputerNodeList(any());
        verify(batchHandler).batchData(anyList(), any(), anyInt());
    }

    private SystemComputerNodeEntity createSystemComputerNodeEntity(Long id) {
        SystemComputerNodeEntity entity = new SystemComputerNodeEntity();
        entity.setId(id);
        entity.setBusinessSystemId(100L);
        entity.setSourceCenterId(10L);
        entity.setTargetCenterId(20L);
        entity.setSourceComputerId(1001L);
        entity.setSourceComputerIp("*************");
        entity.setTargetComputerId(2001L);
        entity.setTargetComputerIp("*************");
        return entity;
    }

    private SystemComputerNodeDto createSystemComputerNodeDto(Long id) {
        SystemComputerNodeDto dto = new SystemComputerNodeDto();
        dto.setId(id);
        dto.setBusinessSystemId(100L);
        dto.setSourceCenterId(10L);
        dto.setTargetCenterId(20L);
        dto.setSourceComputerId(1001L);
        dto.setSourceComputerIp("*************");
        dto.setTargetComputerId(2001L);
        dto.setTargetComputerIp("*************");
        return dto;
    }

    private SystemComputerNodeListBean createSystemComputerNodeListBean(Long id) {
        SystemComputerNodeListBean bean = new SystemComputerNodeListBean();
        bean.setId(id);
        bean.setBusinessSystemId(100L);
        bean.setSourceCenterId(10L);
        bean.setSourceCenterName("源中心");
        bean.setSourceComputerIp("*************");
        bean.setTargetCenterId(20L);
        bean.setTargetCenterName("目标中心");
        bean.setTargetComputerIp("*************");
        return bean;
    }

    private SystemComputerNodeBatchDto createSystemComputerNodeBatchDto() {
        SystemComputerNodeBatchDto dto = new SystemComputerNodeBatchDto();
        dto.setBusinessSystemId(100L);
        dto.setSourceCenterId(10L);
        dto.setTargetCenterId(20L);
        dto.setSourceComputerId(1001L);
        dto.setTargetComputerIdList(Arrays.asList(2001L, 2002L));
        return dto;
    }

    private UserDto createUserDto(Long id) {
        UserDto dto = new UserDto();
        dto.setId(id);
        dto.setFullName("测试用户");
        return dto;
    }

    private SystemComputerEntity createSystemComputerEntity(Long id) {
        SystemComputerEntity entity = new SystemComputerEntity();
        entity.setId(id);
        entity.setComputerId(id);
        entity.setComputerIp("192.168.1." + (id % 100));
        return entity;
    }
} 