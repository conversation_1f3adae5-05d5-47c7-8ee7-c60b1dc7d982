package com.ideal.envc.common;

import com.ideal.envc.config.Base64SecurityConfig;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ContrastToolUtils新版本单元测试
 * 专门测试调整后的getFromBase64方法的安全功能
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ContrastToolUtilsNewTest {

    @Test
    @DisplayName("Test valid Base64 decoding")
    public void testGetFromBase64_ValidBase64() {
        String input = "SGVsbG8gV29ybGQ="; // "Hello World" in base64
        String result = ContrastToolUtils.getFromBase64(input);
        assertEquals("Hello World", result);
    }

    @Test
    @DisplayName("Test null input")
    public void testGetFromBase64_NullInput() {
        String result = ContrastToolUtils.getFromBase64(null);
        assertNull(result);
    }

    @Test
    @DisplayName("Test empty string input")
    public void testGetFromBase64_EmptyInput() {
        String result = ContrastToolUtils.getFromBase64("");
        assertNull(result);
    }

    @Test
    @DisplayName("Test blank string input")
    public void testGetFromBase64_BlankInput() {
        String result = ContrastToolUtils.getFromBase64("   ");
        assertNull(result);
    }

    @Test
    @DisplayName("Test input length exceeds security limit")
    public void testGetFromBase64_InputLengthExceedsLimit() {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set small input length limit for testing
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(10);
            
            String input = "SGVsbG8gV29ybGQgVGhpcyBpcyBhIGxvbmcgc3RyaW5n"; // Length exceeds 10
            String result = ContrastToolUtils.getFromBase64(input);
            assertNull(result);
        }
    }

    @Test
    @DisplayName("Test decoded content size exceeds security limit")
    public void testGetFromBase64_DecodedSizeExceedsLimit() {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set small decoded size limit for testing
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(1000);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(5);
            
            String input = "SGVsbG8gV29ybGQ="; // "Hello World" decoded length is 11, exceeds limit 5
            String result = ContrastToolUtils.getFromBase64(input);
            assertNull(result);
        }
    }

    @Test
    @DisplayName("Test Base64 decoding exception")
    public void testGetFromBase64_DecodingException() {
        String input = "Invalid!@#$%^&*()"; // Use obviously invalid BASE64 format input
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @Test
    @DisplayName("Test Base64 string with invalid characters")
    public void testGetFromBase64_InvalidCharacters() {
        String input = "SGVsbG8gV29ybGQ=!"; // Added an invalid character !
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @Test
    @DisplayName("Test edge case: only padding characters in base64")
    public void testGetFromBase64_OnlyPaddingCharacters() {
        String input = "====";
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @ParameterizedTest
    @DisplayName("Test various invalid Base64 inputs")
    @ValueSource(strings = {"", "   ", "Invalid!@#", "SGVsbG8=!", "123", "abc", "!@#$%^&*()"})
    public void testGetFromBase64_InvalidInputs(String input) {
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @ParameterizedTest
    @DisplayName("Test various valid Base64 inputs")
    @MethodSource("validBase64Inputs")
    public void testGetFromBase64_ValidInputs(String input, String expected) {
        String result = ContrastToolUtils.getFromBase64(input);
        assertEquals(expected, result);
    }

    private static Stream<Object[]> validBase64Inputs() {
        return Stream.of(
            new Object[]{"SGVsbG8=", "Hello"},
            new Object[]{"V29ybGQ=", "World"},
            new Object[]{"SGVsbG8gV29ybGQ=", "Hello World"},
            new Object[]{"VGVzdA==", "Test"},
            new Object[]{"MTIz", "123"}
        );
    }

    @Test
    @DisplayName("Test Base64SecurityConfig configuration reading")
    public void testGetFromBase64_SecurityConfigIntegration() {
        // Test configuration reading in normal cases
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(1000);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(1000);
            
            String input = "SGVsbG8="; // "Hello"
            String result = ContrastToolUtils.getFromBase64(input);
            assertEquals("Hello", result);
        }
    }

    @Test
    @DisplayName("Test input at exact security limit boundary")
    public void testGetFromBase64_AtSecurityLimitBoundary() {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set limits exactly at the input size
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(16);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(11);
            
            String input = "SGVsbG8gV29ybGQ="; // "Hello World", length 16, decoded length 11
            String result = ContrastToolUtils.getFromBase64(input);
            assertEquals("Hello World", result);
        }
    }

    @Test
    @DisplayName("Test input just below security limit")
    public void testGetFromBase64_JustBelowSecurityLimit() {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set limits just above the input size
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(17);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(12);
            
            String input = "SGVsbG8gV29ybGQ="; // "Hello World", length 16, decoded length 11
            String result = ContrastToolUtils.getFromBase64(input);
            assertEquals("Hello World", result);
        }
    }

    @Test
    @DisplayName("Test input just above security limit")
    public void testGetFromBase64_JustAboveSecurityLimit() {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set limits just below the input size
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(15);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(10);
            
            String input = "SGVsbG8gV29ybGQ="; // "Hello World", length 16, decoded length 11
            String result = ContrastToolUtils.getFromBase64(input);
            assertNull(result);
        }
    }

    @Test
    @DisplayName("Test trimmed input processing")
    public void testGetFromBase64_TrimmedInput() {
        String input = "  SGVsbG8=  "; // "Hello" with spaces
        String result = ContrastToolUtils.getFromBase64(input);
        assertEquals("Hello", result);
    }

    @Test
    @DisplayName("Test very long valid Base64 string")
    public void testGetFromBase64_VeryLongValidString() {
        // Create a long but valid base64 string
        StringBuilder longInput = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longInput.append("SGVsbG8="); // "Hello" repeated
        }
        
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set high limits to allow this test
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(10000);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(10000);
            
            String result = ContrastToolUtils.getFromBase64(longInput.toString());
            // Should fail due to invalid base64 format (concatenated base64 strings)
            assertNull(result);
        }
    }

    @Test
    @DisplayName("Test special characters in Base64")
    public void testGetFromBase64_SpecialCharacters() {
        String input = "SGVsbG8rV29ybGQ/"; // Valid base64 with + and /
        String result = ContrastToolUtils.getFromBase64(input);
        assertNotNull(result);
    }

    @ParameterizedTest
    @DisplayName("Test security limits with different configurations")
    @MethodSource("securityLimitTestCases")
    public void testGetFromBase64_SecurityLimits(int maxInputLength, int maxDecodedSize, String input, boolean shouldSucceed) {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(maxInputLength);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(maxDecodedSize);
            
            String result = ContrastToolUtils.getFromBase64(input);
            
            if (shouldSucceed) {
                assertNotNull(result);
            } else {
                assertNull(result);
            }
        }
    }

    private static Stream<Object[]> securityLimitTestCases() {
        return Stream.of(
            // maxInputLength, maxDecodedSize, input, shouldSucceed
            new Object[]{100, 100, "SGVsbG8=", true},      // Normal case
            new Object[]{5, 100, "SGVsbG8=", false},       // Input too long
            new Object[]{100, 3, "SGVsbG8=", false},       // Decoded too large
            new Object[]{8, 5, "SGVsbG8=", true},          // Exactly at limits
            new Object[]{7, 5, "SGVsbG8=", false},         // Input just over limit
            new Object[]{8, 4, "SGVsbG8=", false}          // Decoded just over limit
        );
    }

    @Test
    @DisplayName("Test system property configuration fallback")
    public void testGetFromBase64_SystemPropertyFallback() {
        // This test verifies that the method works with default configuration
        // when no mocking is applied (uses actual Base64SecurityConfig)
        String input = "SGVsbG8="; // "Hello"
        String result = ContrastToolUtils.getFromBase64(input);
        assertEquals("Hello", result);
    }
}
