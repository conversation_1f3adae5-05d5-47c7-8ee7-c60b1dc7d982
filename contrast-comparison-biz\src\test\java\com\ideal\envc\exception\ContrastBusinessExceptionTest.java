package com.ideal.envc.exception;

import org.junit.Test;
import static org.junit.Assert.*;

/**
 * ContrastBusinessException单元测试
 * <AUTHOR>
 */
public class ContrastBusinessExceptionTest {

    @Test
    public void testConstructorWithMessage() {
        String message = "测试业务异常消息";
        ContrastBusinessException exception = new ContrastBusinessException(message);
        
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithMessageAndCause() {
        String message = "测试业务异常消息";
        Throwable cause = new RuntimeException("原始异常");
        ContrastBusinessException exception = new ContrastBusinessException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    public void testConstructorWithNullMessage() {
        ContrastBusinessException exception = new ContrastBusinessException(null);
        
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithEmptyMessage() {
        String message = "";
        ContrastBusinessException exception = new ContrastBusinessException(message);
        
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testConstructorWithNullMessageAndCause() {
        Throwable cause = new RuntimeException("原始异常");
        ContrastBusinessException exception = new ContrastBusinessException(null, cause);
        
        assertNull(exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    public void testConstructorWithMessageAndNullCause() {
        String message = "测试业务异常消息";
        ContrastBusinessException exception = new ContrastBusinessException(message, null);
        
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    public void testExceptionInheritance() {
        ContrastBusinessException exception = new ContrastBusinessException("测试消息");
        
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    @Test
    public void testExceptionStackTrace() {
        ContrastBusinessException exception = new ContrastBusinessException("测试消息");
        
        StackTraceElement[] stackTrace = exception.getStackTrace();
        assertNotNull(stackTrace);
        assertTrue(stackTrace.length > 0);
    }
} 