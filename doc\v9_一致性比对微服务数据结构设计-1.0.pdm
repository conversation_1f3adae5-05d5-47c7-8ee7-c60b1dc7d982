<?xml version="1.0" encoding="UTF-8"?>
<?PowerDesigner AppLocale="UTF16" ID="{A8C8815B-885E-4C12-AFF2-2C01D67A88BE}" Label="" LastModificationDate="1744598633" Name="一致性比对服务" Objects="352" Symbols="52" Target="MySQL 5.0" Type="{CDE44E21-9669-11D1-9914-006097355D9B}" signature="PDM_DATA_MODEL_XML" version="16.5.0.3982"?>
<!-- do not edit this file -->

<Model xmlns:a="attribute" xmlns:c="collection" xmlns:o="object">

<o:RootObject Id="o1">
<c:Children>
<o:Model Id="o2">
<a:ObjectID>A8C8815B-885E-4C12-AFF2-2C01D67A88BE</a:ObjectID>
<a:Name>一致性比对服务</a:Name>
<a:Code>一致性比对服务</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PackageOptionsText>[FolderOptions]

[FolderOptions\Physical Objects]
GenerationCheckModel=Yes
GenerationPath=
GenerationOptions=
GenerationTasks=
GenerationTargets=
GenerationSelections=
RevPkey=Yes
RevFkey=Yes
RevAkey=Yes
RevCheck=Yes
RevIndx=Yes
RevOpts=Yes
RevViewAsTabl=No
RevViewOpts=Yes
RevSystAsTabl=Yes
RevTablPerm=No
RevViewPerm=No
RevProcPerm=No
RevDbpkPerm=No
RevSqncPerm=No
RevAdtPerm=No
RevUserPriv=No
RevUserOpts=No
RevGrpePriv=No
RevRolePriv=No
RevDtbsOpts=Yes
RevDtbsPerm=No
RevViewIndx=Yes
RevJidxOpts=Yes
RevStats=No
RevTspcPerm=No
RevCaseSensitive=No
GenTrgrStdMsg=Yes
GenTrgrMsgTab=
GenTrgrMsgNo=
GenTrgrMsgTxt=
TrgrPreserve=No
TrgrIns=Yes
TrgrUpd=Yes
TrgrDel=Yes
TrgrC2Ins=Yes
TrgrC2Upd=Yes
TrgrC3=Yes
TrgrC4=Yes
TrgrC5=Yes
TrgrC6=Yes
TrgrC7=Yes
TrgrC8=Yes
TrgrC9=Yes
TrgrC10=Yes
TrgrC11=Yes
TrgrC1=Yes
TrgrC12Ins=Yes
TrgrC12Upd=Yes
TrgrC13=Yes
UpdateTableStatistics=Yes
UpdateColumnStatistics=Yes

[FolderOptions\Physical Objects\Database Generation]
GenScriptName=crebas.sql
GenScriptName0=
GenScriptName1=
GenScriptName2=
GenScriptName3=
GenScriptName4=
GenScriptName5=
GenScriptName6=
GenScriptName7=
GenScriptName8=
GenScriptName9=
GenPathName=F:\工作相关\项目相关\微服务改造\pdm\doc\database-microservice\
GenSingleFile=Yes
GenODBC=No
GenCheckModel=Yes
GenScriptPrev=Yes
GenArchiveModel=No
GenUseSync=No
GenSyncChoice=0
GenSyncArch=
GenSyncRmg=0

[FolderOptions\Physical Objects\Database Generation\Format]
GenScriptTitle=Yes
GenScriptNamLabl=No
GenScriptQDtbs=No
GenScriptQOwnr=Yes
GenScriptCase=0
GenScriptEncoding=ANSI
GenScriptNAcct=No
IdentifierDelimiter=&quot;

[FolderOptions\Physical Objects\Database Generation\Database]
Create=Yes
Open=Yes
Close=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Database\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Tablespace]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Tablespace\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Storage]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\User]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\User\Create]
Physical Options=No

[FolderOptions\Physical Objects\Database Generation\Group]
Create=Yes
Drop=Yes
Comment=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\Role]
Create=Yes
Drop=Yes
Privilege=No

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\UserDefinedDataType\Create]
Default value=Yes
Check=Yes

[FolderOptions\Physical Objects\Database Generation\AbstractDataType]
Create=Yes
Header=Yes
Footer=Yes
Drop=Yes
Comment=Yes
Install JAVA class=Yes
Remove JAVA class=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Rule]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Default]
Create=Yes
Comment=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Sequence]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create]
Check=Yes
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Table\Create\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column]
User datatype=No
Default value=Yes
Check=Yes
Physical Options=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Column\Check]
Constraint declaration=No

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key]

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Primary key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Key\Alternate key\Create]
Constraint declaration=No
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Foreign key\Create]
Constraint declaration=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Create]
Constraint declaration=Yes
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Index\Filter]
Primary key=No
Foreign key=No
Alternate key=No
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Table&amp;&amp;Column\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\View]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\View\Create]
Force Column list=No
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewColumn]
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Create]
Physical Options=Yes

[FolderOptions\Physical Objects\Database Generation\View\ViewIndex\Filter]
Cluster=Yes
Other=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\View\Trigger\Filter]
For insert=Yes
For update=Yes
For delete=Yes
For other=Yes

[FolderOptions\Physical Objects\Database Generation\DBMSTrigger]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synonym\Filter]
Table=Yes
View=Yes
Proc=Yes
Synonym=Yes
Database Package=Yes
Sequence=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\JoinIndex\Create]
Physical Options=Yes
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\Procedure]
Create=Yes
Drop=Yes
Comment=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\Procedure\Create]
Header=Yes
Footer=Yes

[FolderOptions\Physical Objects\Database Generation\DatabasePackage]
Create=Yes
Drop=Yes
Permission=No

[FolderOptions\Physical Objects\Database Generation\WebService]
Create=Yes
Drop=Yes
Comment=Yes

[FolderOptions\Physical Objects\Database Generation\Dimension]
Create=Yes
Drop=Yes

[FolderOptions\Physical Objects\Database Generation\Synchronization]
GenBackupTabl=1
GenKeepBackTabl=1
GenTmpTablDrop=No
GenKeepTablOpts=No

[FolderOptions\Physical Objects\Test Data]
GenDataPathName=
GenDataSinglefile=Yes
GenDataScriptName=testdata
GenDataScriptName0=
GenDataScriptName1=
GenDataScriptName2=
GenDataScriptName3=
GenDataScriptName4=
GenDataScriptName5=
GenDataScriptName6=
GenDataScriptName7=
GenDataScriptName8=
GenDataScriptName9=
GenDataOdbc=0
GenDataDelOld=No
GenDataTitle=No
GenDataDefNumRows=20
GenDataCommit=0
GenDataPacket=0
GenDataOwner=No
GenDataProfNumb=
GenDataProfChar=
GenDataProfDate=
GenDataCSVSeparator=,
GenDataFileFormat=CSV
GenDataUseWizard=No

[FolderOptions\Pdm]
IndxIQName=%COLUMN%_%INDEXTYPE%
IndxPK=Yes
IndxFK=Yes
IndxAK=Yes
IndxPKName=%TABLE%_PK
IndxFKName=%REFR%_FK
IndxAKName=%AKEY%_AK
IndxPreserve=No
IndxThreshold=0
IndxStats=No
RefrPreserve=No
JidxPreserve=No
RbldMultiFact=Yes
RbldMultiDim=Yes
RbldMultiJidx=Yes
CubePreserve=No
TablStProcPreserve=No
ProcDepPreserve=Yes
TrgrDepPreserve=Yes
CubeScriptPath=
CubeScriptCase=0
CubeScriptEncoding=ANSI
CubeScriptNacct=No
CubeScriptHeader=No
CubeScriptExt=csv
CubeScriptExt0=txt
CubeScriptExt1=
CubeScriptExt2=
CubeScriptSep=,
CubeScriptDeli=&quot;
EstimationYears=0
DfltDomnName=D_%.U:VALUE%
DfltColnName=D_%.U:VALUE%
DfltReuse=Yes
DfltDrop=Yes

[FolderOptions\CheckModel]

[FolderOptions\CheckModel\Package]

[FolderOptions\CheckModel\Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CheckPackageMissTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\DefaultCheckPackageMissTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularReference]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ConstraintName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CnstMaxLen]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\CircularDependency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Package\ShortcutUniqCode]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table]

[FolderOptions\CheckModel\Table\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\UniqIndex]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - INDXCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - KEYCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyCollYesYes]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\TableIndexes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Table\CheckTablePartitionKey]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableStartDate]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableRefNoLifecycle]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableSourceMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTablePartialColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableKeyColumnMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\CheckTableNotOnLifecycleTablespace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table\MYSQL50_Table_Table_storage_type]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column]

[FolderOptions\CheckModel\Table.Column\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\DomainDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnMandatory]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyDttpDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\FkeyCheckDivergence]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncNoKey]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColnSqncDttp]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\SerialColumnFK]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\ColumnCompExpr]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnOneToOneMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnDataTypeMapping]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckColumnNoMapping]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Auto_increment_key]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Column\MYSQL50_Column_Datatype_attributes]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index]

[FolderOptions\CheckModel\Table.Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\UndefIndexType]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\IQIndxHNGUniq]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Index\MYSQL50_Index_Fulltext_indexes_validity]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key]

[FolderOptions\CheckModel\Table.Key\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\EmptyColl - COLNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Key\MultiKeySqnc]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger]

[FolderOptions\CheckModel\Table.Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table.Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index]

[FolderOptions\CheckModel\Join Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Join Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View]

[FolderOptions\CheckModel\View\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\View.View Index]

[FolderOptions\CheckModel\View.View Index\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\EmptyColl - CIDXCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\IndexColumnCount]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View.View Index\CheckIncludeColl - Tabl]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference]

[FolderOptions\CheckModel\Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\Reflexive]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\EmptyColl - RFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\IncompleteJoin]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Reference\JoinOrder]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference]

[FolderOptions\CheckModel\View Reference\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\View Reference\EmptyColl - VRFJNCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain]

[FolderOptions\CheckModel\Domain\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckNumParam]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckPrecSupLng]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Domain\CheckDttpIncompatibleFormat]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default]

[FolderOptions\CheckModel\Default\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltValeEmpty]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Default\DfltSameVale]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User]

[FolderOptions\CheckModel\User\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\User\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Group]

[FolderOptions\CheckModel\Group\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Group\UniquePassword]
CheckSeverity=No
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Role]

[FolderOptions\CheckModel\Role\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Role\EmptyColl - USERCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure]

[FolderOptions\CheckModel\Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\ProcBodyEmpty]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Procedure\EmptyColl - PERMCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\DBMS Trigger]

[FolderOptions\CheckModel\DBMS Trigger\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\DBMS Trigger\DbmsTriggerEvent]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source]

[FolderOptions\CheckModel\Data Source\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\EmptyColl - MODLSRC]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\DtscTargets]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Source\CheckDataSourceModels]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning]

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Horizontal Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning]

[FolderOptions\CheckModel\Vertical Partitioning\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\EmptyColl - PARTCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Vertical Partitioning\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing]

[FolderOptions\CheckModel\Table Collapsing\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\EmptyColl - TargetTable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Table Collapsing\TargetTables]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact]

[FolderOptions\CheckModel\Fact\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - MEASCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\EmptyColl - ALLOLINKCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact\CubeDupAssociation]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension]

[FolderOptions\CheckModel\Dimension\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\EmptyColl - HIERCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDupHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\DimnDefHierarchy]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\Mapping]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\MappingSFMap]
CheckSeverity=No
FixRequested=Yes
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension\SerialColumnNumber]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association]

[FolderOptions\CheckModel\Association\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Association\EmptyColl - Hierarchy]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute]

[FolderOptions\CheckModel\Dimension.Attribute\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Attribute\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure]

[FolderOptions\CheckModel\Fact.Measure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Fact.Measure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy]

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Dimension.Hierarchy\EmptyColl - DATTRCOL]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym]

[FolderOptions\CheckModel\Synonym\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Synonym\EmptyColl - BASEOBJ]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type]

[FolderOptions\CheckModel\Abstract Data Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtInstantiable]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type\AdtAbstractUsed]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure]

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\AdtProcUniqName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Abstract Data Type.Abstract Data Type Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package]

[FolderOptions\CheckModel\Database Package\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - NAME]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - PROCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package\EmptyColl - CURCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - VARCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - TYPCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package\EmptyColl - EXCCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure]

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Procedure\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Procedure\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence]

[FolderOptions\CheckModel\Sequence\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Sequence\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor]

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\ReturnDataType]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Cursor\EmptyColl - PARM]
CheckSeverity=Yes
FixRequested=No
CheckRequested=No

[FolderOptions\CheckModel\Database Package.Database Package Variable]

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Variable\CheckUndefDttp]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type]

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Type\UniqueDefinition]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception]

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database Package.Database Package Exception\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace]

[FolderOptions\CheckModel\Tablespace\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Tablespace\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage]

[FolderOptions\CheckModel\Storage\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Storage\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database]

[FolderOptions\CheckModel\Database\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Database\IsObjectUsed]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service]

[FolderOptions\CheckModel\Web Service\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation]

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Web Service.Web Operation\MaxLen - CODE]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle]

[FolderOptions\CheckModel\Lifecycle\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecyclePhase]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckLifecycleRetention]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle\CheckPartitionRange]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase]

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIQTbspace]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDuplicateTbspace]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseTbspaceCurrency]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseRetention]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseIdlePeriod]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseDataSource]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Lifecycle.Phase\CheckPhaseExternalOnFirst]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Replication]

[FolderOptions\CheckModel\Replication\PartialReplication]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule]

[FolderOptions\CheckModel\Business Rule\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Business Rule\EmptyColl - OBJCOL]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object]

[FolderOptions\CheckModel\Extended Object\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Object\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link]

[FolderOptions\CheckModel\Extended Link\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Extended Link\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File]

[FolderOptions\CheckModel\File\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\File\CheckPathExists]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format]

[FolderOptions\CheckModel\Data Format\CheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseOnlyTerms]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\DefaultCheckUseTermBySynonym]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\NotApprovedTerms]
CheckSeverity=Yes
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueName]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\UniqueCode]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes

[FolderOptions\CheckModel\Data Format\CheckDataFormatNullExpression]
CheckSeverity=No
FixRequested=No
CheckRequested=Yes</a:PackageOptionsText>
<a:ModelOptionsText>[ModelOptions]

[ModelOptions\Physical Objects]
CaseSensitive=No
DisplayName=Yes
EnableTrans=No
UseTerm=No
EnableRequirements=No
EnableFullShortcut=Yes
DefaultDttp=
IgnoreOwner=No
RebuildTrigger=Yes
RefrUnique=No
RefrAutoMigrate=Yes
RefrMigrateReuse=Yes
RefrMigrateDomain=Yes
RefrMigrateCheck=Yes
RefrMigrateRule=Yes
RefrMigrateExtd=No
RefrMigrDefaultLink=No
RefrDfltImpl=D
RefrPrgtColn=No
RefrMigrateToEnd=No
RebuildTriggerDep=No
ColnFKName=%.3:PARENT%_%COLUMN%
ColnFKNameUse=No
DomnCopyDttp=Yes
DomnCopyChck=No
DomnCopyRule=No
DomnCopyMand=No
DomnCopyExtd=No
DomnCopyProf=No
Notation=0
DomnDefaultMandatory=No
ColnDefaultMandatory=No
TablDefaultOwner=
ViewDefaultOwner=
TrgrDefaultOwnerTabl=
TrgrDefaultOwnerView=
IdxDefaultOwnerTabl=
IdxDefaultOwnerView=
JdxDefaultOwner=
DBPackDefaultOwner=
SeqDefaultOwner=
ProcDefaultOwner=
DBMSTrgrDefaultOwner=
Currency=USD
RefrDeleteConstraint=1
RefrUpdateConstraint=1
RefrParentMandatory=No
RefrParentChangeAllow=Yes
RefrCheckOnCommit=No

[ModelOptions\Physical Objects\NamingOptionsTemplates]

[ModelOptions\Physical Objects\ClssNamingOptions]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMPCKG\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN]

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\PDMDOMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL]

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\TABL\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN]

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\COLN\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX]

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\INDX\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR]

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\REFR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF]

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VREF\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEW\Code]
Template=
MaxLen=64
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC]

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\VIEWC\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBSERV\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP]

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WEBOP\Code]
Template=
MaxLen=254
Case=M
ValidChar=&#39;a&#39;-&#39;z&#39;,&#39;A&#39;-&#39;Z&#39;,&#39;0&#39;-&#39;9&#39;,&quot;/-_.!~*&#39;()&quot;
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM]

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\WPARAM\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT]

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FACT\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN]

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DIMN\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS]

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\MEAS\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR]

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DATTR\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO]

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FILO\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMEOBJ\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK]

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\FRMELNK\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass]

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Name]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Physical Objects\ClssNamingOptions\DefaultClass\Code]
Template=
MaxLen=254
Case=M
ValidChar=
InvldChar=
AllValid=Yes
NoAccent=No
DefaultChar=
Script=
ConvTable=
ConvTablePath=%_HOME%\Resource Files\Conversion Tables

[ModelOptions\Connection]

[ModelOptions\Pdm]

[ModelOptions\Generate]

[ModelOptions\Generate\Xsm]
GenRootElement=Yes
GenComplexType=No
GenAttribute=Yes
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=No

[ModelOptions\Generate\Pdm]
RRMapping=No

[ModelOptions\Generate\Cdm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No
Notation=2

[ModelOptions\Generate\Oom]
CheckModel=Yes
SaveLinks=Yes
ORMapping=No
NameToCode=Yes
ClassPrefix=

[ModelOptions\Generate\Ldm]
CheckModel=Yes
SaveLinks=Yes
NameToCode=No

[ModelOptions\Default Opts]

[ModelOptions\Default Opts\TABL]
PhysOpts=

[ModelOptions\Default Opts\COLN]
PhysOpts=

[ModelOptions\Default Opts\INDX]
PhysOpts=

[ModelOptions\Default Opts\AKEY]
PhysOpts=

[ModelOptions\Default Opts\PKEY]
PhysOpts=

[ModelOptions\Default Opts\STOR]
PhysOpts=

[ModelOptions\Default Opts\TSPC]
PhysOpts=

[ModelOptions\Default Opts\SQNC]
PhysOpts=

[ModelOptions\Default Opts\DTBS]
PhysOpts=

[ModelOptions\Default Opts\USER]
PhysOpts=

[ModelOptions\Default Opts\JIDX]
PhysOpts=</a:ModelOptionsText>
<c:DBMS>
<o:Shortcut Id="o3">
<a:ObjectID>D6566FA6-AF4F-4485-8A43-F05939C9D2FE</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:TargetStereotype/>
<a:TargetID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetID>
<a:TargetClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetClassID>
</o:Shortcut>
</c:DBMS>
<c:PhysicalDiagrams>
<o:PhysicalDiagram Id="o4">
<a:ObjectID>D2AB6FC7-A52E-4F2A-876B-BEF16ADB8BE9</a:ObjectID>
<a:Name>PhysicalDiagram_1</a:Name>
<a:Code>PhysicalDiagram_1</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DisplayPreferences>[DisplayPreferences]

[DisplayPreferences\PDM]

[DisplayPreferences\General]
Adjust to text=Yes
Snap Grid=No
Constrain Labels=Yes
Display Grid=No
Show Page Delimiter=Yes
Show Links intersections=No
Activate automatic link routing=No
Grid size=800
Graphic unit=2
Window color=255 255 255
Background image=
Background mode=8
Watermark image=
Watermark mode=8
Show watermark on screen=No
Gradient mode=0
Gradient end color=255 255 255
Show Swimlane=No
SwimlaneVert=Yes
TreeVert=No
CompDark=0

[DisplayPreferences\Object]
Show Icon=No
Mode=2
Trunc Length=40
Word Length=40
Word Text=!&quot;#$%&amp;&#39;)*+,-./:;=&gt;?@\]^_`|}~
Shortcut IntIcon=Yes
Shortcut IntLoct=Yes
Shortcut IntFullPath=No
Shortcut IntLastPackage=Yes
Shortcut ExtIcon=Yes
Shortcut ExtLoct=No
Shortcut ExtFullPath=No
Shortcut ExtLastPackage=Yes
Shortcut ExtIncludeModl=Yes
EObjShowStrn=Yes
ExtendedObject.Comment=No
ExtendedObject.IconPicture=No
ExtendedObject.TextStyle=No
ExtendedObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Object Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
ELnkShowStrn=Yes
ELnkShowName=Yes
ExtendedLink_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
FileObject.Stereotype=No
FileObject.DisplayName=Yes
FileObject.LocationOrName=No
FileObject.IconPicture=No
FileObject.TextStyle=No
FileObject.IconMode=Yes
FileObject_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Location&quot; Attribute=&quot;LocationOrName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Package.Stereotype=Yes
Package.Comment=No
Package.IconPicture=No
Package.TextStyle=No
Package_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Display Model Version=Yes
Table.Stereotype=Yes
Table.DisplayName=Yes
Table.OwnerDisplayName=No
Table.Columns=Yes
Table.Columns._Filter=&quot;All Columns&quot; PDMCOLNALL
Table.Columns._Columns=Stereotype DataType KeyIndicator
Table.Columns._Limit=-5
Table.Keys=No
Table.Keys._Columns=Stereotype Indicator
Table.Indexes=No
Table.Indexes._Columns=Stereotype
Table.Triggers=No
Table.Triggers._Columns=Stereotype
Table.Comment=Yes
Table.IconPicture=No
Table.TextStyle=No
Table_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nDomain No\r\nKeyIndicator No\r\nIndexIndicator No\r\nNullStatus No&quot; Filters=&quot;&amp;quot;All Columns&amp;quot;  PDMCOLNALL &amp;quot;&amp;quot;\r\n&amp;quot;PK Columns&amp;quot;  PDMCOLNPK &amp;quot;\&amp;quot;PRIM \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;\r\n&amp;quot;Key Columns&amp;quot;  PDMCOLNKEY &amp;quot;\&amp;quot;KEYS \&amp;quot;TRUE\&amp;quot; TRUE\&amp;quot;&amp;quot;&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Keys&quot; Collection=&quot;Keys&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes\r\nIndicator No&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Triggers&quot; Collection=&quot;Triggers&quot; Columns=&quot;Stereotype No\r\nDisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
View.Stereotype=Yes
View.DisplayName=Yes
View.OwnerDisplayName=No
View.Columns=Yes
View.Columns._Columns=DisplayName
View.Columns._Limit=-5
View.TemporaryVTables=Yes
View.Indexes=No
View.Comment=Yes
View.IconPicture=No
View.TextStyle=No
View_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Columns&quot; Collection=&quot;Columns&quot; Columns=&quot;DisplayName No\r\nExpression No\r\nDataType No\r\nSymbolDataType No &amp;quot;Domain or Data type&amp;quot;\r\nIndexIndicator No&quot; HasLimit=&quot;Yes&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Tables&quot; Collection=&quot;TemporaryVTables&quot; Columns=&quot;Name Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardCollection Name=&quot;Indexes&quot; Collection=&quot;Indexes&quot; Columns=&quot;DisplayName Yes&quot; HasLimit=&quot;No&quot; HideEmpty=&quot;No&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Procedure.Stereotype=No
Procedure.DisplayName=Yes
Procedure.OwnerDisplayName=No
Procedure.Comment=No
Procedure.IconPicture=No
Procedure.TextStyle=No
Procedure_SymbolLayout=&lt;Form&gt;[CRLF] &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;Yes&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Owner and Name&quot; Attribute=&quot;OwnerDisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/ExclusiveChoice&gt;[CRLF] &lt;Separator Name=&quot;Separator&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Comment&quot; Attribute=&quot;Comment&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;LEFT&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Icon&quot; Attribute=&quot;IconPicture&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF] &lt;StandardAttribute Name=&quot;Force top align&quot; Attribute=&quot;TextStyle&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Alignment=&quot;CNTR&quot; Caption=&quot;&quot; Mandatory=&quot;Yes&quot; /&gt;[CRLF]&lt;/Form&gt;
Reference.Cardinality=No
Reference.ImplementationType=No
Reference.ChildRole=Yes
Reference.Stereotype=Yes
Reference.DisplayName=No
Reference.ForeignKeyConstraintName=No
Reference.JoinExpression=No
Reference.Integrity=No
Reference.ParentRole=Yes
Reference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Cardinality&quot; Attribute=&quot;Cardinality&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Implementation&quot; Attribute=&quot;ImplementationType&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Cons&amp;amp;traint Name&quot; Attribute=&quot;ForeignKeyConstraintName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Cons&amp;amp;traint Name&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Join&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF]  &lt;StandardAttribute Name=&quot;Referential integrity&quot; Attribute=&quot;Integrity&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;Referential integrity&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;
ViewReference.ChildRole=Yes
ViewReference.Stereotype=Yes
ViewReference.DisplayName=No
ViewReference.JoinExpression=No
ViewReference.ParentRole=Yes
ViewReference_SymbolLayout=&lt;Form&gt;[CRLF] &lt;Form Name=&quot;Source&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Child Role&quot; Attribute=&quot;ChildRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Center&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Stereotype&quot; Attribute=&quot;Stereotype&quot; Prefix=&quot;&amp;lt;&amp;lt;&quot; Suffix=&quot;&amp;gt;&amp;gt;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;ExclusiveChoice Name=&quot;Exclusive Choice&quot; Mandatory=&quot;No&quot; Display=&quot;HorizontalRadios&quot; &gt;[CRLF]   &lt;StandardAttribute Name=&quot;Name&quot; Attribute=&quot;DisplayName&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]   &lt;StandardAttribute Name=&quot;Join Expression&quot; Attribute=&quot;JoinExpression&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF]  &lt;/ExclusiveChoice&gt;[CRLF] &lt;/Form&gt;[CRLF] &lt;Form Name=&quot;Destination&quot; &gt;[CRLF]  &lt;StandardAttribute Name=&quot;Parent Role&quot; Attribute=&quot;ParentRole&quot; Prefix=&quot;&quot; Suffix=&quot;&quot; Caption=&quot;&quot; Mandatory=&quot;No&quot; /&gt;[CRLF] &lt;/Form&gt;[CRLF]&lt;/Form&gt;

[DisplayPreferences\Symbol]

[DisplayPreferences\Symbol\FRMEOBJ]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=6000
Height=2000
Brush color=255 255 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=64
Brush gradient color=192 192 192
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 255 128 128
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FRMELNK]
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\FILO]
OBJSTRNFont=新宋体,8,N
OBJSTRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LCNMFont=新宋体,8,N
LCNMFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=3600
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PDMPCKG]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 178 178 178
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\TABL]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0 0 0
KeysFont=新宋体,8,N
KeysFont color=0 0 0
IndexesFont=新宋体,8,N
IndexesFont color=0 0 0
TriggersFont=新宋体,8,N
TriggersFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=178 214 252
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VIEW]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
ColumnsFont=新宋体,8,N
ColumnsFont color=0 0 0
TablePkColumnsFont=新宋体,8,U
TablePkColumnsFont color=0 0 0
TableFkColumnsFont=新宋体,8,N
TableFkColumnsFont color=0 0 0
TemporaryVTablesFont=新宋体,8,N
TemporaryVTablesFont color=0 0 0
IndexesFont=新宋体,8,N
IndexesFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4800
Height=4000
Brush color=208 208 255
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\PROC]
STRNFont=新宋体,8,N
STRNFont color=0 0 0
DISPNAMEFont=新宋体,8,N
DISPNAMEFont color=0 0 0
OWNRDISPNAMEFont=新宋体,8,N
OWNRDISPNAMEFont color=0 0 0
LABLFont=新宋体,8,N
LABLFont color=0 0 0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Width=4000
Height=1000
Brush color=255 255 192
Fill Color=Yes
Brush style=6
Brush bitmap mode=12
Brush gradient mode=65
Brush gradient color=255 255 255
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 108 0
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\REFR]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\VREF]
SOURCEFont=新宋体,8,N
SOURCEFont color=0 0 0
CENTERFont=新宋体,8,N
CENTERFont color=0 0 0
DESTINATIONFont=新宋体,8,N
DESTINATIONFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 128 128 192
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\USRDEPD]
OBJXSTRFont=新宋体,8,N
OBJXSTRFont color=0 0 0
Line style=1
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=2 0 128 128 255
Shadow color=192 192 192
Shadow=0

[DisplayPreferences\Symbol\Free Symbol]
Free TextFont=新宋体,8,N
Free TextFont color=0 0 0
Line style=0
AutoAdjustToText=Yes
Keep aspect=No
Keep center=No
Keep size=No
Brush color=255 255 255
Fill Color=Yes
Brush style=1
Brush bitmap mode=12
Brush gradient mode=0
Brush gradient color=118 118 118
Brush background image=
Custom shape=
Custom text mode=0
Pen=1 0 0 0 255
Shadow color=192 192 192
Shadow=0</a:DisplayPreferences>
<a:PaperSize>(8268, 11693)</a:PaperSize>
<a:PageMargins>((315,354), (433,354))</a:PageMargins>
<a:PageOrientation>1</a:PageOrientation>
<a:PaperSource>15</a:PaperSource>
<c:Symbols>
<o:ExtendedDependencySymbol Id="o5">
<a:CreationDate>1743994785</a:CreationDate>
<a:ModificationDate>1743994817</a:ModificationDate>
<a:Rect>((48829,91228), (54919,93011))</a:Rect>
<a:ListOfPoints>((48829,93011),(52042,93011),(52042,91528),(54919,91528))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o6"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o7"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o8"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o9">
<a:CreationDate>1744161943</a:CreationDate>
<a:ModificationDate>1744598633</a:ModificationDate>
<a:Rect>((94811,91727), (108214,92327))</a:Rect>
<a:ListOfPoints>((108214,92027),(94811,92027))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o10"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o11"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o12"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o13">
<a:CreationDate>1744164100</a:CreationDate>
<a:ModificationDate>1744176432</a:ModificationDate>
<a:Rect>((84910,60321), (85510,86478))</a:Rect>
<a:ListOfPoints>((85210,60321),(85210,86478))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o11"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o15"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o16">
<a:CreationDate>1744165005</a:CreationDate>
<a:ModificationDate>1744176432</a:ModificationDate>
<a:Rect>((87276,55044), (107016,87789))</a:Rect>
<a:ListOfPoints>((87276,55044),(106716,55044),(106716,87789))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o10"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o17"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o18">
<a:CreationDate>1744176100</a:CreationDate>
<a:ModificationDate>1744176466</a:ModificationDate>
<a:Rect>((90054,34121), (98627,34721))</a:Rect>
<a:ListOfPoints>((98627,34482),(94853,34482),(94853,34421),(90054,34421))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o19"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o20"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o21"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o22">
<a:CreationDate>1744176115</a:CreationDate>
<a:ModificationDate>1744176466</a:ModificationDate>
<a:Rect>((84833,35447), (85433,48428))</a:Rect>
<a:ListOfPoints>((85133,35447),(85133,48428))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o20"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o14"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o23"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o24">
<a:CreationDate>1744197825</a:CreationDate>
<a:ModificationDate>1744266749</a:ModificationDate>
<a:Rect>((42552,56561), (49172,57161))</a:Rect>
<a:ListOfPoints>((49172,56861),(42552,56861))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o25"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o26"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o27"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o28">
<a:CreationDate>1744197830</a:CreationDate>
<a:ModificationDate>1744266749</a:ModificationDate>
<a:Rect>((63758,55839), (80083,56439))</a:Rect>
<a:ListOfPoints>((80083,56139),(63758,56139))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o14"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o25"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o29"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o30">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((60167,58611), (78737,85521))</a:Rect>
<a:ListOfPoints>((60167,58611),(60167,85221),(78737,85221))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<a:AutomaticRoutingState>2</a:AutomaticRoutingState>
<c:SourceSymbol>
<o:TableSymbol Ref="o25"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o11"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o31"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o32">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((32737,34448), (33337,50672))</a:Rect>
<a:ListOfPoints>((33037,34448),(33037,50672))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o33"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o26"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o34"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o35">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((32397,5857), (32997,22217))</a:Rect>
<a:ListOfPoints>((32697,5857),(32697,22217))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o36"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o33"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o37"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o38">
<a:CreationDate>********07</a:CreationDate>
<a:ModificationDate>********07</a:ModificationDate>
<a:Rect>((39417,15), (46545,615))</a:Rect>
<a:ListOfPoints>((46545,315),(39417,315))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o39"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o36"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o40"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o41">
<a:CreationDate>********08</a:CreationDate>
<a:ModificationDate>********08</a:ModificationDate>
<a:Rect>((57067,-833), (69761,-233))</a:Rect>
<a:ListOfPoints>((69761,-533),(57067,-533))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o42"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o39"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o43"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o44">
<a:CreationDate>********20</a:CreationDate>
<a:ModificationDate>1744598292</a:ModificationDate>
<a:Rect>((74348,-25984), (74948,-13624))</a:Rect>
<a:ListOfPoints>((74648,-25984),(74648,-13624))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o45"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o42"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o46"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o47">
<a:CreationDate>********22</a:CreationDate>
<a:ModificationDate>********22</a:ModificationDate>
<a:Rect>((77703,-18162), (93587,-9184))</a:Rect>
<a:ListOfPoints>((93587,-18162),(93587,-9484),(77703,-9484))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o48"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o42"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o49"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o50">
<a:CreationDate>********23</a:CreationDate>
<a:ModificationDate>1744598292</a:ModificationDate>
<a:Rect>((81097,-27098), (91619,-26498))</a:Rect>
<a:ListOfPoints>((91619,-26821),(85994,-26821),(85994,-26798),(81097,-26798))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o48"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o45"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o51"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:ExtendedDependencySymbol Id="o52">
<a:CreationDate>********24</a:CreationDate>
<a:ModificationDate>1744598317</a:ModificationDate>
<a:Rect>((100783,-22032), (115586,-21432))</a:Rect>
<a:ListOfPoints>((115586,-21733),(109961,-21733),(109961,-21732),(100783,-21732))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o53"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o48"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o54"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:TextSymbol Id="o55">
<a:Text>比对分类</a:Text>
<a:CreationDate>1743993974</a:CreationDate>
<a:ModificationDate>1743993985</a:ModificationDate>
<a:Rect>((41739,103986), (52685,99152))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>Arial,24,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:TextSymbol>
<o:ExtendedDependencySymbol Id="o56">
<a:CreationDate>**********</a:CreationDate>
<a:ModificationDate>**********</a:ModificationDate>
<a:Rect>((-8811,49588), (3069,50188))</a:Rect>
<a:ListOfPoints>((3069,49888),(-8811,49888))</a:ListOfPoints>
<a:CornerStyle>1</a:CornerStyle>
<a:ArrowStyle>8</a:ArrowStyle>
<a:LineColor>********</a:LineColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>OBJXSTR 0 新宋体,8,N</a:FontList>
<c:SourceSymbol>
<o:TableSymbol Ref="o57"/>
</c:SourceSymbol>
<c:DestinationSymbol>
<o:TableSymbol Ref="o58"/>
</c:DestinationSymbol>
<c:Object>
<o:ExtendedDependency Ref="o59"/>
</c:Object>
</o:ExtendedDependencySymbol>
<o:TableSymbol Id="o6">
<a:CreationDate>1743993974</a:CreationDate>
<a:ModificationDate>1744198156</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((33379,86467), (49401,95439))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>********</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o60"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o7">
<a:CreationDate>1743993974</a:CreationDate>
<a:ModificationDate>1744198160</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((54684,89613), (67288,95379))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>********</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o61"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o11">
<a:CreationDate>1744072597</a:CreationDate>
<a:ModificationDate>1744163897</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((77924,80639), (96024,96145))</a:Rect>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<c:Object>
<o:Table Ref="o62"/>
</c:Object>
</o:TableSymbol>
<o:TextSymbol Id="o63">
<a:Text>系统配置</a:Text>
<a:CreationDate>1744072606</a:CreationDate>
<a:ModificationDate>1744072614</a:ModificationDate>
<a:Rect>((92518,103347), (103464,98513))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>Arial,24,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:TextSymbol>
<o:TableSymbol Id="o10">
<a:CreationDate>1744075346</a:CreationDate>
<a:ModificationDate>1744598633</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((106188,79444), (122160,92970))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o64"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o14">
<a:CreationDate>1744164014</a:CreationDate>
<a:ModificationDate>1744176432</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((77286,46808), (93258,60702))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o65"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o20">
<a:CreationDate>1744165091</a:CreationDate>
<a:ModificationDate>1744176466</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((77237,16369), (93209,36343))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o66"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o19">
<a:CreationDate>1744175929</a:CreationDate>
<a:ModificationDate>1744176466</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((97463,31463), (113435,37611))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o67"/>
</c:Object>
</o:TableSymbol>
<o:TextSymbol Id="o68">
<a:Text>节点关系</a:Text>
<a:CreationDate>1744176435</a:CreationDate>
<a:ModificationDate>1744176448</a:ModificationDate>
<a:Rect>((86451,69393), (97397,64559))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>Arial,24,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:TextSymbol>
<o:TextSymbol Id="o69">
<a:Text>信息配置</a:Text>
<a:CreationDate>1744176469</a:CreationDate>
<a:ModificationDate>1744176479</a:ModificationDate>
<a:Rect>((85002,43737), (95948,38903))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>Arial,24,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:TextSymbol>
<o:TableSymbol Id="o26">
<a:CreationDate>1744194153</a:CreationDate>
<a:ModificationDate>1744194454</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((26904,45956), (42926,60228))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o70"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o25">
<a:CreationDate>1744194442</a:CreationDate>
<a:ModificationDate>1744266749</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((48634,50990), (64656,60254))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o71"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o33">
<a:CreationDate>1744194675</a:CreationDate>
<a:ModificationDate>1744195002</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((26776,20726), (42798,34998))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o72"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o36">
<a:CreationDate>1744195055</a:CreationDate>
<a:ModificationDate>1744196425</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((26232,-8052), (42254,6220))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o73"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o42">
<a:CreationDate>1744195493</a:CreationDate>
<a:ModificationDate>1744196471</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((67671,-15389), (83643,5811))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o74"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o39">
<a:CreationDate>1744195514</a:CreationDate>
<a:ModificationDate>1744196425</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((45404,-12482), (61376,6038))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o75"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o48">
<a:CreationDate>1744197078</a:CreationDate>
<a:ModificationDate>1744598287</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((87991,-32889), (106831,-16987))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o76"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o53">
<a:CreationDate>1744197313</a:CreationDate>
<a:ModificationDate>1744598317</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((113708,-25427), (132548,-15699))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o77"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o45">
<a:CreationDate>1744197461</a:CreationDate>
<a:ModificationDate>1744598292</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((67958,-36158), (83930,-23012))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o78"/>
</c:Object>
</o:TableSymbol>
<o:TextSymbol Id="o79">
<a:Text>比对方案</a:Text>
<a:CreationDate>1744197860</a:CreationDate>
<a:ModificationDate>1744197884</a:ModificationDate>
<a:Rect>((39238,67770), (50184,62936))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>Arial,24,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:TextSymbol>
<o:TextSymbol Id="o80">
<a:Text>比对任务</a:Text>
<a:CreationDate>1744197891</a:CreationDate>
<a:ModificationDate>1744197918</a:ModificationDate>
<a:Rect>((40120,41597), (51066,36763))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>Arial,24,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:TextSymbol>
<o:TableSymbol Id="o58">
<a:CreationDate>1744341127</a:CreationDate>
<a:ModificationDate>1744341519</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-23448,46418), (-7426,60690))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o81"/>
</c:Object>
</o:TableSymbol>
<o:TableSymbol Id="o57">
<a:CreationDate>1744341253</a:CreationDate>
<a:ModificationDate>1744341514</a:ModificationDate>
<a:IconMode>-1</a:IconMode>
<a:Rect>((-75,42711), (15947,60943))</a:Rect>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>16512</a:LineColor>
<a:FillColor>16570034</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontList>STRN 0 Arial,8,N
DISPNAME 0 Arial,8,N
OWNRDISPNAME 0 新宋体,8,N
Columns 0 Arial,8,N
TablePkColumns 0 Arial,8,U
TableFkColumns 0 Arial,8,N
Keys 0 Arial,8,N
Indexes 0 Arial,8,N
Triggers 0 Arial,8,N
LABL 0 Arial,8,N</a:FontList>
<a:BrushStyle>6</a:BrushStyle>
<a:GradientFillMode>65</a:GradientFillMode>
<a:GradientEndColor>16777215</a:GradientEndColor>
<a:ManuallyResized>1</a:ManuallyResized>
<c:Object>
<o:Table Ref="o82"/>
</c:Object>
</o:TableSymbol>
<o:TextSymbol Id="o83">
<a:Text>字典</a:Text>
<a:CreationDate>1744341498</a:CreationDate>
<a:ModificationDate>1744341507</a:ModificationDate>
<a:Rect>((-8262,69176), (2684,64342))</a:Rect>
<a:TextStyle>4130</a:TextStyle>
<a:AutoAdjustToText>0</a:AutoAdjustToText>
<a:LineColor>0</a:LineColor>
<a:DashStyle>7</a:DashStyle>
<a:FillColor>0</a:FillColor>
<a:ShadowColor>********</a:ShadowColor>
<a:FontName>Arial,24,N</a:FontName>
<a:ManuallyResized>1</a:ManuallyResized>
</o:TextSymbol>
</c:Symbols>
</o:PhysicalDiagram>
</c:PhysicalDiagrams>
<c:DefaultDiagram>
<o:PhysicalDiagram Ref="o4"/>
</c:DefaultDiagram>
<c:Tables>
<o:Table Id="o60">
<a:ObjectID>82F11DC4-2485-46EC-B1DB-5AB6A31420C2</a:ObjectID>
<a:Name>ieai_envc_classification</a:Name>
<a:Code>ieai_envc_classification</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994812</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>比对分类（v8：IEAI_COMPARE_CLASS）</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o84">
<a:ObjectID>75F2A984-1BAC-4D2A-8484-E52E506F6B25</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994535</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Identity>1</a:Identity>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o85">
<a:ObjectID>97C289A1-1EEC-4D42-BAC4-2C8CC9322AEC</a:ObjectID>
<a:Name>iclassification_name</a:Name>
<a:Code>iclassification_name</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994734</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>比对分类名称</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o86">
<a:ObjectID>227A5580-56CE-4D56-8092-C1B558F4A086</a:ObjectID>
<a:Name>iclassification_desc</a:Name>
<a:Code>iclassification_desc</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994734</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>比对分类描述</a:Comment>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o87">
<a:ObjectID>B0382F50-50DF-451C-B6EE-C5A54FD2ADC5</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164911</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o88">
<a:ObjectID>D5517EC5-A39F-413B-BAB6-CC05795CD19B</a:ObjectID>
<a:Name>iparent_id</a:Name>
<a:Code>iparent_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994734</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否父级分类</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o89">
<a:ObjectID>A8D4D410-AD6B-4001-8D4F-D3B6D1F3B9B4</a:ObjectID>
<a:Name>iclassification_type</a:Name>
<a:Code>iclassification_type</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994734</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>分类类类型</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o90">
<a:ObjectID>BD03DF39-222E-438B-9259-D6192E8385CE</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_CLASS</a:Name>
<a:Code>PK_IEAI_COMPARE_CLASS</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743993974</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_CLASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o84"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o90"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o61">
<a:ObjectID>DFA1E4B7-6E64-49EC-BB24-045F56556C15</a:ObjectID>
<a:Name>ieai_envc_relation</a:Name>
<a:Code>ieai_compare_relation</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994893</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>比对分类系统关联表（V8:IEAI_COMPARE_RELATION）</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o91">
<a:ObjectID>63AF0441-DD60-4966-9BE7-BB127AA82411</a:ObjectID>
<a:Name>IID</a:Name>
<a:Code>IID</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743993974</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Identity>1</a:Identity>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o92">
<a:ObjectID>1C8DEE0E-6729-46C3-863D-AB746D1A10B4</a:ObjectID>
<a:Name>ibusiness_system_id</a:Name>
<a:Code>ibusiness_system_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744005756</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务系统ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o93">
<a:ObjectID>E5A11837-E882-44A2-A6FA-950EFE06BF4D</a:ObjectID>
<a:Name>iclassification_id</a:Name>
<a:Code>iclassification_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994867</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>比对分类ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o94">
<a:ObjectID>32A5ADA4-70D0-458A-A27B-E79580D2C1C1</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_RELATION</a:Name>
<a:Code>PK_IEAI_COMPARE_RELATION</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743993974</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_RELATION</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o91"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o94"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o62">
<a:ObjectID>324B921F-9A0C-46A2-962B-E304768CDA42</a:ObjectID>
<a:Name>ieai_envc_project</a:Name>
<a:Code>ieai_envc_project</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744334715</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>比对业务系统表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o95">
<a:ObjectID>5E932C25-7448-4FE6-8757-C48358C14D5A</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075216</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o96">
<a:ObjectID>8D00B863-9E30-4163-A469-728DC99DF57E</a:ObjectID>
<a:Name>ibusiness_system_id</a:Name>
<a:Code>ibusiness_system_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744334741</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o97">
<a:ObjectID>72DF1EAC-A414-428C-9A5F-D40485838D85</a:ObjectID>
<a:Name>ibusiness_system_code</a:Name>
<a:Code>ibusiness_system_code</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075216</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统编码</a:Comment>
<a:DataType>VARCHAR(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o98">
<a:ObjectID>E8958718-59FC-4441-9F87-3EF2CE26F470</a:ObjectID>
<a:Name>ibusiness_system_name</a:Name>
<a:Code>ibusiness_system_name</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075216</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统名称</a:Comment>
<a:DataType>VARCHAR(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o99">
<a:ObjectID>12F8E0CF-6FF2-46AA-8505-8067D089A79C</a:ObjectID>
<a:Name>ibusiness_system_unique</a:Name>
<a:Code>ibusiness_system_unique</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075216</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统唯一标识</a:Comment>
<a:DataType>VARCHAR(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o100">
<a:ObjectID>6BB1065C-6B18-44D3-B434-7A6D4FCD9CFF</a:ObjectID>
<a:Name>ibusiness_system_desc</a:Name>
<a:Code>ibusiness_system_desc</a:Code>
<a:CreationDate>1744199158</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744199191</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统描述</a:Comment>
<a:DataType>VARCHAR(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o101">
<a:ObjectID>9E9432C5-54D9-4C8F-93B1-4A595C3D00C8</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744072655</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075216</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o102">
<a:ObjectID>E83AFF4F-C05C-40F8-8A09-BF4369DAC26C</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744072655</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075216</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人</a:Comment>
<a:DataType>VARCHAR(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o103">
<a:ObjectID>1F97F042-7576-4528-B8B9-5C5DBFC8947D</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744073996</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164895</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>添加时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o104">
<a:ObjectID>BE8C49D6-D654-466C-95CC-49354BEB8093</a:ObjectID>
<a:Name>istatus</a:Name>
<a:Code>istatus</a:Code>
<a:CreationDate>1744072655</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075313</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否有效（1：有效，0：失效）</a:Comment>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o105">
<a:ObjectID>9E1D6C06-BE66-4185-915A-F8A9C5DCD5A6</a:ObjectID>
<a:Name>iupdator_id</a:Name>
<a:Code>iupdator_id</a:Code>
<a:CreationDate>1744164921</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164984</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o106">
<a:ObjectID>AAE85DA9-18F4-46F6-B1D7-230BAE5752AA</a:ObjectID>
<a:Name>iupdator_name</a:Name>
<a:Code>iupdator_name</a:Code>
<a:CreationDate>1744164921</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164984</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o107">
<a:ObjectID>2395C35F-2B28-4C81-9497-47A702909A05</a:ObjectID>
<a:Name>iupdate_time</a:Name>
<a:Code>iupdate_time</a:Code>
<a:CreationDate>1744164921</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164984</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o108">
<a:ObjectID>090ADE99-7A67-47A9-B819-8F207F8A09CB</a:ObjectID>
<a:Name>PK_IEAI_PROJECT</a:Name>
<a:Code>PK_IEAI_PROJECT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744072597</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_PROJECT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o95"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o108"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o64">
<a:ObjectID>8741DD74-B80A-4026-B939-C38FBB374C73</a:ObjectID>
<a:Name>ieai_envc_system_agent</a:Name>
<a:Code>ieai_envc_system_agent</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744266439</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统节点关系表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o109">
<a:ObjectID>117C6A4F-86F3-4B93-8B1A-C9EB2DFDB5E6</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744335343</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o110">
<a:ObjectID>12374509-7185-4F76-ADDE-2F295AA07A67</a:ObjectID>
<a:Name>ibusiness_system_id</a:Name>
<a:Code>ibusiness_system_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744335236</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o111">
<a:ObjectID>F5FC62B7-5284-4560-B5D3-F9CE19F388AB</a:ObjectID>
<a:Name>iagent_id</a:Name>
<a:Code>iagent_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744161820</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>代理ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o112">
<a:ObjectID>5FA36D59-DE21-45E2-9C31-C3033A867860</a:ObjectID>
<a:Name>iagent_ip</a:Name>
<a:Code>iagent_ip</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744163495</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>代理IP</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o113">
<a:ObjectID>0C3CB624-4659-4680-990B-A5400609EEAB</a:ObjectID>
<a:Name>iagent_port</a:Name>
<a:Code>iagent_port</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744163495</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>代理端口</a:Comment>
<a:DataType>integer</a:DataType>
</o:Column>
<o:Column Id="o114">
<a:ObjectID>AFF5EBA7-44F3-4DF3-A9A3-183951A98F97</a:ObjectID>
<a:Name>iagent_name</a:Name>
<a:Code>iagent_name</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744198625</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>代理名称</a:Comment>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o115">
<a:ObjectID>CBB4E948-6BA5-444A-A3E3-AD547350563E</a:ObjectID>
<a:Name>icenter_id</a:Name>
<a:Code>icenter_id</a:Code>
<a:CreationDate>1744198351</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744198445</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中心ID</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o116">
<a:ObjectID>530A834B-F625-40FC-8CD1-FB9B1399085E</a:ObjectID>
<a:Name>icenter_name</a:Name>
<a:Code>icenter_name</a:Code>
<a:CreationDate>1744198351</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744198445</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>中心名称</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o117">
<a:ObjectID>C6EC66B5-6D48-41E1-A929-A1E6A285DF81</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744266304</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744266439</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>存储时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o118">
<a:ObjectID>B0FCC254-DF2B-4171-968E-B1B4656769DB</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744266343</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744266439</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>添加人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o119">
<a:ObjectID>EBCACBF9-9985-4B13-8350-66189AAB2CC0</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744266343</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744266439</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o120">
<a:ObjectID>0060230F-35BB-4816-8439-B52F781B3BAE</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744075346</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o109"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o120"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o65">
<a:ObjectID>E324733C-3C69-42FD-8B12-69D096BB236F</a:ObjectID>
<a:Name>ieai_envc_system_agent_node</a:Name>
<a:Code>ieai_envc_system_agent_node</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744165639</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统与agent节点关系表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o121">
<a:ObjectID>64E2C53E-391B-4AB5-98FF-CA989E41DEC1</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744335674</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o122">
<a:ObjectID>D8B4AC7B-83B5-40D2-9F91-11DCBE5BD234</a:ObjectID>
<a:Name>ibusiness_system_id</a:Name>
<a:Code>ibusiness_system_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744335909</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o123">
<a:ObjectID>FA65D9CD-7DB1-442E-872D-FC26926269D7</a:ObjectID>
<a:Name>isource_center_id</a:Name>
<a:Code>isource_center_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源中心ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o124">
<a:ObjectID>744ED317-7BBB-490B-842A-DA02DBD10D98</a:ObjectID>
<a:Name>itarget_center_id</a:Name>
<a:Code>itarget_center_id</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标中心ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o125">
<a:ObjectID>BA3E4484-00B5-4D35-BD84-32A98E2172DE</a:ObjectID>
<a:Name>isource_agent_id</a:Name>
<a:Code>isource_agent_id</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源代理ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o126">
<a:ObjectID>6DE24CCC-215D-4433-8438-ACAC7FF40FF8</a:ObjectID>
<a:Name>isource_agent_ip</a:Name>
<a:Code>isource_agent_ip</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源代理IP</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o127">
<a:ObjectID>5357045B-35AC-4ED4-95A5-2A8017B50655</a:ObjectID>
<a:Name>itarget_agent_id</a:Name>
<a:Code>itarget_agent_id</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标代理ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o128">
<a:ObjectID>36DAFF53-38B7-48CF-A32D-9B5D4CE1F0A8</a:ObjectID>
<a:Name>itarget_agent_ip</a:Name>
<a:Code>itarget_agent_ip</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标代理IP</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o129">
<a:ObjectID>BB0B5AE0-A22E-4D87-BAFC-D47B90A1E311</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o130">
<a:ObjectID>70251F3F-4FA7-469D-9F6A-E8078A2A0682</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164605</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o131">
<a:ObjectID>9DBA0E1F-682A-4546-B9D6-B68E0A33CE52</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164884</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o132">
<a:ObjectID>B59181BE-E1BD-4D27-9E34-9ECE0524017A</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164014</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o121"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o132"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o66">
<a:ObjectID>66F63408-DFF2-4405-93B8-AA201EBEC02A</a:ObjectID>
<a:Name>ieai_envc_node_relation</a:Name>
<a:Code>ieai_envc_node_relation</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744176086</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点关系规则</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o133">
<a:ObjectID>00B24EE7-5BB0-4150-A50F-1DC986E7B29F</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744335927</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o134">
<a:ObjectID>117A1E5E-3F7F-4F26-B33D-786E6A675E8F</a:ObjectID>
<a:Name>ienvc_system_agent_node_id</a:Name>
<a:Code>ienvc_system_agent_node_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744170880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点关系ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o135">
<a:ObjectID>520CD3BE-2C6F-45C3-9C3E-11400ECA2CF9</a:ObjectID>
<a:Name>imodel</a:Name>
<a:Code>imodel</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744266549</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>模式（0：比对，1：同步，2：比对后同步）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o136">
<a:ObjectID>9316E26F-EDA4-43BB-95F8-08AF7C09BDE7</a:ObjectID>
<a:Name>itype</a:Name>
<a:Code>itype</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744170880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>模块类型（0：目录，1;文件，2：脚本）</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o137">
<a:ObjectID>3182A553-AB17-4296-9C35-C8B03981ED64</a:ObjectID>
<a:Name>ipath</a:Name>
<a:Code>ipath</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744170880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>路径</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o138">
<a:ObjectID>9B02B810-E99B-491E-8D04-57C7457BE387</a:ObjectID>
<a:Name>iencode</a:Name>
<a:Code>iencode</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744170880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字符集</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o139">
<a:ObjectID>4E0347C4-3D48-4580-8EE0-9DD43A7C077F</a:ObjectID>
<a:Name>iway</a:Name>
<a:Code>iway</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744170880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方式（0：全部:1：部分）</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o140">
<a:ObjectID>02B6BAA4-A81E-4BC3-9B30-F33F8326FCA8</a:ObjectID>
<a:Name>irule_type</a:Name>
<a:Code>irule_type</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744170880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>规则类型（0：匹配，1：排除）</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o141">
<a:ObjectID>81CA8923-86FB-401D-AC3D-192E58ACEAF8</a:ObjectID>
<a:Name>ienabled</a:Name>
<a:Code>ienabled</a:Code>
<a:CreationDate>1744165671</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744170880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否有效（0：有效，1：无效）</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o142">
<a:ObjectID>A5B78662-8D58-4956-BB3C-C7C993F8CA01</a:ObjectID>
<a:Name>ichild_level</a:Name>
<a:Code>ichild_level</a:Code>
<a:CreationDate>1744175353</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744175742</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否子集（0:是，1：否）</a:Comment>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o143">
<a:ObjectID>5CC09433-F76D-4BFB-9F1C-E9097A10F4DC</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744165091</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o144">
<a:ObjectID>C595876D-7437-4CA7-AD65-D75278C3D827</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744165091</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o145">
<a:ObjectID>4F826D25-B3E2-4219-93E4-92E782DF76C6</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744165091</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o146">
<a:ObjectID>50DF4D7E-7F10-4074-909A-97D0917528D8</a:ObjectID>
<a:Name>iupdator_id</a:Name>
<a:Code>iupdator_id</a:Code>
<a:CreationDate>1744175742</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744175829</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o147">
<a:ObjectID>7D2926D5-490F-46F0-92C1-63760E9D0528</a:ObjectID>
<a:Name>iupdator_name</a:Name>
<a:Code>iupdator_name</a:Code>
<a:CreationDate>1744175742</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744175829</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o148">
<a:ObjectID>3F38C106-C605-42F5-9E45-EDD4FEABB88C</a:ObjectID>
<a:Name>iupdate_time</a:Name>
<a:Code>iupdate_time</a:Code>
<a:CreationDate>1744175742</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744175829</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o149">
<a:ObjectID>AF98A636-0829-40BC-B68A-A69A1A5CFE44</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744165091</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o133"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o149"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o67">
<a:ObjectID>516B2EE1-0260-413D-93E0-1D3AAF9FAF0E</a:ObjectID>
<a:Name>ieai_envc_node_rule_content</a:Name>
<a:Code>ieai_envc_node_rule_content</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744176387</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点关系规则</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o150">
<a:ObjectID>FDEE8AEE-C97D-473F-ACC9-56170464C105</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744336033</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o151">
<a:ObjectID>9BA7023D-05CD-4013-9A0F-38874B5F08D3</a:ObjectID>
<a:Name>ienvc_node_relation_id</a:Name>
<a:Code>ienvc_node_relation_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744176019</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>信息配置ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o152">
<a:ObjectID>2B4B2054-EBD0-489C-8A53-A3382DCC84F7</a:ObjectID>
<a:Name>irule_content</a:Name>
<a:Code>irule_content</a:Code>
<a:CreationDate>1744165671</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744176078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>规则内容</a:Comment>
<a:DataType>longtext</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o153">
<a:ObjectID>BEB7578D-48A9-431A-963F-98293C13512F</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744175929</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o150"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o153"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o70">
<a:ObjectID>9E0CE39A-4EAF-4A89-9A5A-142CB17A3516</a:ObjectID>
<a:Name>ieai_envc_plan</a:Name>
<a:Code>ieai_envc_plan</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案信息</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o154">
<a:ObjectID>0ED31738-988C-4D31-9496-710148EC1336</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744336098</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o155">
<a:ObjectID>B2149899-2348-4297-991B-B9BA73F17A0F</a:ObjectID>
<a:Name>iname</a:Name>
<a:Code>iname</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案名称</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o156">
<a:ObjectID>24A54068-2D10-48E9-BEEB-160A593DF82B</a:ObjectID>
<a:Name>iplan_desc</a:Name>
<a:Code>iplan_desc</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案描述</a:Comment>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o157">
<a:ObjectID>9A7A28B4-D870-4D93-A0F9-50577AB6F33F</a:ObjectID>
<a:Name>isource_center_id</a:Name>
<a:Code>isource_center_id</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源中心ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o158">
<a:ObjectID>F085024D-7243-4223-AAA5-0D3FB1770044</a:ObjectID>
<a:Name>itarget_center_id</a:Name>
<a:Code>itarget_center_id</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标中心ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o159">
<a:ObjectID>50CAD7B8-FEF3-4052-A1E1-7340B77D2DD6</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o160">
<a:ObjectID>493D7CAD-AD25-4DA6-8037-DDD95EC098DF</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o161">
<a:ObjectID>BFFF4F78-B0C2-4695-872D-D23430C045C7</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194153</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o162">
<a:ObjectID>021248CD-9E5E-4D62-ADE3-495C5782359B</a:ObjectID>
<a:Name>iupdator_id</a:Name>
<a:Code>iupdator_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o163">
<a:ObjectID>8C6BDC22-AFE8-4A5A-9716-D1673CF83B33</a:ObjectID>
<a:Name>iupdator_name</a:Name>
<a:Code>iupdator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o164">
<a:ObjectID>985D9D78-EAF9-4E4F-BC98-FE71065B4447</a:ObjectID>
<a:Name>iupdate_time</a:Name>
<a:Code>iupdate_time</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194414</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o165">
<a:ObjectID>767BCD53-93DB-49B2-88F6-C3AB63C67D33</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_CLASS</a:Name>
<a:Code>PK_IEAI_COMPARE_CLASS</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194153</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_CLASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o154"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o165"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o71">
<a:ObjectID>54477A08-47D0-429F-BB7F-BA0DFC293F8B</a:ObjectID>
<a:Name>ieai_envc_plan_relation</a:Name>
<a:Code>ieai_envc_plan_relation</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744199065</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案信息</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o166">
<a:ObjectID>D7AE08E4-71F6-4CE3-966E-56BEA5862C44</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744336168</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o167">
<a:ObjectID>450A6964-FA70-45A9-8B81-63BAF3606E01</a:ObjectID>
<a:Name>ienvc_plan_id</a:Name>
<a:Code>ienvc_plan_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194663</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o168">
<a:ObjectID>C2873FB0-8971-4473-943C-2E42C4BC1E33</a:ObjectID>
<a:Name>ibusiness_system_id</a:Name>
<a:Code>ibusiness_system_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194663</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>业务系统ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o169">
<a:ObjectID>98AB5D75-7A70-4FDB-89CB-3CC8B5832A1F</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744198984</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744199065</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o170">
<a:ObjectID>BD11047B-635C-4D67-88DA-5C6F7ACCB86E</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744198984</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744199065</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o171">
<a:ObjectID>270CDD88-70BA-4361-9236-6AFAF4F58076</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744198984</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744199065</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o172">
<a:ObjectID>FACED959-1962-4C77-8500-102DBF03A290</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_CLASS</a:Name>
<a:Code>PK_IEAI_COMPARE_CLASS</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194442</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_CLASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o166"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o172"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o72">
<a:ObjectID>2D1671A5-9EA4-4F28-A60C-B8751B77E280</a:ObjectID>
<a:Name>ieai_envc_task</a:Name>
<a:Code>ieai_envc_task</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194953</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>任务表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o173">
<a:ObjectID>AC8AB3D1-3C4C-41CE-8C03-7BE2E37C1FD6</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744336375</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o174">
<a:ObjectID>85A73743-420E-44D2-A675-40A6EB3B88A8</a:ObjectID>
<a:Name>ienvc_plan_id</a:Name>
<a:Code>ienvc_plan_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194830</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o175">
<a:ObjectID>D49595FE-3FAC-4DB8-BC98-3E59CC6CCF7E</a:ObjectID>
<a:Name>icron</a:Name>
<a:Code>icron</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194830</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>周期表达式</a:Comment>
<a:DataType>varchar(300)</a:DataType>
<a:Length>300</a:Length>
</o:Column>
<o:Column Id="o176">
<a:ObjectID>5E799382-A8C5-4A43-BB94-5289AD188D8A</a:ObjectID>
<a:Name>ienabled</a:Name>
<a:Code>ienabled</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194953</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否启用（1:启用，0：禁用）</a:Comment>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o177">
<a:ObjectID>42A48C93-15D7-49A4-8ED8-195F4BA7537F</a:ObjectID>
<a:Name>istate</a:Name>
<a:Code>istate</a:Code>
<a:CreationDate>1744194902</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194953</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启停状态（0:启动，1：停止）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o178">
<a:ObjectID>C8890398-3103-4426-B6C7-7A485E9A6469</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194675</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o179">
<a:ObjectID>EB75F9A5-290F-4CBE-AF41-632D6112C72E</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194675</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o180">
<a:ObjectID>D34BB3E9-72B1-4FD2-AD0C-8CF71AB63CFE</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194675</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o181">
<a:ObjectID>F62C0F96-4AB0-446E-A7A8-6AE454C83C01</a:ObjectID>
<a:Name>iupdator_id</a:Name>
<a:Code>iupdator_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194675</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o182">
<a:ObjectID>EEA887FE-E522-4E27-AB37-C239F095F467</a:ObjectID>
<a:Name>iupdator_name</a:Name>
<a:Code>iupdator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194675</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o183">
<a:ObjectID>70E4A38D-3951-4EB5-9159-2C194FE36474</a:ObjectID>
<a:Name>iupdate_time</a:Name>
<a:Code>iupdate_time</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194675</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o184">
<a:ObjectID>4A746F2E-1E60-482A-B4F4-05C08F1804B1</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_CLASS</a:Name>
<a:Code>PK_IEAI_COMPARE_CLASS</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744194675</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_CLASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o173"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o184"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o73">
<a:ObjectID>27BF4D34-CDFD-4041-BCC2-BCDAD4633742</a:ObjectID>
<a:Name>ieai_envc_run_instance</a:Name>
<a:Code>ieai_envc_run_instance</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744267040</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>实例表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o185">
<a:ObjectID>DF2CEDC0-BA1B-47B5-A36B-6A4EBFA8F2F0</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744337816</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o186">
<a:ObjectID>484FD40F-BD0A-4101-AF6F-BEC7EC811891</a:ObjectID>
<a:Name>ienvc_plan_id</a:Name>
<a:Code>ienvc_plan_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195055</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o187">
<a:ObjectID>A9F43C11-4ED6-4D86-9DFF-077CA6DC57D8</a:ObjectID>
<a:Name>ienvc_task_id</a:Name>
<a:Code>ienvc_task_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744346341</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>周期任务ID（方案启动和重试无任务id）</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o188">
<a:ObjectID>28785F48-4888-4E37-BBF3-E1CB64D19216</a:ObjectID>
<a:Name>iresult</a:Name>
<a:Code>iresult</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196385</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>结果状态（-1:运行中，0:一致/成功，1：不一致/失败）</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o189">
<a:ObjectID>FAC6EE01-DF8E-48B5-B551-C872391B4C5A</a:ObjectID>
<a:Name>istate</a:Name>
<a:Code>istate</a:Code>
<a:CreationDate>1744195928</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196154</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启停状态（0：运行中，1：已完成，2：终止）</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o190">
<a:ObjectID>B6D706F9-09B1-46CE-BC93-1CE594A30CF0</a:ObjectID>
<a:Name>ifrom</a:Name>
<a:Code>ifrom</a:Code>
<a:CreationDate>1744266862</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744267040</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>触发来源：（1：周期触发，2：手动触发，3：重试）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o191">
<a:ObjectID>DA3AE661-117F-41F0-92D6-FD359B6196CD</a:ObjectID>
<a:Name>istarter_name</a:Name>
<a:Code>istarter_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195468</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启动人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o192">
<a:ObjectID>D493C0D9-B7B0-4187-A6C3-1E77AFF6C462</a:ObjectID>
<a:Name>istarter_id</a:Name>
<a:Code>istarter_id</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195468</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启动人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o193">
<a:ObjectID>67BB8995-55AA-4BF6-99F0-390EB520E290</a:ObjectID>
<a:Name>istart_time</a:Name>
<a:Code>istart_time</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195468</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启动时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o194">
<a:ObjectID>FD2DD07A-0E1F-49A0-AF70-518CF17B1F90</a:ObjectID>
<a:Name>iend_time</a:Name>
<a:Code>iend_time</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195468</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>结束时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o195">
<a:ObjectID>3C3DB5E1-D9BA-4EAC-B853-56F6C925328C</a:ObjectID>
<a:Name>ielapsed_time</a:Name>
<a:Code>ielapsed_time</a:Code>
<a:CreationDate>1744196493</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197791</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>耗时</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o196">
<a:ObjectID>95D0847D-D525-4F43-9154-4285F3CE0F70</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_CLASS</a:Name>
<a:Code>PK_IEAI_COMPARE_CLASS</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195055</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_CLASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o185"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o196"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o74">
<a:ObjectID>3DF93E2F-6BBD-498A-BE8F-A69701FD2A97</a:ObjectID>
<a:Name>ieai_envc_run_rule</a:Name>
<a:Code>ieai_envc_run_rule</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744267264</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点规则结果表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o197">
<a:ObjectID>456BE479-A3EA-45DC-8E37-B5995B1AFE36</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744338058</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o198">
<a:ObjectID>7FC732CE-3F75-4B0A-9D62-0322B39130AC</a:ObjectID>
<a:Name>ienvc_run_instance_info_id</a:Name>
<a:Code>ienvc_run_instance_info_id</a:Code>
<a:CreationDate>1744196493</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197069</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>实例详情ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o199">
<a:ObjectID>17933106-5B2C-4A29-8CA9-ECD3D7EB27BB</a:ObjectID>
<a:Name>imodel</a:Name>
<a:Code>imodel</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>模式（0：比对，1：同步，2：比对后同步）</a:Comment>
<a:DefaultValue>-1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o200">
<a:ObjectID>9508DE2F-54C7-44AB-A876-52B3FA0FB5F0</a:ObjectID>
<a:Name>itype</a:Name>
<a:Code>itype</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>模块类型（0：目录，1;文件，2：脚本）</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o201">
<a:ObjectID>D716F960-8EB2-406A-B35A-D2A9BD17461E</a:ObjectID>
<a:Name>ipath</a:Name>
<a:Code>ipath</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>路径</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o202">
<a:ObjectID>A6AAC116-CA3B-4516-96C8-B2175A2D1F30</a:ObjectID>
<a:Name>iencode</a:Name>
<a:Code>iencode</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字符集</a:Comment>
<a:DataType>varchar(32)</a:DataType>
<a:Length>32</a:Length>
</o:Column>
<o:Column Id="o203">
<a:ObjectID>F9280ABF-053B-42A1-A3EB-1430377D4BCB</a:ObjectID>
<a:Name>iway</a:Name>
<a:Code>iway</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方式（0：全部:1：部分）</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o204">
<a:ObjectID>221EC599-A06A-4B8A-AB5E-675BBBC6CDC5</a:ObjectID>
<a:Name>irule_type</a:Name>
<a:Code>irule_type</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>规则类型（0：匹配，1：排除）</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o205">
<a:ObjectID>8BF8A145-2118-4D54-85F8-3B410B928882</a:ObjectID>
<a:Name>ienabled</a:Name>
<a:Code>ienabled</a:Code>
<a:CreationDate>1744165671</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否有效（0：有效，1：无效）</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o206">
<a:ObjectID>9FEB486A-C9DC-4F8C-8EC1-D286126F584A</a:ObjectID>
<a:Name>ichild_level</a:Name>
<a:Code>ichild_level</a:Code>
<a:CreationDate>1744175353</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否子集（0:是，1：否）</a:Comment>
<a:DefaultValue>1</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o207">
<a:ObjectID>A1D80434-3C6A-48D1-B735-D88738B81FB4</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o208">
<a:ObjectID>C0F80E70-65FB-4FBE-B71E-69759E6846B5</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o209">
<a:ObjectID>09063D43-046A-4ED0-AC1E-A519E14BA8D2</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o210">
<a:ObjectID>265C4643-B091-47FE-9834-BEDEA3C79A50</a:ObjectID>
<a:Name>iend_time</a:Name>
<a:Code>iend_time</a:Code>
<a:CreationDate>1744175742</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197038</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o211">
<a:ObjectID>98CAABBD-7425-4D27-8E6F-FDC20A0CACC0</a:ObjectID>
<a:Name>iresult</a:Name>
<a:Code>iresult</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197038</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>结果状态（-1:运行中，0:一致/成功，1：不一致/失败）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o212">
<a:ObjectID>A9F097DC-B7EB-4AEC-87DC-CA261AE08039</a:ObjectID>
<a:Name>istate</a:Name>
<a:Code>istate</a:Code>
<a:CreationDate>1744196386</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197038</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启停状态（0：运行中，1：已完成，2：终止）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o213">
<a:ObjectID>C4F2C7C6-A923-4B02-90E6-5EF78C858B08</a:ObjectID>
<a:Name>ielapsed_time</a:Name>
<a:Code>ielapsed_time</a:Code>
<a:CreationDate>1744196493</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197038</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>耗时</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o214">
<a:ObjectID>7CD615CC-AD88-44BC-8D9F-3571482C2718</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195493</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o197"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o214"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o75">
<a:ObjectID>8C17402E-C287-4341-B42C-3D2566A708AD</a:ObjectID>
<a:Name>ieai_envc_run_instance_info</a:Name>
<a:Code>ieai_envc_run_instance_info</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196415</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>实例详情</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o215">
<a:ObjectID>53235856-6193-45B8-AE23-43B7A597ED5E</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744337880</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o216">
<a:ObjectID>2554CB72-26C6-42DA-A90C-65C9CD50916B</a:ObjectID>
<a:Name>ienvc_run_instance_id</a:Name>
<a:Code>ienvc_run_instance_id</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196623</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>实例ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o217">
<a:ObjectID>A14DFF6A-322E-4173-8B69-5111E94AA4FD</a:ObjectID>
<a:Name>ienvc_plan_id</a:Name>
<a:Code>ienvc_plan_id</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196623</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>方案ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o218">
<a:ObjectID>D91AFBCC-BAE5-4029-A9D8-F3C684A8D5F6</a:ObjectID>
<a:Name>ibusiness_system_id</a:Name>
<a:Code>ibusiness_system_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744337928</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>系统ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o219">
<a:ObjectID>1B52B7CA-734C-4166-9A94-CC900AB20791</a:ObjectID>
<a:Name>isource_center_id</a:Name>
<a:Code>isource_center_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195514</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源中心ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o220">
<a:ObjectID>396C3B64-152D-47F9-B36B-EBF5F36896C7</a:ObjectID>
<a:Name>itarget_center_id</a:Name>
<a:Code>itarget_center_id</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195514</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标中心ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o221">
<a:ObjectID>6F2ECB37-333D-4502-8504-A3EF566B1452</a:ObjectID>
<a:Name>isource_agent_id</a:Name>
<a:Code>isource_agent_id</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195514</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源代理ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o222">
<a:ObjectID>836EFD00-7633-43AE-BBAD-7E8D928112B5</a:ObjectID>
<a:Name>isource_agent_ip</a:Name>
<a:Code>isource_agent_ip</a:Code>
<a:CreationDate>1744162039</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195514</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源代理IP</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o223">
<a:ObjectID>975B2DDA-0E85-4D20-A8B4-5671492EAB21</a:ObjectID>
<a:Name>isource_agent_port</a:Name>
<a:Code>isource_agent_port</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195924</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>源代理端口</a:Comment>
<a:DataType>integer</a:DataType>
</o:Column>
<o:Column Id="o224">
<a:ObjectID>0AF9CBE6-12F1-483C-B368-730BB0D15E8B</a:ObjectID>
<a:Name>itarget_agent_id</a:Name>
<a:Code>itarget_agent_id</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195514</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标代理ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o225">
<a:ObjectID>240FA4B9-A976-4DD0-BE34-E6C6B1796891</a:ObjectID>
<a:Name>itarget_agent_ip</a:Name>
<a:Code>itarget_agent_ip</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195514</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标代理IP</a:Comment>
<a:DataType>varchar(255)</a:DataType>
<a:Length>255</a:Length>
</o:Column>
<o:Column Id="o226">
<a:ObjectID>548CBB6E-A4DE-4E4E-8AB3-08775E39458C</a:ObjectID>
<a:Name>itarget_agent_port</a:Name>
<a:Code>itarget_agent_port</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195924</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>目标代理端口</a:Comment>
<a:DataType>integer</a:DataType>
</o:Column>
<o:Column Id="o227">
<a:ObjectID>9E088627-94A9-4C34-A587-D16DE208086B</a:ObjectID>
<a:Name>istore_time</a:Name>
<a:Code>istore_time</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196345</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>存储时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o228">
<a:ObjectID>8A4D67EF-99DD-4D88-8F1B-4AA017BAB56B</a:ObjectID>
<a:Name>iresult</a:Name>
<a:Code>iresult</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196415</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>结果状态（-1:运行中，0:一致/成功，1：不一致/失败）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o229">
<a:ObjectID>52AB02F6-6BE6-4307-8C70-4254D329A89B</a:ObjectID>
<a:Name>istate</a:Name>
<a:Code>istate</a:Code>
<a:CreationDate>1744196386</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744196415</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启停状态（0：运行中，1：已完成，2：终止）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o230">
<a:ObjectID>E05030E3-7545-43AF-ABE1-9B82F9150C18</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744195514</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o215"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o230"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o76">
<a:ObjectID>7FFB41F8-4358-47BF-A35E-372A237AE978</a:ObjectID>
<a:Name>ieai_envc_run_flow</a:Name>
<a:Code>ieai_envc_run_flow</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744598615</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点规则流程表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o231">
<a:ObjectID>EEA79293-54D6-4F09-BF36-6081495A11E9</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744338287</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o232">
<a:ObjectID>AEC76E51-2E74-4D4C-A11D-6C5DB179054B</a:ObjectID>
<a:Name>iflowid</a:Name>
<a:Code>iflowid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197174</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>流程ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o233">
<a:ObjectID>E8EA03BC-2F6A-452E-8BF7-E17CE3848777</a:ObjectID>
<a:Name>irun_biz_id</a:Name>
<a:Code>irun_biz_id</a:Code>
<a:CreationDate>1744196493</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197663</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>比对规则ID或者比对规则对应的同步id</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o234">
<a:ObjectID>C55026B5-1CF9-4EAA-BED7-EA61EB8A4405</a:ObjectID>
<a:Name>imodel</a:Name>
<a:Code>imodel</a:Code>
<a:CreationDate>1744197543</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744267370</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>来源标识（0：比对，1：同步）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o235">
<a:ObjectID>AFDB87B8-60F6-47A6-8992-EB248AA1DD75</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o236">
<a:ObjectID>EF575548-A8B1-4707-BD49-040529E9B908</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o237">
<a:ObjectID>1CA16E3D-BE4C-4E4F-AA36-610BC4862B51</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o238">
<a:ObjectID>CBC4E3CE-371F-4A61-8B9B-A3645AB40BB1</a:ObjectID>
<a:Name>iend_time</a:Name>
<a:Code>iend_time</a:Code>
<a:CreationDate>1744175742</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o239">
<a:ObjectID>8C02A2C3-070E-4719-983A-C4512AE40420</a:ObjectID>
<a:Name>istate</a:Name>
<a:Code>istate</a:Code>
<a:CreationDate>1744196386</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启停状态（0：运行中，1：已完成，2：终止）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o240">
<a:ObjectID>AFFCFAA6-5906-4BE7-8DE0-A190F22A1F9F</a:ObjectID>
<a:Name>ielapsed_time</a:Name>
<a:Code>ielapsed_time</a:Code>
<a:CreationDate>1744196493</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>耗时</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o241">
<a:ObjectID>C83F3F7D-0E24-4ACE-948B-B40228125C6A</a:ObjectID>
<a:Name>iret</a:Name>
<a:Code>iret</a:Code>
<a:CreationDate>1744197213</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197297</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>执行结束码</a:Comment>
<a:DataType>varchar(10)</a:DataType>
<a:Length>10</a:Length>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o242">
<a:ObjectID>0EB88A19-2AE9-4028-AE7F-58389B4B3B3A</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197078</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o231"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o242"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o77">
<a:ObjectID>246E6005-83CB-4DD4-942F-13658A2A6301</a:ObjectID>
<a:Name>ieai_envc_run_flow_result</a:Name>
<a:Code>ieai_envc_run_flow_result</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744198871</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>流程输出结果表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o243">
<a:ObjectID>FDF559C4-D124-47C6-876E-7880069F0C9C</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197313</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Identity>1</a:Identity>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o244">
<a:ObjectID>57BB2E50-103E-4F57-BB45-47CC39FD1F64</a:ObjectID>
<a:Name>ienvc_run_flow_id</a:Name>
<a:Code>ienvc_run_flow_id</a:Code>
<a:CreationDate>1744198847</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744198896</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点规则流程主键</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o245">
<a:ObjectID>F405F596-D44A-4207-B64E-232745A3E271</a:ObjectID>
<a:Name>iflowid</a:Name>
<a:Code>iflowid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197313</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>流程ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o246">
<a:ObjectID>C7B5AE94-186A-461D-9069-B704D134A60A</a:ObjectID>
<a:Name>icontent</a:Name>
<a:Code>icontent</a:Code>
<a:CreationDate>1744197335</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197452</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>输出内容</a:Comment>
<a:DataType>longtext</a:DataType>
</o:Column>
<o:Column Id="o247">
<a:ObjectID>D8BAC12E-041F-4490-BA6E-5C3CDCFC1AC0</a:ObjectID>
<a:Name>istderr</a:Name>
<a:Code>istderr</a:Code>
<a:CreationDate>1744197374</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197452</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>错误输出内容</a:Comment>
<a:DataType>longtext</a:DataType>
</o:Column>
<o:Column Id="o248">
<a:ObjectID>05B0F363-8A46-460A-B335-14F5F0EA7666</a:ObjectID>
<a:Name>istore_time</a:Name>
<a:Code>istore_time</a:Code>
<a:CreationDate>1744197374</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197452</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>结果入库时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o249">
<a:ObjectID>9A32008E-5E93-4FE2-9556-9F44B9D3CDE0</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197313</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o243"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o249"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o78">
<a:ObjectID>968FE81F-C7F2-4685-AA02-2E15E4DAB2BC</a:ObjectID>
<a:Name>ieai_envc_run_rule_sync</a:Name>
<a:Code>ieai_envc_run_rule_sync</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744338247</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点规则同步结果</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o250">
<a:ObjectID>5EAB5A90-7280-4C76-8F3B-272DCC5727C7</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744338144</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键ID</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o251">
<a:ObjectID>53B6B336-B924-4FE9-963E-661A9F7EE474</a:ObjectID>
<a:Name>ienvc_run_rule_id</a:Name>
<a:Code>ienvc_run_rule_id</a:Code>
<a:CreationDate>1744196493</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744198817</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>节点规则结果ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o252">
<a:ObjectID>3D201E50-07B0-4F24-BC0B-59DD4C064AF7</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o253">
<a:ObjectID>8EE7C97B-4CD5-4DDB-89C2-5DD4739FF4FB</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o254">
<a:ObjectID>D9B4FFA8-F291-447F-82AB-E50924FA46B6</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1744164249</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o255">
<a:ObjectID>4C7BFEF4-CE49-45FA-9B6A-96DFAE40F297</a:ObjectID>
<a:Name>iend_time</a:Name>
<a:Code>iend_time</a:Code>
<a:CreationDate>1744175742</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o256">
<a:ObjectID>FD333B1D-7AB7-428D-9A92-7FA7659ACD5E</a:ObjectID>
<a:Name>iresult</a:Name>
<a:Code>iresult</a:Code>
<a:CreationDate>1744195645</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>结果状态（-1:运行中，0:一致/成功，1：不一致/失败）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o257">
<a:ObjectID>9DAB295A-3788-4FBC-B2D1-E65C3222B82D</a:ObjectID>
<a:Name>istate</a:Name>
<a:Code>istate</a:Code>
<a:CreationDate>1744196386</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>启停状态（0：运行中，1：已完成，2：终止）</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o258">
<a:ObjectID>47EB84F0-06DE-4397-B50C-6E903358BCB4</a:ObjectID>
<a:Name>ielapsed_time</a:Name>
<a:Code>ielapsed_time</a:Code>
<a:CreationDate>1744196493</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>耗时</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o259">
<a:ObjectID>EDA83C3D-C100-44EB-A6DB-A9DE50A6CC2A</a:ObjectID>
<a:Name>PK_IEAI_SYNC_SYSTEMAGENT</a:Name>
<a:Code>PK_IEAI_SYNC_SYSTEMAGENT</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197461</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_SYNC_SYSTEMAGENT</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o250"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o259"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o81">
<a:ObjectID>5EC7125F-897B-41A2-9188-3D7309B00E3F</a:ObjectID>
<a:Name>ieai_envc_dictionary</a:Name>
<a:Code>ieai_envc_dictionary</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341152</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字典码表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o260">
<a:ObjectID>88AC86F9-57E7-4E2D-B638-7A5DC187F4D0</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o261">
<a:ObjectID>262F5013-BCBF-4D93-A4A2-96D9EDC51D53</a:ObjectID>
<a:Name>icode</a:Name>
<a:Code>icode</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341249</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字典码</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o262">
<a:ObjectID>21981191-E95A-4472-8431-D1AD561629FE</a:ObjectID>
<a:Name>idescription</a:Name>
<a:Code>idescription</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341249</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字典描述</a:Comment>
<a:DataType>varchar(150)</a:DataType>
<a:Length>150</a:Length>
</o:Column>
<o:Column Id="o263">
<a:ObjectID>2B9F0432-F8D0-429A-9EF4-9F8757917ED9</a:ObjectID>
<a:Name>init</a:Name>
<a:Code>init</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341249</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>是否初始化 0：否，1：是</a:Comment>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o264">
<a:ObjectID>909FC8F0-580E-447C-93D4-7E93B66FD38C</a:ObjectID>
<a:Name>ideleted</a:Name>
<a:Code>ideleted</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341249</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>删除标识 0：否，1：是</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o265">
<a:ObjectID>4C827714-F253-4071-AD63-B02C7F70ED9B</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o266">
<a:ObjectID>3C2DA426-37E5-43B9-973C-65157D765DDC</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o267">
<a:ObjectID>F7822E41-3B23-4BAF-9AC8-0C2F64EAB04F</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o268">
<a:ObjectID>F9B1B035-7B8C-4EB8-B9CA-19A5FCB19642</a:ObjectID>
<a:Name>iupdator_id</a:Name>
<a:Code>iupdator_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o269">
<a:ObjectID>E8F8652B-5A08-4D8E-BE2B-6265036992DC</a:ObjectID>
<a:Name>iupdator_name</a:Name>
<a:Code>iupdator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o270">
<a:ObjectID>90068D95-35F3-4BCB-B630-A54B50ACB06E</a:ObjectID>
<a:Name>iupdate_time</a:Name>
<a:Code>iupdate_time</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o271">
<a:ObjectID>D860DB7F-FC1A-40FE-91BB-BD1425613407</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_CLASS</a:Name>
<a:Code>PK_IEAI_COMPARE_CLASS</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341127</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_CLASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o260"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o271"/>
</c:PrimaryKey>
</o:Table>
<o:Table Id="o82">
<a:ObjectID>064E431E-31D5-4B32-9659-92529FAA9401</a:ObjectID>
<a:Name>ieai_envc_dictionary_detail</a:Name>
<a:Code>ieai_envc_dictionary_detail</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341486</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字典详情表</a:Comment>
<a:PhysicalOptions>pctfree 10
initrans 1
storage
(
    initial 64K
    next 1024K
    minextents 1
    maxextents unlimited
)
tablespace ENTEGOR_TSPACE
logging
nocompress
 monitoring
 noparallel</a:PhysicalOptions>
<a:TotalSavingCurrency/>
<c:Columns>
<o:Column Id="o272">
<a:ObjectID>4CB7C8EB-5417-492A-8B5F-A5798C8D23E8</a:ObjectID>
<a:Name>iid</a:Name>
<a:Code>iid</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>主键</a:Comment>
<a:DataType>bigint</a:DataType>
<a:Column.Mandatory>1</a:Column.Mandatory>
</o:Column>
<o:Column Id="o273">
<a:ObjectID>1632AC21-595A-4E91-9B36-0D777A46EBE8</a:ObjectID>
<a:Name>ienvc_dictionary_id</a:Name>
<a:Code>ienvc_dictionary_id</a:Code>
<a:CreationDate>1744341277</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341486</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>码表主键</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o274">
<a:ObjectID>9C9387F0-6BB4-49B9-808B-FD543A2640BB</a:ObjectID>
<a:Name>icode</a:Name>
<a:Code>icode</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字典码</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o275">
<a:ObjectID>B1ED1546-F17C-4079-8C8E-5C0C8543A13F</a:ObjectID>
<a:Name>ilable</a:Name>
<a:Code>ilable</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341486</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>显示名称</a:Comment>
<a:DataType>varchar(100)</a:DataType>
<a:Length>100</a:Length>
</o:Column>
<o:Column Id="o276">
<a:ObjectID>1098CABF-5E7E-4E33-A56C-E87DCB98CAD6</a:ObjectID>
<a:Name>ivalue</a:Name>
<a:Code>ivalue</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341486</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>显示值</a:Comment>
<a:DataType>varchar(200)</a:DataType>
<a:Length>200</a:Length>
</o:Column>
<o:Column Id="o277">
<a:ObjectID>6757B1BA-E027-4255-8183-B7602FD83D5D</a:ObjectID>
<a:Name>isort</a:Name>
<a:Code>isort</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341486</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>排序序号</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o278">
<a:ObjectID>028E7786-A0B5-42C3-9460-0003C70EAC7F</a:ObjectID>
<a:Name>ideleted</a:Name>
<a:Code>ideleted</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>删除标识 0：否，1：是</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o279">
<a:ObjectID>386A11B4-673F-4814-9AB0-7E91AE6120F5</a:ObjectID>
<a:Name>iarray_flag</a:Name>
<a:Code>iarray_flag</a:Code>
<a:CreationDate>1744341277</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341486</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>数组标识 0：否，1：是</a:Comment>
<a:DefaultValue>0</a:DefaultValue>
<a:DataType>smallint</a:DataType>
</o:Column>
<o:Column Id="o280">
<a:ObjectID>FDBC05C0-EE3B-45F3-AA19-19F9E7870688</a:ObjectID>
<a:Name>ivalue_type</a:Name>
<a:Code>ivalue_type</a:Code>
<a:CreationDate>1744341277</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341486</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>字典值类型</a:Comment>
<a:DataType>varchar(20)</a:DataType>
<a:Length>20</a:Length>
</o:Column>
<o:Column Id="o281">
<a:ObjectID>07A40875-E712-4309-8FFA-96EC351E206C</a:ObjectID>
<a:Name>icreator_name</a:Name>
<a:Code>icreator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o282">
<a:ObjectID>D9A0E762-CE0F-4DE6-B055-BA38401DB895</a:ObjectID>
<a:Name>icreator_id</a:Name>
<a:Code>icreator_id</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o283">
<a:ObjectID>44EB6370-05EC-4A19-9973-2E7A2591A0B7</a:ObjectID>
<a:Name>icreate_time</a:Name>
<a:Code>icreate_time</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>创建时间</a:Comment>
<a:DataType>timestamp</a:DataType>
</o:Column>
<o:Column Id="o284">
<a:ObjectID>6CC0D2E2-9192-424D-95FC-E1BAFC68EB52</a:ObjectID>
<a:Name>iupdator_id</a:Name>
<a:Code>iupdator_id</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人ID</a:Comment>
<a:DataType>bigint</a:DataType>
</o:Column>
<o:Column Id="o285">
<a:ObjectID>B522AB1F-CD62-4599-834A-25599C531A5D</a:ObjectID>
<a:Name>iupdator_name</a:Name>
<a:Code>iupdator_name</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:Comment>更新人名称</a:Comment>
<a:DataType>varchar(50)</a:DataType>
<a:Length>50</a:Length>
</o:Column>
<o:Column Id="o286">
<a:ObjectID>F4AA02E6-47B9-4209-829D-BFCCDAC62F53</a:ObjectID>
<a:Name>iupdate_time</a:Name>
<a:Code>iupdate_time</a:Code>
<a:CreationDate>1744194184</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:DataType>timestamp</a:DataType>
</o:Column>
</c:Columns>
<c:Keys>
<o:Key Id="o287">
<a:ObjectID>D88D594C-B502-47BC-8095-E677442461CE</a:ObjectID>
<a:Name>PK_IEAI_COMPARE_CLASS</a:Name>
<a:Code>PK_IEAI_COMPARE_CLASS</a:Code>
<a:CreationDate>1686558787</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744341253</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:PhysicalOptions>using index
    pctfree 10
    initrans 2
    storage
    (
        initial 64K
        next 1024K
        minextents 1
        maxextents unlimited
    )
    tablespace ENTEGOR_TSPACE
     logging</a:PhysicalOptions>
<a:ConstraintName>PK_IEAI_COMPARE_CLASS</a:ConstraintName>
<c:Key.Columns>
<o:Column Ref="o272"/>
</c:Key.Columns>
</o:Key>
</c:Keys>
<c:PrimaryKey>
<o:Key Ref="o287"/>
</c:PrimaryKey>
</o:Table>
</c:Tables>
<c:DefaultGroups>
<o:Group Id="o288">
<a:ObjectID>73276AFC-731E-499D-84EB-750F434D060B</a:ObjectID>
<a:Name>PUBLIC</a:Name>
<a:Code>PUBLIC</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
</o:Group>
</c:DefaultGroups>
<c:ChildTraceabilityLinks>
<o:ExtendedDependency Id="o8">
<a:ObjectID>9AE2FEE4-555A-41B8-94A9-CAF14C604080</a:ObjectID>
<a:CreationDate>1743994785</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1743994785</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o61"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o60"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o12">
<a:ObjectID>DD1E330F-0CA5-4D37-9DBA-A39F1BC3C4ED</a:ObjectID>
<a:CreationDate>1744161943</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744161943</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o62"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o64"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o15">
<a:ObjectID>145CB20C-A2FF-477A-A730-D43AAE79404F</a:ObjectID>
<a:CreationDate>1744164100</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744164100</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o62"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o65"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o17">
<a:ObjectID>EE43B07E-DDEB-4B03-B543-89F9DDD73ECF</a:ObjectID>
<a:CreationDate>1744165005</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744165005</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o64"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o65"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o21">
<a:ObjectID>7AF10760-241B-43CC-8BF8-270CAD396A6F</a:ObjectID>
<a:CreationDate>1744176100</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744176100</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o66"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o67"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o23">
<a:ObjectID>B21178F0-BD3E-40A6-9FEA-7E983C2BE773</a:ObjectID>
<a:CreationDate>1744176115</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744176115</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o65"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o66"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o27">
<a:ObjectID>4A358A82-927C-444C-8539-D296811FAFBC</a:ObjectID>
<a:CreationDate>1744197825</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197825</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o70"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o71"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o29">
<a:ObjectID>DCA55A71-A08D-4B16-BE78-B5A4AE60DDB3</a:ObjectID>
<a:CreationDate>1744197830</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1744197830</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o71"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o65"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o31">
<a:ObjectID>9F682E85-4B44-4E11-9740-211BA335952C</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o62"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o71"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o34">
<a:ObjectID>F9E24DDE-82C5-4219-BC12-80DBAA3D7231</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o70"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o72"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o37">
<a:ObjectID>D3582EF9-85F1-41F1-8245-1B16A32DE195</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o72"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o73"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o40">
<a:ObjectID>2F616A03-BBE9-47BF-AE23-147A9A97EE98</a:ObjectID>
<a:CreationDate>********07</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>********07</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o73"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o75"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o43">
<a:ObjectID>33E07E22-F47D-45C1-9AD8-562FA58BCB29</a:ObjectID>
<a:CreationDate>********08</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>********08</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o75"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o74"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o46">
<a:ObjectID>827A9E53-740C-4AFE-A031-251085A234C1</a:ObjectID>
<a:CreationDate>********20</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>********20</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o74"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o78"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o49">
<a:ObjectID>32A92AFD-0F07-458D-B27C-28F42DEE6581</a:ObjectID>
<a:CreationDate>********22</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>********22</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o74"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o76"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o51">
<a:ObjectID>AD8A339A-417E-437B-89F1-80A498F636C1</a:ObjectID>
<a:CreationDate>********23</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>********23</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o78"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o76"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o54">
<a:ObjectID>C44EB93A-C108-4F0C-91E5-394BBF94A518</a:ObjectID>
<a:CreationDate>********24</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>********24</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o76"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o77"/>
</c:Object2>
</o:ExtendedDependency>
<o:ExtendedDependency Id="o59">
<a:ObjectID>233A20A9-3AF8-4D73-A0BE-EC68C6707038</a:ObjectID>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>**********</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<c:Object1>
<o:Table Ref="o81"/>
</c:Object1>
<c:Object2>
<o:Table Ref="o82"/>
</c:Object2>
</o:ExtendedDependency>
</c:ChildTraceabilityLinks>
<c:TargetModels>
<o:TargetModel Id="o289">
<a:ObjectID>B35C7532-0518-4CF0-8E89-8613FCCBD47D</a:ObjectID>
<a:Name>MySQL 5.0</a:Name>
<a:Code>MYSQL50</a:Code>
<a:CreationDate>**********</a:CreationDate>
<a:Creator>Administrator</a:Creator>
<a:ModificationDate>1689583475</a:ModificationDate>
<a:Modifier>Administrator</a:Modifier>
<a:TargetModelURL>file:///%_DBMS%/mysql50.xdb</a:TargetModelURL>
<a:TargetModelID>F4F16ECD-F2F1-4006-AF6F-638D5C65F35E</a:TargetModelID>
<a:TargetModelClassID>4BA9F647-DAB1-11D1-9944-006097355D9B</a:TargetModelClassID>
<a:TargetModelLastModificationDate>1276524678</a:TargetModelLastModificationDate>
<c:SessionShortcuts>
<o:Shortcut Ref="o3"/>
</c:SessionShortcuts>
</o:TargetModel>
</c:TargetModels>
</o:Model>
</c:Children>
</o:RootObject>

</Model>