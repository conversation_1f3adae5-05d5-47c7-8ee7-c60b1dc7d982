package com.ideal.envc.model.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.ideal.snowflake.annotion.IdGenerator;

/**
 * 节点规则流程对象 ieai_envc_run_flow
 *
 * <AUTHOR>
 */
public class RunFlowEntity implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @IdGenerator
    private Long id;
    
    /** 流程ID */
    private Long flowid;
    
    /** 比对规则ID或者比对规则对应的同步id */
    private Long runBizId;
    
    /** 来源标识（0：比对，1：同步） */
    private Integer model;
    
    /** 创建人ID */
    private Long creatorId;
    
    /** 创建人名称 */
    private String creatorName;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;
    
    /** 耗时 */
    private Long elapsedTime;
    
    /** 执行结束码 */
    private String ret;
    
    /** 引擎消息时间字段 */
    private Long updateOrderTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setFlowid(Long flowid){
        this.flowid = flowid;
    }

    public Long getFlowid(){
        return flowid;
    }

    public void setRunBizId(Long runBizId){
        this.runBizId = runBizId;
    }

    public Long getRunBizId(){
        return runBizId;
    }

    public void setModel(Integer model){
        this.model = model;
    }

    public Integer getModel(){
        return model;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setEndTime(Date endTime){
        this.endTime = endTime;
    }

    public Date getEndTime(){
        return endTime;
    }

    public void setState(Integer state){
        this.state = state;
    }

    public Integer getState(){
        return state;
    }

    public void setElapsedTime(Long elapsedTime){
        this.elapsedTime = elapsedTime;
    }

    public Long getElapsedTime(){
        return elapsedTime;
    }

    public void setRet(String ret){
        this.ret = ret;
    }

    public String getRet(){
        return ret;
    }

    public void setUpdateOrderTime(Long updateOrderTime){
        this.updateOrderTime = updateOrderTime;
    }

    public Long getUpdateOrderTime(){
        return updateOrderTime;
    }

    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",flowid="+getFlowid()+
                    ",runBizId="+getRunBizId()+
                    ",model="+getModel()+
                    ",creatorId="+getCreatorId()+
                    ",creatorName="+getCreatorName()+
                    ",createTime="+getCreateTime()+
                    ",endTime="+getEndTime()+
                    ",state="+getState()+
                    ",elapsedTime="+getElapsedTime()+
                    ",ret="+getRet()+
                    ",updateOrderTime="+getUpdateOrderTime()+
                "]";
    }
}

