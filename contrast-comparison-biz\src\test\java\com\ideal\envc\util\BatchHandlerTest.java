package com.ideal.envc.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BatchHandlerTest {

    private BatchHandler<String> batchHandler;

    @Mock
    private Consumer<List<String>> processor;

    @Mock
    private Consumer<Integer> progressCallback;

    @Mock
    private Function<Exception, Boolean> errorHandler;

    @BeforeEach
    void setUp() {
        batchHandler = new BatchHandler<>();
    }

    @Test
    @DisplayName("测试空列表不执行处理")
    void testEmptyList() {
        List<String> emptyList = new ArrayList<>();
        batchHandler.batchData(emptyList, processor);
        verify(processor, never()).accept(any());
    }

    @Test
    @DisplayName("测试正常批量处理")
    void testNormalBatchProcessing() {
        List<String> dataList = Arrays.asList("a", "b", "c");
        batchHandler.setBatchSize(2).batchData(dataList, processor);
        verify(processor, times(2)).accept(any());
    }

    @Test
    @DisplayName("测试进度回调")
    void testProgressCallback() {
        List<String> dataList = Arrays.asList("a", "b", "c", "d", "e");
        batchHandler.setBatchSize(2).setProgressCallback(progressCallback).batchData(dataList, processor);
        verify(progressCallback, atLeastOnce()).accept(anyInt());
    }

    @Test
    @DisplayName("测试错误处理且继续")
    void testErrorHandlerContinue() {
        List<String> dataList = Arrays.asList("a", "b", "c");
        doThrow(new RuntimeException("模拟异常")).doNothing().when(processor).accept(any());
        when(errorHandler.apply(any())).thenReturn(true);
        batchHandler.setErrorHandler(errorHandler).batchData(dataList, processor);
        verify(processor, atLeast(2)).accept(any());
    }

    @Test
    @DisplayName("测试错误处理且中断")
    void testErrorHandlerAbort() {
        List<String> dataList = Arrays.asList("a", "b", "c");
        doThrow(new RuntimeException("模拟异常")).when(processor).accept(any());
        when(errorHandler.apply(any())).thenReturn(false);
        assertThrows(RuntimeException.class, () -> batchHandler.setErrorHandler(errorHandler).batchData(dataList, processor));
    }

    @Test
    @DisplayName("测试重试次数达到上限")
    void testMaxRetriesReached() {
        List<String> dataList = Arrays.asList("a", "b", "c");
        doThrow(new RuntimeException("模拟异常")).when(processor).accept(any());
        when(errorHandler.apply(any())).thenReturn(true);
        batchHandler.setRetryTimes(2).setErrorHandler(errorHandler);
        assertThrows(RuntimeException.class, () -> batchHandler.batchData(dataList, processor));
    }

    @Test
    @DisplayName("测试自定义批次大小")
    void testCustomBatchSize() {
        List<String> dataList = Arrays.asList("a", "b", "c", "d", "e");
        batchHandler.setBatchSize(3).batchData(dataList, processor);
        verify(processor, times(2)).accept(any());
    }

    @Test
    @DisplayName("测试线程中断")
    void testThreadInterruption() {
        List<String> dataList = Arrays.asList("a", "b", "c");
        doThrow(new RuntimeException("模拟异常")).when(processor).accept(any());
        when(errorHandler.apply(any())).thenReturn(true);
        Thread.currentThread().interrupt();
        assertThrows(RuntimeException.class, () -> batchHandler.setErrorHandler(errorHandler).batchData(dataList, processor));
        Thread.interrupted(); // 清除中断状态
    }
} 