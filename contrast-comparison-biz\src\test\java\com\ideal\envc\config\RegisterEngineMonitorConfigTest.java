package com.ideal.envc.config;

import com.ideal.envc.component.EngineDataExecuteComponent;
import com.ideal.monitor.mq.MonitorFlowDto;
import com.ideal.monitor.mq.MonitorFowActiveNodeDto;
import com.ideal.monitor.service.INoticerFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.Timestamp;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * RegisterEngineMonitorConfig单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class RegisterEngineMonitorConfigTest {

    @Mock
    private EngineDataExecuteComponent engineDataExecuteComponent;

    @Mock
    private INoticerFactory noticerFactory;

    @InjectMocks
    private RegisterEngineMonitorConfig config;

    private MonitorFlowDto monitorFlowDto;
    private MonitorFowActiveNodeDto activeNodeDto;

    @BeforeEach
    void setUp() {
        monitorFlowDto = new MonitorFlowDto();
        monitorFlowDto.setFlowId(1L);
        monitorFlowDto.setFlowStatus(1); // 使用Integer类型
        monitorFlowDto.setBizUniqueId(100L);
        monitorFlowDto.setDateTime(new Timestamp(System.currentTimeMillis()));
        monitorFlowDto.setFlowStartTime(System.currentTimeMillis());
        monitorFlowDto.setFlowEndTime(System.currentTimeMillis() + 1000);
        monitorFlowDto.setUpdateOrderTime(System.currentTimeMillis());

        activeNodeDto = new MonitorFowActiveNodeDto();
        activeNodeDto.setReqId("test_req_id");
        activeNodeDto.setActStatus("FINISH");
        activeNodeDto.setActDefName("CompareContent");
        activeNodeDto.setActName("test_activity");
    }

    @Test
    @DisplayName("测试初始化方法 - 正常注册回调")
    void testInit_NormalRegistration() {
        // 执行初始化方法
        config.init();

        // 验证注册回调
        verify(noticerFactory).registor(any(Consumer.class));
    }

    @Test
    @DisplayName("测试回调处理 - 正常流程信息")
    void testCallbackHandler_NormalFlowInfo() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(null);

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(monitorFlowDto);
    }

    @Test
    @DisplayName("测试回调处理 - 正常活动信息")
    void testCallbackHandler_NormalActivityInfo() {
        // 准备测试数据
        monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(monitorFlowDto);
    }

    @Test
    @DisplayName("测试回调处理 - null消息")
    void testCallbackHandler_NullMessage() {
        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> callback.accept(null));
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(null);
    }

    @Test
    @DisplayName("测试回调处理 - 组件异常")
    void testCallbackHandler_ComponentException() {
        // 准备测试数据
        monitorFlowDto.setFlowId(1L);
        monitorFlowDto.setBizUniqueId(100L);
        monitorFlowDto.setDateTime(new Timestamp(System.currentTimeMillis()));

        // Mock组件抛出异常
        doThrow(new RuntimeException("组件异常")).when(engineDataExecuteComponent).executeMonitorData(any(MonitorFlowDto.class));

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数 - 异常应该被捕获并吞掉，不会抛出
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(monitorFlowDto);
    }

    @Test
    @DisplayName("测试回调处理 - 边界值")
    void testCallbackHandler_BoundaryValues() {
        // 准备边界值测试数据
        monitorFlowDto.setFlowId(1L);
        monitorFlowDto.setBizUniqueId(1L);
        monitorFlowDto.setDateTime(new Timestamp(0L));

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(monitorFlowDto);
    }

    @Test
    @DisplayName("测试回调处理 - 复杂消息")
    void testCallbackHandler_ComplexMessage() {
        // 准备复杂测试数据
        monitorFlowDto.setFlowId(Long.MAX_VALUE);
        monitorFlowDto.setBizUniqueId(Long.MAX_VALUE);
        monitorFlowDto.setDateTime(new Timestamp(Long.MAX_VALUE));
        monitorFlowDto.setFlowStatus(2); // 使用Integer类型
        monitorFlowDto.setFlowStartTime(System.currentTimeMillis());
        monitorFlowDto.setFlowEndTime(System.currentTimeMillis() + 1000);
        monitorFlowDto.setUpdateOrderTime(System.currentTimeMillis());

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(monitorFlowDto);
    }

    @Test
    @DisplayName("测试回调处理 - 多次调用")
    void testCallbackHandler_MultipleCalls() {
        // 准备测试数据
        MonitorFlowDto dto1 = new MonitorFlowDto();
        dto1.setFlowId(1L);
        dto1.setBizUniqueId(100L);
        dto1.setDateTime(new Timestamp(System.currentTimeMillis()));
        
        MonitorFlowDto dto2 = new MonitorFlowDto();
        dto2.setFlowId(2L);
        dto2.setBizUniqueId(200L);
        dto2.setDateTime(new Timestamp(System.currentTimeMillis()));

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> {
            callback.accept(dto1);
            callback.accept(dto2);
        });
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(dto1);
        verify(engineDataExecuteComponent).executeMonitorData(dto2);
    }

    @Test
    @DisplayName("测试回调处理 - 各种活动状态")
    void testCallbackHandler_VariousActivityStates() {
        // 测试各种活动状态
        String[] states = {"FINISH", "FAIL", "FAIL_BUSINESS", "FAIL_SKIPPED", "SKIPPED", "RUNNING", "INVALID_STATE"};

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();

        for (String state : states) {
            // 准备测试数据
            activeNodeDto.setActStatus(state);
            monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);

            // 执行回调函数
            assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        }
    }

    @Test
    @DisplayName("测试回调处理 - 各种活动类型")
    void testCallbackHandler_VariousActivityTypes() {
        // 测试各种活动类型
        String[] types = {"CompareContent", "SyncContent", "ShellCmd", "InvalidType"};

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();

        for (String type : types) {
            // 准备测试数据
            activeNodeDto.setActDefName(type);
            monitorFlowDto.setMonitorFowActiveNodeDto(activeNodeDto);

            // 执行回调函数
            assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        }
    }

    @Test
    @DisplayName("测试回调处理 - 各种流程状态")
    void testCallbackHandler_VariousFlowStates() {
        // 测试各种流程状态
        Integer[] states = {1, 2, 3, 4, 5}; // 使用Integer类型

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();

        for (Integer state : states) {
            // 准备测试数据
            monitorFlowDto.setFlowStatus(state);
            monitorFlowDto.setMonitorFowActiveNodeDto(null);

            // 执行回调函数
            assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        }
    }

    @Test
    @DisplayName("测试回调处理 - 并发场景")
    void testCallbackHandler_ConcurrentScenario() throws InterruptedException {
        // 准备测试数据
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();

        // 创建多个线程同时调用回调方法
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            new Thread(() -> {
                try {
                    MonitorFlowDto dto = new MonitorFlowDto();
                    dto.setFlowId((long) index + 1);
                    dto.setFlowStatus(1); // 使用Integer类型
                    dto.setBizUniqueId((long) index + 100);
                    dto.setDateTime(new Timestamp(System.currentTimeMillis()));
                    dto.setFlowStartTime(System.currentTimeMillis());
                    dto.setFlowEndTime(System.currentTimeMillis() + 1000);
                    dto.setUpdateOrderTime(System.currentTimeMillis());

                    callback.accept(dto);
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        // 等待所有线程完成
        boolean completed = latch.await(5, TimeUnit.SECONDS);
        assertTrue(completed, "并发测试应该在5秒内完成");
    }

    @Test
    @DisplayName("测试回调处理 - 异常场景")
    void testCallbackHandler_ExceptionScenario() {
        // 准备测试数据
        monitorFlowDto.setFlowId(1L);
        monitorFlowDto.setBizUniqueId(100L);
        monitorFlowDto.setDateTime(new Timestamp(System.currentTimeMillis()));

        // Mock组件抛出各种异常
        doThrow(new RuntimeException("运行时异常")).when(engineDataExecuteComponent).executeMonitorData(any(MonitorFlowDto.class));

        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        // 执行回调函数 - 异常应该被捕获并吞掉
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();
        assertDoesNotThrow(() -> callback.accept(monitorFlowDto));
        
        // 验证组件方法被调用
        verify(engineDataExecuteComponent).executeMonitorData(monitorFlowDto);
    }

    @Test
    @DisplayName("测试回调处理 - 内存压力场景")
    void testCallbackHandler_MemoryPressureScenario() {
        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();

        // 准备测试数据 - 大量数据
        for (int i = 0; i < 1000; i++) {
            MonitorFlowDto dto = new MonitorFlowDto();
            dto.setFlowId((long) i);
            dto.setFlowStatus(1); // 使用Integer类型
            dto.setBizUniqueId((long) i + 1000);
            dto.setDateTime(new Timestamp(System.currentTimeMillis()));
            dto.setFlowStartTime(System.currentTimeMillis());
            dto.setFlowEndTime(System.currentTimeMillis() + 1000);
            dto.setUpdateOrderTime(System.currentTimeMillis());

            // 执行回调函数 - 不应该抛出异常
            assertDoesNotThrow(() -> callback.accept(dto));
        }
    }

    @Test
    @DisplayName("测试回调处理 - 性能测试")
    void testCallbackHandler_PerformanceTest() {
        // 执行初始化方法
        config.init();
        
        // 捕获注册的回调函数
        ArgumentCaptor<Consumer<MonitorFlowDto>> callbackCaptor = ArgumentCaptor.forClass(Consumer.class);
        verify(noticerFactory).registor(callbackCaptor.capture());
        
        Consumer<MonitorFlowDto> callback = callbackCaptor.getValue();

        // 准备测试数据
        long startTime = System.currentTimeMillis();

        // 执行多次调用
        for (int i = 0; i < 100; i++) {
            MonitorFlowDto dto = new MonitorFlowDto();
            dto.setFlowId((long) i);
            dto.setFlowStatus(1); // 使用Integer类型
            dto.setBizUniqueId((long) i + 100);
            dto.setDateTime(new Timestamp(System.currentTimeMillis()));
            dto.setFlowStartTime(System.currentTimeMillis());
            dto.setFlowEndTime(System.currentTimeMillis() + 1000);
            dto.setUpdateOrderTime(System.currentTimeMillis());

            callback.accept(dto);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证性能 - 100次调用应该在1秒内完成
        assertTrue(duration < 1000, "100次调用应该在1秒内完成，实际耗时: " + duration + "ms");
    }
} 