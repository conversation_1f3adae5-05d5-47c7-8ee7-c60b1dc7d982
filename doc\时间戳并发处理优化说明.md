# 时间戳并发处理优化说明

## 问题分析

### 1. 原始问题
`RunInstanceInfoStateConsumer`和`RunInstanceStateConsumer`都使用消息中的时间戳作为乐观锁条件存在以下问题：

#### 时间戳失真问题
- **消息时间戳**：来自发送消息时的时间点
- **状态计算时间**：消费消息时重新查询数据库的最新状态
- **时间差异**：消息发送和消费之间的时间差导致timestamp与实际状态计算时间不匹配

#### 并发更新竞争
- 同一个`instanceInfoId`的多个`rule`同时更新会发送多个消息（RunInstanceInfoStateConsumer）
- 同一个`instanceId`的多个`instanceInfo`同时更新会发送多个消息（RunInstanceStateConsumer）
- 每个消息的`timestamp`不同，但都要更新同一条记录
- 可能出现"后计算但timestamp更早"的消息无法更新的情况

#### 乐观锁失效
- 原始SQL条件：`AND iupdate_time = FROM_UNIXTIME(#{timestamp}/1000)`
- 严格相等判断导致并发更新时乐观锁机制失效

## 解决方案

### 1. 改进SQL更新条件
```sql
-- 原始条件
AND iupdate_time = FROM_UNIXTIME(#{timestamp}/1000)

-- 优化后条件  
AND iupdate_time >= FROM_UNIXTIME(#{timestamp}/1000)
```
**优点**：允许基于更新时间的向前兼容性

**影响的文件**：
- `RunInstanceInfoMapper.xml` - updateStateAndResultByIdAndTimestamp
- `RunInstanceMapper.xml` - updateStateAndResultByIdAndTimestamp

### 2. 使用数据库当前时间戳
- **查询当前记录**：获取数据库中记录的`updateTime`
- **使用当前时间戳**：作为乐观锁条件而非消息中的时间戳
- **避免时间差异**：确保时间戳与状态计算时间一致

### 3. 添加消息过期机制
```java
private static final long MESSAGE_EXPIRE_TIME = 30000L; // 30秒

// 检查消息是否过期
if (messageTimestamp != null && currentTimestamp > messageTimestamp + MESSAGE_EXPIRE_TIME) {
    logger.warn("消息已过期，跳过处理");
    return true;
}
```
**优点**：避免处理过期的消息，减少无效计算

### 4. 实现重试机制
```java
private static final int RETRY_COUNT = 3;

// 重试机制处理并发更新
for (int retry = 0; retry < RETRY_COUNT && !updateSuccess; retry++) {
    if (retry > 0) {
        Thread.sleep(100); // 短暂延迟
    }
    updateSuccess = processInstanceInfoUpdate(instanceInfoId, ruleId, messageTimestamp);
}
```
**优点**：提高并发环境下的更新成功率

### 5. 状态计算逻辑优化
- 将状态计算逻辑提取为独立方法
- 清晰的状态计算规则和优先级
- 详细的日志记录便于问题排查

## 优化范围

### 消费者类
1. **RunInstanceInfoStateConsumer**
   - 处理`ieai_envc_run_rule`表更新后的汇总消息
   - 更新`ieai_envc_run_instance_info`表状态

2. **RunInstanceStateConsumer**  
   - 处理`ieai_envc_run_instance_info`表更新后的汇总消息
   - 更新`ieai_envc_run_instance`表状态

### Mapper接口
1. **RunInstanceInfoMapper**
   - `updateStateAndResultByIdAndTimestamp`方法优化

2. **RunInstanceMapper**
   - `updateStateAndResultByIdAndTimestamp`方法优化

## 核心改进点

### 时间戳使用策略
1. **查询时获取**：从数据库查询当前记录的`updateTime`
2. **作为锁条件**：使用当前时间戳而非消息时间戳作为乐观锁条件
3. **向前兼容**：SQL条件改为`>=`允许时间向前推进

### 并发处理机制
1. **重试机制**：最多重试3次，每次间隔100ms
2. **过期检查**：超过30秒的消息自动丢弃
3. **状态检查**：状态无变化时跳过更新

### 错误处理优化
1. **分类处理**：区分不同类型的处理结果
2. **避免无效重试**：记录不存在、无关联规则等情况直接返回成功
3. **详细日志**：记录关键的调试信息

## 预期效果

### 1. 解决并发问题
- 减少因时间戳不匹配导致的更新失败
- 提高高并发场景下的数据一致性

### 2. 提升性能
- 避免处理过期消息
- 减少无效的数据库更新操作

### 3. 增强可靠性
- 重试机制提高更新成功率
- 详细日志便于问题定位

### 4. 改善监控
- 清晰的处理状态反馈
- 便于运维人员监控系统健康状态

## 状态流转关系

```
ieai_envc_run_rule (规则表)
       ↓ 汇总状态
ieai_envc_run_instance_info (实例信息表) 
       ↓ 汇总状态  
ieai_envc_run_instance (实例表)
```

1. 多个`run_rule`记录汇总到一个`run_instance_info`记录
2. 多个`run_instance_info`记录汇总到一个`run_instance`记录
3. 每级汇总都可能存在并发更新问题，现已统一优化

## 注意事项

1. **重试延迟**：100ms的重试延迟需要根据实际并发情况调整
2. **过期时间**：30秒的过期时间需要根据业务特性调整
3. **日志级别**：生产环境中可能需要调整某些INFO级别的日志
4. **监控指标**：建议添加更新成功率、重试次数等监控指标
5. **一致性保证**：两个Consumer使用相同的优化策略确保行为一致 