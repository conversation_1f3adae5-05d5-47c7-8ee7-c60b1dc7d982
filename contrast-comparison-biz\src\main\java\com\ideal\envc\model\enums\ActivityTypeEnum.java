package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 活动类型枚举
 * <AUTHOR>
 */
public enum ActivityTypeEnum {

    /**
     * 脚本活动
     */
    SHELL_CMD("shellcmd", "脚本活动"),

    /**
     * 文件比对活动
     */
    COMPARE_CONTENT("Compare Content", "文件比对活动"),

    /**
     * 文件同步活动
     */
    SYNC_CONTENT("Sync Content", "文件同步活动");

    /**
     * 活动类型代码
     */
    private final String code;

    /**
     * 活动类型名称
     */
    private final String name;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<String, ActivityTypeEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ActivityTypeEnum::getCode, Function.identity()));

    /**
     * 构造函数
     * @param code 活动类型代码
     * @param name 活动类型名称
     */
    ActivityTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取活动类型代码
     * @return 活动类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取活动类型名称
     * @return 活动类型名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据活动类型代码获取枚举实例
     * @param code 活动类型代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static ActivityTypeEnum getByCode(String code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据活动类型代码获取活动类型名称
     * @param code 活动类型代码
     * @return 活动类型名称，如果不存在则返回"未知活动类型"
     */
    public static String getNameByCode(String code) {
        return Optional.ofNullable(getByCode(code))
                .map(ActivityTypeEnum::getName)
                .orElse("未知活动类型");
    }

    /**
     * 判断给定的活动类型代码是否在枚举允许的范围内
     * @param code 活动类型代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(String code) {
        return code != null && CODE_MAP.containsKey(code);
    }
}
