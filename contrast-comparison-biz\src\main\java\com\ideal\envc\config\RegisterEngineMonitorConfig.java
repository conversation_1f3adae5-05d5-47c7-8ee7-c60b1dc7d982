package com.ideal.envc.config;

import com.ideal.envc.component.EngineDataExecuteComponent;
import com.ideal.monitor.service.INoticerFactory;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 注册监控数据回调处理
 * <AUTHOR>
 */
@Configuration
public class RegisterEngineMonitorConfig {

    private final EngineDataExecuteComponent engineDataExecuteComponent;
    private final INoticerFactory noticerFactory;

    public RegisterEngineMonitorConfig(EngineDataExecuteComponent engineDataExecuteComponent, INoticerFactory noticerFactory) {
        this.engineDataExecuteComponent = engineDataExecuteComponent;
        this.noticerFactory = noticerFactory;
    }

    @PostConstruct
    void init() {
        noticerFactory.registor(dto -> {
            try {
                engineDataExecuteComponent.executeMonitorData(dto);
            } catch (Exception e) {
                // 可选：记录日志，防止异常影响主流程
                // System.err.println("Monitor callback error: " + e.getMessage());
            }
        });
    }
}
