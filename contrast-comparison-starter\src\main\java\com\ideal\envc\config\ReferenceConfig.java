package com.ideal.envc.config;



import com.ideal.engine.api.IActivity;
import com.ideal.engine.api.IStartFlow;
import com.ideal.system.api.IBusinessSystem;
import com.ideal.system.api.IBusinessSystemCompuerList;
import com.ideal.system.api.ICenter;

import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.spring.ReferenceBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class ReferenceConfig {

    @Bean
    @DubboReference(check = false, group = "system", version = "1.0.0")
    public ReferenceBean<IBusinessSystem> businessSystemApi() {
        return new ReferenceBean<>();
    }

    @Bean
    @DubboReference(check = false, group = "system", version = "1.0.0")
    public ReferenceBean<IBusinessSystemCompuerList> businessSystemCompuerListApi() {
        return new ReferenceBean<>();
    }

    @Bean
    @DubboReference(check = false, group = "system", version = "1.0.0")
    public ReferenceBean<ICenter> centerApi() {
        return new ReferenceBean<>();
    }

    @Bean
    @DubboReference(check = false, group = "engine", version = "1.0.0" ,providedBy = {"dubbo-engine"})
    public ReferenceBean<IStartFlow> engineStartFlowService() {
        return new ReferenceBean<>();
    }

    @Bean
    @DubboReference(check = false, group = "engine", version = "1.0.0" ,providedBy = {"dubbo-engine"})
    public ReferenceBean<IActivity> engineActivityService() {
        return new ReferenceBean<>();
    }
}
