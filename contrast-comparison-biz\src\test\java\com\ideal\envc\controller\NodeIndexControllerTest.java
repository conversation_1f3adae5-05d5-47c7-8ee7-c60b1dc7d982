package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.*;
import com.ideal.envc.service.INodeIndexService;
import com.ideal.envc.service.ISystemComputerNodeService;
import com.ideal.envc.service.ISystemComputerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 节点索引控制器测试类
 */
@ExtendWith(MockitoExtension.class)
public class NodeIndexControllerTest {

    @Mock
    private INodeIndexService nodeIndexService;

    @Mock
    private ISystemComputerService systemComputerService;

    @Mock
    private ISystemComputerNodeService systemComputerNodeService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private NodeIndexController nodeIndexController;

    private UserDto userDto;
    private NodeBatchSaveRequestDto nodeBatchSaveRequestDto;
    private SystemListQueryDto systemListQueryDto;
    private TableQueryDto<SystemListQueryDto> systemTableQueryDto;
    private SystemComputerNodeQueryDto systemComputerNodeQueryDto;
    private TableQueryDto<SystemComputerNodeQueryDto> nodeTableQueryDto;
    private SystemComputerNodeDto systemComputerNodeDto;
    private SystemComputerQueryPageDto systemComputerQueryPageDto;
    private SystemComputerQueryDto systemComputerQueryDto;
    private PageInfo<SystemListDto> systemListPageInfo;
    private PageInfo<SystemComputerNodeListDto> nodeListPageInfo;
    private PageInfo<SystemComputerDto> computerPageInfo;
    private List<SystemComputerDto> computerList;

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        userDto.setLoginName("testuser");

        // 初始化批量保存节点请求DTO
        nodeBatchSaveRequestDto = new NodeBatchSaveRequestDto();
        nodeBatchSaveRequestDto.setBusinessSystemId(1L);
        nodeBatchSaveRequestDto.setSourceCenterId(1L);
        nodeBatchSaveRequestDto.setTargetCenterId(2L);
        
        // 初始化系统列表查询条件
        systemListQueryDto = new SystemListQueryDto();
        systemListQueryDto.setBusinessSystemName("测试系统");
        systemTableQueryDto = new TableQueryDto<>();
        systemTableQueryDto.setQueryParam(systemListQueryDto);
        systemTableQueryDto.setPageNum(1);
        systemTableQueryDto.setPageSize(10);
        
        // 初始化系统节点关系查询条件
        systemComputerNodeQueryDto = new SystemComputerNodeQueryDto();
        systemComputerNodeQueryDto.setBusinessSystemId(1L);
        nodeTableQueryDto = new TableQueryDto<>();
        nodeTableQueryDto.setQueryParam(systemComputerNodeQueryDto);
        nodeTableQueryDto.setPageNum(1);
        nodeTableQueryDto.setPageSize(10);
        
        // 初始化系统节点关系DTO
        systemComputerNodeDto = new SystemComputerNodeDto();
        systemComputerNodeDto.setBusinessSystemId(1L);
        systemComputerNodeDto.setSourceComputerId(1L);
        systemComputerNodeDto.setTargetComputerId(2L);

        // 初始化设备查询条件（分页）
        systemComputerQueryPageDto = new SystemComputerQueryPageDto();
        systemComputerQueryPageDto.setBusinessSystemId(1L);
        systemComputerQueryPageDto.setSourceCenterId(1L);

        // 初始化设备查询条件（不分页）
        systemComputerQueryDto = new SystemComputerQueryDto();
        systemComputerQueryDto.setBusinessSystemId(1L);
        systemComputerQueryDto.setCenterId(1L);

        // 初始化系统列表分页数据
        List<SystemListDto> systemList = new ArrayList<>();
        SystemListDto systemListDto = new SystemListDto();
        systemListDto.setBusinessSystemId(1L);
        systemListDto.setBusinessSystemName("测试系统");
        systemList.add(systemListDto);
        systemListPageInfo = new PageInfo<>(systemList);
        
        // 初始化节点关系列表分页数据
        List<SystemComputerNodeListDto> nodeList = new ArrayList<>();
        SystemComputerNodeListDto nodeListDto = new SystemComputerNodeListDto();
        nodeListDto.setId(1L);
        nodeListDto.setBusinessSystemId(1L);
        nodeListDto.setSourceCenterName("测试系统");
        nodeList.add(nodeListDto);
        nodeListPageInfo = new PageInfo<>(nodeList);
        
        // 初始化设备列表分页数据
        List<SystemComputerDto> computers = new ArrayList<>();
        SystemComputerDto computerDto = new SystemComputerDto();
        computerDto.setId(1L);
        computerDto.setComputerIp("***********");
        computerDto.setBusinessSystemId(1L);
        computers.add(computerDto);
        computerPageInfo = new PageInfo<>(computers);
        computerList = computers;
    }

    @Test
    @DisplayName("测试批量保存节点关系 - 成功情况")
    void testSaveSuccess() {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeIndexService.batchSaveNodeRelation(any(NodeBatchSaveRequestDto.class), any(UserDto.class))).thenReturn(true);

        // 执行方法
        R<Void> result = nodeIndexController.save(nodeBatchSaveRequestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(nodeIndexService).batchSaveNodeRelation(eq(nodeBatchSaveRequestDto), eq(userDto));
    }

    @Test
    @DisplayName("测试批量保存节点关系 - 失败情况")
    void testSaveFail() {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeIndexService.batchSaveNodeRelation(any(NodeBatchSaveRequestDto.class), any(UserDto.class))).thenReturn(false);

        // 执行方法
        R<Void> result = nodeIndexController.save(nodeBatchSaveRequestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(nodeIndexService).batchSaveNodeRelation(eq(nodeBatchSaveRequestDto), eq(userDto));
    }

    @Test
    @DisplayName("测试批量保存节点关系 - 异常情况")
    void testSaveException() {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeIndexService.batchSaveNodeRelation(any(NodeBatchSaveRequestDto.class), any(UserDto.class)))
            .thenThrow(new RuntimeException("测试异常"));

        // 执行方法
        R<Void> result = nodeIndexController.save(nodeBatchSaveRequestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("139900", result.getCode());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(nodeIndexService).batchSaveNodeRelation(eq(nodeBatchSaveRequestDto), eq(userDto));
    }

    @Test
    @DisplayName("测试查询已绑定设备的业务系统列表")
    void testSystemList() {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerService.selectSystemList(
                any(SystemListQueryDto.class), anyInt(), anyInt(), anyLong()
        )).thenReturn(systemListPageInfo);

        // 执行方法
        R<PageInfo<SystemListDto>> result = nodeIndexController.systemList(systemTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());
        assertEquals("测试系统", result.getData().getList().get(0).getBusinessSystemName());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(systemComputerService).selectSystemList(
                eq(systemListQueryDto),
                eq(1),
                eq(10),
                eq(userDto.getId())
        );
    }

    @Test
    @DisplayName("测试查询系统已绑定源目标设备列表")
    void testNodeList() {
        // 设置Mock行为
        when(systemComputerNodeService.selectSystemComputerNodeBeanList(
                any(SystemComputerNodeQueryDto.class), 
                anyInt(), 
                anyInt())
        ).thenReturn(nodeListPageInfo);

        // 执行方法
        R<PageInfo<SystemComputerNodeListDto>> result = nodeIndexController.nodeList(nodeTableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());
        assertEquals("测试系统", result.getData().getList().get(0).getSourceCenterName());

        // 验证方法调用
        verify(systemComputerNodeService).selectSystemComputerNodeBeanList(
                eq(systemComputerNodeQueryDto), 
                eq(1), 
                eq(10)
        );
    }

    @Test
    @DisplayName("测试系统绑定源目标设备 - 成功情况")
    void testBindSuccess() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerNodeService.batchBindSystemComputerNode(any(SystemComputerNodeBatchDto.class), any(UserDto.class))).thenReturn(1);

        // 创建批量绑定DTO
        SystemComputerNodeBatchDto batchDto = new SystemComputerNodeBatchDto();
        batchDto.setBusinessSystemId(1L);
        batchDto.setSourceComputerId(1L);
        batchDto.setSourceCenterId(1L);
        batchDto.setTargetCenterId(2L);
        batchDto.setTargetComputerIdList(Arrays.asList(2L));

        // 执行方法
        R<Void> result = nodeIndexController.bind(batchDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(systemComputerNodeService).batchBindSystemComputerNode(eq(batchDto), eq(userDto));
    }

    @Test
    @DisplayName("测试系统绑定源目标设备 - 关系已存在")
    void testBindExist() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerNodeService.batchBindSystemComputerNode(any(SystemComputerNodeBatchDto.class), any(UserDto.class)))
            .thenThrow(new ContrastBusinessException("20001：节点关系已存在"));

        // 创建批量绑定DTO
        SystemComputerNodeBatchDto batchDto = new SystemComputerNodeBatchDto();
        batchDto.setBusinessSystemId(1L);
        batchDto.setSourceComputerId(1L);
        batchDto.setSourceCenterId(1L);
        batchDto.setTargetCenterId(2L);
        batchDto.setTargetComputerIdList(Arrays.asList(2L));

        // 执行方法
        R<Void> result = nodeIndexController.bind(batchDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("20001", result.getCode());
        assertEquals("节点关系已存在", result.getMessage());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(systemComputerNodeService).batchBindSystemComputerNode(eq(batchDto), eq(userDto));
    }

    @Test
    @DisplayName("测试系统绑定源目标设备 - 插入失败")
    void testBindInsertFail() throws ContrastBusinessException {
        // 设置Mock行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(systemComputerNodeService.batchBindSystemComputerNode(any(SystemComputerNodeBatchDto.class), any(UserDto.class))).thenReturn(0);

        // 执行方法
        R<Void> result = nodeIndexController.bind(new SystemComputerNodeBatchDto());

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(systemComputerNodeService).batchBindSystemComputerNode(any(SystemComputerNodeBatchDto.class), eq(userDto));
    }

    @Test
    @DisplayName("测试系统解绑源目标设备")
    void testUnbind() {
        // 要解绑的ID数组
        Long[] ids = new Long[]{1L, 2L};

        // 执行方法
        R<Void> result = nodeIndexController.unbind(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证方法调用
        verify(systemComputerNodeService).deleteSystemComputerNodeByIds(eq(ids));
    }

    @Test
    @DisplayName("测试删除节点关系记录")
    void testComputerNodeRemove() {
        // 要删除的ID数组
        Long[] ids = new Long[]{1L, 2L};

        // 执行方法
        R<Void> result = nodeIndexController.computerNodeRemove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证方法调用
        verify(systemComputerNodeService).deleteSystemComputerNodeByIds(eq(ids));
    }

    @Test
    @DisplayName("测试查询待绑定目标带绑定设备列表（分页）")
    void testComputerListPage() throws ContrastBusinessException {
        // 设置Mock行为
        SystemComputerQueryPageDto queryDto = new SystemComputerQueryPageDto();
        queryDto.setBusinessSystemId(1L);
        queryDto.setSourceCenterId(1L);
        queryDto.setTargetCenterId(2L);
        queryDto.setSourceComputerId(1L);
        
        TableQueryDto<SystemComputerQueryPageDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        when(nodeIndexService.selectSystemComputerListPage(any(TableQueryDto.class))).thenReturn(computerPageInfo);

        // 执行方法
        R<PageInfo<SystemComputerDto>> result = nodeIndexController.computerListPage(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());

        // 验证方法调用
        verify(nodeIndexService).selectSystemComputerListPage(eq(tableQueryDto));
    }

    @Test
    @DisplayName("测试查询待绑定源设备列表")
    void testComputerList() throws ContrastBusinessException {
        // 设置Mock行为
        when(nodeIndexService.selectSystemComputerList(anyLong(), anyLong(), any())).thenReturn(computerList);

        // 创建查询DTO
        SystemComputerQueryDto queryDto = new SystemComputerQueryDto();
        queryDto.setBusinessSystemId(1L);
        queryDto.setCenterId(1L);
        queryDto.setExcludeComputerIds(Arrays.asList(3L, 4L));

        // 执行方法
        R<List<SystemComputerDto>> result = nodeIndexController.computerList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(computerList, result.getData());

        // 验证方法调用
        verify(nodeIndexService).selectSystemComputerList(
            eq(queryDto.getBusinessSystemId()), 
            eq(queryDto.getCenterId()), 
            eq(queryDto.getExcludeComputerIds())
        );
    }
} 