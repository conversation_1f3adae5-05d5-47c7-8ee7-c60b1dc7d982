# 启动监控相关配置示例
# 可以将这些配置添加到主配置文件中

# 启动监控配置
startup:
  monitor:
    enabled: true  # 启用启动监控器，默认为true
  health:
    enabled: true  # 启用启动健康检查，默认为true

# Spring Boot Actuator配置（用于健康检查端点）
management:
  endpoints:
    web:
      exposure:
        include: health,info  # 暴露健康检查和信息端点
  endpoint:
    health:
      show-details: always  # 显示详细的健康检查信息
      show-components: true  # 显示各个组件的健康状态
  health:
    defaults:
      enabled: true  # 启用默认健康检查

# 日志配置（可选）
logging:
  level:
    com.ideal.envc.Bootstrap: INFO  # Bootstrap启动类日志级别
    com.ideal.envc.startup: INFO   # 启动监控相关类日志级别
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# JVM参数建议（在启动脚本中设置）
# -Xms512m          # 初始堆内存
# -Xmx1024m         # 最大堆内存
# -XX:+HeapDumpOnOutOfMemoryError  # OOM时生成堆转储
# -XX:HeapDumpPath=/tmp/heapdump.hprof  # 堆转储文件路径
