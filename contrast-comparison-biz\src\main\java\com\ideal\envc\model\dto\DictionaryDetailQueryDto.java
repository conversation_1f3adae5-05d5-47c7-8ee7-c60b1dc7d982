package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 字典详情对象 ieai_envc_dictionary_detail
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class DictionaryDetailQueryDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;
    /** 码表主键 */
    private Long envcDictionaryId;
    /** 字典码 */
    private String code;
    /** 显示名称 */
    private String lable;
    /** 显示值 */
    private String value;
    /** 排序序号 */
    private Long sort;
    /** 删除标识 0：否，1：是 */
    private Integer deleted;
    /** 数组标识 0：否，1：是 */
    private Integer arrayFlag;
    /** 字典值类型 */
    private String valueType;
    /** 创建人名称 */
    private String creatorName;
    /** 创建人ID */
    private Long creatorId;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcDictionaryId(Long envcDictionaryId){
        this.envcDictionaryId = envcDictionaryId;
    }

    public Long getEnvcDictionaryId(){
        return envcDictionaryId;
    }

    public void setCode(String code){
        this.code = code;
    }

    public String getCode(){
        return code;
    }

    public void setLable(String lable){
        this.lable = lable;
    }

    public String getLable(){
        return lable;
    }

    public void setValue(String value){
        this.value = value;
    }

    public String getValue(){
        return value;
    }

    public void setSort(Long sort){
        this.sort = sort;
    }

    public Long getSort(){
        return sort;
    }

    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    public Integer getDeleted(){
        return deleted;
    }

    public void setArrayFlag(Integer arrayFlag){
        this.arrayFlag = arrayFlag;
    }

    public Integer getArrayFlag(){
        return arrayFlag;
    }

    public void setValueType(String valueType){
        this.valueType = valueType;
    }

    public String getValueType(){
        return valueType;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
        " ["+
        "Hash = "+hashCode()+
            ",id="+getId()+
            ",envcDictionaryId="+getEnvcDictionaryId()+
            ",code="+getCode()+
            ",lable="+getLable()+
            ",value="+getValue()+
            ",sort="+getSort()+
            ",deleted="+getDeleted()+
            ",arrayFlag="+getArrayFlag()+
            ",valueType="+getValueType()+
            ",creatorName="+getCreatorName()+
            ",creatorId="+getCreatorId()+
            ",createTime="+getCreateTime()+
            ",updatorId="+getUpdatorId()+
            ",updatorName="+getUpdatorName()+
            ",updateTime="+getUpdateTime()+
        "]";
    }
}

