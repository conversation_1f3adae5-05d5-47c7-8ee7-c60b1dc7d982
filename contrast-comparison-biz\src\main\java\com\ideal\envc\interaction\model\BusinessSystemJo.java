package com.ideal.envc.interaction.model;

import java.io.Serializable;

/**
 * 业务系统对象
 *
 * <AUTHOR>
 */
public class BusinessSystemJo implements Serializable {
    private static final long serialVersionUID=1L;

    /** 业务系统ID */
    private Long businessSystemId;
    /** 业务系统编码 */
    private String businessSystemCode;
    /** 业务系统名称 */
    private String businessSystemName;
    /** 业务系统描述 */
    private String businessSystemDesc;
    /** 删除标志（0代表存在 1代表删除） */
    private Integer deleted;
    /** ID */
    private Long id;
    /** 名称 */
    private String name;

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setBusinessSystemCode(String businessSystemCode){
        this.businessSystemCode = businessSystemCode;
    }

    public String getBusinessSystemCode(){
        return businessSystemCode;
    }

    public void setBusinessSystemName(String businessSystemName){
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemName(){
        return businessSystemName;
    }

    public void setBusinessSystemDesc(String businessSystemDesc){
        this.businessSystemDesc = businessSystemDesc;
    }

    public String getBusinessSystemDesc(){
        return businessSystemDesc;
    }

    public void setDeleted(Integer deleted){
        this.deleted = deleted;
    }

    public Integer getDeleted(){
        return deleted;
    }

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }

    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",businessSystemId="+getBusinessSystemId()+
                    ",businessSystemCode="+getBusinessSystemCode()+
                    ",businessSystemName="+getBusinessSystemName()+
                    ",businessSystemDesc="+getBusinessSystemDesc()+
                    ",deleted="+getDeleted()+
                    ",id="+getId()+
                    ",name="+getName()+
                "]";
    }
}
