package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 节点规则内容查询DTO
 *
 * <AUTHOR>
 */
public class RunRuleContentQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 节点规则结果ID */
    private Long envcRunRuleId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcRunRuleId() {
        return envcRunRuleId;
    }

    public void setEnvcRunRuleId(Long envcRunRuleId) {
        this.envcRunRuleId = envcRunRuleId;
    }

    @Override
    public String toString() {
        return "RunRuleContentQueryDto{" +
                "id=" + id +
                ", envcRunRuleId=" + envcRunRuleId +
                '}';
    }
}
