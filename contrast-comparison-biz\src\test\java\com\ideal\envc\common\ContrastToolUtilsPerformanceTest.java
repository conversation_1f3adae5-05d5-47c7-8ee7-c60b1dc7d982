package com.ideal.envc.common;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ContrastToolUtils Base64解码性能测试
 */
class ContrastToolUtilsPerformanceTest {

    @Test
    @DisplayName("测试小数据量Base64解码性能")
    @Timeout(value = 1, unit = TimeUnit.SECONDS)
    void testSmallDataPerformance() {
        String smallBase64 = "SGVsbG8gV29ybGQ="; // "Hello World"
        
        // 执行1000次解码操作
        for (int i = 0; i < 1000; i++) {
            String result = ContrastToolUtils.getFromBase64(smallBase64);
            assertEquals("Hello World", result);
        }
    }

    @Test
    @DisplayName("测试中等数据量Base64解码性能")
    @Timeout(value = 5, unit = TimeUnit.SECONDS)
    void testMediumDataPerformance() {
        // 创建约1KB的数据
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            content.append("Hello World ");
        }
        
        String base64Content = java.util.Base64.getEncoder().encodeToString(content.toString().getBytes());
        
        // 执行100次解码操作
        for (int i = 0; i < 100; i++) {
            String result = ContrastToolUtils.getFromBase64(base64Content);
            assertNotNull(result);
            assertTrue(result.contains("Hello World"));
        }
    }

    @Test
    @DisplayName("测试大数据量Base64解码性能")
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testLargeDataPerformance() {
        // 创建约100KB的数据
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            content.append("Hello World ");
        }
        
        String base64Content = java.util.Base64.getEncoder().encodeToString(content.toString().getBytes());
        
        // 执行10次解码操作
        for (int i = 0; i < 10; i++) {
            String result = ContrastToolUtils.getFromBase64(base64Content);
            assertNotNull(result);
            assertTrue(result.contains("Hello World"));
        }
    }

    @Test
    @DisplayName("测试格式验证性能影响")
    @Timeout(value = 2, unit = TimeUnit.SECONDS)
    void testFormatValidationPerformance() {
        String validBase64 = "SGVsbG8gV29ybGQ=";
        String invalidBase64 = "SGVsbG8gV29ybGQ"; // 缺少填充
        
        // 测试有效格式的性能
        long startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            ContrastToolUtils.getFromBase64(validBase64);
        }
        long validTime = System.nanoTime() - startTime;
        
        // 测试无效格式的性能（应该快速失败）
        startTime = System.nanoTime();
        for (int i = 0; i < 1000; i++) {
            ContrastToolUtils.getFromBase64(invalidBase64);
        }
        long invalidTime = System.nanoTime() - startTime;
        
        // 无效格式的处理应该更快（快速失败）
        assertTrue(invalidTime <= validTime * 2, "无效格式处理时间不应该显著超过有效格式");
    }

    @Test
    @DisplayName("测试安全检查的性能开销")
    @Timeout(value = 3, unit = TimeUnit.SECONDS)
    void testSecurityCheckOverhead() {
        String testBase64 = "SGVsbG8gV29ybGQ=";
        
        // 预热JVM
        for (int i = 0; i < 100; i++) {
            ContrastToolUtils.getFromBase64(testBase64);
        }
        
        // 测试带安全检查的性能
        long startTime = System.nanoTime();
        for (int i = 0; i < 10000; i++) {
            ContrastToolUtils.getFromBase64(testBase64);
        }
        long totalTime = System.nanoTime() - startTime;
        
        // 平均每次操作时间应该在合理范围内（小于1毫秒）
        long avgTimeNanos = totalTime / 10000;
        long avgTimeMicros = avgTimeNanos / 1000;
        
        assertTrue(avgTimeMicros < 1000, 
                  String.format("平均解码时间过长：%d微秒", avgTimeMicros));
    }

    @Test
    @DisplayName("测试并发解码性能")
    @Timeout(value = 10, unit = TimeUnit.SECONDS)
    void testConcurrentDecodePerformance() throws InterruptedException {
        String testBase64 = "SGVsbG8gV29ybGQ=";
        int threadCount = 10;
        int operationsPerThread = 100;
        
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String result = ContrastToolUtils.getFromBase64(testBase64);
                        if (!"Hello World".equals(result)) {
                            results[threadIndex] = false;
                            return;
                        }
                    }
                    results[threadIndex] = true;
                } catch (Exception e) {
                    results[threadIndex] = false;
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }
        
        // 验证所有线程都成功完成
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], "线程 " + i + " 执行失败");
        }
    }

    @Test
    @DisplayName("测试内存使用效率")
    void testMemoryEfficiency() {
        // 获取初始内存使用情况
        Runtime runtime = Runtime.getRuntime();
        System.gc(); // 建议进行垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 执行大量Base64解码操作
        String testBase64 = "SGVsbG8gV29ybGQ=";
        for (int i = 0; i < 10000; i++) {
            ContrastToolUtils.getFromBase64(testBase64);
        }
        
        System.gc(); // 建议进行垃圾回收
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        
        // 内存增长应该在合理范围内
        long memoryIncrease = finalMemory - initialMemory;
        long memoryIncreaseMB = memoryIncrease / (1024 * 1024);
        
        assertTrue(memoryIncreaseMB < 50, 
                  String.format("内存使用增长过多：%dMB", memoryIncreaseMB));
    }

    @Test
    @DisplayName("测试超时保护机制")
    @Timeout(value = 1, unit = TimeUnit.SECONDS)
    void testTimeoutProtection() {
        // 测试正常大小的数据不会超时
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            content.append("Test data ");
        }
        
        String base64Content = java.util.Base64.getEncoder().encodeToString(content.toString().getBytes());
        
        // 这个操作应该在1秒内完成
        String result = ContrastToolUtils.getFromBase64(base64Content);
        assertNotNull(result);
        assertTrue(result.contains("Test data"));
    }
}
