package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 任务对象 ieai_envc_task
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class TaskDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;
    /** 方案ID */
    @NotNull(message = "方案ID不能为空")
    private Long envcPlanId;
    /** 方案名称 */
    private String planName;
    /** 方案描述 */
    private String planDesc;
    /** 周期表达式 */
    @NotBlank(message = "cron表达式不能为空")
    private String cron;
    /** 是否启用（1:启用，0：禁用） */
    private Integer enabled;
    /** 启停状态（0:启动，1：停止） */
    private Integer state;
    /** 源中心ID */
    private Long sourceCenterId;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 定时ID */
    private Long scheduledId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建人ID */
    private Long creatorId;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;
    
    /** 源中心名称 */
    private String sourceCenterName;
    /** 目标中心名称 */
    private String targetCenterName;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcPlanId(Long envcPlanId){
        this.envcPlanId = envcPlanId;
    }

    public Long getEnvcPlanId(){
        return envcPlanId;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public void setCron(String cron){
        this.cron = cron;
    }

    public String getCron(){
        return cron;
    }

    public void setEnabled(Integer enabled){
        this.enabled = enabled;
    }

    public Integer getEnabled(){
        return enabled;
    }

    public void setState(Integer state){
        this.state = state;
    }

    public Integer getState(){
        return state;
    }

    public void setSourceCenterId(Long sourceCenterId){
        this.sourceCenterId = sourceCenterId;
    }

    public Long getSourceCenterId(){
        return sourceCenterId;
    }

    public void setTargetCenterId(Long targetCenterId){
        this.targetCenterId = targetCenterId;
    }

    public Long getTargetCenterId(){
        return targetCenterId;
    }

    public void setScheduledId(Long scheduledId){
        this.scheduledId = scheduledId;
    }

    public Long getScheduledId(){
        return scheduledId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public String getPlanDesc() {
        return planDesc;
    }

    public void setPlanDesc(String planDesc) {
        this.planDesc = planDesc;
    }

    @Override
    public String toString() {
        return "TaskDto{" +
                "id=" + id +
                ", envcPlanId=" + envcPlanId +
                ", planName='" + planName + '\'' +
                ", planDesc='" + planDesc + '\'' +
                ", cron='" + cron + '\'' +
                ", enabled=" + enabled +
                ", state=" + state +
                ", sourceCenterId=" + sourceCenterId +
                ", targetCenterId=" + targetCenterId +
                ", scheduledId=" + scheduledId +
                ", creatorName='" + creatorName + '\'' +
                ", creatorId=" + creatorId +
                ", createTime=" + createTime +
                ", updatorId=" + updatorId +
                ", updatorName='" + updatorName + '\'' +
                ", updateTime=" + updateTime +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", targetCenterName='" + targetCenterName + '\'' +
                '}';
    }
}

