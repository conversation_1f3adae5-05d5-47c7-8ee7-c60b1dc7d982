package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.interaction.model.ComputerJo;
import com.ideal.envc.interaction.model.SystemComputerListQueryIo;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.model.bean.SystemListBean;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryDto;
import com.ideal.envc.model.dto.SystemListDto;
import com.ideal.envc.model.dto.SystemListQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.NodeRelationEntity;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

/**
 * 系统计算机Service单元测试
 */
@ExtendWith(MockitoExtension.class)
public class SystemComputerServiceImplTest {

    @Mock
    private SystemComputerMapper systemComputerMapper;

    @Mock
    private SystemComputerNodeMapper systemComputerNodeMapper;

    @Mock
    private NodeRelationMapper nodeRelationMapper;

    @Mock
    private NodeRuleContentMapper nodeRuleContentMapper;

    @Mock
    private BatchHandler batchHandler;

    @Mock
    private SystemInteract systemInteract;

    @InjectMocks
    private SystemComputerServiceImpl systemComputerService;

    private SystemComputerDto systemComputerDto;
    private SystemComputerEntity systemComputerEntity;
    private SystemComputerQueryDto systemComputerQueryDto;
    private UserDto userDto;
    private List<SystemComputerEntity> systemComputerEntityList;
    private List<SystemComputerDto> systemComputerDtoList;
    private R<PageInfo<ComputerJo>> computerJoPageInfoR;
    private SystemListQueryDto systemListQueryDto;
    private List<Long> businessSystemIdList;
    private List<SystemListBean> systemListBeans;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        systemComputerDto = new SystemComputerDto();
        systemComputerDto.setId(1L);
        systemComputerDto.setBusinessSystemId(100L);
        systemComputerDto.setComputerName("测试计算机");
        systemComputerDto.setComputerIp("***********");
        systemComputerDto.setCenterId(1L);

        systemComputerEntity = new SystemComputerEntity();
        systemComputerEntity.setId(1L);
        systemComputerEntity.setBusinessSystemId(100L);
        systemComputerEntity.setComputerName("测试计算机");
        systemComputerEntity.setComputerIp("***********");
        systemComputerEntity.setCenterId(1L);

        systemComputerQueryDto = new SystemComputerQueryDto();
        systemComputerQueryDto.setBusinessSystemId(100L);
        systemComputerQueryDto.setComputerName("测试计算机");
        systemComputerQueryDto.setComputerIp("***********");
        systemComputerQueryDto.setCenterId(1L);
        
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        
        systemComputerEntityList = new ArrayList<>();
        systemComputerEntityList.add(systemComputerEntity);
        
        systemComputerDtoList = new ArrayList<>();
        systemComputerDtoList.add(systemComputerDto);
        
        computerJoPageInfoR = new R<>();
        
        systemListQueryDto = new SystemListQueryDto();
        systemListQueryDto.setBusinessSystemName("测试业务系统");
        
        businessSystemIdList = new ArrayList<>();
        businessSystemIdList.add(100L);
        businessSystemIdList.add(200L);
        
        systemListBeans = new ArrayList<>();
        SystemListBean systemListBean = new SystemListBean();
        systemListBean.setBusinessSystemId(100L);
        systemListBean.setBusinessSystemName("测试业务系统");
        systemListBeans.add(systemListBean);
    }

    @Test
    @DisplayName("测试根据ID查询系统计算机")
    void testSelectSystemComputerById() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            doReturn(systemComputerEntity).when(systemComputerMapper).selectSystemComputerById(anyLong());
            mockedBeanUtils.when(() -> BeanUtils.copy(any(SystemComputerEntity.class), eq(SystemComputerDto.class))).thenReturn(systemComputerDto);
            
            // 执行测试方法
            SystemComputerDto result = systemComputerService.selectSystemComputerById(1L);
            
            // 验证结果
            assertNotNull(result);
            
            // 验证方法调用
            verify(systemComputerMapper).selectSystemComputerById(1L);
            mockedBeanUtils.verify(() -> BeanUtils.copy(systemComputerEntity, SystemComputerDto.class));
        }
    }

    @Test
    @DisplayName("测试查询系统计算机列表")
    void testSelectSystemComputerList() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class);
             MockedStatic<PageDataUtil> mockedPageDataUtil = mockStatic(PageDataUtil.class)) {
            
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(any(SystemComputerQueryDto.class), eq(SystemComputerEntity.class))).thenReturn(systemComputerEntity);
            doReturn(systemComputerEntityList).when(systemComputerMapper).selectSystemComputerList(any(SystemComputerEntity.class));
            
            // 创建PageInfo
            PageInfo<SystemComputerDto> pageInfo = new PageInfo<>();
            List<SystemComputerDto> dtoList = new ArrayList<>();
            dtoList.add(systemComputerDto);
            pageInfo.setList(dtoList);
            
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(SystemComputerDto.class))).thenReturn(pageInfo);
            
            // 执行测试方法
            PageInfo<SystemComputerDto> result = systemComputerService.selectSystemComputerList(systemComputerQueryDto, 1, 10);
            
            // 验证结果
            assertNotNull(result);
            
            // 验证方法调用
            verify(systemComputerMapper).selectSystemComputerList(systemComputerEntity);
            mockedBeanUtils.verify(() -> BeanUtils.copy(systemComputerQueryDto, SystemComputerEntity.class));
            mockedPageDataUtil.verify(() -> PageDataUtil.toDtoPage(systemComputerEntityList, SystemComputerDto.class));
        }
    }

    @Test
    @DisplayName("测试查询待绑定系统计算机列表")
    void testPendingSystemComputerList() throws ContrastBusinessException {
        // 设置Mock行为
        doReturn(computerJoPageInfoR).when(systemInteract).getBusinessSystemComputerList(any(SystemComputerListQueryIo.class));
        
        // 执行测试方法
        R<PageInfo<ComputerJo>> result = systemComputerService.pendingSystemComputerList(systemComputerQueryDto, 1, 10);
        
        // 验证结果
        assertEquals(computerJoPageInfoR, result);
        
        // 验证方法调用
        verify(systemInteract).getBusinessSystemComputerList(any(SystemComputerListQueryIo.class));
    }
    
    @Test
    @DisplayName("测试查询待绑定系统计算机列表 - 业务系统ID为空")
    void testPendingSystemComputerListWithNullBusinessSystemId() throws ContrastBusinessException {
        // 设置空的业务系统ID
        SystemComputerQueryDto queryDto = new SystemComputerQueryDto();
        
        // 执行测试方法
        R<PageInfo<ComputerJo>> result = systemComputerService.pendingSystemComputerList(queryDto, 1, 10);
        
        // 验证结果
        assertEquals("13000", result.getCode());
        // 验证方法调用
        verifyNoMoreInteractions(systemInteract);
    }

    @Test
    @DisplayName("测试新增系统计算机")
    void testInsertSystemComputer() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(any(SystemComputerDto.class), eq(SystemComputerEntity.class))).thenReturn(systemComputerEntity);
            doReturn(1).when(systemComputerMapper).insertSystemComputer(any(SystemComputerEntity.class));
            
            // 执行测试方法
            int result = systemComputerService.insertSystemComputer(systemComputerDto);
            
            // 验证结果
            assertEquals(1, result);
            
            // 验证方法调用
            verify(systemComputerMapper).insertSystemComputer(systemComputerEntity);
            mockedBeanUtils.verify(() -> BeanUtils.copy(systemComputerDto, SystemComputerEntity.class));
        }
    }

    @Test
    @DisplayName("测试修改系统计算机")
    void testUpdateSystemComputer() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(any(SystemComputerDto.class), eq(SystemComputerEntity.class))).thenReturn(systemComputerEntity);
            doReturn(1).when(systemComputerMapper).updateSystemComputer(any(SystemComputerEntity.class));
            
            // 执行测试方法
            int result = systemComputerService.updateSystemComputer(systemComputerDto);
            
            // 验证结果
            assertEquals(1, result);
            
            // 验证方法调用
            verify(systemComputerMapper).updateSystemComputer(systemComputerEntity);
            mockedBeanUtils.verify(() -> BeanUtils.copy(systemComputerDto, SystemComputerEntity.class));
        }
    }

    @Test
    @DisplayName("测试批量删除系统计算机")
    void testDeleteSystemComputerByIds() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = new Long[]{1L};
        List<SystemComputerEntity> systemComputerList = new ArrayList<>();
        SystemComputerEntity computer = new SystemComputerEntity();
        computer.setId(1L);
        computer.setBusinessSystemId(100L);
        computer.setComputerId(1L);
        systemComputerList.add(computer);
        
        List<SystemComputerNodeEntity> nodeList = new ArrayList<>();
        SystemComputerNodeEntity node = new SystemComputerNodeEntity();
        node.setId(1L);
        nodeList.add(node);
        
        List<NodeRelationEntity> relationList = new ArrayList<>();
        NodeRelationEntity relation = new NodeRelationEntity();
        relation.setId(1L);
        relationList.add(relation);
        
        // 设置Mock行为
        doReturn(systemComputerList).when(systemComputerMapper).selectSystemComputerByIds(ids);
        doReturn(nodeList).when(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIdAndComputerIds(anyLong(), anyList());
        doReturn(relationList).when(nodeRelationMapper).selectNodeRelationBySystemComputerNodeIds(anyList());
        doReturn(1).when(systemComputerNodeMapper).deleteSystemComputerNodeByIds(any(Long[].class));
        doReturn(1).when(nodeRelationMapper).deleteNodeRelationByIds(any(Long[].class));
        doReturn(1).when(nodeRuleContentMapper).deleteNodeRuleContentByNodeRelationIds(any(Long[].class));
        doReturn(1).when(systemComputerMapper).deleteSystemComputerByIds(ids);
        
        // 执行测试方法
        int result = systemComputerService.deleteSystemComputerByIds(ids);
        
        // 验证结果
        assertEquals(1, result);
        
        // 验证方法调用顺序
        verify(systemComputerMapper).selectSystemComputerByIds(ids);
        verify(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIdAndComputerIds(eq(100L), anyList());
        verify(nodeRelationMapper).selectNodeRelationBySystemComputerNodeIds(anyList());
        verify(systemComputerNodeMapper).deleteSystemComputerNodeByIds(any(Long[].class));
        verify(nodeRelationMapper).deleteNodeRelationByIds(any(Long[].class));
        verify(nodeRuleContentMapper).deleteNodeRuleContentByNodeRelationIds(any(Long[].class));
        verify(systemComputerMapper).deleteSystemComputerByIds(ids);
    }

    @Test
    @DisplayName("测试批量删除系统计算机 - 空ID数组")
    void testDeleteSystemComputerByIdsWithEmptyIds() {
        // 要删除的ID数组为空
        Long[] ids = new Long[]{};

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> systemComputerService.deleteSystemComputerByIds(ids));

        // 验证异常信息
        assertEquals("删除失败，主键集合为空", exception.getMessage());

        // 验证没有调用任何删除方法
        verifyNoMoreInteractions(systemComputerMapper);
        verifyNoMoreInteractions(systemComputerNodeMapper);
        verifyNoMoreInteractions(nodeRelationMapper);
        verifyNoMoreInteractions(nodeRuleContentMapper);
    }

    @Test
    @DisplayName("测试删除系统设备关系 - 无记录")
    void testDeleteSystemComputerByIdsWithNoRecords() {
        // Given
        Long[] ids = {1L, 2L};
        when(systemComputerMapper.selectSystemComputerByIds(ids)).thenReturn(Collections.emptyList());

        // When & Then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> systemComputerService.deleteSystemComputerByIds(ids));
        assertEquals("未找到对应的系统设备关系记录，删除失败", exception.getMessage());

        // Verify
        verify(systemComputerMapper).selectSystemComputerByIds(ids);
        verifyNoMoreInteractions(systemComputerMapper);
    }

    @Test
    @DisplayName("测试新增业务系统下设备")
    void testAddSystemComputer() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(any(SystemComputerDto.class), eq(SystemComputerEntity.class))).thenReturn(systemComputerEntity);
            
            // 执行测试方法
            int result = systemComputerService.addSystemComputer(systemComputerDtoList, userDto);
            
            // 验证结果
            assertEquals(1, result);
            
            // 验证方法调用
            verify(batchHandler).batchData(anyList(), any(), eq(500));
            mockedBeanUtils.verify(() -> BeanUtils.copy(systemComputerDto, SystemComputerEntity.class));
        }
    }
    
    @Test
    @DisplayName("测试新增业务系统下设备 - 空列表")
    void testAddSystemComputerWithEmptyList() {
        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            systemComputerService.addSystemComputer(Collections.emptyList(), userDto);
        });
        
        // 验证异常信息
        assertEquals("新增设备列表不能为空", exception.getMessage());
        
        // 验证方法调用
        verifyNoMoreInteractions(batchHandler);
    }

    @Test
    @DisplayName("测试查询系统列表")
    void testSelectSystemList() throws ContrastBusinessException {
        // 准备测试参数
        SystemListQueryDto queryDto = new SystemListQueryDto();
        queryDto.setBusinessSystemName("测试业务系统");
        
        // 模拟systemInteract.getBusinessSystemIdList方法
        doReturn(businessSystemIdList).when(systemInteract).getBusinessSystemIdList(anyLong());
        
        // 模拟systemComputerMapper.selectSystemList方法
        doReturn(systemListBeans).when(systemComputerMapper).selectSystemList(
            eq("测试业务系统"), 
            isNull(), 
            eq(businessSystemIdList)
        );
        
        // 执行测试方法
        PageInfo<SystemListDto> result = systemComputerService.selectSystemList(queryDto, 1, 10, userDto.getId());
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());
        assertEquals(1, result.getList().size());
        
        // 验证方法调用
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(systemComputerMapper).selectSystemList(
            eq("测试业务系统"), 
            isNull(), 
            eq(businessSystemIdList)
        );
    }
    
    @Test
    @DisplayName("测试查询已绑定设备的业务系统列表 - 无权限")
    void testSelectSystemListWithNoPermission() throws ContrastBusinessException {
        // 设置Mock行为
        doReturn(Collections.emptyList()).when(systemInteract).getBusinessSystemIdList(anyLong());
        
        // 执行测试方法
        PageInfo<SystemListDto> result = systemComputerService.selectSystemList(systemListQueryDto, 1, 10, 1L);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getList().size());
        
        // 验证方法调用
        verify(systemInteract).getBusinessSystemIdList(1L);
        verifyNoMoreInteractions(systemComputerMapper);
    }
    
    @Test
    @DisplayName("测试查询已绑定设备的业务系统列表 - 排除业务系统后无权限")
    void testSelectSystemListWithExcludeBusinessSystemIds() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            doReturn(businessSystemIdList).when(systemInteract).getBusinessSystemIdList(anyLong());
            
            // 设置要排除的业务系统ID
            systemListQueryDto.setExcludeBusinessSystemIds(new ArrayList<>(businessSystemIdList));

            // 执行测试方法
            PageInfo<SystemListDto> result = systemComputerService.selectSystemList(systemListQueryDto, 1, 10, userDto.getId());

            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.getList().size());

            // 验证方法调用
            verify(systemInteract).getBusinessSystemIdList(userDto.getId());
            verifyNoMoreInteractions(systemComputerMapper);
        }
    }

    @Test
    @DisplayName("测试新增系统设备关系 - 空列表")
    void testAddSystemComputerRelationWithEmptyList() {
        // Given
        List<SystemComputerDto> systemComputerDtoList = Collections.emptyList();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");

        // When & Then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> systemComputerService.addSystemComputer(systemComputerDtoList, userDto));
        assertEquals("新增设备列表不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试删除系统设备关系 - 记录不存在")
    void testDeleteSystemComputerRelationByIdsNotFound() {
        // Given
        Long[] ids = {1L, 2L};
        when(systemComputerMapper.selectSystemComputerByIds(ids)).thenReturn(Collections.emptyList());

        // When & Then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> systemComputerService.deleteSystemComputerByIds(ids));
        assertEquals("未找到对应的系统设备关系记录，删除失败", exception.getMessage());
    }

    @Test
    @DisplayName("测试新增业务系统下设备 - 设备重复")
    void testAddSystemComputer_DuplicateDevices() throws ContrastBusinessException {
        // 准备测试数据 - 模拟已存在的设备
        List<SystemComputerEntity> existingDevices = new ArrayList<>();
        SystemComputerEntity existingDevice = new SystemComputerEntity();
        existingDevice.setId(2L);
        existingDevice.setBusinessSystemId(100L);
        existingDevice.setComputerId(1L);
        existingDevices.add(existingDevice);

        // 设置Mock行为
        when(systemComputerMapper.selectByBusinessSystemIdAndComputerIds(anyLong(), anyList()))
                .thenReturn(existingDevices);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            systemComputerService.addSystemComputer(systemComputerDtoList, userDto);
        });

        // 验证异常信息
        assertEquals("部分设备已存在，禁止重复添加！", exception.getMessage());

        // 验证方法调用
        verify(systemComputerMapper).selectByBusinessSystemIdAndComputerIds(eq(100L), anyList());
        verifyNoMoreInteractions(batchHandler);
    }

    @Test
    @DisplayName("测试新增业务系统下设备 - null列表")
    void testAddSystemComputer_NullList() {
        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            systemComputerService.addSystemComputer(null, userDto);
        });

        // 验证异常信息
        assertEquals("新增设备列表不能为空", exception.getMessage());

        // 验证方法调用
        verifyNoMoreInteractions(batchHandler);
    }

    @Test
    @DisplayName("测试批量删除系统计算机 - 无节点关系")
    void testDeleteSystemComputerByIds_NoNodeRelations() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = new Long[]{1L};
        List<SystemComputerEntity> systemComputerList = new ArrayList<>();
        SystemComputerEntity computer = new SystemComputerEntity();
        computer.setId(1L);
        computer.setBusinessSystemId(100L);
        computer.setComputerId(1L);
        systemComputerList.add(computer);

        // 设置Mock行为 - 无节点关系
        doReturn(systemComputerList).when(systemComputerMapper).selectSystemComputerByIds(ids);
        doReturn(Collections.emptyList()).when(systemComputerNodeMapper)
                .selectSystemComputerNodeByBusinessSystemIdAndComputerIds(anyLong(), anyList());
        doReturn(1).when(systemComputerMapper).deleteSystemComputerByIds(ids);

        // 执行测试方法
        int result = systemComputerService.deleteSystemComputerByIds(ids);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(systemComputerMapper).selectSystemComputerByIds(ids);
        verify(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIdAndComputerIds(eq(100L), anyList());
        verify(systemComputerMapper).deleteSystemComputerByIds(ids);
        // 验证没有调用节点关系相关的删除方法
        verify(nodeRelationMapper, never()).selectNodeRelationBySystemComputerNodeIds(anyList());
        verify(nodeRuleContentMapper, never()).deleteNodeRuleContentByNodeRelationIds(any(Long[].class));
        verify(nodeRelationMapper, never()).deleteNodeRelationByIds(any(Long[].class));
        verify(systemComputerNodeMapper, never()).deleteSystemComputerNodeByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试批量删除系统计算机 - 有节点但无关系")
    void testDeleteSystemComputerByIds_HasNodesButNoRelations() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = new Long[]{1L};
        List<SystemComputerEntity> systemComputerList = new ArrayList<>();
        SystemComputerEntity computer = new SystemComputerEntity();
        computer.setId(1L);
        computer.setBusinessSystemId(100L);
        computer.setComputerId(1L);
        systemComputerList.add(computer);

        List<SystemComputerNodeEntity> nodeList = new ArrayList<>();
        SystemComputerNodeEntity node = new SystemComputerNodeEntity();
        node.setId(1L);
        nodeList.add(node);

        // 设置Mock行为 - 有节点但无关系
        doReturn(systemComputerList).when(systemComputerMapper).selectSystemComputerByIds(ids);
        doReturn(nodeList).when(systemComputerNodeMapper)
                .selectSystemComputerNodeByBusinessSystemIdAndComputerIds(anyLong(), anyList());
        doReturn(Collections.emptyList()).when(nodeRelationMapper).selectNodeRelationBySystemComputerNodeIds(anyList());
        doReturn(1).when(systemComputerNodeMapper).deleteSystemComputerNodeByIds(any(Long[].class));
        doReturn(1).when(systemComputerMapper).deleteSystemComputerByIds(ids);

        // 执行测试方法
        int result = systemComputerService.deleteSystemComputerByIds(ids);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(systemComputerMapper).selectSystemComputerByIds(ids);
        verify(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIdAndComputerIds(eq(100L), anyList());
        verify(nodeRelationMapper).selectNodeRelationBySystemComputerNodeIds(anyList());
        verify(systemComputerNodeMapper).deleteSystemComputerNodeByIds(any(Long[].class));
        verify(systemComputerMapper).deleteSystemComputerByIds(ids);
        // 验证没有调用规则内容和关系删除方法
        verify(nodeRuleContentMapper, never()).deleteNodeRuleContentByNodeRelationIds(any(Long[].class));
        verify(nodeRelationMapper, never()).deleteNodeRelationByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试查询待绑定系统计算机列表 - 无已绑定设备")
    void testPendingSystemComputerList_NoExistingDevices() throws ContrastBusinessException {
        // 设置Mock行为 - 无已绑定设备
        when(systemComputerMapper.selectByBusinessSystemId(anyLong())).thenReturn(Collections.emptyList());
        when(systemInteract.getBusinessSystemComputerList(any(SystemComputerListQueryIo.class)))
                .thenReturn(computerJoPageInfoR);

        // 执行测试方法
        R<PageInfo<ComputerJo>> result = systemComputerService.pendingSystemComputerList(systemComputerQueryDto, 1, 10);

        // 验证结果
        assertEquals(computerJoPageInfoR, result);

        // 验证方法调用
        verify(systemComputerMapper).selectByBusinessSystemId(100L);
        verify(systemInteract).getBusinessSystemComputerList(any(SystemComputerListQueryIo.class));
    }

    @Test
    @DisplayName("测试查询待绑定系统计算机列表 - 完整查询条件")
    void testPendingSystemComputerList_FullQueryConditions() throws ContrastBusinessException {
        // 设置完整的查询条件
        systemComputerQueryDto.setComputerName("测试设备");
        systemComputerQueryDto.setComputerIp("***********00");
        systemComputerQueryDto.setCenterId(5L);
        systemComputerQueryDto.setExcludeComputerIds(Arrays.asList(10L, 20L));
        systemComputerQueryDto.setAppointComputerIds(Arrays.asList(30L, 40L));

        // 设置Mock行为
        when(systemComputerMapper.selectByBusinessSystemId(anyLong())).thenReturn(systemComputerEntityList);
        when(systemInteract.getBusinessSystemComputerList(any(SystemComputerListQueryIo.class)))
                .thenReturn(computerJoPageInfoR);

        // 执行测试方法
        R<PageInfo<ComputerJo>> result = systemComputerService.pendingSystemComputerList(systemComputerQueryDto, 1, 10);

        // 验证结果
        assertEquals(computerJoPageInfoR, result);

        // 验证方法调用
        verify(systemComputerMapper).selectByBusinessSystemId(100L);
        verify(systemInteract).getBusinessSystemComputerList(any(SystemComputerListQueryIo.class));
    }



    @Test
    @DisplayName("测试查询系统列表 - null查询条件")
    void testSelectSystemList_NullQueryDto() throws ContrastBusinessException {
        // 设置Mock行为
        doReturn(businessSystemIdList).when(systemInteract).getBusinessSystemIdList(anyLong());
        doReturn(systemListBeans).when(systemComputerMapper).selectSystemList(
                isNull(), isNull(), eq(businessSystemIdList));

        // 执行测试方法
        PageInfo<SystemListDto> result = systemComputerService.selectSystemList(null, 1, 10, userDto.getId());

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getList());

        // 验证方法调用
        verify(systemInteract).getBusinessSystemIdList(userDto.getId());
        verify(systemComputerMapper).selectSystemList(isNull(), isNull(), eq(businessSystemIdList));
    }

    @Test
    @DisplayName("测试批量删除系统计算机 - null ID数组")
    void testDeleteSystemComputerByIds_NullIds() {
        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> systemComputerService.deleteSystemComputerByIds(null));

        // 验证异常信息
        assertEquals("删除失败，主键集合为空", exception.getMessage());

        // 验证没有调用任何删除方法
        verifyNoMoreInteractions(systemComputerMapper);
        verifyNoMoreInteractions(systemComputerNodeMapper);
        verifyNoMoreInteractions(nodeRelationMapper);
        verifyNoMoreInteractions(nodeRuleContentMapper);
    }

    @Test
    @DisplayName("测试查询待绑定系统计算机列表 - null查询条件")
    void testPendingSystemComputerList_NullQueryDto() {
        // 执行测试方法
        R<PageInfo<ComputerJo>> result = systemComputerService.pendingSystemComputerList(null, 1, 10);

        // 验证结果
        assertEquals("13000", result.getCode());
        assertEquals("传入业务系统ID为空！", result.getMessage());

        // 验证方法调用
        verifyNoMoreInteractions(systemInteract);
    }
} 