package com.ideal.envc.service.impl;

import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.interaction.model.BusinessSystemJo;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.ProjectMapper;
import com.ideal.envc.model.entity.ProjectEntity;
import com.ideal.envc.service.IProjectService;
import com.ideal.envc.model.dto.ProjectDto;
import com.ideal.envc.model.dto.ProjectQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.mapper.PlanRelationMapper;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import com.ideal.envc.model.entity.NodeRelationEntity;
import com.ideal.envc.exception.ContrastBusinessException;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 比对业务系统Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class ProjectServiceImpl implements IProjectService {
    private final Logger logger = LoggerFactory.getLogger(ProjectServiceImpl.class);

    private final ProjectMapper projectMapper;
    private final SystemInteract systemInteract;
    private final BatchHandler batchHandler;
    private final SystemComputerMapper systemComputerMapper;
    private final NodeRelationMapper nodeRelationMapper;
    private final NodeRuleContentMapper nodeRuleContentMapper;
    private final SystemComputerNodeMapper systemComputerNodeMapper;
    private final PlanRelationMapper planRelationMapper;

    public ProjectServiceImpl(ProjectMapper projectMapper, SystemInteract systemInteract, BatchHandler batchHandler, SystemComputerMapper systemComputerMapper, NodeRelationMapper nodeRelationMapper, NodeRuleContentMapper nodeRuleContentMapper, SystemComputerNodeMapper systemComputerNodeMapper, PlanRelationMapper planRelationMapper) {
        this.projectMapper = projectMapper;
        this.systemInteract = systemInteract;
        this.batchHandler = batchHandler;
        this.systemComputerMapper = systemComputerMapper;
        this.nodeRelationMapper = nodeRelationMapper;
        this.nodeRuleContentMapper = nodeRuleContentMapper;
        this.systemComputerNodeMapper = systemComputerNodeMapper;
        this.planRelationMapper = planRelationMapper;
    }

    /**
     * 查询比对业务系统
     *
     * @param id 比对业务系统主键
     * @return 比对业务系统
     */
    @Override
    public ProjectDto selectProjectById(Long id) throws ContrastBusinessException {
        try {
            ProjectEntity project = projectMapper.selectProjectById(id);
            if (project == null) {
                throw new ContrastBusinessException("未找到对应的比对业务系统");
            }
            return BeanUtils.copy(project, ProjectDto.class);
        } catch (Exception e) {
            logger.error("查询比对业务系统失败", e);
            throw new ContrastBusinessException("查询比对业务系统失败：" + e.getMessage());
        }
    }

    /**
     * 查询比对业务系统列表
     *
     * @param projectQueryDto 比对业务系统
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 比对业务系统
     */
    @Override
    public PageInfo<ProjectDto> selectProjectList(ProjectQueryDto projectQueryDto, UserDto userDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            ProjectEntity query = BeanUtils.copy(projectQueryDto, ProjectEntity.class);
            Long userId = userDto.getId();

            //根据用户ID查询平台管理具备权限的业务系统ID列表
            List<Long> businessSystemIdList = systemInteract.getBusinessSystemIdList(userId);
            if(businessSystemIdList == null || businessSystemIdList.isEmpty()){
                return new PageInfo<ProjectDto>();
            }
            PageMethod.startPage(pageNum, pageSize);
            List<ProjectEntity> projectList = projectMapper.selectProjectList(query, businessSystemIdList);
            return PageDataUtil.toDtoPage(projectList, ProjectDto.class);
        } catch (Exception e) {
            logger.error("查询比对业务系统列表失败", e);
            throw new ContrastBusinessException("查询比对业务系统列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询待绑定比对业务系统列表
     *
     * @param projectQueryDto 比对业务系统
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 比对业务系统
     */
    @Override
    public PageInfo<ProjectDto> getPendingSystemList(ProjectQueryDto projectQueryDto, UserDto userDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            //根据用户ID查询平台管理具备权限的业务系统ID列表
            List<Long> businessSystemIdList = systemInteract.getBusinessSystemIdList(userDto.getId());
            if(businessSystemIdList == null || businessSystemIdList.isEmpty()){
                return new PageInfo<>();
            }
            List<Long> excludeIds = projectMapper.selectAllProjectIds(businessSystemIdList);
            projectQueryDto.setExcludeIds(excludeIds);
            R<PageInfo<ProjectDto>> result = systemInteract.getBusinessSystemListOfPage(projectQueryDto, userDto, pageNum, pageSize);
            if (result == null || !ResponseCodeEnum.SUCCESS.getCode().equals(result.getCode())) {
                throw new ContrastBusinessException(result != null ? result.getMessage() : "查询失败");
            }
            return result.getData();
        } catch (Exception e) {
            logger.error("查询待绑定比对业务系统列表失败", e);
            throw new ContrastBusinessException("查询待绑定比对业务系统列表失败：" + e.getMessage());
        }
    }

    /**
     * 新增比对业务系统
     *
     * @param projectDto 比对业务系统
     * @return 结果
     */
    @Override
    public int insertProject(ProjectDto projectDto) throws ContrastBusinessException {
        try {
            ProjectEntity project = BeanUtils.copy(projectDto, ProjectEntity.class);
            return projectMapper.insertProject(project);
        } catch (Exception e) {
            logger.error("新增比对业务系统失败", e);
            throw new ContrastBusinessException("新增比对业务系统失败：" + e.getMessage());
        }
    }

    /**
     * 修改比对业务系统
     *
     * @param projectDto 比对业务系统
     * @return 结果
     */
    @Override
    public int updateProject(ProjectDto projectDto) throws ContrastBusinessException {
        try {
            ProjectEntity project = BeanUtils.copy(projectDto, ProjectEntity.class);
            return projectMapper.updateProject(project);
        } catch (Exception e) {
            logger.error("修改比对业务系统失败", e);
            throw new ContrastBusinessException("修改比对业务系统失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除比对业务系统
     *
     * @param ids 需要删除的比对业务系统主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProjectByIds(Long[] ids) throws ContrastBusinessException {
        if (ids == null || ids.length == 0) {
            logger.error("本次删除比对业务系统主键集合为空");
            throw new ContrastBusinessException("删除失败，主键集合为空");
        }

        try {
            // 1. 查询所有业务系统ID
            List<ProjectEntity> projectEntities = projectMapper.selectProjectByIds(ids);
            List<Long> businessSystemIdList = projectEntities.stream()
                    .map(ProjectEntity::getBusinessSystemId)
                    .distinct()
                    .collect(Collectors.toList());

            if (businessSystemIdList.isEmpty()) {
                logger.warn("未找到对应的业务系统ID，直接返回");
                throw new ContrastBusinessException("未找到对应的业务系统ID，删除失败");
            }

            // 2. 删除ieai_envc_plan_relation
            planRelationMapper.deletePlanRelationByBusinessSystemIds(businessSystemIdList);

            // 3. 删除ieai_envc_system_computer，并记录iid
            List<SystemComputerEntity> systemComputerEntities = systemComputerMapper.selectSystemComputerByBusinessSystemIds(businessSystemIdList);
            List<Long> systemComputerIds = systemComputerEntities.stream()
                    .map(SystemComputerEntity::getId)
                    .collect(Collectors.toList());
            if (!systemComputerIds.isEmpty()) {
                systemComputerMapper.deleteSystemComputerByIds(systemComputerIds.toArray(new Long[0]));
            }

            // 4. 删除ieai_envc_system_computer_node，并记录iid
            List<SystemComputerNodeEntity> systemComputerNodeEntities = systemComputerNodeMapper.selectSystemComputerNodeByBusinessSystemIds(businessSystemIdList);
            List<Long> systemComputerNodeIds = systemComputerNodeEntities.stream()
                    .map(SystemComputerNodeEntity::getId)
                    .collect(Collectors.toList());
            if (!systemComputerNodeIds.isEmpty()) {
                systemComputerNodeMapper.deleteSystemComputerNodeByIds(systemComputerNodeIds.toArray(new Long[0]));
            }

            // 5. 删除ieai_envc_node_relation
            if(!systemComputerNodeIds.isEmpty()){
                List<NodeRelationEntity> nodeRelationEntities = nodeRelationMapper.selectNodeRelationBySystemComputerNodeIds(systemComputerNodeIds);
                List<Long> nodeRelationIds = nodeRelationEntities.stream()
                        .map(NodeRelationEntity::getId)
                        .collect(Collectors.toList());
                if (!nodeRelationIds.isEmpty()) {
                    nodeRelationMapper.deleteNodeRelationByIds(nodeRelationIds.toArray(new Long[0]));
                }

                // 6. 删除ieai_envc_node_rule_content
                if (!nodeRelationIds.isEmpty()) {
                    nodeRuleContentMapper.deleteNodeRuleContentByNodeRelationIds(nodeRelationIds.toArray(new Long[0]));
                }
            }


            // 7. 删除ieai_envc_project
            return projectMapper.deleteProjectByIds(ids);
        } catch (Exception e) {
            logger.error("批量删除比对业务系统失败", e);
            throw new ContrastBusinessException("批量删除比对业务系统失败：" + e.getMessage());
        }
    }

    /**
     * 根据平台管理业务系统ID集合新增比对业务系统
     *
     * @param businessSystemIds 比对业务系统ID集合
     * @param  userDto 用户对象
     * @return 结果
     */
    @Override
    public int addProject(List<Long> businessSystemIds, UserDto userDto) throws ContrastBusinessException {
        try {
            //验证传入业务系统是否已经在我方服务存在
            List<ProjectEntity> projectEntities = projectMapper.selectProjectByBusinessSystemIds(businessSystemIds);
            if(projectEntities != null && !projectEntities.isEmpty()){
                throw new ContrastBusinessException("部分业务系统已存在，禁止重复添加！");
            }
            List<BusinessSystemJo> businessSystemJoList = systemInteract.getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);
            if(businessSystemJoList == null || businessSystemJoList.isEmpty()){
                throw new ContrastBusinessException("未获取到平台管理的业务系统信息！");
            }
            if(businessSystemJoList.size() != businessSystemIds.size()){
                logger.error("本次获取平台管理配置下特定业务系统ID集合为{},业务系统总数为{}", businessSystemIds, businessSystemJoList.size());
                throw new ContrastBusinessException("部分业务系统ID无效或未配置！");
            }
            List<ProjectDto> projectDtoList = buildProjectDtoList(businessSystemJoList, userDto);
            List<ProjectEntity> projectEntityList = BeanUtils.copy(projectDtoList, ProjectEntity.class);
            if(projectEntityList == null || projectEntityList.isEmpty()){
                if (projectEntityList != null) {
                    logger.error("本次获取平台管理配置下特定业务系统ID集合为{},业务系统转化总数为{}", businessSystemIds, projectEntityList.size());
                }
                logger.error("本次获取平台管理配置下特定业务系统ID集合为{},业务系统转化总数为0", businessSystemIds);
                throw new ContrastBusinessException("业务系统数据转化异常！");
            }
            //批量插入，每500条一次提交
            batchHandler.batchData(projectEntityList, projectMapper::insertProject, 500);
            return projectEntityList.size();
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("新增比对业务系统失败", e);
            throw new ContrastBusinessException("新增比对业务系统失败：" + e.getMessage());
        }
    }

    private List<ProjectDto> buildProjectDtoList(List<BusinessSystemJo> businessSystemJoList, UserDto userDto) {
        List<ProjectDto> projectDtoList = new ArrayList<>();
        for(BusinessSystemJo businessSystemJo : businessSystemJoList){
            ProjectDto projectDto = new ProjectDto();
            projectDto.setBusinessSystemName(businessSystemJo.getBusinessSystemName());
            projectDto.setBusinessSystemCode(businessSystemJo.getBusinessSystemCode());
            projectDto.setBusinessSystemId(businessSystemJo.getBusinessSystemId());
            projectDto.setStatus((businessSystemJo.getDeleted()!=null&& businessSystemJo.getDeleted()==1)?0:1);
            projectDto.setCreatorId(userDto.getId());
            projectDto.setCreatorName(userDto.getFullName());
            projectDto.setUpdatorId(userDto.getId());
            projectDto.setUpdatorName(userDto.getFullName());
            projectDto.setBusinessSystemDesc(businessSystemJo.getBusinessSystemDesc());
            projectDtoList.add(projectDto);
        }
        return projectDtoList;
    }
}
