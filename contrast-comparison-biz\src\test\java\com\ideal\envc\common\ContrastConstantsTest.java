package com.ideal.envc.common;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * ContrastConstants的单元测试类
 * <AUTHOR>
 */
public class ContrastConstantsTest {

    @Test
    public void testConstantValues() {
        // 测试所有常量值是否正确
        assertEquals("CompareTemplate", ContrastConstants.CONTRAST_PRJ_NAME);
        assertEquals("ActExec_Compare", ContrastConstants.CONTRAST_FILE_COMPARE_FLOW_NAME);
        assertEquals("ActExec_Compare_Dir", ContrastConstants.CONTRAST_DIR_COMPARE_FLOW_NAME);
        assertEquals(13, ContrastConstants.CONTRAST_PRJ_TYPE);
        assertEquals("butterflyversion", ContrastConstants.CONTRAST_FLOW_ENV_BUTTERFLY_VERSION);
        assertEquals("compareResult", ContrastConstants.COMPARE_RESULT);
        assertEquals("ret", ContrastConstants.RET);
        assertEquals("syncResult", ContrastConstants.SYNC_RESULT);
        assertEquals("stdout", ContrastConstants.STDOUT);
        assertEquals("stderr", ContrastConstants.STDERR);
        assertEquals("err", ContrastConstants.ERR);
        
        // 测试计数器相关常量
        assertEquals("RunInstanceState_", ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX);
        assertEquals("RunInstanceInfoState_", ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX);
        
        // 测试锁前缀常量
        assertEquals("RUN_INSTANCE_COUNTER_LOCK_", ContrastConstants.RUN_INSTANCE_COUNTER_LOCK_PREFIX);
        assertEquals("RUN_INSTANCE_INFO_COUNTER_LOCK_", ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX);
        
        // 确保所有常量都不为null
        assertNotNull(ContrastConstants.CONTRAST_PRJ_NAME);
        assertNotNull(ContrastConstants.CONTRAST_FILE_COMPARE_FLOW_NAME);
        assertNotNull(ContrastConstants.CONTRAST_DIR_COMPARE_FLOW_NAME);
        assertNotNull(ContrastConstants.CONTRAST_FLOW_ENV_BUTTERFLY_VERSION);
        assertNotNull(ContrastConstants.COMPARE_RESULT);
        assertNotNull(ContrastConstants.RET);
        assertNotNull(ContrastConstants.SYNC_RESULT);
        assertNotNull(ContrastConstants.STDOUT);
        assertNotNull(ContrastConstants.STDERR);
        assertNotNull(ContrastConstants.ERR);
        assertNotNull(ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX);
        assertNotNull(ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX);
        assertNotNull(ContrastConstants.RUN_INSTANCE_COUNTER_LOCK_PREFIX);
        assertNotNull(ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX);
    }
} 