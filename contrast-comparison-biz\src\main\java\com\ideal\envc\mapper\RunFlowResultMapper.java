package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 流程输出结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface RunFlowResultMapper {
    /**
     * 查询流程输出结果
     *
     * @param id 流程输出结果主键
     * @return 流程输出结果
     */
    RunFlowResultEntity selectRunFlowResultById(Long id);

    /**
     * 查询流程输出结果
     *
     * @param envcRunFlowId 流程表主键
     * @return 流程输出结果
     */
    RunFlowResultEntity selectRunFlowResultByEnvcRunFlowId(Long envcRunFlowId);

    /**
     * 根据流程ID查询流程输出结果
     *
     * @param flowId 流程ID
     * @return 流程输出结果
     */
    RunFlowResultEntity selectRunFlowResultByFlowId(@Param("flowId") Long flowId);

    /**
     * 查询流程输出结果列表
     *
     * @param runFlowResult 流程输出结果
     * @return 流程输出结果集合
     */
    List<RunFlowResultEntity> selectRunFlowResultList(RunFlowResultEntity runFlowResult);

    /**
     * 新增流程输出结果
     *
     * @param runFlowResult 流程输出结果
     * @return 结果
     */
    int insertRunFlowResult(RunFlowResultEntity runFlowResult);

    /**
     * 修改流程输出结果
     *
     * @param runFlowResult 流程输出结果
     * @return 结果
     */
    int updateRunFlowResult(RunFlowResultEntity runFlowResult);

    /**
     * 删除流程输出结果
     *
     * @param id 流程输出结果主键
     * @return 结果
     */
    int deleteRunFlowResultById(Long id);

    /**
     * 批量删除流程输出结果
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRunFlowResultByIds(Long[] ids);
}
