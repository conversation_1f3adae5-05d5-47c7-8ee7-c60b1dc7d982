package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 实例详情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Mapper
public interface RunInstanceInfoMapper {
    /**
     * 查询实例详情
     *
     * @param id 实例详情主键
     * @return 实例详情
     */
    RunInstanceInfoEntity selectRunInstanceInfoById(Long id);

    /**
     * 查询实例详情列表
     *
     * @param runInstanceInfo 实例详情
     * @return 实例详情集合
     */
    List<RunInstanceInfoEntity> selectRunInstanceInfoList(RunInstanceInfoEntity runInstanceInfo);

    /**
     * 新增实例详情
     *
     * @param runInstanceInfo 实例详情
     * @return 结果
     */
    int insertRunInstanceInfo(RunInstanceInfoEntity runInstanceInfo);

    /**
     * 修改实例详情
     *
     * @param runInstanceInfo 实例详情
     * @return 结果
     */
    int updateRunInstanceInfo(RunInstanceInfoEntity runInstanceInfo);

    /**
     * 删除实例详情
     *
     * @param id 实例详情主键
     * @return 结果
     */
    int deleteRunInstanceInfoById(Long id);

    /**
     * 批量删除实例详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRunInstanceInfoByIds(Long[] ids);

    /**
     * 根据ID和时间戳更新运行实例信息的状态和结果
     * 使用乐观锁机制防止并发更新冲突
     *
     * @param id 运行实例信息ID
     * @param state 状态（0：运行中，1：已完成，2：终止）
     * @param result 结果（-1：运行中，0：一致/成功，1：不一致/失败）
     * @return 更新的行数，0表示并发冲突或记录不存在
     */
    int updateStateAndResultByIdAndTimestamp(@Param("id") Long id, 
                                           @Param("state") int state, 
                                           @Param("result") int result);

    /**
     * 根据运行实例ID查询关联的运行实例信息列表
     *
     * @param instanceId 运行实例ID
     * @return 运行实例信息列表
     */
    List<RunInstanceInfoEntity> selectInstanceInfosByInstanceId(Long instanceId);
}
