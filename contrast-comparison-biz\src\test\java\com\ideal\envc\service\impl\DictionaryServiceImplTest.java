package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.github.pagehelper.page.PageMethod;
import com.ideal.envc.mapper.DictionaryMapper;
import com.ideal.envc.model.dto.DictionaryDto;
import com.ideal.envc.model.dto.DictionaryQueryDto;
import com.ideal.envc.model.entity.DictionaryEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 字典码Service业务层的单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DictionaryServiceImpl单元测试")
class DictionaryServiceImplTest {

    @Mock
    private DictionaryMapper dictionaryMapper;

    @InjectMocks
    private DictionaryServiceImpl dictionaryService;

    private DictionaryDto dictionaryDto;
    private DictionaryEntity dictionaryEntity;
    private DictionaryQueryDto dictionaryQueryDto;
    private List<DictionaryEntity> dictionaryEntityList;
    private PageInfo<DictionaryDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        dictionaryDto = new DictionaryDto();
        dictionaryDto.setId(1L);
        dictionaryDto.setCode("test_code");
        dictionaryDto.setDescription("测试字典");
        
        dictionaryEntity = new DictionaryEntity();
        dictionaryEntity.setId(1L);
        dictionaryEntity.setCode("test_code");
        dictionaryEntity.setDescription("测试字典");
        
        dictionaryQueryDto = new DictionaryQueryDto();
        dictionaryQueryDto.setCode("test_code");
        
        dictionaryEntityList = new ArrayList<>();
        dictionaryEntityList.add(dictionaryEntity);
    }

    @Test
    @DisplayName("根据ID查询字典码 - 成功场景")
    void selectDictionaryById_Success() {
        // 准备测试数据
        Long id = 1L;
        dictionaryEntity = createDictionaryEntity(id, "test_code", "测试字典");

        // Mock依赖
        when(dictionaryMapper.selectDictionaryById(id)).thenReturn(dictionaryEntity);

        // 执行测试
        DictionaryDto result = dictionaryService.selectDictionaryById(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("test_code", result.getCode());
        assertEquals("测试字典", result.getDescription());
        verify(dictionaryMapper).selectDictionaryById(id);
    }

    @Test
    @DisplayName("查询字典码列表 - 成功场景")
    void selectDictionaryList_Success() {
        // 准备测试数据
        dictionaryQueryDto = new DictionaryQueryDto();
        dictionaryEntityList = Arrays.asList(
            createDictionaryEntity(1L, "code1", "字典1"),
            createDictionaryEntity(2L, "code2", "字典2")
        );

        // 创建Page对象包装实体列表
        Page<DictionaryEntity> page = new Page<>(1, 10);
        page.addAll(dictionaryEntityList);
        page.setTotal(dictionaryEntityList.size());

        // 准备DTO列表
        List<DictionaryDto> dictionaryDtoList = Arrays.asList(
            createDictionaryDto(1L, "code1", "字典1"),
            createDictionaryDto(2L, "code2", "字典2")
        );

        // Mock依赖
        doReturn(page).when(dictionaryMapper).selectDictionaryList(any());

        // 执行测试
        PageInfo<DictionaryDto> result = dictionaryService.selectDictionaryList(dictionaryQueryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        verify(dictionaryMapper).selectDictionaryList(any());
    }

    @Test
    @DisplayName("新增字典码 - 成功场景")
    void insertDictionary_Success() {
        // 准备测试数据
        dictionaryDto = createDictionaryDto(null, "new_code", "新字典");

        // Mock依赖
        doReturn(1).when(dictionaryMapper).insertDictionary(any());

        // 执行测试
        int result = dictionaryService.insertDictionary(dictionaryDto);

        // 验证结果
        assertEquals(1, result);
        verify(dictionaryMapper).insertDictionary(any());
    }

    @Test
    @DisplayName("修改字典码 - 成功场景")
    void updateDictionary_Success() {
        // 准备测试数据
        dictionaryDto = createDictionaryDto(1L, "update_code", "更新字典");

        // Mock依赖
        doReturn(1).when(dictionaryMapper).updateDictionary(any());

        // 执行测试
        int result = dictionaryService.updateDictionary(dictionaryDto);

        // 验证结果
        assertEquals(1, result);
        verify(dictionaryMapper).updateDictionary(any());
    }

    @Test
    @DisplayName("批量删除字典码 - 成功场景")
    void deleteDictionaryByIds_Success() {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // Mock依赖
        doReturn(2).when(dictionaryMapper).deleteDictionaryByIds(ids);

        // 执行测试
        int result = dictionaryService.deleteDictionaryByIds(ids);

        // 验证结果
        assertEquals(2, result);
        verify(dictionaryMapper).deleteDictionaryByIds(ids);
    }

    @Test
    @DisplayName("验证字典码是否存在 - 存在场景")
    void validateDictionaryCode_Exists() {
        // 准备测试数据
        String code = "test_code";
        dictionaryEntityList = Collections.singletonList(
            createDictionaryEntity(1L, code, "测试字典")
        );

        // Mock依赖
        doReturn(dictionaryEntityList).when(dictionaryMapper).selectDictionaryList(any());

        // 执行测试
        Boolean result = dictionaryService.validateDictionaryCode(code);

        // 验证结果
        assertTrue(result);
        verify(dictionaryMapper).selectDictionaryList(any());
    }

    @Test
    @DisplayName("验证字典码是否存在 - 不存在场景")
    void validateDictionaryCode_NotExists() {
        // 准备测试数据
        String code = "non_exist_code";

        // Mock依赖
        doReturn(Collections.emptyList()).when(dictionaryMapper).selectDictionaryList(any());

        // 执行测试
        Boolean result = dictionaryService.validateDictionaryCode(code);

        // 验证结果
        assertFalse(result);
        verify(dictionaryMapper).selectDictionaryList(any());
    }

    @Test
    @DisplayName("验证字典码是否存在 - 空字典码")
    void validateDictionaryCode_EmptyCode() {
        // 执行测试
        Boolean result = dictionaryService.validateDictionaryCode("");

        // 验证结果
        assertFalse(result);
        verify(dictionaryMapper, never()).selectDictionaryList(any());
    }

    @Test
    @DisplayName("验证字典码是否存在 - 空值")
    void validateDictionaryCode_NullCode() {
        // 执行测试
        Boolean result = dictionaryService.validateDictionaryCode(null);

        // 验证结果
        assertFalse(result);
        verify(dictionaryMapper, never()).selectDictionaryList(any());
    }

    @Test
    @DisplayName("分页查询字典码列表 - 成功场景")
    void selectDictionaryPage_Success() {
        // 准备测试数据
        dictionaryQueryDto = new DictionaryQueryDto();
        dictionaryEntityList = Arrays.asList(
            createDictionaryEntity(1L, "code1", "字典1"),
            createDictionaryEntity(2L, "code2", "字典2")
        );
        
        // 创建Page对象包装实体列表
        Page<DictionaryEntity> page = new Page<>(1, 10);
        page.addAll(dictionaryEntityList);
        page.setTotal(dictionaryEntityList.size());
        
        // 准备DTO列表
        List<DictionaryDto> dictionaryDtoList = Arrays.asList(
            createDictionaryDto(1L, "code1", "字典1"),
            createDictionaryDto(2L, "code2", "字典2")
        );
        
        // Mock依赖
        doReturn(page).when(dictionaryMapper).selectDictionaryList(any());

        // 执行测试
        PageInfo<DictionaryDto> result = dictionaryService.selectDictionaryList(dictionaryQueryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        verify(dictionaryMapper).selectDictionaryList(any());
    }

    private DictionaryEntity createDictionaryEntity(Long id, String code, String description) {
        DictionaryEntity entity = new DictionaryEntity();
        entity.setId(id);
        entity.setCode(code);
        entity.setDescription(description);
        return entity;
    }

    private DictionaryDto createDictionaryDto(Long id, String code, String description) {
        DictionaryDto dto = new DictionaryDto();
        dto.setId(id);
        dto.setCode(code);
        dto.setDescription(description);
        return dto;
    }
} 