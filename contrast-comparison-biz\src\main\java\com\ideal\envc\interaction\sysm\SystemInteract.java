package com.ideal.envc.interaction.sysm;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.envc.interaction.model.AgentInfoJo;
import com.ideal.envc.interaction.model.BusinessSystemJo;
import com.ideal.envc.interaction.model.CenterDto;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.interaction.model.ComputerJo;
import com.ideal.envc.interaction.model.SystemComputerListQueryInnerIo;
import com.ideal.envc.interaction.model.SystemComputerListQueryIo;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.common.util.BeanUtils;
import com.ideal.envc.common.PageInfoConvertUtils;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.ComputerInfoDto;
import com.ideal.envc.model.dto.ProjectDto;
import com.ideal.envc.model.dto.ProjectQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.system.api.IBusinessSystem;
import com.ideal.system.api.IBusinessSystemCompuerList;
import com.ideal.system.api.ICenter;
import com.ideal.system.dto.AgentInfoApiDto;
import com.ideal.system.dto.BusinessSystemApiDto;
import com.ideal.system.dto.BusinessSystemCompuerListApiDto;
import com.ideal.system.dto.BusinessSystemQueryDto;
import com.ideal.system.dto.CenterApiDto;
import com.ideal.system.dto.SystemComputerListQueryBean;
import com.ideal.system.dto.SystemComputerListQueryDto;
import com.ideal.system.dto.TagApiDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 与平台管理对接处理类
 * <AUTHOR>
 */
@Component
public class SystemInteract {

    private static final Logger log = LoggerFactory.getLogger(SystemInteract.class);
    private static final String AGENT_BUSINESS_TYPE = "一致性比对";
    private static  final String ACQUIRE_COMPUTER = "本次获取平台管理业务系统ID为{},设备总数为{}";
    private static  final String ACQUIRE_COMPUTER_ERROR = "本次获取平台管理业务系统ID为{},获取返回数据异常";

    /**
     * 平台管理接口
     */
    private final IBusinessSystem businessSystem;
    private final IBusinessSystemCompuerList businessSystemComputerList;
    private final ICenter center;


    public SystemInteract(IBusinessSystem businessSystem, IBusinessSystemCompuerList businessSystemComputerList, ICenter center) {
        this.businessSystem = businessSystem;
        this.businessSystemComputerList = businessSystemComputerList;
        this.center = center;
    }

    /**
     * 获取权限用户下的业务系统列表
     * @param userId 用户id
     * @return 用户具有权限的业务系统列表
     */
    public List<BusinessSystemJo> getBusinessSystemList(Long userId){
        List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.getBusinessSystemInfoByUserIdForApi(userId);
        if(businessSystemApiDtoList == null){
            log.info("本次获取平台管理配置下用户ID为{},业务系统总数为{}",userId,0);
            return Collections.emptyList();
        }
        log.info("本次获取平台管理配置下用户ID为{},业务系统总数为{}",userId,businessSystemApiDtoList.size());
        return getBusinessSystemApiDtoList(businessSystemApiDtoList);
    }

    /**
     * 分页查询业务系统列表
     *
     * @param projectQueryDto 查询条件
     * @param userDto 用户信息
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 业务系统列表
     */
    public R<PageInfo<ProjectDto>> getBusinessSystemListOfPage(ProjectQueryDto projectQueryDto, UserDto userDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            Long userId = userDto.getId();
            BusinessSystemQueryDto businessSystemQuery = new BusinessSystemQueryDto();
            businessSystemQuery.setPageNum(pageNum);
            businessSystemQuery.setPageSize(pageSize);
            BusinessSystemApiDto businessSystemApiDto = new BusinessSystemApiDto();
            businessSystemApiDto.setUserId(userId);
            businessSystemApiDto.setDeleted(0);
            businessSystemApiDto.setExcludeIds(projectQueryDto.getExcludeIds());
            if(StringUtils.isNotEmpty(projectQueryDto.getBusinessSystemName())){
                businessSystemApiDto.setName(projectQueryDto.getBusinessSystemName());
            }
            if(StringUtils.isNotEmpty(projectQueryDto.getBusinessSystemCode())){
                businessSystemApiDto.setCode(projectQueryDto.getBusinessSystemCode());
            }
            if(projectQueryDto.getBusinessSystemId()!=null){
                businessSystemApiDto.setId(projectQueryDto.getBusinessSystemId());
            }
            if(projectQueryDto.getBusinessSystemDesc()!=null){
                businessSystemApiDto.setDescription(projectQueryDto.getBusinessSystemDesc());
            }
            businessSystemQuery.setQueryParam(businessSystemApiDto);
            PageInfo<BusinessSystemApiDto> page = businessSystem.selectBusinessSystemList(businessSystemQuery);
            if(page == null || page.getList() == null){
                log.error("getBusinessSystemListOfPage 本次获取平台管理配置下用户ID为{},获取获取业务系统失败", userId);
                return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), ResponseCodeEnum.QUERY_FAIL.getDesc());
            }
            log.info("getBusinessSystemListOfPage 本次获取平台管理配置下用户ID为{},业务系统总数为{}", userId, page.getList().size());
            List<BusinessSystemApiDto> businessSystemApiDtoList = page.getList();
            List<ProjectDto> projectDtoList = getProjectDtoList(businessSystemApiDtoList);
            PageInfo<ProjectDto> resultPage = PageInfoConvertUtils.convertPageInfo(page, null, projectDtoList);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), resultPage, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            log.error("调用平台管理分页查询业务系统列表异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 将平台管理返回的dto对象转换为Jo对象
     * @param businessSystemApiDtoList 平台管理返回的
     * @return 转换后的业务系统列表
     */
    private List<BusinessSystemJo> getBusinessSystemApiDtoList(List<BusinessSystemApiDto> businessSystemApiDtoList) {
        List<BusinessSystemJo> jos = new ArrayList<>();
        for(BusinessSystemApiDto dto : businessSystemApiDtoList){
            BusinessSystemJo jo = new BusinessSystemJo();
            jo.setBusinessSystemId(dto.getId());
            jo.setBusinessSystemName(dto.getName());
            jo.setBusinessSystemCode(dto.getCode());
            jo.setId(dto.getId());
            jo.setName(dto.getName());
            jo.setDeleted(dto.getDeleted());
            jo.setBusinessSystemDesc(dto.getDescription());
            jos.add(jo);
        }
        return jos;
    }

    /**
     * 将平台管理返回的dto对象转换为Jo对象
     * @param businessSystemApiDtoList 平台管理返回的
     * @return 转换后的业务系统列表
     */
    private List<ProjectDto> getProjectDtoList(List<BusinessSystemApiDto> businessSystemApiDtoList) {
        List<ProjectDto> projectDtoList = new ArrayList<>();
        for(BusinessSystemApiDto dto : businessSystemApiDtoList){
            ProjectDto projectDto = new ProjectDto();
            projectDto.setBusinessSystemId(dto.getId());
            projectDto.setBusinessSystemName(dto.getName());
            projectDto.setBusinessSystemCode(dto.getCode());
            projectDto.setStatus(dto.getDeleted()==0?1:0);
            projectDto.setBusinessSystemDesc(dto.getDescription());
            projectDtoList.add(projectDto);
        }
        return projectDtoList;
    }

    /**
     * 获取权限用户下的业务系统ID集合
     * @param userId 用户id
     * @return 用户具有权限的业务系统ID集合
     */
    public List<Long> getBusinessSystemIdList(Long userId){
        List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.getBusinessSystemInfoByUserIdForApi(userId);
        if(businessSystemApiDtoList == null){
            log.info("getBusinessSystemIdList 本次获取平台管理配置下用户ID为{},业务系统总数为{}",userId,0);
            return Collections.emptyList();
        }
        log.info("getBusinessSystemIdList 本次获取平台管理配置下用户ID为{},业务系统总数为{}",userId,businessSystemApiDtoList.size());
        if(businessSystemApiDtoList.isEmpty()){
            return Collections.emptyList();
        }
        List<Long> businessSystemIdList = new ArrayList<>();
        for(BusinessSystemApiDto dto : businessSystemApiDtoList){
            businessSystemIdList.add(dto.getId());
        }
        return businessSystemIdList;
    }


    /**
     * 获取特定业务系统ID集合下的业务系统信息
     * @param businessSystemIds 业务系统ID集合
     * @return List<BusinessSystemJo>
     */
    public List<BusinessSystemJo> getBusinessSystemInfoByBusSystemIdForApi(List<Long> businessSystemIds){

        if(CollectionUtils.isEmpty(businessSystemIds)){
            return Collections.emptyList();
        }
        List<BusinessSystemApiDto> businessSystemApiDtoList =businessSystem.getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);
        String buss = businessSystemIds.toString();
        if(businessSystemApiDtoList == null){
            log.info("本次获取平台管理配置下特定业务系统ID集合为{},业务系统总数为{}",buss,0);
            return Collections.emptyList();
        }
        log.info("本次获取平台管理配置下特定业务系统ID集合为{},业务系统总数为{}",buss,businessSystemApiDtoList.size());
        return getBusinessSystemApiDtoList(businessSystemApiDtoList);

    }


    /*
     * *******************设备信息对接*******************************
     */
    /**
     * 封装查询条件，及巡检服务固定参数
     * @param systemComputerListQueryIo 设备查询条件对象
     * @return 平台管理设备查询条件对象
     */

    private SystemComputerListQueryDto buildSystemComputerListQueryDto(SystemComputerListQueryIo systemComputerListQueryIo)  {
        SystemComputerListQueryDto systemComputerListQueryDto = new SystemComputerListQueryDto();
        systemComputerListQueryDto.setPageSize(systemComputerListQueryIo.getPageSize());
        systemComputerListQueryDto.setPageNum(systemComputerListQueryIo.getPageNum());
        systemComputerListQueryDto.setBusinessSystemId(systemComputerListQueryIo.getBusinessSystemId());
        SystemComputerListQueryInnerIo systemComputerListQueryInnerIo = systemComputerListQueryIo.getQueryParam();
        SystemComputerListQueryBean systemComputerListQueryBean = new SystemComputerListQueryBean();
        if(systemComputerListQueryInnerIo != null){
            //业务系统ID集合
            systemComputerListQueryBean.setBusinessSystemIds(systemComputerListQueryInnerIo.getBusinessSystemIds());
            //物理中心ID集合
            systemComputerListQueryBean.setCentralIds(systemComputerListQueryInnerIo.getCentralIds());
            //业务系统ID
            systemComputerListQueryBean.setBusinessSystemId(systemComputerListQueryInnerIo.getBusinessSystemId());
            //指定设备ID集合
            systemComputerListQueryBean.setAppointComputerIds(systemComputerListQueryInnerIo.getAppointComputerIds());
            //排除设备ID集合
            systemComputerListQueryBean.setExcludeComputerIds(systemComputerListQueryInnerIo.getExcludeComputerIds());
            //指定设备IP集合
            systemComputerListQueryBean.setAppointComputerIps(systemComputerListQueryInnerIo.getAppointComputerIps());
            //设备IP
            systemComputerListQueryBean.setComputerIp(systemComputerListQueryInnerIo.getComputerIp());

            //设备名称
            systemComputerListQueryBean.setHostName(systemComputerListQueryInnerIo.getHostName());
            //排除设备IP集合
            systemComputerListQueryBean.setExcludeComputerIps(systemComputerListQueryInnerIo.getExcludeComputerIps());
            //当前登录用户ID
//            systemComputerListQueryBean.setUserId(systemComputerListQueryInnerIo.getUserId());
            //是否查询业务系统
            systemComputerListQueryBean.setResultBusinessSystem(systemComputerListQueryInnerIo.getResultBusinessSystem());

            //设备类别 0：服务器设备，1：非服务器设备
            systemComputerListQueryBean.setComputerType(0);
            //设备型号ID
            systemComputerListQueryBean.setComputerModelId(systemComputerListQueryInnerIo.getComputerModelId());

            //主机名称
            systemComputerListQueryBean.setHostName(systemComputerListQueryInnerIo.getHostName());

            //agent业务类型
            if(StringUtils.isNotBlank(systemComputerListQueryInnerIo.getAgentBusinessType())){
                systemComputerListQueryBean.setAgentBusinessType(systemComputerListQueryInnerIo.getAgentBusinessType());
            }else{
                systemComputerListQueryBean.setAgentBusinessType(AGENT_BUSINESS_TYPE);
            }

            //数据中心id
            systemComputerListQueryBean.setCentralId(systemComputerListQueryInnerIo.getCentralId());
            //是否排除Agent
            systemComputerListQueryBean.setAgentParamIsExclude(systemComputerListQueryInnerIo.getAgentParamIsExclude());

            //已选择设备id集合
            systemComputerListQueryBean.setComputerIds(systemComputerListQueryInnerIo.getComputerIds());

            //回显设备id集合
            systemComputerListQueryBean.setEchoComputerIds(systemComputerListQueryInnerIo.getEchoComputerIds());

            //业务系统编码
            systemComputerListQueryBean.setBusinessSystemCode(systemComputerListQueryInnerIo.getBusinessSystemCode());

            //区域id
            systemComputerListQueryBean.setAreaId(systemComputerListQueryInnerIo.getAreaId());

            //区域名称
            systemComputerListQueryBean.setAreaName(systemComputerListQueryInnerIo.getAreaName());

            //设备标签
            organizeTags(systemComputerListQueryInnerIo, systemComputerListQueryBean);

            if(systemComputerListQueryInnerIo.getResultBusinessSystem()==null){
                systemComputerListQueryBean.setResultBusinessSystem(true);
            }else{
                systemComputerListQueryBean.setResultBusinessSystem(systemComputerListQueryInnerIo.getResultBusinessSystem());
            }
            systemComputerListQueryBean.setResultComputerUser(true);
        }else{
            systemComputerListQueryBean.setResultBusinessSystem(false);
            systemComputerListQueryBean.setResultComputerUser(false);
            //设备类别 0：服务器设备，1：非服务器设备
            systemComputerListQueryBean.setComputerType(0);
            systemComputerListQueryBean.setAgentBusinessType(AGENT_BUSINESS_TYPE);
        }
        systemComputerListQueryDto.setQueryParam(systemComputerListQueryBean);
        return systemComputerListQueryDto;
    }

    /**
     * 组装设备标签数据
     * @param systemComputerListQueryInnerIo systemComputerListQueryInnerIo
     * @param systemComputerListQueryBean systemComputerListQueryBean
     */
    private void organizeTags(SystemComputerListQueryInnerIo systemComputerListQueryInnerIo, SystemComputerListQueryBean systemComputerListQueryBean) {
        if(StringUtils.isNotBlank(systemComputerListQueryInnerIo.getTags())){
            List<TagApiDto> tagApiDtoList = new ArrayList<>();
            TagApiDto tagApiDto = new TagApiDto();
            String[] tagArray = systemComputerListQueryInnerIo.getTags().split(",");
            for(String tag : tagArray){
                tagApiDto.setName(tag);
                tagApiDtoList.add(tagApiDto);
            }

            systemComputerListQueryBean.setTagList(tagApiDtoList);
        }
    }
    public com.ideal.common.dto.R<PageInfo<ComputerJo>> getBusinessSystemComputerList(SystemComputerListQueryIo systemComputerListQueryIo){
        PageInfo<ComputerJo>   pageInfo = getBusinessSystemCompuerListOfPageInfo( systemComputerListQueryIo);
        if(pageInfo == null || pageInfo.getList() == null){
           return R.fail("130100","查询失败");
        }
        return R.ok("10000",pageInfo,"查询成功");
    }





    public PageInfo<ComputerJo> getBusinessSystemCompuerListOfPageInfo(SystemComputerListQueryIo systemComputerListQueryIo)  {
        PageInfo<ComputerJo> pageInfoNew = new PageInfo<>();
        //查询映射表组成，设备类型ID--》设备类别的map
        SystemComputerListQueryDto systemComputerListQueryDto = null;
        try {
            systemComputerListQueryDto = buildSystemComputerListQueryDto(systemComputerListQueryIo);
        } catch (Exception e) {
            log.error("组织查巡平台管理数据组装查询参数出错！{}", e.getMessage());
            return pageInfoNew;
        }

        //控制一下日志长度 超过两百截断
        String logParam = truncateExcessivelyLongLogs(JSON.toJSONString(systemComputerListQueryDto));
        log.info("getBusinessSystemComputerListOfPageInfo()--systemComputerListQueryDto:{}", logParam);
        PageInfo<BusinessSystemCompuerListApiDto> pageInfo = businessSystemComputerList.queryBusinessSystemComputerList(systemComputerListQueryDto);
        String logResult = truncateExcessivelyLongLogs(JSON.toJSONString(pageInfo));
        log.info("平台管理queryBusinessSystemComputerList接口返回:{}", logResult);
        if(pageInfo==null){
            log.error(ACQUIRE_COMPUTER_ERROR,systemComputerListQueryDto.getQueryParam().getBusinessSystemId());
            return pageInfoNew;
        }
        List<ComputerJo> computerJos = new ArrayList<>();
        for(BusinessSystemCompuerListApiDto dto : pageInfo.getList()){
            ComputerJo jo = buildComputerJo(dto,systemComputerListQueryIo.getType());
            computerJos.add(jo);
        }
        return PageInfoConvertUtils.convertPageInfo(pageInfo,pageInfoNew,computerJos);
    }

    /**
     * 截断超长日志
     * @param log 日志内容
     * @return 处理后日志
     */
    private String truncateExcessivelyLongLogs(String log){
        int length = 2000;
        return log.length() > length ? log.substring(0,length) : log;
    }

    private ComputerJo buildComputerJo(BusinessSystemCompuerListApiDto businessSystemCompuerListApiDto,Integer requestType){
        ComputerJo computerJo =   BeanUtils.copy(businessSystemCompuerListApiDto,ComputerJo.class);
        //对于后续变化属性key不一致或者需要单独处理的属性
        // systemDtoList  computerSignJoList tags  type
        /**
        List<BusinessSystemApiDto> businessSystemApiDtoList = businessSystemCompuerListApiDto.getBusinessSystemApiDtoList();


         if(businessSystemApiDtoList != null){
            List<SystemDto> systemDtoList = new ArrayList<>();
            for (BusinessSystemApiDto businessSystemApiDto : businessSystemApiDtoList) {
                SystemDto systemDto = new SystemDto();
                systemDto.setBusinessSystemId(businessSystemApiDto.getId());
                systemDto.setBusinessSystemCode(businessSystemApiDto.getCode());
                Integer systemType = businessSystemApiDto.getSystemType();
                systemDto.setBusinessSystemSystype(systemType!=null? systemType.longValue():0L);
                systemDto.setBusinessSystemName(businessSystemApiDto.getName());
                systemDto.setBusinessSystemTypename(businessSystemApiDto.getTypeName());
                systemDto.setDeleted(businessSystemApiDto.getDeleted());
                systemDtoList.add(systemDto);
            }
            computerJo.setSystemDtoList(systemDtoList);
        }*/

        //标签的封装
        List<TagApiDto> tagApiDtoList =  businessSystemCompuerListApiDto.getTagList();
        if(tagApiDtoList != null){
            List<String> tags = new ArrayList<>();
            for (TagApiDto tagApiDto : tagApiDtoList) {
                tags.add(tagApiDto.getName());
            }
            computerJo.setTags(StringUtils.join(tags,","));
        }

        //封装设备类别
        computerJo.setType(requestType);

        //封装agent
        try{
            List<AgentInfoApiDto> agentInfoApiDtoList = businessSystemCompuerListApiDto.getAllAgentInfoList();
            List<AgentInfoJo> agentInfoJoList = BeanUtils.copy(agentInfoApiDtoList, AgentInfoJo.class);
            computerJo.setAllAgentInfoList(agentInfoJoList);
        }catch (Exception e){
            log.info("封装平台管理设备信息中agent集合对象失败",e);
        }




        return computerJo;

    }

    /**
     * 获取设备ID与设备信息的映射关系
     *
     * @param systemComputerListQueryIo 设备查询条件
     * @return 设备ID与设备信息的映射关系，key为设备ID，value为设备信息
     */
    public Map<Long, ComputerInfoDto> getComputerInfoMap(SystemComputerListQueryIo systemComputerListQueryIo) {
        Map<Long, ComputerInfoDto> computerInfoMap = new HashMap<>();

        // 组装查询参数
        SystemComputerListQueryDto systemComputerListQueryDto = null;
        try {
            systemComputerListQueryDto = buildSystemComputerListQueryDto(systemComputerListQueryIo);
        } catch (Exception e) {
            log.error("组织查询平台管理数据组装查询参数出错！{}", e.getMessage());
            return computerInfoMap;
        }

        // 控制日志长度
        String logParam = truncateExcessivelyLongLogs(JSON.toJSONString(systemComputerListQueryDto));
        log.info("getComputerInfoMap()--systemComputerListQueryDto:{}", logParam);

        // 调用接口获取设备列表
        List<BusinessSystemCompuerListApiDto> businessSystemCompuerListApiDtoList = businessSystemComputerList.queryComputerList(systemComputerListQueryDto);

        // 日志记录
        String logResult = truncateExcessivelyLongLogs(JSON.toJSONString(businessSystemCompuerListApiDtoList));
        log.info("平台管理queryComputerList接口返回:{}", logResult);

        if (businessSystemCompuerListApiDtoList == null || businessSystemCompuerListApiDtoList.isEmpty()) {
            log.error(ACQUIRE_COMPUTER_ERROR, systemComputerListQueryDto.getQueryParam().getBusinessSystemId());
            return computerInfoMap;
        }

        log.info(ACQUIRE_COMPUTER, systemComputerListQueryDto.getQueryParam().getBusinessSystemId(), businessSystemCompuerListApiDtoList.size());

        // 转换为Map
        for (BusinessSystemCompuerListApiDto dto : businessSystemCompuerListApiDtoList) {
            if (dto.getComputerId() != null) {
                ComputerInfoDto computerInfoDto = new ComputerInfoDto();

                // 设置端口，从字符串转为整数
                try {
                    if (StringUtils.isNotBlank(dto.getAgentPort())) {
                        computerInfoDto.setAgentPort(Integer.parseInt(dto.getAgentPort()));
                    }
                } catch (NumberFormatException e) {
                    log.warn("设备ID为{}的端口转换失败，原始值为{}", dto.getComputerId(), dto.getAgentPort());
                }

                // 设置操作系统名称
                computerInfoDto.setOsName(dto.getOsName());

                computerInfoMap.put(dto.getComputerId(), computerInfoDto);
            }
        }

        log.info("本次获取设备ID与设备信息映射关系总数为{}", computerInfoMap.size());
        return computerInfoMap;
    }



    /******************************物理中心对接*****************************************************************/
    /**
     * 获取中心列表
     *
     * @return 中心列表
     */
    public List<CenterDto> getCenterList(CenterQueryDto centerQueryDto) {

        CenterApiDto centerApiDto = new CenterApiDto();
        if(centerQueryDto != null){
            if(StringUtils.isNotBlank(centerQueryDto.getName())){
                centerApiDto.setName(centerQueryDto.getName());
            }
            if(centerQueryDto.getId() != null){
                centerApiDto.setId(centerQueryDto.getId());
            }
            if(centerQueryDto.getExcludeIds() != null && !centerQueryDto.getExcludeIds().isEmpty()){
                centerApiDto.setExcludeIds(centerQueryDto.getExcludeIds());
            }
            if(centerQueryDto.getAppointIds() != null && !centerQueryDto.getAppointIds().isEmpty()){
                centerApiDto.setAppointIds(centerQueryDto.getAppointIds());
            }
        }
        List<CenterApiDto> centerApiDtoList = center.getCenterListForApi(centerApiDto);
        if (centerApiDtoList == null) {
            log.info("本次获取平台管理中心列表为空");
            return Collections.emptyList();
        }
        //lambda表达式对centerApiDtoList进行遍历，去除对象中deleted=0的对象
        centerApiDtoList.removeIf(centerApiDto1 -> (centerApiDto1.getDeleted()!=null &&  centerApiDto1.getDeleted()== 0));
        log.info("本次获取平台管理中心列表总数为{}", centerApiDtoList.size());
        return BeanUtils.copy(centerApiDtoList, CenterDto.class);
    }

    /**
     * 获取中心ID与名称的映射关系
     *
     * @param centerQueryDto 中心查询条件
     * @return 中心ID与名称的映射关系，key为中心ID，value为中心名称
     */
    public Map<Long, String> getCenterMap(CenterQueryDto centerQueryDto) {
        List<CenterDto> centerList = getCenterList(centerQueryDto);
        Map<Long, String> centerMap = new HashMap<>();
        if (centerList != null && !centerList.isEmpty()) {
            for (CenterDto centerDto : centerList) {
                centerMap.put(centerDto.getId(), centerDto.getName());
            }
            log.info("本次获取平台管理中心ID与名称映射关系总数为{}", centerMap.size());
        } else {
            log.info("本次获取平台管理中心ID与名称映射关系为空");
        }
        return centerMap;
    }

    /**
     * 将BusinessSystemJo转换为ProjectDto
     *
     * @param businessSystemJo 业务系统对象
     * @return ProjectDto对象
     */
    private ProjectDto convertToProjectDto(BusinessSystemJo businessSystemJo) {
        ProjectDto projectDto = new ProjectDto();
        projectDto.setBusinessSystemId(businessSystemJo.getBusinessSystemId());
        projectDto.setBusinessSystemName(businessSystemJo.getBusinessSystemName());
        projectDto.setBusinessSystemCode(businessSystemJo.getBusinessSystemCode());
//        projectDto.setBusinessSystemDesc(businessSystemJo.get);
        projectDto.setStatus(businessSystemJo.getDeleted() != null && businessSystemJo.getDeleted() == 1 ? 0 : 1);
        return projectDto;
    }

}
