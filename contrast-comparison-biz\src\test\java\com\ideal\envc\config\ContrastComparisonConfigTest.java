package com.ideal.envc.config;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * ContrastComparisonConfig的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ContrastComparisonConfig单元测试")
class ContrastComparisonConfigTest {

    @InjectMocks
    private ContrastComparisonConfig contrastComparisonConfig;

    @Test
    @DisplayName("测试配置类实例化")
    void testConfigInstance() {
        assertNotNull(contrastComparisonConfig);
    }
} 