package com.ideal.envc.common;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public class PageInfoConvertUtils<T> {

    public static <T> PageInfo<T> convertPageInfo(PageInfo<?> pageSource, PageInfo<T> target, List<T> data) {
        if(target == null){
            target = new PageInfo<>();
        }
        target.setPageNum(pageSource.getPageNum());
        target.setPageSize(pageSource.getPageSize());
        target.setTotal(pageSource.getTotal());
        target.setPages(pageSource.getPages());
        target.setSize(pageSource.getSize());
        target.setStartRow(pageSource.getStartRow());
        target.setEndRow(pageSource.getEndRow());
        target.setPrePage(pageSource.getPrePage());
        target.setNextPage(pageSource.getNextPage());
        target.setIsFirstPage(pageSource.isIsFirstPage());
        target.setIsLastPage(pageSource.isIsLastPage());
        target.setHasPreviousPage(pageSource.isHasPreviousPage());
        target.setHasNextPage(pageSource.isHasNextPage());
        target.setNavigateFirstPage(pageSource.getNavigateFirstPage());
        target.setNavigateLastPage(pageSource.getNavigateLastPage());
        target.setNavigatePages(pageSource.getNavigatePages());
        target.setNavigatepageNums(pageSource.getNavigatepageNums());
        target.setList(data);
        return target;
    }

}
