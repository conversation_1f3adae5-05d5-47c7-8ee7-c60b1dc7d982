<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.RunFlowResultMapper">

    <resultMap type="com.ideal.envc.model.entity.RunFlowResultEntity" id="RunFlowResultResult">
            <result property="id" column="iid"/>
            <result property="envcRunFlowId" column="ienvc_run_flow_id"/>
            <result property="flowid" column="iflowid"/>
            <result property="content" column="icontent"/>
            <result property="stderr" column="istderr"/>
            <result property="storeTime" column="istore_time"/>
            <result property="updateOrderTime" column="iupdate_order_time"/>
    </resultMap>

    <sql id="selectRunFlowResult">
        select iid, ienvc_run_flow_id, iflowid, icontent, istderr, istore_time, iupdate_order_time
        from ieai_envc_run_flow_result
    </sql>

    <select id="selectRunFlowResultList" parameterType="com.ideal.envc.model.entity.RunFlowResultEntity" resultMap="RunFlowResultResult">
        <include refid="selectRunFlowResult"/>
        <where>
                        <if test="envcRunFlowId != null ">
                            and ienvc_run_flow_id = #{envcRunFlowId}
                        </if>
                        <if test="flowid != null ">
                            and iflowid = #{flowid}
                        </if>
                        <if test="content != null  and content != ''">
                            and icontent = #{content}
                        </if>
                        <if test="stderr != null  and stderr != ''">
                            and istderr = #{stderr}
                        </if>
                        <if test="storeTime != null ">
                            and istore_time = #{storeTime}
                        </if>
                        <if test="updateOrderTime != null">
                            and iupdate_order_time = #{updateOrderTime}
                        </if>
        </where>
    </select>

    <select id="selectRunFlowResultById" parameterType="Long"
            resultMap="RunFlowResultResult">
            <include refid="selectRunFlowResult"/>
            where iid = #{id}
    </select>

    <select id="selectRunFlowResultByEnvcRunFlowId" parameterType="Long"
            resultMap="RunFlowResultResult">
            <include refid="selectRunFlowResult"/>
            where ienvc_run_flow_id = #{envcRunFlowId}
    </select>

    <select id="selectRunFlowResultByFlowId" parameterType="Long"
            resultMap="RunFlowResultResult">
            <include refid="selectRunFlowResult"/>
            where iflowid = #{flowId}
    </select>

    <insert id="insertRunFlowResult" parameterType="com.ideal.envc.model.entity.RunFlowResultEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_run_flow_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="envcRunFlowId != null">ienvc_run_flow_id,
                    </if>
                    <if test="flowid != null">iflowid,
                    </if>
                    <if test="content != null">icontent,
                    </if>
                    <if test="stderr != null">istderr,
                    </if>
                    <if test="updateOrderTime != null">iupdate_order_time,
                    </if>
                    istore_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="envcRunFlowId != null">#{envcRunFlowId},
                    </if>
                    <if test="flowid != null">#{flowid},
                    </if>
                    <if test="content != null">#{content},
                    </if>
                    <if test="stderr != null">#{stderr},
                    </if>
                    <if test="updateOrderTime != null">#{updateOrderTime},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateRunFlowResult" parameterType="com.ideal.envc.model.entity.RunFlowResultEntity">
        update ieai_envc_run_flow_result
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcRunFlowId != null">ienvc_run_flow_id =
                        #{envcRunFlowId},
                    </if>
                    <if test="flowid != null">iflowid =
                        #{flowid},
                    </if>
                    <if test="content != null">icontent =
                        #{content},
                    </if>
                    <if test="stderr != null">istderr =
                        #{stderr},
                    </if>
                    <if test="updateOrderTime != null">iupdate_order_time =
                        #{updateOrderTime},
                    </if>
                    istore_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id} and iupdate_order_time &lt;= #{updateOrderTime}
    </update>

    <delete id="deleteRunFlowResultById" parameterType="Long">
        delete
        from ieai_envc_run_flow_result where iid = #{id}
    </delete>

    <delete id="deleteRunFlowResultByIds" parameterType="String">
        delete from ieai_envc_run_flow_result where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>