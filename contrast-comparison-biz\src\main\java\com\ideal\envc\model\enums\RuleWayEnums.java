package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则方式枚举
 * <AUTHOR>
 */
public enum RuleWayEnums {

    /**
     * 全部
     */
    ALL(0, "全部"),

    /**
     * 部分
     */
    PARTIAL(1, "部分");

    /**
     * 规则方式代码
     */
    private final Integer code;

    /**
     * 规则方式名称
     */
    private final String name;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<Integer, RuleWayEnums> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(RuleWayEnums::getCode, Function.identity()));

    /**
     * 构造函数
     * @param code 规则方式代码
     * @param name 规则方式名称
     */
    RuleWayEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取规则方式代码
     * @return 规则方式代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取规则方式名称
     * @return 规则方式名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据规则方式代码获取枚举实例
     * @param code 规则方式代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static RuleWayEnums getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据规则方式代码获取规则方式名称
     * @param code 规则方式代码
     * @return 规则方式名称，如果不存在则返回"未知规则方式"
     */
    public static String getNameByCode(Integer code) {
        return Optional.ofNullable(getByCode(code))
                .map(RuleWayEnums::getName)
                .orElse("未知规则方式");
    }

    /**
     * 判断给定的规则方式代码是否在枚举允许的范围内
     * @param code 规则方式代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return code != null && CODE_MAP.containsKey(code);
    }
}
