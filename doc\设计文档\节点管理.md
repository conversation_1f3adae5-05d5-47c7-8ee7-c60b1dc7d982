# 节点关系

- [ ] ## 中心下拉获取

1. 首先需要在sysm包的SystemInteract类中增加注入ICenter 类型接口服务。调用此注入接口的getCenterListForApi方法获取中心集合。

2. 中心对象结构如下：

   ```
   /**
    * 主键，中心ID
    */
   private Long id;
   /**
    * 中心名称
    */
   private String name;
   /**
    * 区域
    */
   private String location;
   /**
    * 状态，0：未生效，1：生效
    */
   private Integer state;
   /**
    * 描述
    */
   private String description;
   /**
    * 所在地
    */
   private String ident;
   
   /**
    * 是否主中心，0：否，1：是
    */
   private Integer main;
   
   /**
    * 删除标识，0否 1是
    */
   private Integer deleted;
   ```

   ​	

3. 需要增加中心Dto类在com.ideal.envc.interaction.model包下，用于转存接口服务提供的中心数据，并返回转换后的List<Dto>

4. 创建在controller包下创建中心相关对外接口请求方法及对应名称的service等类。





- [ ] ## 节点系统列表功能

- 功能描述：基于“系统配置”功能模块中已添加的业务系统及系统下配置的设备为前提和数据来源。支持通过业务系统名称和系统描述做为查询条件分页查询系统列表
- 数据来源：来源于系统配功能中添加绑定的业务系统并且系统下绑定了设备的业务系统，系统配置中业务系统如果未绑定设备，则不在查询范围。
- 权限：需要当前登录用户所具备的业务系统权限作为权限条件，确定该用户有哪些系统权限需要通过调用平台管理接口获取具有权限的所有业务系统ID
  权限的确定调用如下代码：

  ```
    //根据用户ID查询平台管理具备权限的业务系统ID列表
          List<Long> businessSystemIdList = systemInteract.getBusinessSystemIdList(userId);
  ```

  数据结构：读取一致性比对微服务表结构.md这个数据结构文档。 主要使用ieai_envc_project表、ieai_envc_system_computer表。以上两个表的关系字段为ibusiness_system_id字段。
- 查询逻辑：基于数据结构中提供的两个表权限对接返回的业务系统id查询出系统列表数据。
- 返回数据：业务系统id，业务系统名称、业务系统编码、业务系统描述、业务系统绑定人（ieai_envc_project表中icreator_name字段）
- 规范要求：需要满足开发规范

  - 入口方法需要在SystemComputerController.java类中增加方法、service及其实现也是对应ISystemComputerService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml
  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。
  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。
  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

## 设备节点

- [ ] ### 系统绑定源目标设备功能


- 功能描述：基于节点系统ID作为依据。以“系统配置”中绑定的设备为来源。添加节点设备需要首先明确源中心和目标中心。源设备的所属中心必须为源中心，目标设备的所属中心必须为目标中心。继而组成节点设备记录进行存储。


- 包含子功能：中心下拉列表（包括：源中心下拉列表，目标中心下拉列表）、设备下拉列表
- 必要验证：节点设备存储记录时，需要验证同一源中心同源设备同目标中心同目标设备是否已经存在，存在则不允许保存。
- 权限：无要求。
- 数据结构：存储节点设备表为ieai_envc_system_computer_node
- 查询数据所需表结构：设备下拉列表查询数据源数据来源于表结构：ieai_envc_system_computer，基于业务系统ID及对应是源还是目标的中心ID为查询依据。

- 规范要求：需要满足开发规范。

  - 入口方法需要在SystemComputerController.java类中增加方法、service及其实现也是对应ISystemComputerService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

- [ ] ### 系统解绑源目标设备功能


- 功能描述：对于系统绑定源目标设备功能中已绑定节点设备记录进行删除功能，支持批量删除。

- 权限：无要求。
- 数据结构：节点设备表为ieai_envc_system_computer_node

- 规范要求：需要满足开发规范。

  - 入口方法需要在SystemComputerController.java类中增加方法、service及其实现也是对应ISystemComputerService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

- [ ] ### 系统已绑定源目标设备列表查询功能


- 功能描述：条件分页查询已绑定原目标设备记录列表。

- 权限：无要求。
- 数据结构：节点设备表为ieai_envc_system_computer_node
- 必有前提条件：业务系统ID
- 请求方式POST
- 入参：建议Dto类型数据
- 查询条件：源中心:下拉列表，目标中心:下拉列表、源IP:模糊查询，目标IP:模糊查询
- 返回数据：源IP、源中心名称、源中心ID、目标IP、目标中心名称、目标中心ID

- 规范要求：需要满足开发规范。

  - 入口方法需要在SystemComputerController.java类中增加方法、service及其实现也是对应ISystemComputerService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印



## 节点信息配置

- 功能描述：对节点进行规则配置

- 权限：无要求

- 数据结构：ieai_envc_node_relation、ieai_envc_node_rule_content含义及关系参考《一致性比对微服务表结构.md》

- 请求方式：POST

- 需要配置项目包括：模式（model）、类型（type）、路径（path）、方式（way）、是否子集（childLevel）、规则类型（ruleType）、规则内容（ruleContent）、字符集（encode）

- 存储内容：在需要配置项目之外还需存储创建人（creatorName）、创建人ID（creatorId)、更新人（updatorName）、创建人ID（updatorId)、节点关系主键（ieai_envc_system_computer_node表主键）

- 规范要求：需要满足开发规范。

  - 入口方法需要在SystemComputerController.java类中增加方法、service及其实现也是对应ISystemComputerService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

    

## 节点信息配置删除

- 功能描述：对节点上规则配置进行删除
- 权限：无要求
- 数据结构：ieai_envc_node_relation、ieai_envc_node_rule_content含义及关系参考《一致性比对微服务表结构.md》
- 请求方式：POST
- 传入参数为节点配置信息主键（ieai_envc_node_relation的ID）
- 规范要求：需要满足开发规范。

  - 需要uterService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

  - 

## 节点信息配置禁用和启用

- 功能描述：对节点上规则配置进行禁用和启用
- 权限：无要求
- 数据结构：ieai_envc_node_relation含义及关系参考《一致性比对微服务表结构.md》。主要是修改ieai_envc_node_relation表中ienabled字段
- 请求方式：POST
- 支持范围：支持批量操作
- 入参：操作类型取值（0：启用，1：禁用）、操作节点信息配置主键集合（ieai_envc_node_relation的ID集合）
- 业务：根据入参操作类型，判断更改为启用还是禁用状态。操作类型取值对应ieai_envc_node_relation表中ienabled字段的值
- 传入参数为节点配置信息主键（ieai_envc_node_relation的ID）
- - 需要uterService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

  - 

## 节点信息配置列表查询

- 功能描述：根据节点关系ID查询其配置记录，条件分页查询

- 权限：无要求

- 必要参数：节点关系主键（ieai_envc_system_computer_node表主键）

- 查询条件：模式（model）：下拉列表。类型（type）:下拉列表。状态（enabled）：下拉列表

- 数据结构：ieai_envc_node_relation含义及关系参考《一致性比对微服务表结构.md》。主要是修改ieai_envc_node_relation表中ienabled字段

- 请求方式：POST

- 参数类型：尽量考虑入参为Dto

- 展示内容：模式、类型、路径。方式、规则、状态

- 传入参数为节点配置信息主键（ieai_envc_node_relation的ID）

- - 需要uterService和SystemComputerServiceImpl中，对应数据库操作在SystemComputerMapper和SystemComputerMapper.xml

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

    

## 方案管理

### 方案下绑定系统列表

- 功能说明：展示方案下所绑定的业务系统

- 必要查询条件：方案ID（planId）

- 普通查询条件：业务系统名称（BusinessSystemName）模糊查询

- 数据结构：ieai_envc_plan_relation、ieai_envc_project 关系为ibusiness_system_id字段

- 查询返回:方案ID（来源于ieai_envc_plan_relation的ienvc_plan_id）、业务系统ID（ieai_envc_project 的ibusiness_system_id）、业务系统名称（来源于ieai_envc_project 的ibusiness_system_name）、业务系统描述（来源于ieai_envc_project 的ibusiness_system_desc）、关系绑定时间（来源于ieai_envc_plan_relation的icreate_time）、绑定关系人（来源于ieai_envc_plan_relation的icreator_name）

- 分页查询

  

### 查询方案系统下源目标设备信息

- 功能说明：基于业务系统ID查询在规则配置源目标设备记录信息

- 分页展示

- 必要条件：业务系统ID

- 代码逻辑：新增入口方法和Service及实现方法

- 实现方法逻辑：实现类注入ISystemComputerNodeService接口，调用该接口中的selectSystemComputerNodeBeanList方法

  



### 比对结果列表查询

- 功能描述：通过查询ieai_envc_run_instance、ieai_envc_run_instance_info、ieai_envc_run_rule、ieai_envc_plan、ieai_envc_plan_relation、ieai_envc_project、ieai_envc_system_computer这几个表查询出列表所需数据。表含义请参考@一致性比对微服务表结构.md

- 查询条件： 

  - 业务系统名称模糊(businessSystemName)
  - 对比类型（model）
  - 对别结果（result）

  ​		

- 列表所需数据：

  - 批次号（ieai_envc_run_instance表的IID）
  - 业务系统ID（ieai_envc_project表的ibusiness_system_id）
  - 业务系统名称（ieai_envc_project表的ibusiness_system_name）
  - 对比类型（ieai_envc_run_rule的imodel）
  - 比对时间（ieai_envc_run_rule的icreate_time）
  - 源服务器（ieai_envc_run_instance_info表中isource_computer_ip）
  - 目标服务器（ieai_envc_run_instance_info表中itarget_computer_ip）
  - 路径（ieai_envc_run_instance_info表中ipath）
  - 路径（ieai_envc_run_instance_info表中ielapsed_time,此字段为数值型，单位为秒，需转为540天9时55分12秒这种格式的字符串）
  - 对比结果（ieai_envc_run_instance_info表中iresult）
  - 状态（ieai_envc_run_instance_info表中istate）

- 是否分页：是

- 规范要求：需要满足开发规范。

  - 新增比对结果ResultMonitorcontroller类，并在该类增加入口方法，并增加IResultMonitorService和ResultMonitorServiceImpl类以及ResultMonitorMapper和ResultMonitorMapper.xml，同时增加相应的Dto和Bean对象以便于实现我们的功能需求

  - controller、service层均以Dto对象进行传递，与Mapper需要以Entity和Bean类型进行交互。因而中间涉及对象转换。

  - Dto类型对象存储于dto包，Entity存储于entity包，Bean存储于bean包，以上对象如果存在复用之，不存在对应位置新建。

  - 所建立方法名和类对象均需要见名知义、有合理的注释和日志打印

