package com.ideal.envc.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.*;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.INodeRelationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * NodeRelationController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class NodeRelationControllerTest {

    @Mock
    private INodeRelationService nodeRelationService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private NodeRelationController nodeRelationController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private UserDto userDto;
    private NodeRelationDto nodeRelationDto;
    private NodeRelationSaveDto nodeRelationSaveDto;
    private NodeRelationQueryDto nodeRelationQueryDto;
    private TableQueryDto<NodeRelationQueryDto> tableQueryDto;
    private NodeRelationEnableDto nodeRelationEnableDto;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(nodeRelationController).build();
        objectMapper = new ObjectMapper();

        // 初始化用户信息
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");

        // 初始化节点关系DTO
        nodeRelationDto = new NodeRelationDto();
        nodeRelationDto.setId(1L);
        nodeRelationDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationDto.setModel(0);
        nodeRelationDto.setType(1L);
        nodeRelationDto.setPath("/test/path");
        nodeRelationDto.setSourcePath("/source/path");
        nodeRelationDto.setEncode("UTF-8");
        nodeRelationDto.setWay(0);
        nodeRelationDto.setRuleType(0);
        nodeRelationDto.setEnabled(0);
        nodeRelationDto.setChildLevel(1);

        // 初始化节点关系保存DTO
        nodeRelationSaveDto = new NodeRelationSaveDto();
        nodeRelationSaveDto.setId(1L);
        nodeRelationSaveDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationSaveDto.setModel(0);
        nodeRelationSaveDto.setType(1L);
        nodeRelationSaveDto.setPath("/test/path");
        nodeRelationSaveDto.setSourcePath("/source/path");
        nodeRelationSaveDto.setEncode("UTF-8");
        nodeRelationSaveDto.setWay(0);
        nodeRelationSaveDto.setRuleType(0);
        nodeRelationSaveDto.setEnabled(0);
        nodeRelationSaveDto.setChildLevel(1);
        nodeRelationSaveDto.setContent("test content");

        // 初始化查询DTO
        nodeRelationQueryDto = new NodeRelationQueryDto();
        nodeRelationQueryDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationQueryDto.setModel(0);
        nodeRelationQueryDto.setEnabled(0);

        // 初始化表格查询DTO
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(nodeRelationQueryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        // 初始化启用禁用DTO
        nodeRelationEnableDto = new NodeRelationEnableDto();
        nodeRelationEnableDto.setOperationType(0); // 启用
        nodeRelationEnableDto.setIds(new Long[]{1L, 2L});
    }

    @Test
    @DisplayName("测试查询节点关系规则列表_成功")
    void testList_Success() throws Exception {
        // 准备测试数据
        List<NodeRelationListDto> nodeRelationList = new ArrayList<>();
        NodeRelationListDto listDto = new NodeRelationListDto();
        listDto.setId(1L);
        listDto.setEnvcSystemComputerNodeId(100L);
        listDto.setModel(0);
        listDto.setPath("/test/path");
        nodeRelationList.add(listDto);

        PageInfo<NodeRelationListDto> pageInfo = new PageInfo<>(nodeRelationList);
        pageInfo.setTotal(1);

        // 模拟依赖行为
        when(nodeRelationService.selectNodeRelationList(any(NodeRelationQueryDto.class), eq(1), eq(10)))
                .thenReturn(pageInfo);

        // 执行测试
        mockMvc.perform(post("/relation/list")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(tableQueryDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SUCCESS.getCode()))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].id").value(1));

        // 验证依赖调用
        verify(nodeRelationService, times(1)).selectNodeRelationList(any(NodeRelationQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("测试查询节点关系规则详细信息_成功")
    void testGetNodeRelationInfo_Success() throws Exception {
        // 模拟依赖行为
        when(nodeRelationService.selectNodeRelationById(1L)).thenReturn(nodeRelationDto);

        // 执行测试
        mockMvc.perform(get("/relation/get")
                        .param("id", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SUCCESS.getCode()))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.envcSystemComputerNodeId").value(100));

        // 验证依赖调用
        verify(nodeRelationService, times(1)).selectNodeRelationById(1L);
    }

    @Test
    @DisplayName("测试新增保存节点关系规则_成功")
    void testSave_Success() throws Exception {
        // 模拟依赖行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.insertNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenReturn(1);

        // 执行测试
        mockMvc.perform(post("/relation/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationSaveDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SUCCESS.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).insertNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试新增保存节点关系规则_异常")
    void testSave_Exception() throws Exception {
        // 模拟依赖行为 - 抛出异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.insertNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenThrow(new RuntimeException("数据库异常"));

        // 执行测试
        mockMvc.perform(post("/relation/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationSaveDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SYSTEM_ERROR.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).insertNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点关系规则_成功")
    void testUpdate_Success() throws Exception {
        // 模拟依赖行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenReturn(1);

        // 执行测试
        mockMvc.perform(post("/relation/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationSaveDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SUCCESS.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点关系规则_参数不能为空异常")
    void testUpdate_ParamNullException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("参数不能为空"));

        // 执行测试
        mockMvc.perform(post("/relation/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationSaveDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点关系规则_未找到要修改的记录异常")
    void testUpdate_DataNotFoundException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("未找到要修改的记录"));

        // 执行测试
        mockMvc.perform(post("/relation/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationSaveDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点关系规则_其他业务异常")
    void testUpdate_OtherBusinessException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("其他业务异常"));

        // 执行测试
        mockMvc.perform(post("/relation/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationSaveDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_FAIL.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点关系规则_系统异常")
    void testUpdate_SystemException() throws Exception {
        // 模拟依赖行为 - 抛出系统异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // 执行测试
        mockMvc.perform(post("/relation/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationSaveDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SYSTEM_ERROR.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试删除节点关系规则_成功")
    void testRemove_Success() throws Exception {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 模拟依赖行为
        when(nodeRelationService.deleteNodeRelationByIds(ids)).thenReturn(2);

        // 执行测试
        mockMvc.perform(post("/relation/remove")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(ids)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SUCCESS.getCode()));

        // 验证依赖调用
        verify(nodeRelationService, times(1)).deleteNodeRelationByIds(ids);
    }

    @Test
    @DisplayName("测试启用节点关系规则_成功")
    void testChangeEnabled_EnableSuccess() throws Exception {
        // 模拟依赖行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class))).thenReturn(2);

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SUCCESS.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试禁用节点关系规则_成功")
    void testChangeEnabled_DisableSuccess() throws Exception {
        // 修改操作类型为禁用
        nodeRelationEnableDto.setOperationType(1);

        // 模拟依赖行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(1), any(UserDto.class))).thenReturn(2);

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SUCCESS.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(1), any(UserDto.class));
    }

    @Test
    @DisplayName("测试启用禁用节点关系规则_ID列表为空异常")
    void testChangeEnabled_IdsNullException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("ID列表不能为空"));

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试启用禁用节点关系规则_操作类型参数错误异常")
    void testChangeEnabled_OperationTypeErrorException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("操作类型参数错误"));

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试启用禁用节点关系规则_用户信息不能为空异常")
    void testChangeEnabled_UserNullException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("用户信息不能为空"));

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试启用禁用节点关系规则_规则不存在异常")
    void testChangeEnabled_RuleNotExistException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("规则不存在"));

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试启用禁用节点关系规则_已经是指定状态异常")
    void testChangeEnabled_AlreadyStatusException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("已经是启用状态"));

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_FAIL.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试启用禁用节点关系规则_其他业务异常")
    void testChangeEnabled_OtherBusinessException() throws Exception {
        // 模拟依赖行为 - 抛出业务异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class)))
                .thenThrow(new ContrastBusinessException("其他业务异常"));

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_FAIL.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试启用禁用节点关系规则_系统异常")
    void testChangeEnabled_SystemException() throws Exception {
        // 模拟依赖行为 - 抛出系统异常
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // 执行测试
        mockMvc.perform(post("/relation/changeEnabled")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(nodeRelationEnableDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(ResponseCodeEnum.SYSTEM_ERROR.getCode()));

        // 验证依赖调用
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }

    @Test
    @DisplayName("测试直接调用list方法_成功")
    void testListMethod_Success() {
        // 准备测试数据
        List<NodeRelationListDto> nodeRelationList = new ArrayList<>();
        NodeRelationListDto listDto = new NodeRelationListDto();
        listDto.setId(1L);
        listDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationList.add(listDto);

        PageInfo<NodeRelationListDto> pageInfo = new PageInfo<>(nodeRelationList);

        // 模拟依赖行为
        when(nodeRelationService.selectNodeRelationList(any(NodeRelationQueryDto.class), eq(1), eq(10)))
                .thenReturn(pageInfo);

        // 执行测试方法
        R<PageInfo<NodeRelationListDto>> result = nodeRelationController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(pageInfo, result.getData());
        verify(nodeRelationService, times(1)).selectNodeRelationList(any(NodeRelationQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("测试直接调用getNodeRelationInfo方法_成功")
    void testGetNodeRelationInfoMethod_Success() {
        // 模拟依赖行为
        when(nodeRelationService.selectNodeRelationById(1L)).thenReturn(nodeRelationDto);

        // 执行测试方法
        R<NodeRelationDto> result = nodeRelationController.getNodeRelationInfo(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(nodeRelationDto, result.getData());
        verify(nodeRelationService, times(1)).selectNodeRelationById(1L);
    }

    @Test
    @DisplayName("测试直接调用save方法_成功")
    void testSaveMethod_Success() {
        // 模拟依赖行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.insertNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenReturn(1);

        // 执行测试方法
        R<Void> result = nodeRelationController.save(nodeRelationSaveDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).insertNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试直接调用update方法_成功")
    void testUpdateMethod_Success() throws ContrastBusinessException {
        // 模拟依赖行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class)))
                .thenReturn(1);

        // 执行测试方法
        R<Void> result = nodeRelationController.update(nodeRelationSaveDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelation(any(NodeRelationSaveDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试直接调用remove方法_成功")
    void testRemoveMethod_Success() {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 模拟依赖行为
        when(nodeRelationService.deleteNodeRelationByIds(ids)).thenReturn(2);

        // 执行测试方法
        R<Void> result = nodeRelationController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        verify(nodeRelationService, times(1)).deleteNodeRelationByIds(ids);
    }

    @Test
    @DisplayName("测试直接调用changeEnabled方法_成功")
    void testChangeEnabledMethod_Success() throws ContrastBusinessException {
        // 模拟依赖行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(nodeRelationService.updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class))).thenReturn(2);

        // 执行测试方法
        R<Void> result = nodeRelationController.changeEnabled(nodeRelationEnableDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        verify(userinfoComponent, times(1)).getUser();
        verify(nodeRelationService, times(1)).updateNodeRelationEnabledByIds(
                any(Long[].class), eq(0), any(UserDto.class));
    }
} 