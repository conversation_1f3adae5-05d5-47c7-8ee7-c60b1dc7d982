package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 一致性比对启动基础DTO
 *
 * <AUTHOR>
 */
public class StartContrastBaseDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 源中心ID（非必选）
     */
    private Long sourceCenterId;

    /**
     * 目标中心ID（非必选）
     */
    private Long targetCenterId;

    /**
     * 启动类型（1:方案级启动, 2:系统级启动, 3:节点级启动, 4:规则级启动, 5:任务级启动）
     */
    private Integer startType;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    @Override
    public String toString() {
        return "StartContrastBaseDto{" +
                "userId=" + userId +
                ", userName='" + userName + '\'' +
                ", sourceCenterId=" + sourceCenterId +
                ", targetCenterId=" + targetCenterId +
                ", startType=" + startType +
                '}';
    }
}
