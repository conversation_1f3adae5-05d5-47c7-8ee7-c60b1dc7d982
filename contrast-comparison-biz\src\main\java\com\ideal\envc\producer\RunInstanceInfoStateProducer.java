package com.ideal.envc.producer;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.RunInstanceInfoStateMessage;
import com.ideal.envc.model.enums.MessageTopicEnum;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 运行实例信息状态生产者
 * 用于在更新ieai_envc_run_rule表后发送消息，以更新ieai_envc_run_instance_info表状态
 * <AUTHOR>
 */
@Component
public class RunInstanceInfoStateProducer {
    private static final Logger logger = LoggerFactory.getLogger(RunInstanceInfoStateProducer.class);
    private final IPublisher publisher;

    public RunInstanceInfoStateProducer(IPublisher publisher) {
        this.publisher = publisher;
    }

    /**
     * 发送运行实例信息状态消息
     *
     * @param message 运行实例信息状态消息
     * @return 是否成功
     */
    public boolean sendRunInstanceInfoStateMessage(RunInstanceInfoStateMessage message) {
        if (message == null) {
            logger.warn("发送运行实例信息状态消息失败：消息为空");
            return false;
        }

        try {
            String messageJson = JSON.toJSONString(message);
            String channel = MessageTopicEnum.RUN_INSTANCE_INFO_STATE_TOPIC.getChannel() + "-out-0";
            logger.info("发送运行实例信息状态消息通道：{},instanceInfoId: {}, ruleId: {}",
                    channel,  message.getInstanceInfoId(), message.getRuleId());


            /**
             * 使用MessageBuilder构建消息
             * 使用instanceInfoId作为顺序消息的key，确保相同instanceInfoId的消息发送到同一个分区
             */
            Message<String> msg = MessageBuilder
                .withPayload(messageJson)
                .setHeader("instanceInfoId", message.getInstanceInfoId())
                .build();
            
            this.publisher.apply(channel, msg);
            logger.info("发送运行实例信息状态消息成功，instanceInfoId: {}, ruleId: {}", 
                message.getInstanceInfoId(), message.getRuleId());
            return true;
        } catch (CommunicationException e) {
            logger.error("发送运行实例信息状态消息失败，instanceInfoId: {}, ruleId: {}", 
                message.getInstanceInfoId(), message.getRuleId(), e);
            return false;
        }
    }
} 