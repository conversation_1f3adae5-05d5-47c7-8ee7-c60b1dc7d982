# IP查询主机名功能说明

## 概述

为了提升文件比较Excel导出报告的可读性，新增了根据IP地址自动查询对应主机名的功能。当用户提供IP地址时，系统会自动从数据库中查询对应的主机名，并在Excel报告中同时显示IP地址和主机名信息。

## 功能特性

### 1. 自动主机名查询
- **输入**：IP地址（如：*************）
- **输出**：对应的主机名（如：A-ECIF-APP01）
- **数据源**：ieai_envc_system_computer表

### 2. 智能降级处理
- **查询成功**：使用查询到的主机名
- **查询失败**：使用IP地址作为主机名
- **异常处理**：确保程序不会因查询失败而中断

### 3. 完整的日志记录
- **INFO级别**：记录查询过程和结果
- **DEBUG级别**：记录详细的查询信息
- **WARN级别**：记录查询不到主机名的情况
- **ERROR级别**：记录查询异常的详细信息

## 技术实现

### 1. 数据库层面

#### 新增Mapper方法
```java
/**
 * 根据IP地址查询主机名
 */
String selectComputerNameByIp(String computerIp);

/**
 * 批量根据IP地址查询主机名
 */
@MapKey("computerIp")
Map<String, SystemComputerEntity> selectComputerNameMapByIps(List<String> computerIps);
```

#### SQL查询语句
```sql
-- 单个IP查询主机名
SELECT icomputer_name
FROM ieai_envc_system_computer
WHERE icomputer_ip = #{computerIp}
LIMIT 1;

-- 批量IP查询主机名
SELECT icomputer_ip, icomputer_name
FROM ieai_envc_system_computer
WHERE icomputer_ip IN ('*************', '*************');
```

### 2. 组件层面

#### 依赖注入
```java
@Component
public class FileComparisonComponent {
    private final IFileComparisonService fileComparisonService;
    private final SystemComputerMapper systemComputerMapper;

    public FileComparisonComponent(IFileComparisonService fileComparisonService, 
                                  SystemComputerMapper systemComputerMapper) {
        this.fileComparisonService = fileComparisonService;
        this.systemComputerMapper = systemComputerMapper;
    }
}
```

#### 主机名查询方法
```java
/**
 * 根据IP地址查询主机名
 */
private String getHostnameByIp(String computerIp) {
    if (StringUtils.isBlank(computerIp)) {
        return computerIp;
    }

    try {
        String hostname = systemComputerMapper.selectComputerNameByIp(computerIp);
        return StringUtils.isNotBlank(hostname) ? hostname : computerIp;
    } catch (Exception e) {
        logger.error("根据IP {} 查询主机名时发生异常：{}", computerIp, e.getMessage(), e);
        return computerIp;
    }
}
```

### 3. 业务流程

#### 修改后的compareAndExport方法
```java
public void compareAndExport(String sourceContent, String targetContent,
                            String baselineServer, String targetServer, 
                            HttpServletResponse response) throws ContrastBusinessException {
    // 1. 根据IP地址查询对应的主机名
    String baselineHostname = getHostnameByIp(baselineServer);
    String targetHostname = getHostnameByIp(targetServer);
    
    // 2. 设置请求参数
    FileComparisonRequestDto request = new FileComparisonRequestDto();
    request.setSourceContent(sourceContent);
    request.setTargetContent(targetContent);
    request.setBaseServerIp(baselineServer);        // IP地址
    request.setTargetServerIp(targetServer);        // IP地址
    request.setBaselineServer(baselineHostname);    // 主机名
    request.setTargetServer(targetHostname);        // 主机名

    // 3. 执行导出
    fileComparisonService.exportComparisonResult(request, response);
}
```

## 使用方式

### 1. 基本调用
```java
@Autowired
private FileComparisonComponent fileComparisonComponent;

// 使用IP地址调用，系统会自动查询主机名
fileComparisonComponent.compareAndExport(
    sourceContent,
    targetContent,
    "*************",  // 基线服务器IP
    "*************",  // 目标服务器IP
    response
);
```

### 2. 执行流程
1. **接收IP参数**：*************, *************
2. **查询主机名**：
   - ************* → A-ECIF-APP01
   - ************* → B-ECIF-APP02
3. **设置请求参数**：
   - baseServerIp: *************
   - targetServerIp: *************
   - baselineServer: A-ECIF-APP01
   - targetServer: B-ECIF-APP02
4. **生成Excel报告**：包含IP和主机名信息

### 3. Excel报告效果
```
汇总表格：
| 服务器类型 | IP            | hostname      | 汇总 | 缺失 | 多出 | 不一致 | 一致 |
|-----------|---------------|---------------|------|------|------|--------|------|
| 基线服务器 | ************* | A-ECIF-APP01  | 216  |      |      |        |      |
| 目标服务器 | ************* | B-ECIF-APP02  | 210  | 13   | 2    | 0      | 393  |
```

## 异常处理

### 1. 处理策略
| 情况 | 处理方式 | 示例 |
|------|----------|------|
| IP为空或null | 返回原值 | "" → "", null → null |
| 查询到主机名 | 返回主机名 | "*************" → "A-ECIF-APP01" |
| 查询不到主机名 | 返回IP地址 | "192.168.1.999" → "192.168.1.999" |
| 查询异常 | 返回IP地址并记录错误日志 | "*************" → "*************" |

### 2. 日志示例
```
INFO  - 开始比较文件内容并导出Excel，基线服务器IP：*************，目标服务器IP：*************
DEBUG - 根据IP ************* 查询到主机名：A-ECIF-APP01
DEBUG - 根据IP ************* 查询到主机名：B-ECIF-APP02
INFO  - 查询到主机名 - 基线服务器：A-ECIF-APP01，目标服务器：B-ECIF-APP02
```

## 数据库要求

### 1. 表结构
```sql
-- ieai_envc_system_computer表需要包含以下字段
CREATE TABLE ieai_envc_system_computer (
    iid BIGINT PRIMARY KEY,
    icomputer_ip VARCHAR(50),     -- IP地址
    icomputer_name VARCHAR(100),  -- 主机名
    -- 其他字段...
);
```

### 2. 数据示例
```sql
INSERT INTO ieai_envc_system_computer (icomputer_ip, icomputer_name) VALUES
('*************', 'A-ECIF-APP01'),
('*************', 'B-ECIF-APP02'),
('*************', 'C-ECIF-APP03');
```

### 3. 索引建议
```sql
-- 为IP地址字段创建索引，提高查询性能
CREATE INDEX idx_computer_ip ON ieai_envc_system_computer(icomputer_ip);
```

## 测试覆盖

### 1. 单元测试场景
- ✅ 查询到主机名的正常情况
- ✅ 查询不到主机名的情况
- ✅ 查询异常的情况
- ✅ IP地址为空的情况
- ✅ 批量查询的情况

### 2. 测试方法
```java
@Test
@DisplayName("测试带IP参数的比较并导出功能 - 查询到主机名")
void testCompareAndExport_WithIpParameters_HostnameFound() {
    // 模拟主机名查询结果
    when(systemComputerMapper.selectComputerNameByIp("*************"))
        .thenReturn("A-ECIF-APP01");
    
    // 执行测试并验证结果
    // ...
}
```

## 性能考虑

### 1. 查询优化
- **单次查询**：适用于少量IP的场景
- **批量查询**：适用于多个IP的场景（未来扩展）
- **索引优化**：为IP字段创建索引

### 2. 缓存策略（未来扩展）
- **本地缓存**：缓存频繁查询的IP-主机名映射
- **缓存过期**：设置合理的缓存过期时间
- **缓存更新**：支持手动刷新缓存

## 业务价值

### 1. 用户体验提升
- **可读性增强**：主机名比IP地址更易读
- **快速识别**：便于运维人员快速识别服务器
- **报告专业性**：Excel报告更加专业和完整

### 2. 运维效率提升
- **环境区分**：快速区分不同环境的服务器
- **问题定位**：通过主机名快速定位问题服务器
- **文档完整性**：提供完整的服务器信息

### 3. 系统集成性
- **数据一致性**：与现有系统数据保持一致
- **扩展性**：为未来功能扩展提供基础
- **兼容性**：不影响现有功能的正常使用

## 注意事项

### 1. 数据准确性
- 确保ieai_envc_system_computer表中的IP-主机名映射数据准确
- 定期维护和更新服务器信息

### 2. 性能监控
- 监控主机名查询的响应时间
- 关注数据库连接池的使用情况

### 3. 异常监控
- 监控主机名查询失败的频率
- 设置适当的告警机制

这个功能增强了文件比较Excel导出的实用性和专业性，为用户提供了更好的使用体验。
