2025-07-19 00:20:26.102 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 00:40:26.085 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 01:10:26.161 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 01:40:26.134 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 02:00:26.207 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 02:40:26.184 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 02:50:26.246 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 03:40:26.239 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 03:40:26.283 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 04:30:26.323 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 04:40:26.295 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 05:20:26.360 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 05:35:27.595 [DubboMetadataReportTimer-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:434 -  [DUBBO] start to publish all metadata., dubbo version: 3.2.5, current host: **********
2025-07-19 05:35:27.596 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@de8893a; definition: {interface=com.ideal.engine.api.IActivity, release=3.2.5, pid=35320, group=engine, application=contrast-dubbo, version=1.0.0, side=consumer, dubbo=2.0.2, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1752831027590}, dubbo version: 3.2.5, current host: **********
2025-07-19 05:35:27.598 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@611e4f1d; definition: {interface=com.ideal.system.api.IBusinessSystemCompuerList, release=3.2.5, pid=35320, group=system, application=contrast-dubbo, version=1.0.0, side=consumer, dubbo=2.0.2, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getBusinessSystemCompuerListByIdForApi,queryBusinessSystemComputerList,queryComputerList,queryComputerListDetail,queryComputerListGroupRole,queryComputerListGroupRoleAll, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1752831028212}, dubbo version: 3.2.5, current host: **********
2025-07-19 05:35:27.599 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@3ea41d60; definition: {interface=com.ideal.system.api.ICenter, release=3.2.5, pid=35320, group=system, application=contrast-dubbo, version=1.0.0, side=consumer, dubbo=2.0.2, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getCenterListForApi,getCenterListForUserId,getCenterPageListForApi, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1752831023134}, dubbo version: 3.2.5, current host: **********
2025-07-19 05:35:27.601 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@36c51522; definition: {interface=com.ideal.system.api.IBusinessSystem, release=3.2.5, pid=35320, group=system, application=contrast-dubbo, version=1.0.0, side=consumer, dubbo=2.0.2, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=delRoleSystem,getBusinessSystemByUserId,getBusinessSystemInfoByBusSystemIdForApi,getBusinessSystemInfoByUserIdForApi,getUserInfosByBusSystemIdForApi,isExistByBusinessSystemName,queryBusinessSystemByCode,queryBusinessSystemByName,queryBusinessSystemByUserId,queryBusinessSystemListByCode,queryBusinessSystemListByName,queryBusinessSystemPageByName,saveBusinessSystem,saveRoleSystem,selectBusinessSystemList,selectComputerInfoList,selectRoleCodeInSystem, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1752831027891}, dubbo version: 3.2.5, current host: **********
2025-07-19 05:35:27.604 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@3bd1dbba; definition: {interface=com.ideal.engine.api.IStartFlow, release=3.2.5, pid=35320, group=engine, application=contrast-dubbo, version=1.0.0, side=consumer, dubbo=2.0.2, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=killFlow,pauseFlow,resumeFlow,startFlow, qos.port=21518, provided-by=dubbo-engine, check=false, timeout=200000, unloadClusterRelated=false, revision=1.7-20250210.005238-1, retries=5, background=false, sticky=false, timestamp=1752831027269}, dubbo version: 3.2.5, current host: **********
2025-07-19 05:35:27.605 [DubboSaveMetadataReport-thread-1] INFO  o.a.dubbo.metadata.store.nacos.NacosMetadataReport:320 -  [DUBBO] store consumer metadata. Identifier : org.apache.dubbo.metadata.report.identifier.MetadataIdentifier@1cc978a7; definition: {interface=com.ideal.system.api.IRoleProjectPermission, release=3.2.5, pid=35320, group=system, application=contrast-dubbo, version=1.0.0, side=consumer, dubbo=2.0.2, executor-management-mode=isolation, file-cache=true, register.ip=**********, methods=getRoleButtonAuthority, qos.port=21518, check=false, timeout=200000, unloadClusterRelated=false, revision=1.6.2-20250417.011711-9, retries=5, background=false, sticky=false, timestamp=1752831026943}, dubbo version: 3.2.5, current host: **********
2025-07-19 05:40:26.348 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 06:10:26.410 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 06:40:26.408 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 07:00:26.457 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 07:40:26.462 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 07:50:26.501 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 08:40:26.507 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 08:40:26.551 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 09:30:26.599 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 09:40:26.553 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.266 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xd64e892d, L:/**********:53222 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.273 [DubboClientHandler-thread-2] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xd64e892d, L:/**********:53222 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.394 [NettyClientWorker-9-3] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:53222 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.738 [nacos-grpc-client-executor-************-22271] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:45:27.740 [nacos-grpc-client-executor-************-22271] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752827428715"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:45:27.740 [nacos-grpc-client-executor-************-22271] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 09:45:27.750 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@7a58da35
2025-07-19 09:45:27.769 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 09:45:27.769 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.776 [nacos-grpc-client-executor-************-22271] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:45:27.777 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.781 [nacos-grpc-client-executor-************-22272] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:45:27.782 [nacos-grpc-client-executor-************-22272] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752827429940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:45:27.782 [nacos-grpc-client-executor-************-22272] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 09:45:27.782 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@2107e8a9
2025-07-19 09:45:27.783 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 09:45:27.783 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.785 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.795 [nacos-grpc-client-executor-************-22272] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:45:27.838 [nacos-grpc-client-executor-************-22268] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:45:27.838 [nacos-grpc-client-executor-************-22268] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752827429940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:45:27.838 [nacos-grpc-client-executor-************-22268] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 09:45:27.839 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 09:45:27.840 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.840 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.840 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.840 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 09:45:27.842 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.842 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.842 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.843 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 09:45:27.845 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xd64e892d, L:/**********:53222 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.852 [nacos-grpc-client-executor-************-22268] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:45:27.855 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:45:27.855 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:22.653 [ConsumeMessageThread_1] INFO  c.i.e.c.ContrastReceiveTaskSendResultHandler:25 - receive task send response is {"taskFlowId":1106119483369189376,"test":null,"isException":true,"startTime":null}
2025-07-19 09:48:25.520 [ConsumeMessageThread_16] INFO  c.i.message.subscriber.EngineMonitorMqSubscriber:44 - {"flowId":1106365525754904576,"projectUuid":null,"projectName":"CompareTemplate","projectType":13,"flowName":"ActExec_Compare","flowInsName":"Instance_1106119483369189376_1106119483369189378","bizUniqueId":1106119483369189379,"runServerIp":"***********","runServerPort":null,"flowStatus":70,"dateTime":1752889706843,"updateOrderTime":1752889706843,"flowStartTime":1752889706843,"flowEndTime":1752889706843,"ifFail":null,"monitorFowActiveNodeDto":null,"monitorActOutput":null,"monitorCallFlow":null,"actOutputDetailNotification":null,"scriptLatestUuid":null}
2025-07-19 09:48:25.542 [ConsumeMessageThread_16] INFO  c.ideal.envc.component.EngineDataExecuteComponent:65 - receive flow monitor callback monitorFlowDto message:{"bizUniqueId":1106119483369189379,"dateTime":"2025-07-19 09:48:26.843","flowEndTime":1752889706843,"flowId":1106365525754904576,"flowInsName":"Instance_1106119483369189376_1106119483369189378","flowName":"ActExec_Compare","flowStartTime":1752889706843,"flowStatus":70,"projectName":"CompareTemplate","projectType":13,"runServerIp":"***********","updateOrderTime":1752889706843}
2025-07-19 09:48:25.661 [nacos-grpc-client-executor-************-22339] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:48:25.661 [nacos-grpc-client-executor-************-22339] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752889703515"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:48:25.661 [nacos-grpc-client-executor-************-22339] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752889703515"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:48:25.662 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 09:48:25.662 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.668 [NettyClientWorker-9-4] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:63500 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.668 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x3e0985d9, L:/**********:63500 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.668 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.677 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.678 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.687 [nacos-grpc-client-executor-************-22339] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:48:25.690 [nacos-grpc-client-executor-************-22340] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:48:25.690 [nacos-grpc-client-executor-************-22340] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752889704640"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:48:25.691 [nacos-grpc-client-executor-************-22340] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752889704640"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:48:25.691 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 09:48:25.691 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.696 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.696 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.704 [nacos-grpc-client-executor-************-22340] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:48:25.760 [nacos-grpc-client-executor-************-22335] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:48:25.760 [nacos-grpc-client-executor-************-22335] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752889704640"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:48:25.760 [nacos-grpc-client-executor-************-22335] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752889704640"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:48:25.760 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 09:48:25.762 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.762 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.763 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.766 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.768 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.768 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.768 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.770 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.771 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.771 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:48:25.776 [nacos-grpc-client-executor-************-22335] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:56:25.530 [DubboClientHandler-thread-5] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x3e0985d9, L:/**********:63500 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:25.622 [DubboClientHandler-thread-5] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x3e0985d9, L:/**********:63500 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.145 [nacos-grpc-client-executor-************-22521] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266327
2025-07-19 09:56:26.145 [nacos-grpc-client-executor-************-22516] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:56:26.145 [nacos-grpc-client-executor-************-22521] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752889704640"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:56:26.145 [nacos-grpc-client-executor-************-22516] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752889704640"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:56:26.146 [nacos-grpc-client-executor-************-22521] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 09:56:26.146 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@7bb22b70
2025-07-19 09:56:26.146 [nacos-grpc-client-executor-************-22516] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 09:56:26.146 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 09:56:26.146 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.146 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.148 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 09:56:26.148 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.148 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.148 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.148 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 09:56:26.149 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.149 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.149 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.149 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 09:56:26.149 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.149 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.157 [nacos-grpc-client-executor-************-22516] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:56:26.160 [nacos-grpc-client-executor-************-22521] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266327
2025-07-19 09:56:26.161 [nacos-grpc-client-executor-************-22522] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:56:26.161 [nacos-grpc-client-executor-************-22522] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752889703515"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:56:26.161 [nacos-grpc-client-executor-************-22522] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 09:56:26.162 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@d2ed0aef
2025-07-19 09:56:26.162 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 09:56:26.162 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.164 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0x3e0985d9, L:/**********:63500 - R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.164 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.164 [NettyClientWorker-9-4] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:63500 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 09:56:26.177 [nacos-grpc-client-executor-************-22522] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:57:34.723 [nacos-grpc-client-executor-************-22549] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266396
2025-07-19 09:57:34.723 [nacos-grpc-client-executor-************-22541] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:57:34.724 [nacos-grpc-client-executor-************-22541] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752890252662"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:57:34.724 [nacos-grpc-client-executor-************-22549] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890251467"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:57:34.724 [nacos-grpc-client-executor-************-22541] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752890252662"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:57:34.724 [nacos-grpc-client-executor-************-22549] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890251467"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:57:34.724 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 09:57:34.725 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.725 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 09:57:34.725 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.725 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.725 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.729 [NettyClientWorker-9-5] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:64376 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.729 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x7813831b, L:/**********:64376 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.729 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.730 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.734 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.735 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.735 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.736 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.736 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.737 [nacos-grpc-client-executor-************-22549] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266396
2025-07-19 09:57:34.739 [nacos-grpc-client-executor-************-22550] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266399
2025-07-19 09:57:34.739 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.740 [nacos-grpc-client-executor-************-22550] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890252662"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:57:34.740 [nacos-grpc-client-executor-************-22550] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890252662"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:57:34.740 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.740 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.741 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 09:57:34.741 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.745 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.745 [nacos-grpc-client-executor-************-22541] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:57:34.745 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 09:57:34.753 [nacos-grpc-client-executor-************-22550] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266399
2025-07-19 09:59:10.325 [DubboClientHandler-thread-7] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x7813831b, L:/**********:64376 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:10.903 [nacos-grpc-client-executor-************-22587] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:59:10.903 [nacos-grpc-client-executor-************-22587] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890251467"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:59:10.903 [nacos-grpc-client-executor-************-22587] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 09:59:10.914 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@175359dd
2025-07-19 09:59:10.914 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 09:59:10.914 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:10.914 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:10.922 [nacos-grpc-client-executor-************-22587] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:59:10.941 [nacos-grpc-client-executor-************-22588] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:59:10.941 [nacos-grpc-client-executor-************-22588] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890252662"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:59:10.942 [nacos-grpc-client-executor-************-22588] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 09:59:10.942 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@c0203c6e
2025-07-19 09:59:10.942 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 09:59:10.943 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:10.943 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:10.951 [nacos-grpc-client-executor-************-22588] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:59:12.105 [nacos-grpc-client-executor-************-22580] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 09:59:12.106 [nacos-grpc-client-executor-************-22580] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752890252662"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 09:59:12.106 [nacos-grpc-client-executor-************-22580] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 09:59:12.106 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 09:59:12.106 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.107 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.107 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.108 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 09:59:12.110 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.110 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.110 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.110 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 09:59:12.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0x7813831b, L:/**********:64376 - R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.111 [NettyClientWorker-9-5] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:64376 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 09:59:12.120 [nacos-grpc-client-executor-************-22580] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:02:36.547 [nacos-grpc-client-executor-************-22665] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:02:36.547 [nacos-grpc-client-executor-************-22665] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890554754"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:02:36.547 [nacos-grpc-client-executor-************-22665] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890554754"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:02:36.548 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 10:02:36.548 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.553 [NettyClientWorker-9-6] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:64855 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.553 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xd80e8932, L:/**********:64855 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.554 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.558 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.558 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.562 [nacos-grpc-client-executor-************-22665] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:02:36.563 [nacos-grpc-client-executor-************-22666] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:02:36.563 [nacos-grpc-client-executor-************-22666] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890553491"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:02:36.563 [nacos-grpc-client-executor-************-22666] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890553491"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:02:36.564 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 10:02:36.564 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.565 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.566 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.571 [nacos-grpc-client-executor-************-22666] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:02:36.647 [nacos-grpc-client-executor-************-22657] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:02:36.647 [nacos-grpc-client-executor-************-22657] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752890554754"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:02:36.647 [nacos-grpc-client-executor-************-22657] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752890554754"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:02:36.647 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 10:02:36.647 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.647 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.648 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.649 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.649 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.650 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.650 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.651 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.651 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.651 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:02:36.657 [nacos-grpc-client-executor-************-22657] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:20:26.638 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.235 [DubboClientHandler-thread-9] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xd80e8932, L:/**********:64855 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.339 [DubboClientHandler-thread-9] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xd80e8932, L:/**********:64855 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.854 [nacos-grpc-client-executor-************-23289] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266539
2025-07-19 10:29:55.855 [nacos-grpc-client-executor-************-23278] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:29:55.855 [nacos-grpc-client-executor-************-23278] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752890554754"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:29:55.855 [nacos-grpc-client-executor-************-23289] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890554754"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:29:55.856 [nacos-grpc-client-executor-************-23278] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 10:29:55.856 [nacos-grpc-client-executor-************-23289] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 10:29:55.856 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 10:29:55.856 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.856 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.856 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.857 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 10:29:55.857 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.857 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.857 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.857 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 10:29:55.858 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.858 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.858 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@8384deef
2025-07-19 10:29:55.858 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 10:29:55.858 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.859 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.871 [nacos-grpc-client-executor-************-23278] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:29:55.871 [nacos-grpc-client-executor-************-23289] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266539
2025-07-19 10:29:55.873 [nacos-grpc-client-executor-************-23290] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:29:55.873 [nacos-grpc-client-executor-************-23290] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752890553491"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:29:55.873 [nacos-grpc-client-executor-************-23290] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 10:29:55.873 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@dc60da5f
2025-07-19 10:29:55.873 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 10:29:55.874 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.875 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xd80e8932, L:/**********:64855 - R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.875 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.875 [NettyClientWorker-9-6] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:64855 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 10:29:55.885 [nacos-grpc-client-executor-************-23290] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:30:31.102 [nacos-grpc-client-executor-************-23291] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = 266615
2025-07-19 10:30:31.102 [nacos-grpc-client-executor-************-23303] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:30:31.103 [nacos-grpc-client-executor-************-23303] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892230253"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:30:31.103 [nacos-grpc-client-executor-************-23291] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752892230253"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:30:31.103 [nacos-grpc-client-executor-************-23303] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892230253"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:30:31.103 [nacos-grpc-client-executor-************-23291] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752892230253"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:30:31.103 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 10:30:31.104 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.104 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.104 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.109 [NettyClientWorker-9-7] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:51256 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.109 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xdb535ca3, L:/**********:51256 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.110 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.110 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.113 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.114 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.114 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.115 [nacos-grpc-client-executor-************-23291] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = 266615
2025-07-19 10:30:31.115 [nacos-grpc-client-executor-************-23303] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:30:31.116 [nacos-grpc-client-executor-************-23304] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:30:31.117 [nacos-grpc-client-executor-************-23304] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892229145"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:30:31.117 [nacos-grpc-client-executor-************-23304] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892229145"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:30:31.117 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.118 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.118 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.118 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 10:30:31.119 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.122 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 10:30:31.122 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.122 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.122 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.123 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.123 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:30:31.137 [nacos-grpc-client-executor-************-23304] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:40:26.601 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.227 [DubboClientHandler-thread-10] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xdb535ca3, L:/**********:51256 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.325 [DubboClientHandler-thread-10] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xdb535ca3, L:/**********:51256 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.833 [nacos-grpc-client-executor-************-23551] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266658
2025-07-19 10:41:20.833 [nacos-grpc-client-executor-************-23538] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:41:20.834 [nacos-grpc-client-executor-************-23538] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752892230253"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:41:20.834 [nacos-grpc-client-executor-************-23551] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892230253"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:41:20.834 [nacos-grpc-client-executor-************-23538] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 10:41:20.834 [nacos-grpc-client-executor-************-23551] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 10:41:20.834 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 10:41:20.834 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.834 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.835 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.835 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 10:41:20.835 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.835 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.835 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.835 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 10:41:20.836 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.836 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.836 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@376340e
2025-07-19 10:41:20.836 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 10:41:20.836 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.836 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.845 [nacos-grpc-client-executor-************-23538] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:41:20.845 [nacos-grpc-client-executor-************-23551] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266658
2025-07-19 10:41:20.847 [nacos-grpc-client-executor-************-23552] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:41:20.847 [nacos-grpc-client-executor-************-23552] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892229145"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:41:20.847 [nacos-grpc-client-executor-************-23552] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 10:41:20.847 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@7fc59e61
2025-07-19 10:41:20.848 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 10:41:20.848 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.848 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xdb535ca3, L:/**********:51256 - R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.850 [NettyClientWorker-9-7] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:51256 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.850 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:41:20.865 [nacos-grpc-client-executor-************-23552] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:42:36.007 [nacos-grpc-client-executor-************-23579] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:42:36.008 [nacos-grpc-client-executor-************-23579] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892953077"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:42:36.008 [nacos-grpc-client-executor-************-23579] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892953077"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:42:36.008 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 10:42:36.009 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.014 [NettyClientWorker-9-8] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:52426 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.014 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x26f50851, L:/**********:52426 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.015 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.019 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.020 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.026 [nacos-grpc-client-executor-************-23579] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:42:36.028 [nacos-grpc-client-executor-************-23580] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:42:36.028 [nacos-grpc-client-executor-************-23580] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892954292"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:42:36.028 [nacos-grpc-client-executor-************-23580] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892954292"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:42:36.028 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 10:42:36.029 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.032 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.032 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.038 [nacos-grpc-client-executor-************-23580] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:42:36.107 [nacos-grpc-client-executor-************-23565] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:42:36.108 [nacos-grpc-client-executor-************-23565] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752892954292"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:42:36.108 [nacos-grpc-client-executor-************-23565] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752892954292"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:42:36.108 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 10:42:36.108 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.108 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.108 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.110 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.111 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.112 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:42:36.117 [nacos-grpc-client-executor-************-23565] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:52:32.863 [DubboClientHandler-thread-12] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x26f50851, L:/**********:52426 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:32.949 [DubboClientHandler-thread-12] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x26f50851, L:/**********:52426 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.390 [nacos-grpc-client-executor-************-23807] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:52:33.391 [nacos-grpc-client-executor-************-23807] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892953077"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:52:33.392 [nacos-grpc-client-executor-************-23807] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 10:52:33.393 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@1434b9a1
2025-07-19 10:52:33.393 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 10:52:33.393 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.394 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.407 [nacos-grpc-client-executor-************-23807] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:52:33.489 [nacos-grpc-client-executor-************-23808] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266773
2025-07-19 10:52:33.490 [nacos-grpc-client-executor-************-23790] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:52:33.490 [nacos-grpc-client-executor-************-23790] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752892954292"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:52:33.490 [nacos-grpc-client-executor-************-23808] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752892954292"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:52:33.490 [nacos-grpc-client-executor-************-23808] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 10:52:33.490 [nacos-grpc-client-executor-************-23790] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 10:52:33.490 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 10:52:33.491 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.491 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.491 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.491 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 10:52:33.491 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.492 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.492 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.492 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 10:52:33.492 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.492 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.493 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@bb2bd4d4
2025-07-19 10:52:33.493 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 10:52:33.493 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.494 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0x26f50851, L:/**********:52426 - R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.496 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.496 [NettyClientWorker-9-8] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:52426 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 10:52:33.504 [nacos-grpc-client-executor-************-23790] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:52:33.504 [nacos-grpc-client-executor-************-23808] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266773
2025-07-19 10:56:25.690 [nacos-grpc-client-executor-************-23895] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266831
2025-07-19 10:56:25.690 [nacos-grpc-client-executor-************-23877] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:56:25.691 [nacos-grpc-client-executor-************-23877] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752893783940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:56:25.691 [nacos-grpc-client-executor-************-23895] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752893782705"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:56:25.691 [nacos-grpc-client-executor-************-23895] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752893782705"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:56:25.691 [nacos-grpc-client-executor-************-23877] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752893783940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:56:25.691 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 10:56:25.692 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.692 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 10:56:25.692 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.692 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.692 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.699 [NettyClientWorker-9-9] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:54202 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.698 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xa6ee6794, L:/**********:54202 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.700 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.702 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.707 [nacos-grpc-client-executor-************-23877] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 10:56:25.707 [nacos-grpc-client-executor-************-23895] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266831
2025-07-19 10:56:25.707 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.707 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.707 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.708 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.708 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.709 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.710 [nacos-grpc-client-executor-************-23896] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266836
2025-07-19 10:56:25.710 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.710 [nacos-grpc-client-executor-************-23896] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752893783940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:56:25.710 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.710 [nacos-grpc-client-executor-************-23896] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752893783940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 10:56:25.710 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 10:56:25.711 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.712 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.713 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 10:56:25.721 [nacos-grpc-client-executor-************-23896] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266836
2025-07-19 11:02:27.309 [DubboClientHandler-thread-14] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xa6ee6794, L:/**********:54202 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.426 [NettyClientWorker-9-9] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:54202 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.888 [nacos-grpc-client-executor-************-24033] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:02:27.889 [nacos-grpc-client-executor-************-24033] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752893782705"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:02:27.889 [nacos-grpc-client-executor-************-24033] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 11:02:27.889 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b2151854
2025-07-19 11:02:27.889 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 11:02:27.889 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.890 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.912 [nacos-grpc-client-executor-************-24033] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:02:27.913 [nacos-grpc-client-executor-************-24034] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:02:27.913 [nacos-grpc-client-executor-************-24034] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752893783940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:02:27.913 [nacos-grpc-client-executor-************-24034] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 11:02:27.915 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@5bcc0a63
2025-07-19 11:02:27.915 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 11:02:27.915 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.915 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.928 [nacos-grpc-client-executor-************-24034] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:02:27.989 [nacos-grpc-client-executor-************-24014] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:02:27.989 [nacos-grpc-client-executor-************-24014] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752893783940"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:02:27.990 [nacos-grpc-client-executor-************-24014] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 11:02:27.990 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 11:02:27.990 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.990 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.990 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.990 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 11:02:27.991 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.991 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.991 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.991 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 11:02:27.992 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xa6ee6794, L:/**********:54202 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.993 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:27.993 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:02:28.000 [nacos-grpc-client-executor-************-24014] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:03:17.050 [nacos-grpc-client-executor-************-24051] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266938
2025-07-19 11:03:17.050 [nacos-grpc-client-executor-************-24031] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:03:17.051 [nacos-grpc-client-executor-************-24031] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752894195083"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:03:17.051 [nacos-grpc-client-executor-************-24051] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894193835"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:03:17.051 [nacos-grpc-client-executor-************-24031] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752894195083"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:03:17.051 [nacos-grpc-client-executor-************-24051] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894193835"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:03:17.051 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 11:03:17.051 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.052 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.052 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.055 [NettyClientWorker-9-10] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:54820 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.056 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x333139c5, L:/**********:54820 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.056 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.056 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.060 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.060 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.060 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.061 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.061 [nacos-grpc-client-executor-************-24031] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:03:17.062 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:60 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.062 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.062 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 11:03:17.063 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.063 [nacos-grpc-client-executor-************-24051] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266938
2025-07-19 11:03:17.065 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.067 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.067 [nacos-grpc-client-executor-************-24052] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:03:17.067 [nacos-grpc-client-executor-************-24052] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894195083"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:03:17.067 [nacos-grpc-client-executor-************-24052] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894195083"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:03:17.067 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 11:03:17.068 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.070 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.070 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:03:17.076 [nacos-grpc-client-executor-************-24052] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:10:26.674 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:42.848 [NettyClientWorker-9-10] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:54820 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.402 [nacos-grpc-client-executor-************-24311] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 266970
2025-07-19 11:14:43.402 [nacos-grpc-client-executor-************-24292] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:14:43.403 [nacos-grpc-client-executor-************-24292] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752894195083"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:14:43.403 [nacos-grpc-client-executor-************-24311] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894195083"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:14:43.403 [nacos-grpc-client-executor-************-24292] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 11:14:43.403 [nacos-grpc-client-executor-************-24311] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 11:14:43.403 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 11:14:43.403 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.403 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.404 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.404 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 11:14:43.404 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.404 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.404 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.404 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 11:14:43.405 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.405 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.405 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@fcfcb1ef
2025-07-19 11:14:43.405 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 11:14:43.405 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.405 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.413 [nacos-grpc-client-executor-************-24292] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:14:43.413 [nacos-grpc-client-executor-************-24311] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 266970
2025-07-19 11:14:43.414 [nacos-grpc-client-executor-************-24312] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:14:43.414 [nacos-grpc-client-executor-************-24312] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894193835"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:14:43.414 [nacos-grpc-client-executor-************-24312] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 11:14:43.414 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@56bd9289
2025-07-19 11:14:43.415 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 11:14:43.415 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.416 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0x333139c5, L:/**********:54820 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.417 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 11:14:43.426 [nacos-grpc-client-executor-************-24312] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:15:40.174 [nacos-grpc-client-executor-************-24333] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:15:40.174 [nacos-grpc-client-executor-************-24333] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894937288"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:15:40.176 [nacos-grpc-client-executor-************-24333] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894937288"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:15:40.176 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 11:15:40.176 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.179 [NettyClientWorker-9-11] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:55966 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.179 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x400faeb7, L:/**********:55966 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.180 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.182 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.182 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.186 [nacos-grpc-client-executor-************-24333] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:15:40.187 [nacos-grpc-client-executor-************-24334] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:15:40.188 [nacos-grpc-client-executor-************-24334] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894938492"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:15:40.188 [nacos-grpc-client-executor-************-24334] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894938492"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:15:40.188 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 11:15:40.188 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.191 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.191 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.200 [nacos-grpc-client-executor-************-24334] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:15:40.274 [nacos-grpc-client-executor-************-24313] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:15:40.275 [nacos-grpc-client-executor-************-24313] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752894938492"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:15:40.275 [nacos-grpc-client-executor-************-24313] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752894938492"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 11:15:40.275 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 11:15:40.275 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.275 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.276 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.278 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.278 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.278 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.278 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.279 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.279 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.280 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 11:15:40.283 [nacos-grpc-client-executor-************-24313] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 11:40:26.666 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 12:00:26.720 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 12:40:26.715 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 12:50:26.759 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 13:40:26.775 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 13:40:26.806 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 14:30:26.856 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 14:40:26.832 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:16.976 [DubboClientHandler-thread-17] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x400faeb7, L:/**********:55966 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.093 [NettyClientWorker-9-11] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:55966 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.529 [nacos-grpc-client-executor-************-29759] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:13:17.531 [nacos-grpc-client-executor-************-29759] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894937288"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:13:17.531 [nacos-grpc-client-executor-************-29759] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 15:13:17.531 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@6ee0447f
2025-07-19 15:13:17.531 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 15:13:17.531 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.532 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.543 [nacos-grpc-client-executor-************-29759] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:13:17.544 [nacos-grpc-client-executor-************-29760] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:13:17.545 [nacos-grpc-client-executor-************-29760] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752894938492"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:13:17.545 [nacos-grpc-client-executor-************-29760] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 15:13:17.545 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@f78ffdde
2025-07-19 15:13:17.546 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 15:13:17.546 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.547 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.556 [nacos-grpc-client-executor-************-29760] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:13:17.631 [nacos-grpc-client-executor-************-29730] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:13:17.631 [nacos-grpc-client-executor-************-29730] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752894938492"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:13:17.631 [nacos-grpc-client-executor-************-29730] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 15:13:17.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 15:13:17.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.632 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 15:13:17.633 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.633 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.633 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.633 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 15:13:17.634 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0x400faeb7, L:/**********:55966 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.635 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.635 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:13:17.645 [nacos-grpc-client-executor-************-29730] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:14:48.346 [nacos-grpc-client-executor-************-29793] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:14:48.347 [nacos-grpc-client-executor-************-29793] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752909285530"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:14:48.348 [nacos-grpc-client-executor-************-29793] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752909285530"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:14:48.348 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 15:14:48.348 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.358 [NettyClientWorker-9-12] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:61629 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.358 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x2c730f81, L:/**********:61629 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.358 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.363 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.363 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.370 [nacos-grpc-client-executor-************-29793] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:14:48.372 [nacos-grpc-client-executor-************-29794] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:14:48.373 [nacos-grpc-client-executor-************-29794] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752909286790"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:14:48.373 [nacos-grpc-client-executor-************-29794] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752909286790"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:14:48.374 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 15:14:48.374 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.376 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.377 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.392 [nacos-grpc-client-executor-************-29794] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:14:48.444 [nacos-grpc-client-executor-************-29763] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:14:48.445 [nacos-grpc-client-executor-************-29763] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752909286790"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:14:48.445 [nacos-grpc-client-executor-************-29763] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752909286790"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:14:48.446 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 15:14:48.446 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.447 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.448 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.455 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.457 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.457 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.458 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.460 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.461 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.461 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:14:48.461 [nacos-grpc-client-executor-************-29763] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:20:26.907 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.247 [DubboClientHandler-thread-19] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x2c730f81, L:/**********:61629 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.336 [DubboClientHandler-thread-19] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x2c730f81, L:/**********:61629 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.470 [NettyClientWorker-9-12] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:61629 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.783 [nacos-grpc-client-executor-************-30047] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:25:53.784 [nacos-grpc-client-executor-************-30047] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752909286790"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:25:53.784 [nacos-grpc-client-executor-************-30047] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 15:25:53.784 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@e6531d80
2025-07-19 15:25:53.785 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 15:25:53.785 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.785 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.800 [nacos-grpc-client-executor-************-30047] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:25:53.802 [nacos-grpc-client-executor-************-30048] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:25:53.802 [nacos-grpc-client-executor-************-30048] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752909285530"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:25:53.802 [nacos-grpc-client-executor-************-30048] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 15:25:53.802 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@3c9d45cc
2025-07-19 15:25:53.803 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 15:25:53.803 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.804 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.815 [nacos-grpc-client-executor-************-30048] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:25:53.884 [nacos-grpc-client-executor-************-30018] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:25:53.885 [nacos-grpc-client-executor-************-30018] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752909286790"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:25:53.885 [nacos-grpc-client-executor-************-30018] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 15:25:53.885 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 15:25:53.886 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.886 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.886 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.886 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 15:25:53.887 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.887 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.888 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.888 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 15:25:53.890 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0x2c730f81, L:/**********:61629 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.892 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.892 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:25:53.900 [nacos-grpc-client-executor-************-30018] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:26:52.857 [nacos-grpc-client-executor-************-30069] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:26:52.857 [nacos-grpc-client-executor-************-30069] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910010506"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:26:52.857 [nacos-grpc-client-executor-************-30069] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910010506"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:26:52.857 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 15:26:52.859 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.862 [NettyClientWorker-9-13] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:62781 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.862 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0xe2dbfa6f, L:/**********:62781 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.862 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.866 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.866 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.870 [nacos-grpc-client-executor-************-30069] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:26:52.872 [nacos-grpc-client-executor-************-30070] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:26:52.872 [nacos-grpc-client-executor-************-30070] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910011750"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:26:52.872 [nacos-grpc-client-executor-************-30070] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910011750"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:26:52.872 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 15:26:52.873 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.875 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.875 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.884 [nacos-grpc-client-executor-************-30070] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:26:52.957 [nacos-grpc-client-executor-************-30039] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:26:52.957 [nacos-grpc-client-executor-************-30039] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752910011750"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:26:52.957 [nacos-grpc-client-executor-************-30039] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752910011750"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:26:52.957 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 15:26:52.957 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.959 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.959 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.960 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.961 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.961 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.961 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.963 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.963 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.963 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:26:52.967 [nacos-grpc-client-executor-************-30039] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:35:01.987 [DubboClientHandler-thread-20] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xe2dbfa6f, L:/**********:62781 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.066 [DubboClientHandler-thread-20] INFO  o.a.d.r.e.support.header.HeaderExchangeHandler:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0xe2dbfa6f, L:/**********:62781 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.195 [NettyClientWorker-9-13] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:74 -  [DUBBO] The connection of /**********:62781 -> /***********:20970 is disconnected., dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.583 [nacos-grpc-client-executor-************-30255] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = 267271
2025-07-19 15:35:02.583 [nacos-grpc-client-executor-************-30226] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:35:02.584 [nacos-grpc-client-executor-************-30255] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910011750"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:35:02.584 [nacos-grpc-client-executor-************-30226] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752910011750"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:35:02.584 [nacos-grpc-client-executor-************-30255] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> []
2025-07-19 15:35:02.584 [nacos-grpc-client-executor-************-30226] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@dubbo-engine -> []
2025-07-19 15:35:02.584 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@70f8f266
2025-07-19 15:35:02.584 [Dubbo-framework-registry-notification-3-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 15:35:02.584 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.585 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 15:35:02.585 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.585 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.585 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.585 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.585 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 15:35:02.586 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.586 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.586 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.586 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] WARN  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:? -  [DUBBO] Received url with EMPTY protocol, will clear all available addresses., dubbo version: 3.2.5, current host: **********, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-19 15:35:02.586 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.586 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.594 [nacos-grpc-client-executor-************-30226] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:35:02.594 [nacos-grpc-client-executor-************-30255] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = 267271
2025-07-19 15:35:02.595 [nacos-grpc-client-executor-************-30256] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:35:02.596 [nacos-grpc-client-executor-************-30256] INFO  com.alibaba.nacos.client.naming:107 - removed ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910010506"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:35:02.596 [nacos-grpc-client-executor-************-30256] INFO  com.alibaba.nacos.client.naming:142 - current ips:(0) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> []
2025-07-19 15:35:02.596 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@e90755eb
2025-07-19 15:35:02.596 [Dubbo-framework-registry-notification-2-thread-1] WARN  org.apache.dubbo.registry.nacos.NacosRegistry:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.5, current host: **********, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-19 15:35:02.596 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.597 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.remoting.transport.netty4.NettyChannel:253 -  [DUBBO] Close netty channel [id: 0xe2dbfa6f, L:/**********:62781 ! R:/***********:20970], dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.598 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.5, current host: **********
2025-07-19 15:35:02.611 [nacos-grpc-client-executor-************-30256] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:36:26.385 [nacos-grpc-client-executor-************-30287] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:36:26.386 [nacos-grpc-client-executor-************-30287] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910584248"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:36:26.386 [nacos-grpc-client-executor-************-30287] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IStartFlow:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"killFlow,pauseFlow,resumeFlow,startFlow","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IStartFlow","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IStartFlow","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910584248"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:36:26.387 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IStartFlow:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@a183e428
2025-07-19 15:36:26.387 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IStartFlow?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IStartFlow&methods=killFlow,pauseFlow,resumeFlow,startFlow&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027269&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.391 [NettyClientWorker-9-14] INFO  o.a.d.remoting.transport.netty4.NettyClientHandler:60 -  [DUBBO] The connection of /**********:63640 -> /***********:20970 is established., dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.391 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:228 -  [DUBBO] Successfully connect to server /***********:20970 from NettyClient ********** using dubbo version 3.2.5, channel is NettyChannel [channel=[id: 0x50d2e18f, L:/**********:63640 - R:/***********:20970]], dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.391 [Dubbo-framework-registry-notification-2-thread-1] INFO  org.apache.dubbo.remoting.transport.AbstractClient:79 -  [DUBBO] Start NettyClient /********** connect to the server /***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.394 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.395 [Dubbo-framework-registry-notification-2-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.399 [nacos-grpc-client-executor-************-30287] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:36:26.399 [nacos-grpc-client-executor-************-30288] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:36:26.401 [nacos-grpc-client-executor-************-30288] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910585477"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:36:26.401 [nacos-grpc-client-executor-************-30288] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine -> [{"instanceId":"***********#20970#null#DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@providers:com.ideal.engine.api.IActivity:1.0.0:engine","metadata":{"side":"provider","release":"3.2.5","methods":"getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity","deprecated":"false","dubbo":"2.0.2","threads":"1000","interface":"com.ideal.engine.api.IActivity","service-name-mapping":"true","version":"1.0.0","timeout":"200000","generic":"false","revision":"1.0.0","path":"com.ideal.engine.api.IActivity","protocol":"dubbo","application":"dubbo-engine","prefer.serialization":"fastjson2,hessian2","dynamic":"true","category":"providers","group":"engine","timestamp":"1752910585477"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:36:26.401 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: providers:com.ideal.engine.api.IActivity:1.0.0:engine to Listener: org.apache.dubbo.registry.nacos.NacosRegistry$RegistryChildListenerImpl@b53cf19c
2025-07-19 15:36:26.401 [Dubbo-framework-registry-notification-3-thread-1] INFO  org.apache.dubbo.registry.nacos.NacosRegistry:514 -  [DUBBO] Notify urls for subscribe url consumer://**********/com.ideal.engine.api.IActivity?application=contrast-dubbo&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=engine&interface=com.ideal.engine.api.IActivity&methods=getActivityOutPut,getTaskDetail,operateActivity,operateActivityStepParam,operateActivityStepPri,retryActivity,skipActivity,utActivity&pid=35320&provided-by=dubbo-engine&qos.port=21518&release=3.2.5&retries=5&revision=1.7-20250210.005238-1&side=consumer&sticky=false&timeout=200000&timestamp=1752831027590&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.402 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:54 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.402 [Dubbo-framework-registry-notification-3-thread-1] INFO  o.a.dubbo.registry.integration.RegistryDirectory:332 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.417 [nacos-grpc-client-executor-************-30288] INFO  com.alibaba.nacos.common.remote.client:63 - [23e8c392-419f-4714-887c-6de4829d3e6d] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:36:26.486 [nacos-grpc-client-executor-************-30257] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Receive server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:36:26.486 [nacos-grpc-client-executor-************-30257] INFO  com.alibaba.nacos.client.naming:101 - new ips(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752910585477"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:36:26.486 [nacos-grpc-client-executor-************-30257] INFO  com.alibaba.nacos.client.naming:142 - current ips:(1) service: DEFAULT_GROUP@@dubbo-engine -> [{"instanceId":"***********#20970#null#dubbo-engine","ip":"***********","port":20970,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dubbo-engine","metadata":{"dubbo.metadata-service.url-params":"{\"prefer.serialization\":\"fastjson2,hessian2\",\"timeout\":\"200000\",\"version\":\"1.0.0\",\"dubbo\":\"2.0.2\",\"release\":\"3.2.5\",\"side\":\"provider\",\"port\":\"20970\",\"protocol\":\"dubbo\"}","dubbo.endpoints":"[{\"port\":20970,\"protocol\":\"dubbo\"}]","dubbo.metadata.revision":"b74f1d5e8a21bfb5792a5106edc088e8","dubbo.metadata.storage-type":"local","timestamp":"1752910585477"},"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000,"ipDeleteTimeout":30000}]
2025-07-19 15:36:26.487 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  com.alibaba.nacos.client.naming:52 - Invoke event groupName: DEFAULT_GROUP, serviceName: dubbo-engine to Listener: org.apache.dubbo.registry.nacos.NacosServiceDiscovery$NacosEventListener@111c5660
2025-07-19 15:36:26.487 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:301 -  [DUBBO] Received instance notification, serviceName: dubbo-engine, instances: 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.487 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:337 -  [DUBBO] 1 unique working revisions: b74f1d5e8a21bfb5792a5106edc088e8 , dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.487 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IStartFlow:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.489 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.489 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IStartFlow:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.489 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IStartFlow:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.489 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener:411 -  [DUBBO] Notify service engine/com.ideal.engine.api.IActivity:1.0.0:null with urls 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.490 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:314 -  [DUBBO] Refreshed invoker size 1, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.490 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.c.m.DefaultMigrationAddressComparator:84 -  [DUBBO] serviceKey:engine/com.ideal.engine.api.IActivity:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.490 [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] INFO  o.a.d.r.client.ServiceDiscoveryRegistryDirectory:338 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: engine/com.ideal.engine.api.IActivity:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20970, dubbo version: 3.2.5, current host: **********
2025-07-19 15:36:26.495 [nacos-grpc-client-executor-************-30257] INFO  com.alibaba.nacos.common.remote.client:63 - [02c3640a-4654-4ac5-8f23-dec39954a650] Ack server push request, request = NotifySubscriberRequest, requestId = ***********-07-19 15:40:26.894 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 16:10:26.946 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 16:40:26.946 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 17:00:26.988 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 17:40:27.002 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 17:50:27.027 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 18:40:27.053 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 18:40:27.068 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 19:30:27.104 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 19:40:27.097 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 20:20:27.140 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 20:39:44.697 [redisson-timer-4-1] ERROR org.redisson.cluster.ClusterConnectionManager:435 - Unable to execute (CLUSTER NODES)
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (CLUSTER NODES), params: [], Redis client: [addr=redis://************:27002]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-07-19 20:40:27.145 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 21:10:27.184 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 21:40:27.200 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 22:00:27.225 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 22:40:27.259 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
2025-07-19 22:50:27.263 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 23:40:27.308 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping mapping caches, latest entries 4, dubbo version: 3.2.5, current host: **********
2025-07-19 23:40:27.310 [Dubbo-framework-cache-refreshing-scheduler-thread-1] INFO  o.a.d.m.AbstractCacheManager$CacheRefreshTask:162 -  [DUBBO] Dumping meta caches, latest entries 12, dubbo version: 3.2.5, current host: **********
