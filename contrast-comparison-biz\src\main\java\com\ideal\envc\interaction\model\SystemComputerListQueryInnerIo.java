package com.ideal.envc.interaction.model;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SystemComputerListQueryInnerIo {

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统ID
     */
    private List<Long> businessSystemIds;

    /**
     * 指定设备ID集合
     */
    private List<Long> appointComputerIds;
    /**
     * 排除设备ID集合
     */
    private List<Long>  excludeComputerIds;

    /**
     * 指定设备IP集合
     */
    private List<String> appointComputerIps;
    /**
     * 排除设备IP集合
     */
    private List<String>  excludeComputerIps;

    /**
     * 当前登录用户ID(标准运维-日常操作,必传)
     */
    private Long userId;

    /**
     * 是否查询业务系统
     */
    private Boolean resultBusinessSystem;



    /**
     * 设备型号ID
     */
    private Long computerModelId;

    /**
     * 主机名称
     */
    private String hostName;


    /**
     * 设备IP
     */
    private String computerIp;


    /**
     * agent业务类型
     */
    private String agentBusinessType;

    /**
     * 数据中心id
     */

    private Long centralId;

    /**
     * 数据中心id
     */

    private List<Long> centralIds;

    /**
     * 是否排除Agent
     */
    private Boolean agentParamIsExclude;

    /**
     * 已选择设备id集合
     */
    private Long[] computerIds;

    /**
     * 回显设备id集合
     */
    private Long[] echoComputerIds;

    /**
     * 业务系统编码
     */
    private String businessSystemCode;

    /**
     * 区域id
     */
    private Long areaId;

    /**
     * 区域名称
     */
    private String areaName;

    private String tags;


    /**
     * agent名称
     */
    private String agentName ;

    /**
     * agent ip
     */
    private String agentIp;

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public List<Long> getAppointComputerIds() {
        return appointComputerIds;
    }

    public void setAppointComputerIds(List<Long> appointComputerIds) {
        this.appointComputerIds = appointComputerIds;
    }

    public List<Long> getExcludeComputerIds() {
        return excludeComputerIds;
    }

    public void setExcludeComputerIds(List<Long> excludeComputerIds) {
        this.excludeComputerIds = excludeComputerIds;
    }

    public List<String> getAppointComputerIps() {
        return appointComputerIps;
    }

    public void setAppointComputerIps(List<String> appointComputerIps) {
        this.appointComputerIps = appointComputerIps;
    }

    public List<String> getExcludeComputerIps() {
        return excludeComputerIps;
    }

    public void setExcludeComputerIps(List<String> excludeComputerIps) {
        this.excludeComputerIps = excludeComputerIps;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Boolean getResultBusinessSystem() {
        return resultBusinessSystem;
    }

    public void setResultBusinessSystem(Boolean resultBusinessSystem) {
        this.resultBusinessSystem = resultBusinessSystem;
    }


    public Long getComputerModelId() {
        return computerModelId;
    }

    public void setComputerModelId(Long computerModelId) {
        this.computerModelId = computerModelId;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getAgentBusinessType() {
        return agentBusinessType;
    }

    public void setAgentBusinessType(String agentBusinessType) {
        this.agentBusinessType = agentBusinessType;
    }

    public Long getCentralId() {
        return centralId;
    }

    public void setCentralId(Long centralId) {
        this.centralId = centralId;
    }

    public Boolean getAgentParamIsExclude() {
        return agentParamIsExclude;
    }

    public void setAgentParamIsExclude(Boolean agentParamIsExclude) {
        this.agentParamIsExclude = agentParamIsExclude;
    }

    public Long[] getComputerIds() {
        return computerIds;
    }

    public void setComputerIds(Long[] computerIds) {
        this.computerIds = computerIds;
    }

    public Long[] getEchoComputerIds() {
        return echoComputerIds;
    }

    public void setEchoComputerIds(Long[] echoComputerIds) {
        this.echoComputerIds = echoComputerIds;
    }

    public String getBusinessSystemCode() {
        return businessSystemCode;
    }

    public void setBusinessSystemCode(String businessSystemCode) {
        this.businessSystemCode = businessSystemCode;
    }

    public Long getAreaId() {
        return areaId;
    }

    public void setAreaId(Long areaId) {
        this.areaId = areaId;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }


    public String getComputerIp() {
        return computerIp;
    }

    public void setComputerIp(String computerIp) {
        this.computerIp = computerIp;
    }


    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentIp() {
        return agentIp;
    }

    public void setAgentIp(String agentIp) {
        this.agentIp = agentIp;
    }

    public List<Long> getBusinessSystemIds() {
        return businessSystemIds;
    }

    public void setBusinessSystemIds(List<Long> businessSystemIds) {
        this.businessSystemIds = businessSystemIds;
    }

    public List<Long> getCentralIds() {
        return centralIds;
    }

    public void setCentralIds(List<Long> centralIds) {
        this.centralIds = centralIds;
    }
}
