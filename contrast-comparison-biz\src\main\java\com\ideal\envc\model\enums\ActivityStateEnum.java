package com.ideal.envc.model.enums;
/**
 * 活动状态
 * <AUTHOR>
 */
public enum ActivityStateEnum {
    // activity state constants
    ACT_STATE_NULL("Null","未运行"),
    ACT_STATE_WAITING("Waiting","等待"),
    ACT_STATE_READY("Ready","就绪"),
      ACT_STATE_RUNNING("Running","运行中"),
      ACT_STATE_FINISH("Finished","完成"),
      ACT_STATE_SKIPPED("Skipped","略过"),
    /**
     * Fail:Skipped:失败:略过
     */
      ACT_STATE_FAIL_SKIPPED("Fail:Skipped","失败:略过"),
      ACT_STATE_FAIL("Fail","失败"),
      /**
       * Fail:Business:失败：业务异常
      */
      ACT_STATE_FAIL_BUSINESS("Fail:Business","失败：业务异常"),
      ACT_STATE_EXECUTE("execute","执行"),
      ACT_STATE_RETRY("Retry","重试"),
      ACT_STATE_OTHER("Other","其他"),
      ACT_STATE_TIMEOUT("Timeout","超时"),
      ACT_STATE_ARRIVED("Arrived","达到"),
      ACT_STATE_OVER("Over","结束"),
      ACT_STATE_MANUAL_RUNNING("ManualRunning","异常处理中"),
      ACT_STATE_MANUAL_FINISH("ManualFinish","人工处理结果"),
      ACT_STATE_ERROR_RETRY("ErrorRetry","异常重试"),
      ACT_STATE_ERROR_SUC("ErrorSuc","异常重试成功"),
      ACT_STATE_CONTINUE_RUNNING("Continue","继续"),
      ACT_STATE_INFOMACTION("InfoMaction","InfoMaction"),
      ACT_STATE_UNRUNED("Not_Allowed_Exec","Not_Allowed_Exec"),
    // 新增Agent宕机监控状态 add by yuyang
      ACT_STATE_AGENT_DISCONNECT("Disconnect","Disconnect"),
      ACT_STATE_AGENT_MANUALDISCONNECT("ManualDisconnect","ManualDisconnect"),
    // 新增活动排队状态
      ACT_STATE_QUEUEUP("QueueUp","排队"),
      ACT_STATE_HANGUP("HangUp","挂起"),
      ACT_STATE_MUTEX_HANGUP("MUTEX_HANGUP","MUTEX_HANGUP"),
      ACT_STATE_DISABLE("Disable","禁用"),
      ACT_AS400_JOBSCREEN_ALTER("AlterAS400","AlterAS400"),
    // 新增活动暂停状态
      ACT_STATE_PAUSED("Paused","Paused");


    private final String code;

    private final String desc;

    ActivityStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
