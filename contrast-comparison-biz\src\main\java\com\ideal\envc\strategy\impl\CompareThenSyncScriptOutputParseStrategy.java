package com.ideal.envc.strategy.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.EngineCompareOutPutDto;
import com.ideal.envc.model.dto.OutputParseResult;
import com.ideal.envc.strategy.OutputParseStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 比对后同步模式-脚本类型输出解析策略
 * <AUTHOR>
 */
@Component
public class CompareThenSyncScriptOutputParseStrategy implements OutputParseStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(CompareThenSyncScriptOutputParseStrategy.class);
    
    @Override
    public OutputParseResult parse(List<EngineActOutputBean> actOutputs,String actDefName) {
        if (actOutputs == null || actOutputs.isEmpty()) {
            return new OutputParseResult(false, "");
        }
        
        EngineActOutputBean engineActOutputBean = actOutputs.get(0);
        try {
            EngineCompareOutPutDto engineCompareOutPutDto = JSON.parseObject(engineActOutputBean.getOutput(), EngineCompareOutPutDto.class);
            boolean ret = engineCompareOutPutDto.getRet() != null ? engineCompareOutPutDto.getRet() : false;
            String content = engineCompareOutPutDto.getCompareResult();
            return new OutputParseResult(ret, content);
        } catch (Exception e) {
            logger.error("解析engineActOutputBean异常", e);
            return new OutputParseResult(false, "");
        }
    }
    
    @Override
    public String getType() {
        return "COMPARE_THEN_SYNC_SCRIPT";
    }
} 