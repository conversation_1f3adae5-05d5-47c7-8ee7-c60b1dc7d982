package com.ideal.envc.model.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.ideal.snowflake.annotion.IdGenerator;

/**
 * 任务对象 ieai_envc_task
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class TaskEntity implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    @IdGenerator
    private Long id;
    /** 方案ID */
    private Long envcPlanId;
    /** 周期表达式 */
    private String cron;
    /** 是否启用（1:启用，0：禁用） */
    private Integer enabled;
    /** 启停状态（0:启动，1：停止） */
    private Integer state;
    /** 源中心ID */
    private Long sourceCenterId;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 定时ID */
    private Long scheduledId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建人ID */
    private Long creatorId;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcPlanId(Long envcPlanId){
        this.envcPlanId = envcPlanId;
    }

    public Long getEnvcPlanId(){
        return envcPlanId;
    }

    public void setCron(String cron){
        this.cron = cron;
    }

    public String getCron(){
        return cron;
    }

    public void setEnabled(Integer enabled){
        this.enabled = enabled;
    }

    public Integer getEnabled(){
        return enabled;
    }

    public void setState(Integer state){
        this.state = state;
    }

    public Integer getState(){
        return state;
    }

    public void setSourceCenterId(Long sourceCenterId){
        this.sourceCenterId = sourceCenterId;
    }

    public Long getSourceCenterId(){
        return sourceCenterId;
    }

    public void setTargetCenterId(Long targetCenterId){
        this.targetCenterId = targetCenterId;
    }

    public Long getTargetCenterId(){
        return targetCenterId;
    }

    public void setScheduledId(Long scheduledId){
        this.scheduledId = scheduledId;
    }

    public Long getScheduledId(){
        return scheduledId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",envcPlanId="+getEnvcPlanId()+
                    ",cron="+getCron()+
                    ",enabled="+getEnabled()+
                    ",state="+getState()+
                    ",sourceCenterId="+getSourceCenterId()+
                    ",targetCenterId="+getTargetCenterId()+
                    ",scheduledId="+getScheduledId()+
                    ",creatorName="+getCreatorName()+
                    ",creatorId="+getCreatorId()+
                    ",createTime="+getCreateTime()+
                    ",updatorId="+getUpdatorId()+
                    ",updatorName="+getUpdatorName()+
                    ",updateTime="+getUpdateTime()+
                "]";
    }
}

