package com.ideal.envc.model.dto;

import java.util.List;

/**
 * 任务级启动DTO
 *
 * <AUTHOR>
 */
public class TaskLevelStartDto extends StartContrastBaseDto {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID列表
     */
    private List<Long> taskIds;
    /** 触发来源 */
    private Integer from;

    public List<Long> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "TaskLevelStartDto{" +
                "taskIds=" + taskIds +
                ", from=" + from +
                '}';
    }
}
