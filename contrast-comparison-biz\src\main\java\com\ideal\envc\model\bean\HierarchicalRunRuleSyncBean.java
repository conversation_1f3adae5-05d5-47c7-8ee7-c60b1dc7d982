package com.ideal.envc.model.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideal.envc.model.entity.RunRuleSyncEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 层次化规则同步对象
 *
 * <AUTHOR>
 */
public class HierarchicalRunRuleSyncBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 节点规则结果ID */
    private Long envcRunRuleId;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /** 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） */
    private Integer result;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;
    /** 耗时 */
    private Long elapsedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcRunRuleId() {
        return envcRunRuleId;
    }

    public void setEnvcRunRuleId(Long envcRunRuleId) {
        this.envcRunRuleId = envcRunRuleId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }
    
    /**
     * 从RunRuleSyncEntity构建HierarchicalRunRuleSyncBean
     * 
     * @param entity RunRuleSyncEntity实体
     * @return HierarchicalRunRuleSyncBean对象
     */
    public static HierarchicalRunRuleSyncBean fromEntity(RunRuleSyncEntity entity) {
        if (entity == null) {
            return null;
        }
        
        HierarchicalRunRuleSyncBean bean = new HierarchicalRunRuleSyncBean();
        bean.setId(entity.getId());
        bean.setEnvcRunRuleId(entity.getEnvcRunRuleId());
        bean.setCreatorId(entity.getCreatorId());
        bean.setCreatorName(entity.getCreatorName());
        bean.setCreateTime(entity.getCreateTime());
        bean.setEndTime(entity.getEndTime());
        bean.setResult(entity.getResult());
        bean.setState(entity.getState());
        bean.setElapsedTime(entity.getElapsedTime());
        
        return bean;
    }

    @Override
    public String toString() {
        return "HierarchicalRunRuleSyncBean{" +
                "id=" + id +
                ", envcRunRuleId=" + envcRunRuleId +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", createTime=" + createTime +
                ", endTime=" + endTime +
                ", result=" + result +
                ", state=" + state +
                ", elapsedTime=" + elapsedTime +
                '}';
    }
}
