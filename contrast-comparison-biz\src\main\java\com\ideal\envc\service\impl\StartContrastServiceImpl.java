package com.ideal.envc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.mapper.StartContrastMapper;
import com.ideal.envc.model.bean.StartContrastQueryBean;
import com.ideal.envc.model.bean.StartPlanBean;
import com.ideal.envc.model.dto.NodeLevelStartDto;
import com.ideal.envc.model.dto.PlanLevelStartDto;
import com.ideal.envc.model.dto.RuleLevelStartDto;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.model.dto.SystemLevelStartDto;
import com.ideal.envc.model.dto.TaskLevelStartDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.StartFromEnums;
import com.ideal.envc.service.IStartContrastBaseService;
import com.ideal.envc.service.IStartContrastCommonService;
import com.ideal.envc.service.IStartContrastService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


/**
 * 一致性比对启动Service实现
 *
 * <AUTHOR>
 */
@Service
public class StartContrastServiceImpl implements IStartContrastService {
    private static final Logger logger = LoggerFactory.getLogger(StartContrastServiceImpl.class);

    private final StartContrastMapper startContrastMapper;
    private final IStartContrastBaseService startContrastBaseService;
    private final IStartContrastCommonService startContrastCommonService;

    public StartContrastServiceImpl(StartContrastMapper startContrastMapper,
                                   IStartContrastBaseService startContrastBaseService,
                                   IStartContrastCommonService startContrastCommonService) {
        this.startContrastMapper = startContrastMapper;
        this.startContrastBaseService = startContrastBaseService;
        this.startContrastCommonService = startContrastCommonService;
    }

    /**
     * 方案级启动
     *
     * @param startDto 方案级启动DTO
     * @param userDto 操作用户
     * @return 启动结果
     */
    @Override
    public StartResult startByPlans(PlanLevelStartDto startDto, UserDto userDto) {
        logger.info("开始方案级启动，参数：{}", startDto);
        StartResult result = new StartResult();

        // 参数校验
        if (startDto == null) {
            result.setSuccess(false);
            result.setMessage("启动参数对象为空");
            logger.error("方案级启动失败，启动参数对象为空");
            return result;
        }
        if (CollectionUtils.isEmpty(startDto.getPlanIds()) && StringUtils.isBlank(startDto.getPlanNameLike())) {
            result.setSuccess(false);
            result.setMessage("需要启动的方案ID集合和方案名称不能同时为空");
            logger.error("方案级启动失败，需要启动的方案ID集合和方案名称不能同时为空");
            return result;
        }

        try {
            // 1. 查询启动信息
            StartContrastQueryBean queryBean = new StartContrastQueryBean();
            queryBean.setPlanIds(startDto.getPlanIds());
            queryBean.setPlanNameLike(startDto.getPlanNameLike());
            queryBean.setSourceCenterId(startDto.getSourceCenterId());
            queryBean.setTargetCenterId(startDto.getTargetCenterId());

            List<StartPlanBean> startPlanList = startContrastMapper.selectStartPlansByQuery(queryBean);
            if (CollectionUtils.isEmpty(startPlanList)) {
                result.setSuccess(false);
                result.setMessage("未找到启动信息");
                logger.error("方案级启动失败，未找到启动信息，查询条件：{}", JSON.toJSONString(queryBean));
                return result;
            }

            // 获取触发来源
            int from = startDto.getFrom() == null ? StartFromEnums.MANUAL_TRIGGER.getCode() : startDto.getFrom();

            // 调用通用处理方法
            return startContrastCommonService.processStart(startPlanList, startDto.getUserId(), startDto.getUserName(), from, "方案级", userDto);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("方案级启动异常：" + e.getMessage());
            logger.error("方案级启动异常", e);
            return result;
        }
    }

    /**
     * 系统级启动
     *
     * @param startDto 系统级启动DTO
     * @param userDto 操作用户
     * @return 启动结果
     */
    @Override
    public StartResult startBySystems(SystemLevelStartDto startDto, UserDto userDto) {
        logger.info("开始系统级启动，参数：{}", startDto);
        StartResult result = new StartResult();

        // 参数校验
        if (startDto == null) {
            result.setSuccess(false);
            result.setMessage("启动参数对象为空");
            logger.error("系统级启动失败，启动参数对象为空");
            return result;
        }
        if (startDto.getPlanId() == null) {
            result.setSuccess(false);
            result.setMessage("方案ID为空");
            logger.error("系统级启动失败，方案ID为空");
            return result;
        }

        try {
            // 1. 查询方案信息
            StartContrastQueryBean queryBean = new StartContrastQueryBean();
            List<Long> planIds = new ArrayList<>();
            planIds.add(startDto.getPlanId());
            queryBean.setPlanIds(planIds);
            queryBean.setSourceCenterId(startDto.getSourceCenterId());
            queryBean.setTargetCenterId(startDto.getTargetCenterId());
            queryBean.setSystemIds(startDto.getSystemIds());

            List<StartPlanBean> startPlanList = startContrastMapper.selectStartPlansByQuery(queryBean);
            if (CollectionUtils.isEmpty(startPlanList)) {
                result.setSuccess(false);
                result.setMessage("未找到启动信息");
                logger.error("系统级启动失败，未找到启动信息，查询条件：{}", JSON.toJSONString(queryBean));
                return result;
            }

            // 获取触发来源
            int from = startDto.getFrom() == null ? StartFromEnums.MANUAL_TRIGGER.getCode() : startDto.getFrom();

            // 调用通用处理方法
            return startContrastCommonService.processStart(startPlanList, startDto.getUserId(), startDto.getUserName(), from, "系统级", userDto);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("系统级启动异常：" + e.getMessage());
            logger.error("系统级启动异常", e);
            return result;
        }
    }

    /**
     * 节点级启动
     *
     * @param startDto 节点级启动DTO
     * @param userDto 操作用户
     * @return 启动结果
     */
    @Override
    public StartResult startByNodes(NodeLevelStartDto startDto, UserDto userDto) {
        logger.info("开始节点级启动，参数：{}", startDto);
        StartResult result = new StartResult();

        // 参数校验
        if (startDto == null) {
            result.setSuccess(false);
            result.setMessage("启动参数对象为空");
            logger.error("节点级启动失败，启动参数对象为空");
            return result;
        }
        if (startDto.getNodeIds() == null) {
            result.setSuccess(false);
            result.setMessage("请选择节点信息");
            logger.error("节点级启动失败，未勾选节点记录为空");
            return result;
        }


        try {
            // 1. 查询方案信息
            StartContrastQueryBean queryBean = new StartContrastQueryBean();
            List<Long> planIds = new ArrayList<>();
            if(startDto.getPlanId() != null && startDto.getPlanId() > 0){
                planIds.add(startDto.getPlanId());
            }
            queryBean.setPlanIds(planIds);
            List<Long> systemIds = new ArrayList<>();
            if(startDto.getSystemId() != null && startDto.getSystemId() > 0){
                systemIds.add(startDto.getSystemId());
            }
            queryBean.setSystemIds(systemIds);
            queryBean.setSourceCenterId(startDto.getSourceCenterId());
            queryBean.setTargetCenterId(startDto.getTargetCenterId());
            queryBean.setNodeIds(startDto.getNodeIds());

            List<StartPlanBean> startPlanList = startContrastMapper.selectStartPlansByQuery(queryBean);
            if (CollectionUtils.isEmpty(startPlanList)) {
                result.setSuccess(false);
                result.setMessage("未找到启动信息");
                logger.error("节点级启动失败，未找到启动信息，查询条件：{}", JSON.toJSONString(queryBean));
                return result;
            }

            // 获取触发来源
            int from = startDto.getFrom() == null ? StartFromEnums.MANUAL_TRIGGER.getCode() : startDto.getFrom();

            // 调用通用处理方法
            return startContrastCommonService.processStart(startPlanList, startDto.getUserId(), startDto.getUserName(), from, "节点级", userDto);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("节点级启动异常：" + e.getMessage());
            logger.error("节点级启动异常", e);
            return result;
        }
    }

    /**
     * 规则级启动
     *
     * @param startDto 规则级启动DTO
     * @param userDto 操作用户
     * @return 启动结果
     */
    @Override
    public StartResult startByRules(RuleLevelStartDto startDto, UserDto userDto) {
        logger.info("开始规则级启动，参数：{}", startDto);
        StartResult result = new StartResult();

        // 参数校验
        if (startDto == null) {
            result.setSuccess(false);
            result.setMessage("启动参数对象为空");
            logger.error("规则级启动失败，启动参数对象为空");
            return result;
        }
        if (startDto.getRuleIds() == null) {
            result.setSuccess(false);
            result.setMessage("未勾选规则信息");
            logger.error("规则级启动失败，规则ID为空");
            return result;
        }

        try {
            // 1. 查询方案信息
            StartContrastQueryBean queryBean = new StartContrastQueryBean();
            List<Long> planIds = new ArrayList<>();
            if(startDto.getPlanId() != null && startDto.getPlanId() > 0){
                planIds.add(startDto.getPlanId());
            }
            queryBean.setPlanIds(planIds);
            List<Long> systemIds = new ArrayList<>();
            if(startDto.getSystemId()!= null && startDto.getSystemId() > 0){
                systemIds.add(startDto.getSystemId());
            }
            queryBean.setSystemIds(systemIds);
            queryBean.setSourceCenterId(startDto.getSourceCenterId());
            queryBean.setTargetCenterId(startDto.getTargetCenterId());
            List<Long> nodeIds = new ArrayList<>();
            if(startDto.getNodeId() != null && startDto.getNodeId() > 0){
                nodeIds.add(startDto.getNodeId());
            }
            queryBean.setNodeIds(nodeIds);
            queryBean.setRuleIds(startDto.getRuleIds());

            List<StartPlanBean> startPlanList = startContrastMapper.selectStartPlansByQuery(queryBean);
            if (CollectionUtils.isEmpty(startPlanList)) {
                result.setSuccess(false);
                result.setMessage("未找到方案信息");
                logger.error("规则级启动失败，未找到方案信息，方案ID：{}", startDto.getPlanId());
                return result;
            }

            // 获取触发来源
            int from = startDto.getFrom() == null ? StartFromEnums.MANUAL_TRIGGER.getCode() : startDto.getFrom();

            // 调用通用处理方法
            return startContrastCommonService.processStart(startPlanList, startDto.getUserId(), startDto.getUserName(), from, "规则级", userDto);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("规则级启动异常：" + e.getMessage());
            logger.error("规则级启动异常", e);
            return result;
        }
    }

    /**
     * 任务级启动
     *
     * @param startDto 任务级启动DTO
     * @param userDto 操作用户
     * @return 启动结果
     */
    @Override
    public StartResult startByTasks(TaskLevelStartDto startDto, UserDto userDto) {
        logger.info("开始任务级启动，参数：{}", startDto);
        StartResult result = new StartResult();

        // 参数校验
        if (startDto == null) {
            result.setSuccess(false);
            result.setMessage("启动参数对象为空");
            logger.error("任务级启动失败，启动参数对象为空");
            return result;
        }
        if (startDto.getTaskIds() == null || startDto.getTaskIds().isEmpty()) {
            result.setSuccess(false);
            result.setMessage("任务ID列表为空");
            logger.error("任务级启动失败，任务ID列表为空");
            return result;
        }

        try {
            // 1. 查询任务信息
            StartContrastQueryBean queryBean = new StartContrastQueryBean();
            queryBean.setTaskIds(startDto.getTaskIds());
            List<StartPlanBean> startPlanList = startContrastMapper.selectStartPlansByQuery(queryBean);
            if (CollectionUtils.isEmpty(startPlanList)) {
                result.setSuccess(false);
                result.setMessage("未找到任务信息");
                logger.error("任务级启动失败，未找到任务信息，任务ID列表：{}", startDto.getTaskIds());
                return result;
            }

            // 获取触发来源
            int from = startDto.getFrom() == null ? StartFromEnums.MANUAL_TRIGGER.getCode() : startDto.getFrom();

            // 调用通用处理方法
            return startContrastCommonService.processStart(startPlanList, startDto.getUserId(), startDto.getUserName(), from, "任务级", userDto);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("任务级启动异常：" + e.getMessage());
            logger.error("任务级启动异常", e);
            return result;
        }
    }


}
