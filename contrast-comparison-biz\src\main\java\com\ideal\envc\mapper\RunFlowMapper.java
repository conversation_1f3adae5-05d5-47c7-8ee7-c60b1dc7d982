package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.RunFlowEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 节点规则流程Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface RunFlowMapper {
    /**
     * 查询节点规则流程
     *
     * @param id 节点规则流程主键
     * @return 节点规则流程
     */
    RunFlowEntity selectRunFlowById(Long id);

    /**
     * 根据流程id查询节点规则流程
     *
     * @param flowId 流程id
     * @return 节点规则流程
     */
    RunFlowEntity selectRunFlowByFlowId(@Param("flowId") Long flowId);

    /**
     * 根据流程id列表批量查询节点规则流程
     *
     * @param flowIds 流程id列表
     * @return 节点规则流程列表
     */
    List<RunFlowEntity> selectRunFlowByFlowIds(@Param("flowIds") List<Long> flowIds);

    /**
     * 查询节点规则流程列表
     *
     * @param runFlow 节点规则流程
     * @return 节点规则流程集合
     */
    List<RunFlowEntity> selectRunFlowList(RunFlowEntity runFlow);

    /**
     * 新增节点规则流程
     *
     * @param runFlow 节点规则流程
     * @return 结果
     */
    int insertRunFlow(RunFlowEntity runFlow);

    /**
     * 修改节点规则流程
     *
     * @param runFlow 节点规则流程
     * @return 结果
     */
    int updateRunFlow(RunFlowEntity runFlow);

    /**
     * 批量更新节点规则流程状态
     *
     * @param flowIds 流程ID列表
     * @param state 状态
     * @param updateOrderTime 更新时间
     * @return 影响行数
     */
    int batchUpdateRunFlowState(@Param("flowIds") List<Long> flowIds,
                               @Param("state") Integer state,
                               @Param("updateOrderTime") Long updateOrderTime);

    /**
     * 修改节点规则流程
     *
     * @param runFlow 节点规则流程
     * @return 结果
     */
    int updateRunFlowOfFirst(RunFlowEntity runFlow);

    /**
     * 删除节点规则流程
     *
     * @param id 节点规则流程主键
     * @return 结果
     */
    int deleteRunFlowById(Long id);

    /**
     * 批量删除节点规则流程
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRunFlowByIds(Long[] ids);
}
