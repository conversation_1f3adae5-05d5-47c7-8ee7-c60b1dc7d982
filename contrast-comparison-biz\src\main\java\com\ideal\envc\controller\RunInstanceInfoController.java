package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.RunInstanceInfoDto;
import com.ideal.envc.model.dto.RunInstanceInfoQueryDto;
import com.ideal.envc.service.IRunInstanceInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 实例详情Controller
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RestController
@RequestMapping("/info")
public class RunInstanceInfoController {
    private final Logger logger = LoggerFactory.getLogger(RunInstanceInfoController.class);

    private final IRunInstanceInfoService runInstanceInfoService;

    public RunInstanceInfoController(IRunInstanceInfoService runInstanceInfoService) {
        this.runInstanceInfoService = runInstanceInfoService;
    }

    /**
     * 查询实例详情列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<RunInstanceInfoDto>> list(TableQueryDto<RunInstanceInfoQueryDto> tableQueryDto) {
        PageInfo<RunInstanceInfoDto> list = runInstanceInfoService.selectRunInstanceInfoList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询实例详情详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<RunInstanceInfoDto> getRunInstanceInfo(@RequestParam(value = "id")Long id) {
        return R.ok(runInstanceInfoService.selectRunInstanceInfoById(id));
    }

    /**
     * 新增保存实例详情
     *
     * @param runInstanceInfoDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody RunInstanceInfoDto runInstanceInfoDto) {
        try {
            if (runInstanceInfoService.insertRunInstanceInfo(runInstanceInfoDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("新增实例详情系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存实例详情
     *
     * @param runInstanceInfoDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody RunInstanceInfoDto runInstanceInfoDto) {
        try {
            runInstanceInfoService.updateRunInstanceInfo(runInstanceInfoDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("修改实例详情系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }


    /**
     * 删除实例详情
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        try {
            runInstanceInfoService.deleteRunInstanceInfoByIds(ids);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("删除实例详情系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
