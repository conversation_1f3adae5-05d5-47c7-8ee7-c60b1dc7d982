package com.ideal.envc.consumer;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.EntegorSendBizUniqueDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.message.center.ISubscriber;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 一致性比对调用引擎下发任务异步结果消费MQ
 * <AUTHOR>
 */
@Component
public class ContrastReceiveTaskSendResultHandler implements ISubscriber {
    private static  final Logger logger = LoggerFactory.getLogger(ContrastReceiveTaskSendResultHandler.class);

    @Override
    public void notice(Object receiveTaskSendResult) {
        if (receiveTaskSendResult != null) {
            String autoDevReceiveTaskSendResult = receiveTaskSendResult.toString();
            if(StringUtils.isNotBlank(autoDevReceiveTaskSendResult)){
                logger.info("receive task send response is {}",receiveTaskSendResult);
                //TODO 此处编写调用service启动方法的逻辑
                EntegorSendBizUniqueDto entegorSendBizUniqueDto = null;
                try{
                    entegorSendBizUniqueDto = JSON.parseObject(autoDevReceiveTaskSendResult,EntegorSendBizUniqueDto.class);
                }catch (Exception e){
                    logger.error("receive task send response is error",e);
                }
                if(entegorSendBizUniqueDto!=null && entegorSendBizUniqueDto.getTaskFlowId()!=null&&entegorSendBizUniqueDto.getIsException()!=null){
                    UserDto user =new UserDto();
                    //处理方法

                }
            }
        }
    }
}
