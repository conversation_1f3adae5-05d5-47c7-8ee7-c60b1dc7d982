package com.ideal.envc.startup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.event.ApplicationStartingEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 应用启动状态监控器
 * 监控应用启动过程中的各个阶段，提供详细的状态信息
 * 
 * <AUTHOR>
 */
@Component
public class StartupMonitor implements ApplicationListener<Object> {
    
    private static final Logger logger = LoggerFactory.getLogger(StartupMonitor.class);
    
    private final AtomicBoolean isStarting = new AtomicBoolean(false);
    private final AtomicBoolean isReady = new AtomicBoolean(false);
    private final AtomicBoolean isFailed = new AtomicBoolean(false);
    
    private long startingTime = 0;
    private long readyTime = 0;
    
    @Override
    public void onApplicationEvent(Object event) {
        if (event instanceof ApplicationStartingEvent) {
            handleApplicationStarting((ApplicationStartingEvent) event);
        } else if (event instanceof ContextRefreshedEvent) {
            handleContextRefreshed((ContextRefreshedEvent) event);
        } else if (event instanceof ApplicationReadyEvent) {
            handleApplicationReady((ApplicationReadyEvent) event);
        } else if (event instanceof ApplicationFailedEvent) {
            handleApplicationFailed((ApplicationFailedEvent) event);
        } else if (event instanceof ContextClosedEvent) {
            handleContextClosed((ContextClosedEvent) event);
        }
    }
    
    /**
     * 处理应用开始启动事件
     */
    private void handleApplicationStarting(ApplicationStartingEvent event) {
        startingTime = System.currentTimeMillis();
        isStarting.set(true);
        logger.info("应用开始启动 - 启动监控器已激活");
    }
    
    /**
     * 处理上下文刷新完成事件
     */
    private void handleContextRefreshed(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            // 只处理根上下文的刷新事件
            logger.info("应用上下文刷新完成 - Bean初始化阶段完成");
        }
    }
    
    /**
     * 处理应用就绪事件
     */
    private void handleApplicationReady(ApplicationReadyEvent event) {
        readyTime = System.currentTimeMillis();
        isReady.set(true);
        isStarting.set(false);
        
        long totalStartupTime = readyTime - startingTime;
        logger.info("应用启动完成 - 总启动时间: {}ms", totalStartupTime);
        
        // 打印启动成功的详细信息
        printStartupSummary(event);
    }
    
    /**
     * 处理应用启动失败事件
     */
    private void handleApplicationFailed(ApplicationFailedEvent event) {
        isFailed.set(true);
        isStarting.set(false);
        
        long failureTime = System.currentTimeMillis();
        long totalTime = startingTime > 0 ? failureTime - startingTime : 0;
        
        logger.error("应用启动失败 - 失败时间: {}ms", totalTime);
        logger.error("失败原因: {}", event.getException().getMessage());
        
        // 提供故障排除建议
        provideTroubleshootingAdvice(event.getException());
    }
    
    /**
     * 处理上下文关闭事件
     */
    private void handleContextClosed(ContextClosedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            // 只处理根上下文的关闭事件
            logger.info("应用上下文已关闭 - 启动监控器已停用");
            reset();
        }
    }
    
    /**
     * 打印启动摘要信息
     */
    private void printStartupSummary(ApplicationReadyEvent event) {
        logger.info("========== 启动摘要 ==========");
        logger.info("应用名称: {}", event.getApplicationContext().getApplicationName());
        logger.info("启动时间: {}ms", readyTime - startingTime);
        logger.info("Bean总数: {}", event.getApplicationContext().getBeanDefinitionCount());
        
        // 打印活跃的配置文件
        String[] activeProfiles = event.getApplicationContext().getEnvironment().getActiveProfiles();
        if (activeProfiles.length > 0) {
            logger.info("活跃配置: {}", String.join(", ", activeProfiles));
        } else {
            logger.info("活跃配置: default");
        }
        
        logger.info("=============================");
    }
    
    /**
     * 提供故障排除建议
     */
    private void provideTroubleshootingAdvice(Throwable exception) {
        logger.error("========== 故障排除建议 ==========");
        
        String exceptionMessage = exception.getMessage();
        String exceptionType = exception.getClass().getSimpleName();
        
        if (exceptionMessage != null) {
            if (exceptionMessage.contains("port") || exceptionMessage.contains("bind")) {
                logger.error("建议: 检查端口是否被占用，或修改application.yml中的server.port配置");
            } else if (exceptionMessage.contains("database") || exceptionMessage.contains("connection")) {
                logger.error("建议: 检查数据库连接配置和数据库服务状态");
            } else if (exceptionMessage.contains("redis")) {
                logger.error("建议: 检查Redis连接配置和Redis服务状态");
            } else if (exceptionMessage.contains("class") || exceptionMessage.contains("ClassNotFoundException")) {
                logger.error("建议: 检查依赖包是否完整，执行 mvn clean install 重新构建");
            } else if (exceptionMessage.contains("configuration") || exceptionMessage.contains("property")) {
                logger.error("建议: 检查配置文件格式和必要的配置项是否正确");
            }
        }
        
        logger.error("通用建议:");
        logger.error("1. 检查日志文件获取详细错误信息");
        logger.error("2. 确认所有外部依赖服务正常运行");
        logger.error("3. 验证配置文件格式和内容正确性");
        logger.error("4. 检查JVM内存设置是否充足");
        logger.error("================================");
    }
    
    /**
     * 重置监控状态
     */
    private void reset() {
        isStarting.set(false);
        isReady.set(false);
        isFailed.set(false);
        startingTime = 0;
        readyTime = 0;
    }
    
    /**
     * 获取当前启动状态
     */
    public StartupStatus getStartupStatus() {
        if (isFailed.get()) {
            return StartupStatus.FAILED;
        } else if (isReady.get()) {
            return StartupStatus.READY;
        } else if (isStarting.get()) {
            return StartupStatus.STARTING;
        } else {
            return StartupStatus.NOT_STARTED;
        }
    }
    
    /**
     * 获取启动耗时
     */
    public long getStartupDuration() {
        if (readyTime > 0 && startingTime > 0) {
            return readyTime - startingTime;
        }
        return 0;
    }
    
    /**
     * 启动状态枚举
     */
    public enum StartupStatus {
        NOT_STARTED("未开始"),
        STARTING("启动中"),
        READY("已就绪"),
        FAILED("启动失败");
        
        private final String description;
        
        StartupStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
