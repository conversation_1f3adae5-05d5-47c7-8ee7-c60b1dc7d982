package com.ideal.envc.model.enums;

/**
 * 任务处理器枚举类
 * <AUTHOR>
 */
public enum JobHandlerEnum {
    /**
     * 对比任务处理器
     */
    CONTRAST_JOB_HANDLER("contrastJobHandler", "对比任务处理器");

    private final String handlerName;
    private final String desc;

    JobHandlerEnum(String handlerName, String desc) {
        this.handlerName = handlerName;
        this.desc = desc;
    }

    public String getHandlerName() {
        return handlerName;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据处理器名称获取枚举实例
     * @param handlerName 处理器名称
     * @return 枚举实例
     */
    public static JobHandlerEnum getByHandlerName(String handlerName) {
        for (JobHandlerEnum value : values()) {
            if (value.getHandlerName().equals(handlerName)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断处理器名称是否有效
     * @param handlerName 处理器名称
     * @return 是否有效
     */
    public static boolean isValidHandlerName(String handlerName) {
        return getByHandlerName(handlerName) != null;
    }
} 