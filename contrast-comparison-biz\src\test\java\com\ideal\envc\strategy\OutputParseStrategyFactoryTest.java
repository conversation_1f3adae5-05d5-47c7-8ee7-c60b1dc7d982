package com.ideal.envc.strategy;

import com.ideal.envc.model.enums.RuleITypeEnums;
import com.ideal.envc.model.enums.RuleModelEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * OutputParseStrategyFactory单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class OutputParseStrategyFactoryTest {

    @Mock
    private OutputParseStrategy mockStrategy1;

    @Mock
    private OutputParseStrategy mockStrategy2;

    private OutputParseStrategyFactory factory;

    @BeforeEach
    void setUp() {
        // 设置Mock策略的类型
        when(mockStrategy1.getType()).thenReturn(RuleModelEnum.COMPARE.name() + "_" + RuleITypeEnums.FILE.name());
        when(mockStrategy2.getType()).thenReturn(RuleModelEnum.SYNC.name() + "_" + RuleITypeEnums.DIRECTORY.name());

        // 创建策略列表
        List<OutputParseStrategy> strategies = Arrays.asList(mockStrategy1, mockStrategy2);
        factory = new OutputParseStrategyFactory(strategies);
    }

    @Test
    @DisplayName("测试获取策略 - 文件比对模式")
    void testGetStrategy_CompareFile() {
        // 准备测试数据
        Integer model = RuleModelEnum.COMPARE.ordinal();
        Long type = RuleITypeEnums.FILE.getCode();

        // 执行测试方法
        OutputParseStrategy result = factory.getStrategy(model, type);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockStrategy1, result);
    }

    @Test
    @DisplayName("测试获取策略 - 目录同步模式")
    void testGetStrategy_SyncDirectory() {
        // 准备测试数据
        Integer model = RuleModelEnum.SYNC.ordinal();
        Long type = RuleITypeEnums.DIRECTORY.getCode();

        // 执行测试方法
        OutputParseStrategy result = factory.getStrategy(model, type);

        // 验证结果
        assertNotNull(result);
        assertEquals(mockStrategy2, result);
    }

    @Test
    @DisplayName("测试获取策略 - 不存在的策略")
    void testGetStrategy_NonExistentStrategy() {
        // 准备测试数据 - 使用有效的枚举值但组合不存在
        Integer model = 0; // COMPARE
        Long type = 2L; // SCRIPT

        // 执行测试方法
        OutputParseStrategy result = factory.getStrategy(model, type);

        // 验证结果
        assertNull(result);
    }

    @Test
    @DisplayName("测试获取策略 - 空策略列表")
    void testGetStrategy_EmptyStrategyList() {
        // 创建空的策略列表
        List<OutputParseStrategy> emptyStrategies = Arrays.asList();
        OutputParseStrategyFactory emptyFactory = new OutputParseStrategyFactory(emptyStrategies);

        // 准备测试数据
        Integer model = RuleModelEnum.COMPARE.ordinal();
        Long type = RuleITypeEnums.FILE.getCode();

        // 执行测试方法
        OutputParseStrategy result = emptyFactory.getStrategy(model, type);

        // 验证结果
        assertNull(result);
    }

    @Test
    @DisplayName("测试获取策略 - 脚本比对模式")
    void testGetStrategy_CompareScript() {
        // 准备测试数据
        Integer model = RuleModelEnum.COMPARE.ordinal();
        Long type = RuleITypeEnums.SCRIPT.getCode();

        // 执行测试方法
        OutputParseStrategy result = factory.getStrategy(model, type);

        // 验证结果 - 由于没有注册脚本比对策略，应该返回null
        assertNull(result);
    }

    @Test
    @DisplayName("测试获取策略 - 文件同步模式")
    void testGetStrategy_SyncFile() {
        // 准备测试数据
        Integer model = RuleModelEnum.SYNC.ordinal();
        Long type = RuleITypeEnums.FILE.getCode();

        // 执行测试方法
        OutputParseStrategy result = factory.getStrategy(model, type);

        // 验证结果 - 由于没有注册文件同步策略，应该返回null
        assertNull(result);
    }

    @Test
    @DisplayName("测试获取策略 - 边界值测试")
    void testGetStrategy_BoundaryValues() {
        // 测试边界值 - 使用不存在的策略组合
        assertNull(factory.getStrategy(999, 999L)); // 使用不存在的枚举值
        assertNull(factory.getStrategy(-1, -1L)); // 使用负数
        assertNull(factory.getStrategy(null, null));
    }
} 