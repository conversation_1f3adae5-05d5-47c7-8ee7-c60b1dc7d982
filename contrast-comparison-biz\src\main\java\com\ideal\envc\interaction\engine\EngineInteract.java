package com.ideal.envc.interaction.engine;

import com.ideal.engine.api.IStartFlow;
import com.ideal.engine.dto.FlowApiDto;
import com.ideal.engine.dto.FlowResultApiDto;
import com.ideal.engine.dto.WorkFlowOperationApiDto;
import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.enums.TaskFlowOperationTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 与引擎对接交互集中处理类
 * <AUTHOR>
 *
 */
@Component
public class EngineInteract {
    private static final Logger logger = LoggerFactory.getLogger(EngineInteract.class);
    private final IStartFlow startFlow;

    public EngineInteract(IStartFlow startFlow) {
        this.startFlow = startFlow;
    }

    /**
     * 终止工作流 暂停工作流 恢复工作流
     *
     * @param flowIds  工作流id集合
     * @param operationType  终止1 暂停2  恢复3
     * @return  返回终止失败的工作流id，分隔
     */
    public String engineKillPauseResumeFlow(Long[] flowIds,Long operationType) throws EngineServiceException {

        if(flowIds == null || flowIds.length == 0){
            throw new EngineServiceException(TaskFlowOperationTypeEnum.getColorByValue(operationType,null)+"工作流操作flowIds为空!");
        }
        String multipleFlowIds = Arrays.toString(flowIds);
        logger.info("flowIds:{},Workflow operation begins!",multipleFlowIds);

        WorkFlowOperationApiDto workFlowOperationApiDto=getFlowResult(flowIds);

        FlowResultApiDto flowResultApiDto = null;
        try {
            if (operationType.equals(TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode())){
                flowResultApiDto=startFlow.killFlow(workFlowOperationApiDto);

            }else if(operationType.equals(TaskFlowOperationTypeEnum.TASK_FLOW_CHANGE.getCode())){
                flowResultApiDto=startFlow.pauseFlow(workFlowOperationApiDto);

            }else if(operationType.equals(TaskFlowOperationTypeEnum.TASK_FLOW_MAINTENANCE.getCode())){
                flowResultApiDto=startFlow.resumeFlow(workFlowOperationApiDto);

            }
        } catch (Exception e) {
            logger.error("Failed to terminate workflow push engine!");
            throw new EngineServiceException(e);
        }
        if(flowResultApiDto==null ){
            throw new EngineServiceException("任务、工作流 "+ TaskFlowOperationTypeEnum.getColorByValue(operationType,null)+"处理失败！详情：响应为空！");
        }
        String  iFlowIds=    getFlowResultIds(flowResultApiDto);
        logger.info("flowIds:{},Workflow operation is over!operate fail workflow is {}",multipleFlowIds,iFlowIds);
        return iFlowIds;
    }

    private   WorkFlowOperationApiDto   getFlowResult(Long[] flowIds) {
        WorkFlowOperationApiDto workFlowOperationApiDto=new WorkFlowOperationApiDto();
        workFlowOperationApiDto.setFlowIds(flowIds);
        workFlowOperationApiDto.setDbType(ContrastConstants.CONTRAST_PRJ_TYPE);
        return workFlowOperationApiDto;
    }

    /**
     * 整理执行失败工作流
     *
     * @param flowResultApiDto  引擎返回数据
     * @return 返回执行失败工作流id
     */
    private  String getFlowResultIds(FlowResultApiDto flowResultApiDto) {
        StringBuilder sbFlowIds = new StringBuilder();
        String flowIds="";
        Boolean resultFlag=    flowResultApiDto.getResultFlag();
        //整体标识如果是失败，代表整体下面一定有操作失败的流程。如果整体是成功，则全部成功
        if(Boolean.FALSE.equals(resultFlag)){
            List<FlowApiDto> listFlowApiDto=   flowResultApiDto.getListFlowDto();
            for (FlowApiDto apiDto : listFlowApiDto) {
                if (Boolean.FALSE.equals(apiDto.getSuccessFlag())) {
                    sbFlowIds.append(apiDto.getFlowId()).append(",");
                }
            }
            flowIds= sbFlowIds.toString();
        }
        return flowIds;
    }
}
