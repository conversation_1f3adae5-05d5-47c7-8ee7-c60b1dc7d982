package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.model.dto.RunRuleContentDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IRunRuleContentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 节点规则内容Controller单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RunRuleContentControllerTest {

    @Mock
    private IRunRuleContentService runRuleContentService;

    @InjectMocks
    private RunRuleContentController runRuleContentController;

    private RunRuleContentDto runRuleContentDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        runRuleContentDto = new RunRuleContentDto();
        runRuleContentDto.setId(1L);
        runRuleContentDto.setEnvcRunRuleId(100L);
        runRuleContentDto.setContent("测试内容");
    }

    @Nested
    @DisplayName("save方法测试")
    class SaveTest {
        @Test
        @DisplayName("新增节点规则内容成功时应返回成功响应")
        void should_return_success_when_save_success() {
            // Given
            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class))).thenReturn(1);

            // When
            R<Void> result = runRuleContentController.save(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("新增节点规则内容失败时应返回失败响应")
        void should_return_fail_when_save_fail() {
            // Given
            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class))).thenReturn(0);

            // When
            R<Void> result = runRuleContentController.save(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.ADD_FAIL.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.ADD_FAIL.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("新增节点规则内容发生异常时应返回系统错误响应")
        void should_return_system_error_when_save_exception() {
            // Given
            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class)))
                    .thenThrow(new RuntimeException("数据库连接异常"));

            // When
            R<Void> result = runRuleContentController.save(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("新增节点规则内容参数为null时应返回系统错误响应")
        void should_return_system_error_when_save_null_param() {
            // Given
            when(runRuleContentService.insertRunRuleContent(null))
                    .thenThrow(new NullPointerException("参数不能为空"));

            // When
            R<Void> result = runRuleContentController.save(null);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(null);
        }

        @Test
        @DisplayName("新增节点规则内容参数错误时应返回参数错误响应")
        void should_return_param_error_when_save_invalid_param() {
            // Given
            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class)))
                    .thenThrow(new IllegalArgumentException("参数格式错误"));

            // When
            R<Void> result = runRuleContentController.save(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.ADD_PARAM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.ADD_PARAM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(runRuleContentDto);
        }
    }

    @Nested
    @DisplayName("update方法测试")
    class UpdateTest {
        @Test
        @DisplayName("修改节点规则内容成功时应返回成功响应")
        void should_return_success_when_update_success() {
            // Given
            when(runRuleContentService.updateRunRuleContent(any(RunRuleContentDto.class))).thenReturn(1);

            // When
            R<Void> result = runRuleContentController.update(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).updateRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("修改节点规则内容失败时应返回成功响应")
        void should_return_success_when_update_fail() {
            // Given
            when(runRuleContentService.updateRunRuleContent(any(RunRuleContentDto.class))).thenReturn(0);

            // When
            R<Void> result = runRuleContentController.update(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).updateRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("修改节点规则内容发生异常时应返回系统错误响应")
        void should_return_system_error_when_update_exception() {
            // Given
            when(runRuleContentService.updateRunRuleContent(any(RunRuleContentDto.class)))
                    .thenThrow(new RuntimeException("数据库连接异常"));

            // When
            R<Void> result = runRuleContentController.update(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).updateRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("修改节点规则内容参数为null时应返回系统错误响应")
        void should_return_system_error_when_update_null_param() {
            // Given
            when(runRuleContentService.updateRunRuleContent(null))
                    .thenThrow(new NullPointerException("参数不能为空"));

            // When
            R<Void> result = runRuleContentController.update(null);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).updateRunRuleContent(null);
        }

        @Test
        @DisplayName("修改节点规则内容参数错误时应返回参数错误响应")
        void should_return_param_error_when_update_invalid_param() {
            // Given
            when(runRuleContentService.updateRunRuleContent(any(RunRuleContentDto.class)))
                    .thenThrow(new IllegalArgumentException("参数格式错误"));

            // When
            R<Void> result = runRuleContentController.update(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.UPDATE_PARAM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).updateRunRuleContent(runRuleContentDto);
        }
    }

    @Nested
    @DisplayName("remove方法测试")
    class RemoveTest {
        @Test
        @DisplayName("删除节点规则内容成功时应返回成功响应")
        void should_return_success_when_remove_success() {
            // Given
            Long[] ids = {1L, 2L, 3L};
            when(runRuleContentService.deleteRunRuleContentByIds(any(Long[].class))).thenReturn(3);

            // When
            R<Void> result = runRuleContentController.remove(ids);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).deleteRunRuleContentByIds(ids);
        }

        @Test
        @DisplayName("删除节点规则内容失败时应返回成功响应")
        void should_return_success_when_remove_fail() {
            // Given
            Long[] ids = {1L, 2L, 3L};
            when(runRuleContentService.deleteRunRuleContentByIds(any(Long[].class))).thenReturn(0);

            // When
            R<Void> result = runRuleContentController.remove(ids);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).deleteRunRuleContentByIds(ids);
        }

        @Test
        @DisplayName("删除节点规则内容发生异常时应返回系统错误响应")
        void should_return_system_error_when_remove_exception() {
            // Given
            Long[] ids = {1L, 2L, 3L};
            when(runRuleContentService.deleteRunRuleContentByIds(any(Long[].class)))
                    .thenThrow(new RuntimeException("数据库连接异常"));

            // When
            R<Void> result = runRuleContentController.remove(ids);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).deleteRunRuleContentByIds(ids);
        }

        @Test
        @DisplayName("删除节点规则内容参数为null时应返回参数错误响应")
        void should_return_param_error_when_remove_null_param() {
            // When
            R<Void> result = runRuleContentController.remove(null);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.DELETE_PARAM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService, never()).deleteRunRuleContentByIds(any());
        }

        @Test
        @DisplayName("删除节点规则内容参数为空数组时应返回参数错误响应")
        void should_return_param_error_when_remove_empty_array() {
            // Given
            Long[] ids = {};

            // When
            R<Void> result = runRuleContentController.remove(ids);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.DELETE_PARAM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService, never()).deleteRunRuleContentByIds(any());
        }

        @Test
        @DisplayName("删除节点规则内容参数错误时应返回参数错误响应")
        void should_return_param_error_when_remove_invalid_param() {
            // Given
            Long[] ids = {1L, 2L, 3L};
            when(runRuleContentService.deleteRunRuleContentByIds(any(Long[].class)))
                    .thenThrow(new IllegalArgumentException("参数格式错误"));

            // When
            R<Void> result = runRuleContentController.remove(ids);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.DELETE_PARAM_ERROR.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).deleteRunRuleContentByIds(ids);
        }
    }

    @Nested
    @DisplayName("异常处理测试")
    static class ExceptionHandlingTest {
        @ParameterizedTest
        @MethodSource("provideExceptionScenarios")
        @DisplayName("各种异常场景测试")
        void should_handle_various_exceptions(Exception exception, String expectedCode, String expectedMessage) {
            // Given
            IRunRuleContentService runRuleContentService = mock(IRunRuleContentService.class);
            RunRuleContentController controller = new RunRuleContentController(runRuleContentService);
            RunRuleContentDto dto = new RunRuleContentDto();
            dto.setId(1L);
            dto.setEnvcRunRuleId(100L);
            dto.setContent("测试内容");
            
            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class)))
                    .thenThrow(exception);

            // When
            R<Void> result = controller.save(dto);

            // Then
            assertNotNull(result);
            assertEquals(expectedCode, result.getCode());
            assertEquals(expectedMessage, result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(dto);
        }

        static Stream<Arguments> provideExceptionScenarios() {
            return Stream.of(
                    Arguments.of(
                            new RuntimeException("数据库连接异常"),
                            ResponseCodeEnum.SYSTEM_ERROR.getCode(),
                            ResponseCodeEnum.SYSTEM_ERROR.getDesc()
                    ),
                    Arguments.of(
                            new IllegalArgumentException("参数错误"),
                            ResponseCodeEnum.ADD_PARAM_ERROR.getCode(),
                            ResponseCodeEnum.ADD_PARAM_ERROR.getDesc()
                    ),
                    Arguments.of(
                            new NullPointerException("空指针异常"),
                            ResponseCodeEnum.SYSTEM_ERROR.getCode(),
                            ResponseCodeEnum.SYSTEM_ERROR.getDesc()
                    ),
                    Arguments.of(
                            new Exception("通用异常"),
                            ResponseCodeEnum.SYSTEM_ERROR.getCode(),
                            ResponseCodeEnum.SYSTEM_ERROR.getDesc()
                    )
            );
        }
    }

    @Nested
    @DisplayName("边界条件测试")
    class BoundaryConditionTest {
        @Test
        @DisplayName("保存操作返回负数时应返回失败响应")
        void should_return_fail_when_save_returns_negative() {
            // Given
            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class))).thenReturn(-1);

            // When
            R<Void> result = runRuleContentController.save(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.ADD_FAIL.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.ADD_FAIL.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("更新操作返回负数时应返回成功响应")
        void should_return_success_when_update_returns_negative() {
            // Given
            when(runRuleContentService.updateRunRuleContent(any(RunRuleContentDto.class))).thenReturn(-1);

            // When
            R<Void> result = runRuleContentController.update(runRuleContentDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).updateRunRuleContent(runRuleContentDto);
        }

        @Test
        @DisplayName("删除操作返回负数时应返回成功响应")
        void should_return_success_when_remove_returns_negative() {
            // Given
            Long[] ids = {1L};
            when(runRuleContentService.deleteRunRuleContentByIds(any(Long[].class))).thenReturn(-1);

            // When
            R<Void> result = runRuleContentController.remove(ids);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).deleteRunRuleContentByIds(ids);
        }
    }

    @Nested
    @DisplayName("数据验证测试")
    class DataValidationTest {
        @Test
        @DisplayName("保存包含特殊字符的内容时应正常处理")
        void should_handle_special_characters_in_content() {
            // Given
            RunRuleContentDto specialDto = new RunRuleContentDto();
            specialDto.setId(1L);
            specialDto.setEnvcRunRuleId(100L);
            specialDto.setContent("特殊字符内容：!@#$%^&*()_+{}|:<>?[]\\;'\",./<script>alert('test')</script>");

            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class))).thenReturn(1);

            // When
            R<Void> result = runRuleContentController.save(specialDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(specialDto);
        }

        @Test
        @DisplayName("保存空内容时应正常处理")
        void should_handle_empty_content() {
            // Given
            RunRuleContentDto emptyDto = new RunRuleContentDto();
            emptyDto.setId(1L);
            emptyDto.setEnvcRunRuleId(100L);
            emptyDto.setContent("");

            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class))).thenReturn(1);

            // When
            R<Void> result = runRuleContentController.save(emptyDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(emptyDto);
        }

        @Test
        @DisplayName("保存超长内容时应正常处理")
        void should_handle_long_content() {
            // Given
            RunRuleContentDto longDto = new RunRuleContentDto();
            longDto.setId(1L);
            longDto.setEnvcRunRuleId(100L);
            StringBuilder longContent = new StringBuilder();
            for (int i = 0; i < 1000; i++) {
                longContent.append("这是一段很长的测试内容");
            }
            longDto.setContent(longContent.toString());

            when(runRuleContentService.insertRunRuleContent(any(RunRuleContentDto.class))).thenReturn(1);

            // When
            R<Void> result = runRuleContentController.save(longDto);

            // Then
            assertNotNull(result);
            assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
            assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
            assertNull(result.getData());

            verify(runRuleContentService).insertRunRuleContent(longDto);
        }
    }
} 