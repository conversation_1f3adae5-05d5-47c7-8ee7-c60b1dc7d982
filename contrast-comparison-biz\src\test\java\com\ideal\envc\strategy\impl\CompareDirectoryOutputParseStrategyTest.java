package com.ideal.envc.strategy.impl;

import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.OutputParseResult;
import com.ideal.envc.model.enums.ActivityTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

/**
 * CompareDirectoryOutputParseStrategy单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CompareDirectoryOutputParseStrategyTest {

    @InjectMocks
    private CompareDirectoryOutputParseStrategy strategy;

    private EngineActOutputBean engineActOutputBean;

    @BeforeEach
    void setUp() {
        engineActOutputBean = new EngineActOutputBean();
    }

    @Test
    @DisplayName("测试解析输出 - 空输出列表")
    void testParse_EmptyOutputList() {
        // 准备测试数据
        List<EngineActOutputBean> actOutputs = new ArrayList<>();
        String actDefName = ActivityTypeEnum.COMPARE_CONTENT.getCode();

        // 执行测试方法
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("测试解析输出 - null输出列表")
    void testParse_NullOutputList() {
        // 准备测试数据
        List<EngineActOutputBean> actOutputs = null;
        String actDefName = ActivityTypeEnum.COMPARE_CONTENT.getCode();

        // 执行测试方法
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("测试解析输出 - 文件比对活动，成功场景")
    void testParse_CompareContent_Success() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(ContrastConstants.RET, "true");
        outputMap.put(ContrastConstants.COMPARE_RESULT, "比对成功");
        
        String outputJson = "{\"ret\":\"true\",\"compareResult\":\"比对成功\"}";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.COMPARE_CONTENT.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isRet());
            assertTrue(result.getContent().contains("比对成功"));
        }
    }

    @Test
    @DisplayName("测试解析输出 - 文件比对活动，失败场景")
    void testParse_CompareContent_Failure() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(ContrastConstants.RET, "false");
        outputMap.put("err", "比对失败");
        
        String outputJson = "{\"ret\":\"false\",\"err\":\"比对失败\"}";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.COMPARE_CONTENT.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isRet());
            assertTrue(result.getContent().contains("比对失败"));
        }
    }

    @Test
    @DisplayName("测试解析输出 - 文件同步活动，成功场景")
    void testParse_SyncContent_Success() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(ContrastConstants.RET, "true");
        outputMap.put(ContrastConstants.SYNC_RESULT, "同步成功");
        
        String outputJson = "{\"ret\":\"true\",\"syncResult\":\"同步成功\"}";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.SYNC_CONTENT.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isRet());
            assertTrue(result.getContent().contains("同步成功"));
        }
    }

    @Test
    @DisplayName("测试解析输出 - 脚本活动，stdout输出")
    void testParse_ShellCmd_Stdout() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(ContrastConstants.RET, "true");
        outputMap.put(ContrastConstants.STDOUT, "脚本执行成功");
        
        String outputJson = "{\"ret\":\"true\",\"stdout\":\"脚本执行成功\"}";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.SHELL_CMD.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isRet());
            assertTrue(result.getContent().contains("脚本执行成功"));
        }
    }

    @Test
    @DisplayName("测试解析输出 - 脚本活动，stderr输出")
    void testParse_ShellCmd_Stderr() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(ContrastConstants.RET, "false");
        outputMap.put(ContrastConstants.STDERR, "脚本执行错误");
        
        String outputJson = "{\"ret\":\"false\",\"stderr\":\"脚本执行错误\"}";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.SHELL_CMD.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isRet());
            assertTrue(result.getContent().contains("脚本执行错误"));
        }
    }

    @Test
    @DisplayName("测试解析输出 - 脚本活动，err输出")
    void testParse_ShellCmd_Err() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(ContrastConstants.RET, "false");
        outputMap.put(ContrastConstants.ERR, "系统错误");
        
        String outputJson = "{\"ret\":\"false\",\"err\":\"系统错误\"}";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.SHELL_CMD.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isRet());
            assertTrue(result.getContent().contains("系统错误"));
        }
    }

    @Test
    @DisplayName("测试解析输出 - 无效JSON格式")
    void testParse_InvalidJson() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put("err", "invalid json format");
        
        engineActOutputBean.setOutput("invalid json format");
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.COMPARE_CONTENT.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isRet());
            assertTrue(result.getContent().contains("invalid json format"));
        }
    }

    @Test
    @DisplayName("测试解析输出 - 空输出内容")
    void testParse_EmptyOutput() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        
        engineActOutputBean.setOutput("");
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = ActivityTypeEnum.COMPARE_CONTENT.getCode();

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.isRet());
            assertEquals("{\"content\":\"\",\"ret\":false}", result.getContent());
        }
    }

    @Test
    @DisplayName("测试获取策略类型")
    void testGetType() {
        // 执行测试方法
        String type = strategy.getType();

        // 验证结果
        assertEquals("COMPARE_DIRECTORY", type);
    }

    @Test
    @DisplayName("测试解析输出 - 未知活动类型")
    void testParse_UnknownActivityType() {
        // 准备测试数据
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(ContrastConstants.RET, "true");
        outputMap.put(ContrastConstants.STDOUT, "未知活动输出");
        
        String outputJson = "{\"ret\":\"true\",\"stdout\":\"未知活动输出\"}";
        engineActOutputBean.setOutput(outputJson);
        
        List<EngineActOutputBean> actOutputs = Arrays.asList(engineActOutputBean);
        String actDefName = "UNKNOWN_ACTIVITY";

        try (MockedStatic<ContrastToolUtils> mockedContrastToolUtils = mockStatic(ContrastToolUtils.class)) {
            // Mock静态方法
            mockedContrastToolUtils.when(() -> ContrastToolUtils.analysisOutPut(anyString())).thenReturn(outputMap);

            // 执行测试方法
            OutputParseResult result = strategy.parse(actOutputs, actDefName);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isRet());
            assertTrue(result.getContent().contains("未知活动输出"));
        }
    }
} 