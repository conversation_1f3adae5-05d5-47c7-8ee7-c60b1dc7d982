package com.ideal.envc.service;

import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IStartContrastCommonBaseService {
    /**
     * 基于层次化实例数据封装出任务流DTO列表
     *
     * @param hierarchicalInstanceList 层次化实例数据列表
     * @return 任务流DTO列表
     */
    List<StartTaskFlowDto> buildTaskFlowDtoList(List<HierarchicalRunInstanceBean> hierarchicalInstanceList, UserDto userDto);


    /**
     * 启动对比并更新状态
     *
     * @param startTaskFlowDtoList 任务流DTO列表
     * @param describe 描述信息
     * @return 启动结果
     * @throws EngineServiceException 引擎服务异常
     */
    boolean startContrastSendAndUpdateState(List<StartTaskFlowDto> startTaskFlowDtoList,String describe) throws EngineServiceException;

}
