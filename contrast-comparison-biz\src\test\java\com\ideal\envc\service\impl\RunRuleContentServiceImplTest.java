package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.envc.mapper.RunRuleContentMapper;
import com.ideal.envc.model.dto.RunRuleContentDto;
import com.ideal.envc.model.dto.RunRuleContentQueryDto;
import com.ideal.envc.model.entity.RunRuleContentEntity;
import com.ideal.envc.service.IRunRuleContentService;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 节点规则内容Service单元测试
 */
@ExtendWith(MockitoExtension.class)
public class RunRuleContentServiceImplTest {

    @Mock
    private RunRuleContentMapper runRuleContentMapper;

    @InjectMocks
    private RunRuleContentServiceImpl runRuleContentService;

    private RunRuleContentEntity mockEntity;
    private RunRuleContentDto mockDto;
    private List<RunRuleContentEntity> mockEntityList;

    @BeforeEach
    void setUp() {
        // 初始化测试实体对象
        mockEntity = new RunRuleContentEntity();
        mockEntity.setId(1L);
        mockEntity.setEnvcRunRuleId(101L);
        mockEntity.setContent("测试内容");

        // 初始化DTO对象
        mockDto = new RunRuleContentDto();
        mockDto.setId(1L);
        mockDto.setEnvcRunRuleId(101L);
        mockDto.setContent("测试内容");

        // 初始化实体列表
        mockEntityList = new ArrayList<>();
        mockEntityList.add(mockEntity);
    }

    @Test
    @DisplayName("测试根据ID查询节点规则内容")
    void testSelectRunRuleContentById() {
        // 模拟Mapper层方法返回
        when(runRuleContentMapper.selectRunRuleContentById(anyLong())).thenReturn(mockEntity);
        // 模拟BeanUtils.copy方法
        try (MockedStatic<BeanUtils> beanUtilsMockedStatic = mockStatic(BeanUtils.class)) {
            beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(RunRuleContentEntity.class), eq(RunRuleContentDto.class)))
                    .thenReturn(mockDto);

            // 执行测试方法
            RunRuleContentDto result = runRuleContentService.selectRunRuleContentById(1L);

            // 断言结果
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals(101L, result.getEnvcRunRuleId());
            assertEquals("测试内容", result.getContent());

            // 验证方法调用
            verify(runRuleContentMapper, times(1)).selectRunRuleContentById(1L);
            beanUtilsMockedStatic.verify(() -> BeanUtils.copy(any(RunRuleContentEntity.class), eq(RunRuleContentDto.class)), times(1));
        }
    }

    @Test
    @DisplayName("测试查询节点规则内容列表")
    void testSelectRunRuleContentList() {
        // 准备测试数据
        RunRuleContentQueryDto queryDto = new RunRuleContentQueryDto();
        queryDto.setEnvcRunRuleId(101L);
        
        // 模拟Mapper层方法返回
        when(runRuleContentMapper.selectRunRuleContentList(any(RunRuleContentEntity.class))).thenReturn(mockEntityList);

        // 模拟BeanUtils.copy方法
        try (MockedStatic<BeanUtils> beanUtilsMockedStatic = mockStatic(BeanUtils.class)) {
            List<RunRuleContentDto> dtoList = new ArrayList<>();
            dtoList.add(mockDto);
            beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)))
                    .thenReturn(dtoList);

            // 执行测试方法
            List<RunRuleContentDto> result = runRuleContentService.selectRunRuleContentList(queryDto);

            // 断言结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).getId());
            assertEquals(101L, result.get(0).getEnvcRunRuleId());
            assertEquals("测试内容", result.get(0).getContent());

            // 验证方法调用
            verify(runRuleContentMapper, times(1)).selectRunRuleContentList(any(RunRuleContentEntity.class));
            beanUtilsMockedStatic.verify(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)), times(1));
        }
    }

    @Test
    @DisplayName("测试查询节点规则内容列表 - 空查询条件")
    void testSelectRunRuleContentListWithNullQuery() {
        // 模拟Mapper层方法返回
        when(runRuleContentMapper.selectRunRuleContentList(any(RunRuleContentEntity.class))).thenReturn(mockEntityList);

        // 模拟BeanUtils.copy方法
        try (MockedStatic<BeanUtils> beanUtilsMockedStatic = mockStatic(BeanUtils.class)) {
            List<RunRuleContentDto> dtoList = new ArrayList<>();
            dtoList.add(mockDto);
            beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)))
                    .thenReturn(dtoList);

            // 执行测试方法
            List<RunRuleContentDto> result = runRuleContentService.selectRunRuleContentList(null);

            // 断言结果
            assertNotNull(result);
            assertEquals(1, result.size());

            // 验证方法调用
            verify(runRuleContentMapper, times(1)).selectRunRuleContentList(any(RunRuleContentEntity.class));
            beanUtilsMockedStatic.verify(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)), times(1));
        }
    }

    @Test
    @DisplayName("测试查询节点规则内容分页")
    void testSelectRunRuleContentPage() {
        // 准备测试参数
        RunRuleContentQueryDto queryDto = new RunRuleContentQueryDto();
        int pageNum = 1;
        int pageSize = 10;

        // 模拟Mapper层方法返回
        when(runRuleContentMapper.selectRunRuleContentList(any())).thenReturn(mockEntityList);

        // 模拟PageHelper.startPage方法
        try (MockedStatic<com.github.pagehelper.page.PageMethod> pageMethodMockedStatic = mockStatic(com.github.pagehelper.page.PageMethod.class);
             MockedStatic<BeanUtils> beanUtilsMockedStatic = mockStatic(BeanUtils.class)) {
            
            // 模拟PageMethod.startPage
            pageMethodMockedStatic.when(() -> com.github.pagehelper.page.PageMethod.startPage(pageNum, pageSize))
                .thenReturn(new com.github.pagehelper.Page<>(pageNum, pageSize));

            List<RunRuleContentDto> dtoList = new ArrayList<>();
            dtoList.add(mockDto);
            beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)))
                    .thenReturn(dtoList);

            // 执行测试方法
            PageInfo<RunRuleContentDto> result = runRuleContentService.selectRunRuleContentPage(queryDto, pageNum, pageSize);

            // 断言结果
            assertNotNull(result);
            assertEquals(1, result.getList().size());
            assertEquals(mockDto, result.getList().get(0));

            // 验证方法调用
            verify(runRuleContentMapper).selectRunRuleContentList(any());
            pageMethodMockedStatic.verify(() -> com.github.pagehelper.page.PageMethod.startPage(pageNum, pageSize));
            beanUtilsMockedStatic.verify(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)));
        }
    }

    @Test
    @DisplayName("测试根据规则ID查询节点规则内容列表")
    void testSelectRunRuleContentListByRuleId() {
        // 模拟Mapper层方法返回
        when(runRuleContentMapper.selectRunRuleContentListByRuleId(anyLong())).thenReturn(mockEntityList);

        // 模拟BeanUtils.copy方法
        try (MockedStatic<BeanUtils> beanUtilsMockedStatic = mockStatic(BeanUtils.class)) {
            List<RunRuleContentDto> dtoList = new ArrayList<>();
            dtoList.add(mockDto);
            beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)))
                    .thenReturn(dtoList);

            // 执行测试方法
            List<RunRuleContentDto> result = runRuleContentService.selectRunRuleContentListByRuleId(101L);

            // 断言结果
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).getId());
            assertEquals(101L, result.get(0).getEnvcRunRuleId());
            assertEquals("测试内容", result.get(0).getContent());

            // 验证方法调用
            verify(runRuleContentMapper, times(1)).selectRunRuleContentListByRuleId(101L);
            beanUtilsMockedStatic.verify(() -> BeanUtils.copy(any(List.class), eq(RunRuleContentDto.class)), times(1));
        }
    }

    @Test
    @DisplayName("测试新增节点规则内容")
    void testInsertRunRuleContent() {
        // 模拟BeanUtils.copy方法
        try (MockedStatic<BeanUtils> beanUtilsMockedStatic = mockStatic(BeanUtils.class)) {
            beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(RunRuleContentDto.class), eq(RunRuleContentEntity.class)))
                    .thenReturn(mockEntity);

            // 模拟Mapper层方法返回
            when(runRuleContentMapper.insertRunRuleContent(any(RunRuleContentEntity.class))).thenReturn(1);

            // 执行测试方法
            int result = runRuleContentService.insertRunRuleContent(mockDto);

            // 断言结果
            assertEquals(1, result);

            // 验证方法调用
            beanUtilsMockedStatic.verify(() -> BeanUtils.copy(any(RunRuleContentDto.class), eq(RunRuleContentEntity.class)), times(1));
            verify(runRuleContentMapper, times(1)).insertRunRuleContent(any(RunRuleContentEntity.class));
        }
    }

    @Test
    @DisplayName("测试修改节点规则内容")
    void testUpdateRunRuleContent() {
        // 模拟BeanUtils.copy方法
        try (MockedStatic<BeanUtils> beanUtilsMockedStatic = mockStatic(BeanUtils.class)) {
            beanUtilsMockedStatic.when(() -> BeanUtils.copy(any(RunRuleContentDto.class), eq(RunRuleContentEntity.class)))
                    .thenReturn(mockEntity);

            // 模拟Mapper层方法返回
            when(runRuleContentMapper.updateRunRuleContent(any(RunRuleContentEntity.class))).thenReturn(1);

            // 执行测试方法
            int result = runRuleContentService.updateRunRuleContent(mockDto);

            // 断言结果
            assertEquals(1, result);

            // 验证方法调用
            beanUtilsMockedStatic.verify(() -> BeanUtils.copy(any(RunRuleContentDto.class), eq(RunRuleContentEntity.class)), times(1));
            verify(runRuleContentMapper, times(1)).updateRunRuleContent(any(RunRuleContentEntity.class));
        }
    }

    @Test
    @DisplayName("测试批量删除节点规则内容")
    void testDeleteRunRuleContentByIds() {
        // 准备测试参数
        Long[] ids = new Long[]{1L, 2L, 3L};

        // 模拟Mapper层方法返回
        when(runRuleContentMapper.deleteRunRuleContentByIds(any(Long[].class))).thenReturn(3);

        // 执行测试方法
        int result = runRuleContentService.deleteRunRuleContentByIds(ids);

        // 断言结果
        assertEquals(3, result);

        // 验证方法调用
        verify(runRuleContentMapper, times(1)).deleteRunRuleContentByIds(ids);
    }

    @Test
    @DisplayName("测试删除节点规则内容")
    void testDeleteRunRuleContentById() {
        // 模拟Mapper层方法返回
        when(runRuleContentMapper.deleteRunRuleContentById(anyLong())).thenReturn(1);

        // 执行测试方法
        int result = runRuleContentService.deleteRunRuleContentById(1L);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleContentMapper, times(1)).deleteRunRuleContentById(1L);
    }

    @Test
    @DisplayName("测试根据规则ID删除节点规则内容")
    void testDeleteRunRuleContentByRuleId() {
        // 模拟Mapper层方法返回
        when(runRuleContentMapper.deleteRunRuleContentByRuleId(anyLong())).thenReturn(1);

        // 执行测试方法
        int result = runRuleContentService.deleteRunRuleContentByRuleId(101L);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleContentMapper, times(1)).deleteRunRuleContentByRuleId(101L);
    }
} 