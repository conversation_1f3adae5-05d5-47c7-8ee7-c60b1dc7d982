package com.ideal.envc.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;

/**
 * 方案绑定系统列表数据传输对象
 *
 * <AUTHOR>
 */
public class PlanSystemListDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 关系ID 主键
     */
    private Long id;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统描述
     */
    private String businessSystemDesc;

    /**
     * 关系绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 绑定关系人
     */
    private String creatorName;

    /**
     * 业务系统描述
     */
    private String businessSystemCode;

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemDesc() {
        return businessSystemDesc;
    }

    public void setBusinessSystemDesc(String businessSystemDesc) {
        this.businessSystemDesc = businessSystemDesc;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBusinessSystemCode() {
        return businessSystemCode;
    }

    public void setBusinessSystemCode(String businessSystemCode) {
        this.businessSystemCode = businessSystemCode;
    }

    @Override
    public String toString() {
        return "PlanSystemListDto{" +
                "id=" + id +
                ", planId=" + planId +
                ", businessSystemId=" + businessSystemId +
                ", businessSystemName='" + businessSystemName + '\'' +
                ", businessSystemDesc='" + businessSystemDesc + '\'' +
                ", createTime=" + createTime +
                ", creatorName='" + creatorName + '\'' +
                ", businessSystemCode='" + businessSystemCode + '\'' +
                '}';
    }
}
