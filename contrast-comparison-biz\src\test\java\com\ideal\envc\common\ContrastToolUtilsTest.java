package com.ideal.envc.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ideal.envc.config.Base64SecurityConfig;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.quartz.CronExpression;
import sun.misc.BASE64Decoder;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ContrastToolUtils单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class ContrastToolUtilsTest {

    @Test
    @DisplayName("Test valid Base64 decoding")
    public void testGetFromBase64_ValidBase64() {
        String input = "SGVsbG8gV29ybGQ="; // "Hello World" in base64
        String result = ContrastToolUtils.getFromBase64(input);
        assertEquals("Hello World", result);
    }

    @Test
    @DisplayName("Test invalid Base64 string")
    public void testGetFromBase64_InvalidBase64() {
        String input = "InvalidBase64!@#";
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @Test
    @DisplayName("Test null input")
    public void testGetFromBase64_NullInput() {
        String result = ContrastToolUtils.getFromBase64(null);
        assertNull(result);
    }

    @Test
    @DisplayName("Test empty string input")
    public void testGetFromBase64_EmptyInput() {
        String result = ContrastToolUtils.getFromBase64("");
        assertNull(result);
    }

    @Test
    @DisplayName("Test blank string input")
    public void testGetFromBase64_BlankInput() {
        String result = ContrastToolUtils.getFromBase64("   ");
        assertNull(result);
    }

    @Test
    @DisplayName("Test input length exceeds security limit")
    public void testGetFromBase64_InputLengthExceedsLimit() {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set small input length limit for testing
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(10);

            String input = "SGVsbG8gV29ybGQgVGhpcyBpcyBhIGxvbmcgc3RyaW5n"; // Length exceeds 10
            String result = ContrastToolUtils.getFromBase64(input);
            assertNull(result);
        }
    }

    @Test
    @DisplayName("Test decoded content size exceeds security limit")
    public void testGetFromBase64_DecodedSizeExceedsLimit() {
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            // Set small decoded size limit for testing
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(1000);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(5);

            String input = "SGVsbG8gV29ybGQ="; // "Hello World" decoded length is 11, exceeds limit 5
            String result = ContrastToolUtils.getFromBase64(input);
            assertNull(result);
        }
    }

    @Test
    @DisplayName("Test Base64 decoding exception")
    public void testGetFromBase64_DecodingException() {
        String input = "Invalid!@#$%^&*()"; // Use obviously invalid BASE64 format input
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @Test
    @DisplayName("Test Base64 string with invalid characters")
    public void testGetFromBase64_InvalidCharacters() {
        String input = "SGVsbG8gV29ybGQ=!"; // Added an invalid character !
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @Test
    @DisplayName("Test edge case: only padding characters in base64")
    public void testGetFromBase64_OnlyPaddingCharacters() {
        String input = "====";
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @ParameterizedTest
    @DisplayName("Test various invalid Base64 inputs")
    @ValueSource(strings = {"", "   ", "Invalid!@#", "SGVsbG8=!", "123", "abc", "!@#$%^&*()"})
    public void testGetFromBase64_InvalidInputs(String input) {
        String result = ContrastToolUtils.getFromBase64(input);
        assertNull(result);
    }

    @ParameterizedTest
    @DisplayName("Test various valid Base64 inputs")
    @MethodSource("validBase64Inputs")
    public void testGetFromBase64_ValidInputs(String input, String expected) {
        String result = ContrastToolUtils.getFromBase64(input);
        assertEquals(expected, result);
    }

    private static Stream<Object[]> validBase64Inputs() {
        return Stream.of(
            new Object[]{"SGVsbG8=", "Hello"},
            new Object[]{"V29ybGQ=", "World"},
            new Object[]{"SGVsbG8gV29ybGQ=", "Hello World"},
            new Object[]{"VGVzdA==", "Test"},
            new Object[]{"MTIz", "123"}
        );
    }

    @Test
    @DisplayName("Test Base64SecurityConfig configuration reading")
    public void testGetFromBase64_SecurityConfigIntegration() {
        // Test configuration reading in normal cases
        try (MockedStatic<Base64SecurityConfig> mockedConfig = mockStatic(Base64SecurityConfig.class)) {
            mockedConfig.when(Base64SecurityConfig::getMaxInputLength).thenReturn(1000);
            mockedConfig.when(Base64SecurityConfig::getMaxDecodedSize).thenReturn(1000);

            String input = "SGVsbG8="; // "Hello"
            String result = ContrastToolUtils.getFromBase64(input);
            assertEquals("Hello", result);
        }
    }

    @Test
    @DisplayName("测试有效JSON的分析输出")
    public void testAnalysisOutPut_ValidJson() {
        String jsonOutput = "{\"stdout\":\"SGVsbG8=\",\"stderr\":\"RXJyb3I=\",\"lastLine\":\"TGFzdExpbmU=\"}";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(jsonOutput);

        assertNotNull(result);
        assertEquals("Hello", result.get("stdout"));
        assertEquals("Error", result.get("stderr"));
        assertEquals("LastLine", result.get("lastLine"));
    }

    @Test
    @DisplayName("测试无效JSON的分析输出")
    public void testAnalysisOutPut_InvalidJson() {
        String invalidOutput = "invalid json content";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(invalidOutput);

        assertNotNull(result);
        assertEquals(invalidOutput, result.get("err"));
    }

    @Test
    @DisplayName("测试null输入的分析输出")
    public void testAnalysisOutPut_NullInput() {
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("测试空字符串输入的分析输出")
    public void testAnalysisOutPut_EmptyInput() {
        Map<String, Object> result = ContrastToolUtils.analysisOutPut("");
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("测试不包含Base64字段的JSON")
    public void testAnalysisOutPut_JsonWithoutBase64Fields() {
        String jsonOutput = "{\"status\":\"success\",\"code\":200}";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(jsonOutput);

        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertEquals(200, result.get("code"));
    }

    @Test
    @DisplayName("测试包含无效Base64的JSON")
    public void testAnalysisOutPut_JsonWithInvalidBase64() {
        // 测试包含无效base64的情况
        String jsonOutput = "{\"stdout\":\"Invalid!@#\",\"stderr\":\"RXJyb3I=\",\"lastLine\":\"TGFzdExpbmU=\"}";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(jsonOutput);

        assertNotNull(result);
        assertNull(result.get("stdout")); // 无效base64应返回null
        assertEquals("Error", result.get("stderr"));
        assertEquals("LastLine", result.get("lastLine"));
    }

    @Test
    @DisplayName("测试JSON解析异常")
    public void testAnalysisOutPut_JsonParsingException() {
        // 使用无效的JSON字符串，确保会触发JsonProcessingException
        String invalidJsonOutput = "{invalid json format}";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(invalidJsonOutput);

        // 验证结果
        assertNotNull(result);
        assertEquals(invalidJsonOutput, result.get("err"));
    }

    @Test
    public void testAnalysisOutPut_AllFieldsPresent() {
        // 测试所有字段都存在的情况
        String jsonOutput = "{\"stdout\":\"SGVsbG8=\",\"stderr\":\"RXJyb3I=\",\"lastLine\":\"TGFzdExpbmU=\",\"status\":\"success\"}";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(jsonOutput);
        
        assertNotNull(result);
        assertEquals("Hello", result.get("stdout"));
        assertEquals("Error", result.get("stderr"));
        assertEquals("LastLine", result.get("lastLine"));
        assertEquals("success", result.get("status"));
    }

    @Test
    public void testAnalysisOutPut_NonStringFields() {
        // 测试字段存在但不是字符串类型的情况
        String jsonOutput = "{\"stdout\":123,\"stderr\":true,\"lastLine\":null}";
        
        try {
            Map<String, Object> result = ContrastToolUtils.analysisOutPut(jsonOutput);
            
            assertNotNull(result);
            // 非字符串类型不会尝试进行base64解码，应保持原值
            assertEquals(123, result.get("stdout"));
            assertEquals(true, result.get("stderr"));
            assertNull(result.get("lastLine"));
        } catch (ClassCastException e) {
            // 如果代码中没有类型检查，会抛出ClassCastException，这也是一种有效结果
            assertTrue(e.getMessage().contains("Integer cannot be cast to java.lang.String"));
        }
    }

    @Test
    public void testAnalysisOutPut_MixedTypes() {
        // 测试包含不同类型值的JSON
        String jsonOutput = "{\"stdout\":\"SGVsbG8=\",\"count\":123,\"isValid\":true,\"data\":null}";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(jsonOutput);
        
        assertNotNull(result);
        assertEquals("Hello", result.get("stdout"));
        assertEquals(123, result.get("count"));
        assertEquals(true, result.get("isValid"));
        assertNull(result.get("data"));
    }

    @Test
    public void testAnalysisOutPut_NestedObjects() {
        // 测试包含嵌套对象的JSON
        String jsonOutput = "{\"stdout\":\"SGVsbG8=\",\"metadata\":{\"version\":\"1.0\",\"type\":\"log\"}}";
        Map<String, Object> result = ContrastToolUtils.analysisOutPut(jsonOutput);
        
        assertNotNull(result);
        assertEquals("Hello", result.get("stdout"));
        assertNotNull(result.get("metadata"));
        assertTrue(result.get("metadata") instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> metadata = (Map<String, Object>) result.get("metadata");
        assertEquals("1.0", metadata.get("version"));
        assertEquals("log", metadata.get("type"));
    }

    @Test
    @DisplayName("测试有效日期的秒数差异计算")
    public void testGetSecondsDifference_ValidDate() {
        Date pastDate = new Date(System.currentTimeMillis() - 5000); // 5秒前
        long difference = ContrastToolUtils.getSecondsDifference(pastDate);
        assertTrue(difference >= 4 && difference <= 6); // 允许1秒误差
    }

    @Test
    @DisplayName("测试null日期抛出异常")
    public void testGetSecondsDifference_NullDate() {
        assertThrows(IllegalArgumentException.class, () -> {
            ContrastToolUtils.getSecondsDifference(null);
        });
    }

    @Test
    @DisplayName("测试未来日期的秒数差异计算")
    public void testGetSecondsDifference_FutureDate() {
        Date futureDate = new Date(System.currentTimeMillis() + 5000); // 5秒后
        long difference = ContrastToolUtils.getSecondsDifference(futureDate);
        assertTrue(difference >= -6 && difference <= -4); // 允许1秒误差，结果应为负数
    }

    @Test
    @DisplayName("测试当前日期的秒数差异计算")
    public void testGetSecondsDifference_CurrentDate() {
        // 测试当前时间的情况
        Date currentDate = new Date();
        long difference = ContrastToolUtils.getSecondsDifference(currentDate);
        assertTrue(difference >= -1 && difference <= 1); // 允许1秒误差
    }

    @Test
    @DisplayName("测试较早日期的秒数差异计算")
    public void testGetSecondsDifference_OldDate() {
        // 测试较早的时间
        Date oldDate = new Date(System.currentTimeMillis() - 3600000); // 1小时前
        long difference = ContrastToolUtils.getSecondsDifference(oldDate);
        assertTrue(difference >= 3599 && difference <= 3601); // 允许1秒误差
    }

    @Test
    @DisplayName("测试有效输入的字符串转Long数组")
    public void testConvertStringToLongArray_ValidInput() {
        String input = "1,2,3,4,5";
        Long[] result = ContrastToolUtils.convertStringToLongArray(input);

        assertNotNull(result);
        assertEquals(5, result.length);
        assertArrayEquals(new Long[]{1L, 2L, 3L, 4L, 5L}, result);
    }

    @Test
    @DisplayName("测试包含空格的字符串转Long数组")
    public void testConvertStringToLongArray_WithSpaces() {
        String input = " 1 , 2 , 3 ";
        Long[] result = ContrastToolUtils.convertStringToLongArray(input);

        assertNotNull(result);
        assertEquals(3, result.length);
        assertArrayEquals(new Long[]{1L, 2L, 3L}, result);
    }

    @Test
    @DisplayName("测试null输入的字符串转Long数组")
    public void testConvertStringToLongArray_NullInput() {
        Long[] result = ContrastToolUtils.convertStringToLongArray(null);
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    @DisplayName("测试空字符串输入的字符串转Long数组")
    public void testConvertStringToLongArray_EmptyInput() {
        Long[] result = ContrastToolUtils.convertStringToLongArray("");
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    @DisplayName("测试空白字符串输入的字符串转Long数组")
    public void testConvertStringToLongArray_BlankInput() {
        Long[] result = ContrastToolUtils.convertStringToLongArray("   ");
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    @DisplayName("测试包含无效数字的字符串转Long数组")
    public void testConvertStringToLongArray_InvalidNumber() {
        // 测试包含非数字的情况
        assertThrows(NumberFormatException.class, () -> {
            ContrastToolUtils.convertStringToLongArray("1,2,abc,3");
        });
    }

    @Test
    public void testConvertStringToLongArray_LargeNumbers() {
        // 测试大数字
        String input = "9223372036854775807,1,2";
        Long[] result = ContrastToolUtils.convertStringToLongArray(input);
        
        assertNotNull(result);
        assertEquals(3, result.length);
        assertEquals(Long.MAX_VALUE, result[0].longValue());
    }

    @Test
    public void testSubtract_ValidArrays() {
        Long[] array1 = {1L, 2L, 3L, 4L, 5L};
        Long[] array2 = {3L, 4L, 6L};
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(3, result.length);
        assertArrayEquals(new Long[]{1L, 2L, 5L}, result);
    }

    @Test
    public void testSubtract_EmptySecondArray() {
        Long[] array1 = {1L, 2L, 3L};
        Long[] array2 = {};
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(3, result.length);
        assertArrayEquals(new Long[]{1L, 2L, 3L}, result);
    }

    @Test
    public void testSubtract_NoCommonElements() {
        Long[] array1 = {1L, 2L, 3L};
        Long[] array2 = {4L, 5L, 6L};
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(3, result.length);
        assertArrayEquals(new Long[]{1L, 2L, 3L}, result);
    }

    @Test
    public void testSubtract_EmptyFirstArray() {
        Long[] array1 = {};
        Long[] array2 = {1L, 2L, 3L};
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testSubtract_BothEmptyArrays() {
        Long[] array1 = {};
        Long[] array2 = {};
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testSubtract_DuplicatesInFirstArray() {
        Long[] array1 = {1L, 2L, 2L, 3L, 3L};
        Long[] array2 = {2L};
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        // 由于结果是通过Stream API过滤得到的，会保留重复元素
        assertEquals(3, result.length);
        
        // 检查结果中包含的元素
        int count1 = 0, count3 = 0;
        for (Long value : result) {
            if (value.equals(1L)) count1++;
            if (value.equals(3L)) count3++;
        }
        assertEquals(1, count1);
        assertEquals(2, count3); // 3应该出现两次
    }

    @Test
    public void testSubtract_AllElementsInSecondArray() {
        // 测试第二个数组包含第一个数组所有元素的情况
        Long[] array1 = {1L, 2L, 3L};
        Long[] array2 = {1L, 2L, 3L, 4L, 5L};
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testSubtract_WithNullValues() {
        // 测试数组中包含null值的情况
        Long[] array1 = {1L, null, 3L, 4L};
        Long[] array2 = {2L, null, 4L};
        
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(2, result.length);
        assertTrue(contains(result, 1L));
        assertTrue(contains(result, 3L));
        assertFalse(contains(result, null));
        assertFalse(contains(result, 2L));
        assertFalse(contains(result, 4L));
    }

    @Test
    public void testSubtract_AllNullValues() {
        // 测试全部为null值的情况
        Long[] array1 = {null, null};
        Long[] array2 = {null};
        
        Long[] result = ContrastToolUtils.subtract(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testIntersection_ValidArrays() {
        Long[] array1 = {1L, 2L, 3L, 4L, 5L};
        Long[] array2 = {3L, 4L, 6L, 7L};
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(2, result.length);
        // 注意：Set的toArray()方法不保证顺序，所以需要分别检查
        assertTrue(contains(result, 3L));
        assertTrue(contains(result, 4L));
    }

    @Test
    public void testIntersection_NoCommonElements() {
        Long[] array1 = {1L, 2L, 3L};
        Long[] array2 = {4L, 5L, 6L};
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testIntersection_EmptyArrays() {
        Long[] array1 = {};
        Long[] array2 = {};
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testIntersection_EmptyFirstArray() {
        Long[] array1 = {};
        Long[] array2 = {1L, 2L, 3L};
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testIntersection_EmptySecondArray() {
        Long[] array1 = {1L, 2L, 3L};
        Long[] array2 = {};
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    public void testIntersection_DuplicatesInArrays() {
        Long[] array1 = {1L, 2L, 2L, 3L};
        Long[] array2 = {2L, 2L, 4L};
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(1, result.length);
        assertEquals(Long.valueOf(2L), result[0]);
    }

    @Test
    public void testIntersection_IdenticalArrays() {
        // 测试两个数组完全相同的情况
        Long[] array1 = {1L, 2L, 3L};
        Long[] array2 = {1L, 2L, 3L};
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(3, result.length);
        assertTrue(contains(result, 1L));
        assertTrue(contains(result, 2L));
        assertTrue(contains(result, 3L));
    }

    @Test
    public void testIntersection_WithNullValues() {
        // 测试数组中包含null值的情况
        Long[] array1 = {1L, null, 3L, 4L};
        Long[] array2 = {2L, null, 4L};
        
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(2, result.length);
        assertTrue(contains(result, null));
        assertTrue(contains(result, 4L));
        assertFalse(contains(result, 1L));
        assertFalse(contains(result, 2L));
        assertFalse(contains(result, 3L));
    }

    @Test
    public void testIntersection_AllNullValues() {
        // 测试全部为null值的情况
        Long[] array1 = {null, null};
        Long[] array2 = {null};
        
        Long[] result = ContrastToolUtils.intersection(array1, array2);
        
        assertNotNull(result);
        assertEquals(1, result.length);
        assertTrue(contains(result, null));
    }

    @Test
    @DisplayName("测试有效的Cron表达式验证")
    public void testIsValidCronExpression_ValidCron() {
        String validCron = "0 0 12 * * ?";
        boolean result = ContrastToolUtils.isValidCronExpression(validCron);
        assertTrue(result);
    }

    @Test
    @DisplayName("测试无效的Cron表达式验证")
    public void testIsValidCronExpression_InvalidCron() {
        String invalidCron = "invalid cron expression";
        boolean result = ContrastToolUtils.isValidCronExpression(invalidCron);
        assertFalse(result);
    }

    @Test
    @DisplayName("测试null输入的Cron表达式验证")
    public void testIsValidCronExpression_NullInput() {
        boolean result = ContrastToolUtils.isValidCronExpression(null);
        assertFalse(result);
    }

    @Test
    @DisplayName("测试空字符串输入的Cron表达式验证")
    public void testIsValidCronExpression_EmptyInput() {
        boolean result = ContrastToolUtils.isValidCronExpression("");
        assertFalse(result);
    }

    @Test
    @DisplayName("测试空白字符串输入的Cron表达式验证")
    public void testIsValidCronExpression_BlankInput() {
        boolean result = ContrastToolUtils.isValidCronExpression("   ");
        assertFalse(result);
    }

    @Test
    @DisplayName("测试Cron表达式验证异常情况")
    public void testIsValidCronExpression_ExceptionCase() {
        // 模拟CronExpression.isValidExpression抛出异常的情况
        try (MockedStatic<CronExpression> mockedCronExpression = mockStatic(CronExpression.class)) {
            mockedCronExpression.when(() -> CronExpression.isValidExpression(anyString()))
                .thenThrow(new RuntimeException("模拟验证失败"));

            boolean result = ContrastToolUtils.isValidCronExpression("0 0 12 * * ?");
            assertFalse(result);
        }
    }

    @Test
    @DisplayName("测试复杂有效的Cron表达式验证")
    public void testIsValidCronExpression_ComplexValidCron() {
        // 测试复杂但有效的cron表达式
        String complexCron = "0 15 10 ? * MON-FRI";
        boolean result = ContrastToolUtils.isValidCronExpression(complexCron);
        assertTrue(result);
    }

    @Test
    @DisplayName("测试有效Cron表达式的验证")
    public void testValidateCronExpression_ValidCron() {
        String validCron = "0 0 12 * * ?";
        assertDoesNotThrow(() -> {
            ContrastToolUtils.validateCronExpression(validCron);
        });
    }

    @Test
    @DisplayName("测试无效Cron表达式抛出异常")
    public void testValidateCronExpression_InvalidCron() {
        String invalidCron = "invalid cron expression";
        assertThrows(RuntimeException.class, () -> {
            ContrastToolUtils.validateCronExpression(invalidCron);
        });
    }

    @Test
    @DisplayName("测试null输入Cron表达式抛出异常")
    public void testValidateCronExpression_NullInput() {
        assertThrows(RuntimeException.class, () -> {
            ContrastToolUtils.validateCronExpression(null);
        });
    }

    @Test
    @DisplayName("测试空字符串输入Cron表达式抛出异常")
    public void testValidateCronExpression_EmptyInput() {
        assertThrows(RuntimeException.class, () -> {
            ContrastToolUtils.validateCronExpression("");
        });
    }

    @Test
    @DisplayName("测试空白字符串输入Cron表达式抛出异常")
    public void testValidateCronExpression_BlankInput() {
        assertThrows(RuntimeException.class, () -> {
            ContrastToolUtils.validateCronExpression("   ");
        });
    }

    @Test
    @DisplayName("测试Cron表达式验证异常情况")
    public void testValidateCronExpression_ExceptionCase() {
        // 模拟CronExpression.isValidExpression抛出异常的情况
        try (MockedStatic<CronExpression> mockedCronExpression = mockStatic(CronExpression.class)) {
            mockedCronExpression.when(() -> CronExpression.isValidExpression(anyString()))
                .thenThrow(new RuntimeException("模拟验证失败"));

            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                ContrastToolUtils.validateCronExpression("0 0 12 * * ?");
            });
            assertTrue(exception.getMessage().contains("cron表达式格式不正确"));
        }
    }

    @Test
    @DisplayName("测试Cron表达式验证返回false的情况")
    public void testValidateCronExpression_InvalidExpressionReturnsFalse() {
        // 测试CronExpression.isValidExpression返回false的情况
        try (MockedStatic<CronExpression> mockedCronExpression = mockStatic(CronExpression.class)) {
            mockedCronExpression.when(() -> CronExpression.isValidExpression(anyString()))
                .thenReturn(false);

            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                ContrastToolUtils.validateCronExpression("0 0 12 * * ?");
            });
            // 检查异常消息是否符合预期，不使用完全匹配
            assertTrue(exception.getMessage().contains("cron表达式格式不正确"));
        }
    }

    @Test
    @DisplayName("测试零秒的时间格式化")
    public void testFormatElapsedTime_ZeroSeconds() {
        String result = ContrastToolUtils.formatElapsedTime(0L);
        assertEquals("0秒", result);
    }

    @Test
    @DisplayName("测试null输入的时间格式化")
    public void testFormatElapsedTime_NullInput() {
        String result = ContrastToolUtils.formatElapsedTime(null);
        assertEquals("0秒", result);
    }

    @Test
    @DisplayName("测试负数输入的时间格式化")
    public void testFormatElapsedTime_NegativeInput() {
        String result = ContrastToolUtils.formatElapsedTime(-1000L);
        assertEquals("0秒", result);
    }

    @Test
    @DisplayName("测试仅秒数的时间格式化")
    public void testFormatElapsedTime_SecondsOnly() {
        String result = ContrastToolUtils.formatElapsedTime(30000L); // 30秒
        assertEquals("30秒", result);
    }

    @Test
    @DisplayName("测试分钟和秒数的时间格式化")
    public void testFormatElapsedTime_MinutesAndSeconds() {
        String result = ContrastToolUtils.formatElapsedTime(90000L); // 1分30秒
        assertEquals("1分30秒", result);
    }

    @Test
    @DisplayName("测试小时分钟秒数的时间格式化")
    public void testFormatElapsedTime_HoursMinutesSeconds() {
        String result = ContrastToolUtils.formatElapsedTime(3661000L); // 1时1分1秒
        assertEquals("1时1分1秒", result);
    }

    @Test
    @DisplayName("测试天小时分钟秒数的时间格式化")
    public void testFormatElapsedTime_DaysHoursMinutesSeconds() {
        String result = ContrastToolUtils.formatElapsedTime(90061000L); // 1天1时1分1秒
        assertEquals("1天1时1分1秒", result);
    }

    @Test
    public void testFormatElapsedTime_ExactlyOneDay() {
        // 测试刚好1天的情况
        String result = ContrastToolUtils.formatElapsedTime(86400000L); // 1天0时0分0秒
        assertEquals("1天0时0分0秒", result);
    }

    @Test
    public void testFormatElapsedTime_ExactlyOneHour() {
        // 测试刚好1小时的情况
        String result = ContrastToolUtils.formatElapsedTime(3600000L); // 1时0分0秒
        assertEquals("1时0分0秒", result);
    }

    @Test
    public void testFormatElapsedTime_ExactlyOneMinute() {
        // 测试刚好1分钟的情况
        String result = ContrastToolUtils.formatElapsedTime(60000L); // 1分0秒
        assertEquals("1分0秒", result);
    }

    @Test
    public void testFormatElapsedTime_MultipleDays() {
        // 测试多天的情况
        String result = ContrastToolUtils.formatElapsedTime(259200000L); // 3天0时0分0秒
        assertEquals("3天0时0分0秒", result);
    }

    @Test
    public void testFormatElapsedTime_LargeValue() {
        // 测试非常大的值
        String result = ContrastToolUtils.formatElapsedTime(31536000000L); // 365天
        assertEquals("365天0时0分0秒", result);
    }

    @Test
    public void testFormatElapsedTime_DaysWithZeroHours() {
        // 测试天数不为0但小时为0的情况
        String result = ContrastToolUtils.formatElapsedTime(86460000L); // 1天0时1分0秒
        assertEquals("1天0时1分0秒", result);
    }

    @Test
    public void testFormatElapsedTime_HoursWithZeroMinutes() {
        // 测试小时不为0但分钟为0的情况
        String result = ContrastToolUtils.formatElapsedTime(3660000L); // 1时1分0秒
        assertEquals("1时1分0秒", result);
    }

    @Test
    public void testFormatElapsedTime_MinutesWithZeroSeconds() {
        // 测试分钟不为0但秒为0的情况
        String result = ContrastToolUtils.formatElapsedTime(60000L); // 1分0秒
        assertEquals("1分0秒", result);
    }

    // 辅助方法
    private boolean contains(Long[] array, Long value) {
        if (array == null) return false;
        
        for (Long item : array) {
            if ((item == null && value == null) || (item != null && item.equals(value))) {
                return true;
            }
        }
        return false;
    }
} 