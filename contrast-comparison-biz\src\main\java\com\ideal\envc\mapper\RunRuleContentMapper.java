package com.ideal.envc.mapper;

import com.ideal.envc.model.entity.RunRuleContentEntity;

import java.util.List;

/**
 * 节点规则内容Mapper接口
 *
 * <AUTHOR>
 */
public interface RunRuleContentMapper {

    /**
     * 查询节点规则内容
     *
     * @param id 节点规则内容ID
     * @return 节点规则内容
     */
    RunRuleContentEntity selectRunRuleContentById(Long id);

    /**
     * 查询节点规则内容列表
     *
     * @param runRuleContent 节点规则内容
     * @return 节点规则内容集合
     */
    List<RunRuleContentEntity> selectRunRuleContentList(RunRuleContentEntity runRuleContent);

    /**
     * 根据规则ID查询节点规则内容列表
     *
     * @param envcRunRuleId 规则ID
     * @return 节点规则内容集合
     */
    List<RunRuleContentEntity> selectRunRuleContentListByRuleId(Long envcRunRuleId);

    /**
     * 新增节点规则内容
     *
     * @param runRuleContent 节点规则内容
     * @return 结果
     */
    int insertRunRuleContent(RunRuleContentEntity runRuleContent);

    /**
     * 修改节点规则内容
     *
     * @param runRuleContent 节点规则内容
     * @return 结果
     */
    int updateRunRuleContent(RunRuleContentEntity runRuleContent);

    /**
     * 删除节点规则内容
     *
     * @param id 节点规则内容ID
     * @return 结果
     */
    int deleteRunRuleContentById(Long id);

    /**
     * 批量删除节点规则内容
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteRunRuleContentByIds(Long[] ids);

    /**
     * 根据规则ID删除节点规则内容
     *
     * @param envcRunRuleId 规则ID
     * @return 结果
     */
    int deleteRunRuleContentByRuleId(Long envcRunRuleId);
}
