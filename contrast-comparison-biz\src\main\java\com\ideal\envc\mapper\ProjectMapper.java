package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.ProjectEntity;

/**
 * 比对业务系统Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface ProjectMapper {
    /**
     * 查询比对业务系统
     *
     * @param id 比对业务系统主键
     * @return 比对业务系统
     */
    ProjectEntity selectProjectById(Long id);

    /**
     * 查询比对业务系统列表
     *
     * @param project 比对业务系统
     * @return 比对业务系统集合
     */
    List<ProjectEntity> selectProjectList(ProjectEntity project,List<Long> businessSystemIdList);

    /**
     * 新增比对业务系统
     *
     * @param project 比对业务系统
     * @return 结果
     */
    int insertProject(ProjectEntity project);

    /**
     * 修改比对业务系统
     *
     * @param project 比对业务系统
     * @return 结果
     */
    int updateProject(ProjectEntity project);

    /**
     * 删除比对业务系统
     *
     * @param id 比对业务系统主键
     * @return 结果
     */
    int deleteProjectById(Long id);

    /**
     * 批量删除比对业务系统
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProjectByIds(Long[] ids);

    /**
     * 查询所有业务系统ID集合
     *
     * @param businessSystemIdList 具有权限的业务系统ID集合
     * @return 业务系统ID集合
     */
    List<Long> selectAllProjectIds(List<Long> businessSystemIdList);

    /**
     * 根据主键批量查询比对业务系统
     * @param ids 主键数组
     * @return 比对业务系统集合
     */
    List<ProjectEntity> selectProjectByIds(Long[] ids);

    /**
     * 根据业务系统ID集合批量查询比对业务系统
     * @param businessSystemIds 业务系统ID集合
     * @return 比对业务系统集合
     */
    List<ProjectEntity> selectProjectByBusinessSystemIds(List<Long> businessSystemIds);
}
