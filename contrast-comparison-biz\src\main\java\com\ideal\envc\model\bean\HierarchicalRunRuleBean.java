package com.ideal.envc.model.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideal.envc.model.entity.RunRuleEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 层次化规则对象
 *
 * <AUTHOR>
 */
public class HierarchicalRunRuleBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 实例详情ID */
    private Long envcRunInstanceInfoId;
    /** 模式（0：比对，1：同步，2：比对后同步） */
    private Integer model;
    /** 模块类型（0：目录，1;文件，2：脚本） */
    private Long type;
    /** 路径 */
    private String path;
    /** 原路径 */
    private String sourcePath;
    /** 字符集 */
    private String encode;
    /** 方式（0：全部:1：部分） */
    private Integer way;
    /** 规则类型（0：匹配，1：排除） */
    private Integer ruleType;
    /** 是否有效（0：有效，1：无效） */
    private Integer enabled;
    /** 是否子集（0:是，1：否） */
    private Integer childLevel;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /** 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） */
    private Integer result;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;
    /** 耗时 */
    private Long elapsedTime;

    /** 规则同步信息 */
    private HierarchicalRunRuleSyncBean ruleSync;

    /** 规则内容信息 */
    private HierarchicalRunRuleContentBean ruleContent;

    /** 规则流程信息 */
    private HierarchicalRunFlowBean ruleFlow;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcRunInstanceInfoId() {
        return envcRunInstanceInfoId;
    }

    public void setEnvcRunInstanceInfoId(Long envcRunInstanceInfoId) {
        this.envcRunInstanceInfoId = envcRunInstanceInfoId;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getEncode() {
        return encode;
    }

    public void setEncode(String encode) {
        this.encode = encode;
    }

    public Integer getWay() {
        return way;
    }

    public void setWay(Integer way) {
        this.way = way;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getChildLevel() {
        return childLevel;
    }

    public void setChildLevel(Integer childLevel) {
        this.childLevel = childLevel;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public HierarchicalRunRuleSyncBean getRuleSync() {
        return ruleSync;
    }

    public void setRuleSync(HierarchicalRunRuleSyncBean ruleSync) {
        this.ruleSync = ruleSync;
    }

    public HierarchicalRunRuleContentBean getRuleContent() {
        return ruleContent;
    }

    public void setRuleContent(HierarchicalRunRuleContentBean ruleContent) {
        this.ruleContent = ruleContent;
    }

    @Override
    public String toString() {
        return "HierarchicalRunRuleBean{" +
                "id=" + id +
                ", envcRunInstanceInfoId=" + envcRunInstanceInfoId +
                ", model=" + model +
                ", type=" + type +
                ", path='" + path + '\'' +
                ", sourcePath='" + sourcePath + '\'' +
                ", encode='" + encode + '\'' +
                ", way=" + way +
                ", ruleType=" + ruleType +
                ", enabled=" + enabled +
                ", childLevel=" + childLevel +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", createTime=" + createTime +
                ", endTime=" + endTime +
                ", result=" + result +
                ", state=" + state +
                ", elapsedTime=" + elapsedTime +
                ", ruleSync=" + ruleSync +
                ", ruleContent=" + ruleContent +
                ", ruleFlow=" + ruleFlow +
                '}';
    }

    public HierarchicalRunFlowBean getRuleFlow() {
        return ruleFlow;
    }

    public void setRuleFlow(HierarchicalRunFlowBean ruleFlow) {
        this.ruleFlow = ruleFlow;
    }

    /**
     * 从RunRuleEntity构建HierarchicalRunRuleBean
     *
     * @param entity RunRuleEntity实体
     * @return HierarchicalRunRuleBean对象
     */
    public static HierarchicalRunRuleBean fromEntity(RunRuleEntity entity) {
        if (entity == null) {
            return null;
        }

        HierarchicalRunRuleBean bean = new HierarchicalRunRuleBean();
        bean.setId(entity.getId());
        bean.setEnvcRunInstanceInfoId(entity.getEnvcRunInstanceInfoId());
        bean.setModel(entity.getModel());
        bean.setType(entity.getType());
        bean.setPath(entity.getPath());
        bean.setSourcePath(entity.getSourcePath());
        bean.setEncode(entity.getEncode());
        bean.setWay(entity.getWay());
        bean.setRuleType(entity.getRuleType());
        bean.setEnabled(entity.getEnabled());
        bean.setChildLevel(entity.getChildLevel());
        bean.setCreatorId(entity.getCreatorId());
        bean.setCreatorName(entity.getCreatorName());
        bean.setCreateTime(entity.getCreateTime());
        bean.setEndTime(entity.getEndTime());
        bean.setResult(entity.getResult());
        bean.setState(entity.getState());
        bean.setElapsedTime(entity.getElapsedTime());

        return bean;
    }

}
