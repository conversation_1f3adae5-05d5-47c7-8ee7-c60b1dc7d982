package com.ideal.envc.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 批量数据处理工具类
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public class BatchHandler<T> {
    private static final Logger logger = LoggerFactory.getLogger(BatchHandler.class);

    private int batchSize = 500;  // 默认批次大小
    private int retryTimes = 3;   // 默认重试次数
    private Consumer<Integer> progressCallback;  // 进度回调
    private Function<Exception, Boolean> errorHandler;  // 错误处理

    /**
     * 设置批次大小
     *
     * @param batchSize 批次大小
     * @return BatchHandler实例
     */
    public BatchHandler<T> setBatchSize(int batchSize) {
        this.batchSize = batchSize;
        return this;
    }

    /**
     * 设置重试次数
     *
     * @param retryTimes 重试次数
     * @return BatchHandler实例
     */
    public BatchHandler<T> setRetryTimes(int retryTimes) {
        this.retryTimes = retryTimes;
        return this;
    }

    /**
     * 设置进度回调
     *
     * @param progressCallback 进度回调函数
     * @return BatchHandler实例
     */
    public BatchHandler<T> setProgressCallback(Consumer<Integer> progressCallback) {
        this.progressCallback = progressCallback;
        return this;
    }

    /**
     * 设置错误处理
     *
     * @param errorHandler 错误处理函数
     * @return BatchHandler实例
     */
    public BatchHandler<T> setErrorHandler(Function<Exception, Boolean> errorHandler) {
        this.errorHandler = errorHandler;
        return this;
    }

    /**
     * 批量处理数据
     *
     * @param dataList 数据列表
     * @param processor 处理器
     */
    public void batchData(List<T> dataList, Consumer<List<T>> processor) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        int totalSize = dataList.size();
        int processedSize = 0;
        int currentBatch = 0;

        while (processedSize < totalSize) {
            int endIndex = Math.min(processedSize + batchSize, totalSize);
            List<T> batch = dataList.subList(processedSize, endIndex);
            
            int retryCount = 0;
            boolean success = false;
            
            while (!success && retryCount < retryTimes) {
                try {
                    processor.accept(batch);
                    success = true;
                } catch (Exception e) {
                    retryCount++;
                    if (errorHandler != null && !errorHandler.apply(e)) {
                        throw new RuntimeException("批量处理失败", e);
                    }
                    if (retryCount >= retryTimes) {
                        throw new RuntimeException("批量处理重试次数已达上限", e);
                    }
                    logger.warn("批量处理失败，正在进行第{}次重试", retryCount, e);
                    try {
                        Thread.sleep((long) Math.pow(2, retryCount) * 1000); // 指数退避
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("批量处理被中断", ie);
                    }
                }
            }

            processedSize = endIndex;
            currentBatch++;
            
            if (progressCallback != null) {
                int progress = (int) ((double) processedSize / totalSize * 100);
                progressCallback.accept(progress);
            }
        }
    }
} 