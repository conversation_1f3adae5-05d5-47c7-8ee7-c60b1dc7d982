package com.ideal.envc.config;

import com.ideal.envc.startup.StartupHealthIndicator;
import com.ideal.envc.startup.StartupMonitor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 启动相关配置类
 * 配置启动监控和健康检查相关的Bean
 * 
 * <AUTHOR>
 */
@Configuration
public class StartupConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(StartupConfiguration.class);
    
    /**
     * 配置启动监控器
     * 可通过配置项 startup.monitor.enabled 控制是否启用
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(name = "startup.monitor.enabled", havingValue = "true", matchIfMissing = true)
    public StartupMonitor startupMonitor() {
        logger.info("启动监控器已启用");
        return new StartupMonitor();
    }
    
    /**
     * 配置启动健康检查指示器
     * 可通过配置项 startup.health.enabled 控制是否启用
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(name = "startup.health.enabled", havingValue = "true", matchIfMissing = true)
    public StartupHealthIndicator startupHealthIndicator() {
        logger.info("启动健康检查指示器已启用");
        return new StartupHealthIndicator();
    }
}
