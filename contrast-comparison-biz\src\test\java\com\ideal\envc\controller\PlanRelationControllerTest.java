package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.*;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IPlanRelationService;
import com.ideal.envc.service.ISystemComputerNodeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PlanRelationController单元测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class PlanRelationControllerTest {

    @Mock
    private IPlanRelationService planRelationService;

    @Mock
    private ISystemComputerNodeService systemComputerNodeService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private PlanRelationController planRelationController;

    private UserDto userDto;
    private PlanRelationDto planRelationDto;
    private PlanRelationQueryDto planRelationQueryDto;
    private PlanRelationBatchDto planRelationBatchDto;
    private PlanSystemQueryDto planSystemQueryDto;
    private SystemComputerNodeQueryDto systemComputerNodeQueryDto;
    private TableQueryDto<PlanRelationQueryDto> tableQueryDto;
    private TableQueryDto<PlanSystemQueryDto> planSystemTableQueryDto;
    private TableQueryDto<SystemComputerNodeQueryDto> systemComputerNodeTableQueryDto;
    private PageInfo<PlanRelationDto> planRelationPageInfo;
    private PageInfo<PlanSystemListDto> planSystemPageInfo;
    private PageInfo<SystemComputerNodeListDto> systemComputerNodePageInfo;

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setLoginName("testUser");
        userDto.setFullName("测试用户");
        userDto.setOrgCode("TEST_ORG");

        // 初始化方案关系DTO
        planRelationDto = new PlanRelationDto();
        planRelationDto.setId(1L);
        planRelationDto.setEnvcPlanId(100L);
        planRelationDto.setBusinessSystemId(200L);
        planRelationDto.setCreatorId(1L);
        planRelationDto.setCreatorName("测试用户");

        // 初始化查询条件
        planRelationQueryDto = new PlanRelationQueryDto();
        planRelationQueryDto.setEnvcPlanId(100L);

        // 初始化批量保存DTO
        planRelationBatchDto = new PlanRelationBatchDto();
        planRelationBatchDto.setEnvcPlanId(100L);
        planRelationBatchDto.setBusinessSystemIdList(Arrays.asList(200L, 201L));

        // 初始化方案系统查询条件
        planSystemQueryDto = new PlanSystemQueryDto();
        planSystemQueryDto.setPlanId(100L);

        // 初始化系统设备节点查询条件
        systemComputerNodeQueryDto = new SystemComputerNodeQueryDto();
        systemComputerNodeQueryDto.setBusinessSystemId(200L);

        // 初始化表格查询DTO
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(planRelationQueryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        planSystemTableQueryDto = new TableQueryDto<>();
        planSystemTableQueryDto.setQueryParam(planSystemQueryDto);
        planSystemTableQueryDto.setPageNum(1);
        planSystemTableQueryDto.setPageSize(10);

        systemComputerNodeTableQueryDto = new TableQueryDto<>();
        systemComputerNodeTableQueryDto.setQueryParam(systemComputerNodeQueryDto);
        systemComputerNodeTableQueryDto.setPageNum(1);
        systemComputerNodeTableQueryDto.setPageSize(10);

        // 初始化分页信息
        List<PlanRelationDto> planRelationList = new ArrayList<>();
        planRelationList.add(planRelationDto);
        planRelationPageInfo = new PageInfo<>(planRelationList);

        List<PlanSystemListDto> planSystemList = new ArrayList<>();
        PlanSystemListDto planSystemListDto = new PlanSystemListDto();
        planSystemListDto.setId(1L);
        planSystemListDto.setPlanId(100L);
        planSystemListDto.setBusinessSystemId(200L);
        planSystemListDto.setBusinessSystemName("测试系统");
        planSystemList.add(planSystemListDto);
        planSystemPageInfo = new PageInfo<>(planSystemList);

        List<SystemComputerNodeListDto> systemComputerNodeList = new ArrayList<>();
        SystemComputerNodeListDto systemComputerNodeListDto = new SystemComputerNodeListDto();
        systemComputerNodeListDto.setId(1L);
        systemComputerNodeListDto.setBusinessSystemId(200L);
        systemComputerNodeList.add(systemComputerNodeListDto);
        systemComputerNodePageInfo = new PageInfo<>(systemComputerNodeList);
    }

    @Test
    @DisplayName("测试查询方案与系统关系列表 - 成功")
    void testList_Success() throws ContrastBusinessException {
        // 设置Mock行为
        doReturn(planRelationPageInfo).when(planRelationService)
                .selectPlanRelationList(any(PlanRelationQueryDto.class), anyInt(), anyInt());

        // 执行测试方法
        R<PageInfo<PlanRelationDto>> result = planRelationController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());

        // 验证方法调用
        verify(planRelationService).selectPlanRelationList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询方案与系统关系列表 - 业务异常")
    void testList_BusinessException() throws ContrastBusinessException {
        // 设置Mock行为
        doThrow(new ContrastBusinessException("查询失败")).when(planRelationService)
                .selectPlanRelationList(any(PlanRelationQueryDto.class), anyInt(), anyInt());

        // 执行测试方法
        R<PageInfo<PlanRelationDto>> result = planRelationController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals("查询失败", result.getMessage());

        // 验证方法调用
        verify(planRelationService).selectPlanRelationList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询方案与系统关系列表 - 系统异常")
    void testList_SystemException() throws ContrastBusinessException {
        // 设置Mock行为
        doThrow(new RuntimeException("系统异常")).when(planRelationService)
                .selectPlanRelationList(any(PlanRelationQueryDto.class), anyInt(), anyInt());

        // 执行测试方法
        R<PageInfo<PlanRelationDto>> result = planRelationController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());

        // 验证方法调用
        verify(planRelationService).selectPlanRelationList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @ParameterizedTest
    @MethodSource("getPlanRelationByIdParams")
    @DisplayName("测试查询方案详细信息")
    void testGetPlanRelationDtoById(Long id, String expectedCode, String expectedMessage, 
                                   PlanRelationDto mockDto, ContrastBusinessException mockException, 
                                   RuntimeException mockSystemException) throws ContrastBusinessException {
        // 设置Mock行为 - 只在需要时设置
        if (id != null) {
            if (mockException != null) {
                doThrow(mockException).when(planRelationService).selectPlanRelationById(id);
            } else if (mockSystemException != null) {
                doThrow(mockSystemException).when(planRelationService).selectPlanRelationById(id);
            } else if (mockDto != null) {
                doReturn(mockDto).when(planRelationService).selectPlanRelationById(id);
            } else {
                doReturn(null).when(planRelationService).selectPlanRelationById(id);
            }
        }

        // 执行测试方法
        R<PlanRelationDto> result = planRelationController.getPlanRelationDtoById(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (id != null) {
            verify(planRelationService).selectPlanRelationById(id);
        } else {
            verify(planRelationService, never()).selectPlanRelationById(anyLong());
        }
    }

    static Stream<Object[]> getPlanRelationByIdParams() {
        PlanRelationDto validDto = new PlanRelationDto();
        validDto.setId(1L);
        
        return Stream.of(
                // ID为null的情况
                new Object[]{null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "ID不能为空", null, null, null},
                // 查询成功的情况
                new Object[]{1L, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc(), validDto, null, null},
                // 数据不存在的情况
                new Object[]{1L, ResponseCodeEnum.DATA_NOT_FOUND.getCode(), ResponseCodeEnum.DATA_NOT_FOUND.getDesc(), null, null, null},
                // 业务异常的情况
                new Object[]{1L, ResponseCodeEnum.QUERY_FAIL.getCode(), "查询失败", null, new ContrastBusinessException("查询失败"), null},
                // 系统异常的情况
                new Object[]{1L, ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc(), null, null, new RuntimeException("系统异常")}
        );
    }

    @ParameterizedTest
    @MethodSource("savePlanRelationParams")
    @DisplayName("测试方案绑定系统信息")
    void testSave(PlanRelationBatchDto batchDto, UserDto mockUser, Integer mockResult, 
                  ContrastBusinessException mockException, RuntimeException mockSystemException,
                  String expectedCode, String expectedMessage) throws ContrastBusinessException {
        // 设置Mock行为
        if (mockUser != null) {
            doReturn(mockUser).when(userinfoComponent).getUser();
        }
        
        if (mockException != null) {
            doThrow(mockException).when(planRelationService).batchInsertPlanRelation(any(PlanRelationBatchDto.class), any(UserDto.class));
        } else if (mockSystemException != null) {
            doThrow(mockSystemException).when(planRelationService).batchInsertPlanRelation(any(PlanRelationBatchDto.class), any(UserDto.class));
        } else if (mockResult != null) {
            doReturn(mockResult).when(planRelationService).batchInsertPlanRelation(any(PlanRelationBatchDto.class), any(UserDto.class));
        }

        // 执行测试方法
        R<Void> result = planRelationController.save(batchDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (mockUser != null) {
            verify(userinfoComponent).getUser();
            if (mockException == null && mockSystemException == null) {
                verify(planRelationService).batchInsertPlanRelation(batchDto, mockUser);
            }
        }
    }

    static Stream<Object[]> savePlanRelationParams() {
        PlanRelationBatchDto validDto = new PlanRelationBatchDto();
        validDto.setEnvcPlanId(100L);
        validDto.setBusinessSystemIdList(Arrays.asList(200L, 201L));
        
        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");
        
        return Stream.of(
                // 保存成功的情况
                new Object[]{validDto, validUser, 2, null, null, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc()},
                // 保存失败的情况
                new Object[]{validDto, validUser, 0, null, null, ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc()},
                // 业务异常的情况
                new Object[]{validDto, validUser, null, new ContrastBusinessException("保存失败"), null, ResponseCodeEnum.ADD_FAIL.getCode(), "保存失败"},
                // 系统异常的情况
                new Object[]{validDto, validUser, null, null, new RuntimeException("系统异常"), ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc()}
        );
    }

    @ParameterizedTest
    @MethodSource("updatePlanRelationParams")
    @DisplayName("测试修改保存方案信息")
    void testUpdate(PlanRelationDto dto, UserDto mockUser, ContrastBusinessException mockException, 
                    RuntimeException mockSystemException, String expectedCode, String expectedMessage) throws ContrastBusinessException {
        // 设置Mock行为
        if (mockUser != null) {
            doReturn(mockUser).when(userinfoComponent).getUser();
        }
        
        if (mockException != null) {
            doThrow(mockException).when(planRelationService).updatePlanRelation(any(PlanRelationDto.class), any(UserDto.class));
        } else if (mockSystemException != null) {
            doThrow(mockSystemException).when(planRelationService).updatePlanRelation(any(PlanRelationDto.class), any(UserDto.class));
        } else {
            when(planRelationService.updatePlanRelation(any(PlanRelationDto.class), any(UserDto.class))).thenReturn(1);
        }

        // 执行测试方法
        R<Void> result = planRelationController.update(dto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (mockUser != null) {
            verify(userinfoComponent).getUser();
            if (mockException == null && mockSystemException == null) {
                verify(planRelationService).updatePlanRelation(dto, mockUser);
            }
        }
    }

    static Stream<Object[]> updatePlanRelationParams() {
        PlanRelationDto validDto = new PlanRelationDto();
        validDto.setId(1L);
        validDto.setEnvcPlanId(100L);
        validDto.setBusinessSystemId(200L);
        
        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");
        
        return Stream.of(
                // 更新成功的情况
                new Object[]{validDto, validUser, null, null, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc()},
                // 业务异常的情况
                new Object[]{validDto, validUser, new ContrastBusinessException("更新失败"), null, ResponseCodeEnum.UPDATE_FAIL.getCode(), "更新失败"},
                // 系统异常的情况
                new Object[]{validDto, validUser, null, new RuntimeException("系统异常"), ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc()}
        );
    }

    @ParameterizedTest
    @MethodSource("removePlanRelationParams")
    @DisplayName("测试方案解绑系统")
    void testRemove(Long[] ids, UserDto mockUser, ContrastBusinessException mockException, 
                    RuntimeException mockSystemException, String expectedCode, String expectedMessage) throws ContrastBusinessException {
        // 设置Mock行为 - 只在需要时设置
        if (ids != null && ids.length > 0) {
            if (mockUser != null) {
                doReturn(mockUser).when(userinfoComponent).getUser();
            }
            
            if (mockException != null) {
                doThrow(mockException).when(planRelationService).deletePlanRelationByIds(any(Long[].class), any(UserDto.class));
            } else if (mockSystemException != null) {
                doThrow(mockSystemException).when(planRelationService).deletePlanRelationByIds(any(Long[].class), any(UserDto.class));
            } else if (mockUser != null) {
                when(planRelationService.deletePlanRelationByIds(any(Long[].class), any(UserDto.class))).thenReturn(ids.length);
            }
        }

        // 执行测试方法
        R<Void> result = planRelationController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (ids != null && ids.length > 0 && mockUser != null) {
            verify(userinfoComponent).getUser();
            if (mockException == null && mockSystemException == null) {
                verify(planRelationService).deletePlanRelationByIds(ids, mockUser);
            }
        }
    }

    static Stream<Object[]> removePlanRelationParams() {
        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");
        
        return Stream.of(
                // ID为null的情况
                new Object[]{null, null, null, null, ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), "删除ID不能为空"},
                // ID为空数组的情况
                new Object[]{new Long[]{}, null, null, null, ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), "删除ID不能为空"},
                // 删除成功的情况
                new Object[]{new Long[]{1L, 2L}, validUser, null, null, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc()},
                // 业务异常的情况
                new Object[]{new Long[]{1L, 2L}, validUser, new ContrastBusinessException("删除失败"), null, ResponseCodeEnum.DELETE_FAIL.getCode(), "删除失败"},
                // 系统异常的情况
                new Object[]{new Long[]{1L, 2L}, validUser, null, new RuntimeException("系统异常"), ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc()}
        );
    }

    @ParameterizedTest
    @MethodSource("systemListParams")
    @DisplayName("测试查询方案已绑定系统列表")
    void testSystemList(TableQueryDto<PlanSystemQueryDto> queryDto, PageInfo<PlanSystemListDto> mockPageInfo,
                        ContrastBusinessException mockException, RuntimeException mockSystemException,
                        String expectedCode, String expectedMessage) throws ContrastBusinessException {
        // 设置Mock行为
        if (mockException != null) {
            doThrow(mockException).when(planRelationService).selectPlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        } else if (mockSystemException != null) {
            doThrow(mockSystemException).when(planRelationService).selectPlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        } else if (mockPageInfo != null) {
            doReturn(mockPageInfo).when(planRelationService).selectPlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        }

        // 执行测试方法
        R<PageInfo<PlanSystemListDto>> result = planRelationController.systemList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (queryDto != null && queryDto.getQueryParam() != null && queryDto.getQueryParam().getPlanId() != null) {
            if (mockException == null && mockSystemException == null) {
                verify(planRelationService).selectPlanSystemList(
                        queryDto.getQueryParam(),
                        queryDto.getPageNum(),
                        queryDto.getPageSize()
                );
            }
        }
    }

    static Stream<Object[]> systemListParams() {
        // 有效查询条件
        PlanSystemQueryDto validQuery = new PlanSystemQueryDto();
        validQuery.setPlanId(100L);
        TableQueryDto<PlanSystemQueryDto> validTableQuery = new TableQueryDto<>();
        validTableQuery.setQueryParam(validQuery);
        validTableQuery.setPageNum(1);
        validTableQuery.setPageSize(10);

        // 无效查询条件 - queryParam为null
        TableQueryDto<PlanSystemQueryDto> invalidTableQuery1 = new TableQueryDto<>();
        invalidTableQuery1.setQueryParam(null);
        invalidTableQuery1.setPageNum(1);
        invalidTableQuery1.setPageSize(10);

        // 无效查询条件 - planId为null
        PlanSystemQueryDto invalidQuery = new PlanSystemQueryDto();
        invalidQuery.setPlanId(null);
        TableQueryDto<PlanSystemQueryDto> invalidTableQuery2 = new TableQueryDto<>();
        invalidTableQuery2.setQueryParam(invalidQuery);
        invalidTableQuery2.setPageNum(1);
        invalidTableQuery2.setPageSize(10);

        // 分页信息
        PageInfo<PlanSystemListDto> pageInfo = new PageInfo<>(new ArrayList<>());
        
        return Stream.of(
                // 查询参数为null的情况
                new Object[]{invalidTableQuery1, null, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空"},
                // planId为null的情况
                new Object[]{invalidTableQuery2, null, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空"},
                // 查询成功的情况
                new Object[]{validTableQuery, pageInfo, null, null, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc()},
                // 业务异常的情况
                new Object[]{validTableQuery, null, new ContrastBusinessException("查询失败"), null, ResponseCodeEnum.QUERY_FAIL.getCode(), "查询失败"},
                // 系统异常的情况
                new Object[]{validTableQuery, null, null, new RuntimeException("系统异常"), ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc()}
        );
    }

    @ParameterizedTest
    @MethodSource("pendingSystemListParams")
    @DisplayName("测试查询方案待绑定系统列表")
    void testPendingSystemList(TableQueryDto<PlanSystemQueryDto> queryDto, PageInfo<PlanSystemListDto> mockPageInfo,
                               ContrastBusinessException mockException, RuntimeException mockSystemException,
                               String expectedCode, String expectedMessage) throws ContrastBusinessException {
        // 设置Mock行为
        if (mockException != null) {
            doThrow(mockException).when(planRelationService).selectAvailablePlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        } else if (mockSystemException != null) {
            doThrow(mockSystemException).when(planRelationService).selectAvailablePlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        } else if (mockPageInfo != null) {
            doReturn(mockPageInfo).when(planRelationService).selectAvailablePlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        }

        // 执行测试方法
        R<PageInfo<PlanSystemListDto>> result = planRelationController.pendingSystemList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (queryDto != null && queryDto.getQueryParam() != null && queryDto.getQueryParam().getPlanId() != null) {
            if (mockException == null && mockSystemException == null) {
                verify(planRelationService).selectAvailablePlanSystemList(
                        queryDto.getQueryParam(),
                        queryDto.getPageNum(),
                        queryDto.getPageSize()
                );
            }
        }
    }

    static Stream<Object[]> pendingSystemListParams() {
        // 有效查询条件
        PlanSystemQueryDto validQuery = new PlanSystemQueryDto();
        validQuery.setPlanId(100L);
        TableQueryDto<PlanSystemQueryDto> validTableQuery = new TableQueryDto<>();
        validTableQuery.setQueryParam(validQuery);
        validTableQuery.setPageNum(1);
        validTableQuery.setPageSize(10);

        // 无效查询条件 - queryParam为null
        TableQueryDto<PlanSystemQueryDto> invalidTableQuery1 = new TableQueryDto<>();
        invalidTableQuery1.setQueryParam(null);
        invalidTableQuery1.setPageNum(1);
        invalidTableQuery1.setPageSize(10);

        // 无效查询条件 - planId为null
        PlanSystemQueryDto invalidQuery = new PlanSystemQueryDto();
        invalidQuery.setPlanId(null);
        TableQueryDto<PlanSystemQueryDto> invalidTableQuery2 = new TableQueryDto<>();
        invalidTableQuery2.setQueryParam(invalidQuery);
        invalidTableQuery2.setPageNum(1);
        invalidTableQuery2.setPageSize(10);

        // 分页信息
        PageInfo<PlanSystemListDto> pageInfo = new PageInfo<>(new ArrayList<>());
        
        return Stream.of(
                // 查询参数为null的情况
                new Object[]{invalidTableQuery1, null, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空"},
                // planId为null的情况
                new Object[]{invalidTableQuery2, null, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空"},
                // 查询成功的情况
                new Object[]{validTableQuery, pageInfo, null, null, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc()},
                // 业务异常的情况
                new Object[]{validTableQuery, null, new ContrastBusinessException("查询失败"), null, ResponseCodeEnum.QUERY_FAIL.getCode(), "查询失败"},
                // 系统异常的情况
                new Object[]{validTableQuery, null, null, new RuntimeException("系统异常"), ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc()}
        );
    }

    @ParameterizedTest
    @MethodSource("systemNodeListParams")
    @DisplayName("测试查询方案系统下源目标设备信息")
    void testSystemNodeList(TableQueryDto<SystemComputerNodeQueryDto> queryDto, PageInfo<SystemComputerNodeListDto> mockPageInfo,
                            RuntimeException mockSystemException, String expectedCode, String expectedMessage) {
        // 设置Mock行为
        if (mockSystemException != null) {
            doThrow(mockSystemException).when(systemComputerNodeService).selectSystemComputerNodeBeanList(any(SystemComputerNodeQueryDto.class), anyInt(), anyInt());
        } else if (mockPageInfo != null) {
            doReturn(mockPageInfo).when(systemComputerNodeService).selectSystemComputerNodeBeanList(any(SystemComputerNodeQueryDto.class), anyInt(), anyInt());
        }

        // 执行测试方法
        R<PageInfo<SystemComputerNodeListDto>> result = planRelationController.systemNodeList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (queryDto != null && queryDto.getQueryParam() != null && queryDto.getQueryParam().getBusinessSystemId() != null) {
            if (mockSystemException == null) {
                verify(systemComputerNodeService).selectSystemComputerNodeBeanList(
                        queryDto.getQueryParam(),
                        queryDto.getPageNum(),
                        queryDto.getPageSize()
                );
            }
        }
    }

    static Stream<Object[]> systemNodeListParams() {
        // 有效查询条件
        SystemComputerNodeQueryDto validQuery = new SystemComputerNodeQueryDto();
        validQuery.setBusinessSystemId(200L);
        TableQueryDto<SystemComputerNodeQueryDto> validTableQuery = new TableQueryDto<>();
        validTableQuery.setQueryParam(validQuery);
        validTableQuery.setPageNum(1);
        validTableQuery.setPageSize(10);

        // 无效查询条件 - queryParam为null
        TableQueryDto<SystemComputerNodeQueryDto> invalidTableQuery1 = new TableQueryDto<>();
        invalidTableQuery1.setQueryParam(null);
        invalidTableQuery1.setPageNum(1);
        invalidTableQuery1.setPageSize(10);

        // 无效查询条件 - businessSystemId为null
        SystemComputerNodeQueryDto invalidQuery = new SystemComputerNodeQueryDto();
        invalidQuery.setBusinessSystemId(null);
        TableQueryDto<SystemComputerNodeQueryDto> invalidTableQuery2 = new TableQueryDto<>();
        invalidTableQuery2.setQueryParam(invalidQuery);
        invalidTableQuery2.setPageNum(1);
        invalidTableQuery2.setPageSize(10);

        // 分页信息
        PageInfo<SystemComputerNodeListDto> pageInfo = new PageInfo<>(new ArrayList<>());
        
        return Stream.of(
                // 查询参数为null的情况
                new Object[]{invalidTableQuery1, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "业务系统ID不能为空"},
                // businessSystemId为null的情况
                new Object[]{invalidTableQuery2, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "业务系统ID不能为空"},
                // 查询成功的情况
                new Object[]{validTableQuery, pageInfo, null, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc()},
                // 系统异常的情况
                new Object[]{validTableQuery, null, new RuntimeException("系统异常"), ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc()}
        );
    }

    @ParameterizedTest
    @MethodSource("availableSystemListParams")
    @DisplayName("测试查询方案可绑定系统列表")
    void testAvailableSystemList(TableQueryDto<PlanSystemQueryDto> queryDto, PageInfo<PlanSystemListDto> mockPageInfo,
                                 ContrastBusinessException mockException, RuntimeException mockSystemException,
                                 String expectedCode, String expectedMessage) throws ContrastBusinessException {
        // 设置Mock行为
        if (mockException != null) {
            doThrow(mockException).when(planRelationService).selectAvailablePlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        } else if (mockSystemException != null) {
            doThrow(mockSystemException).when(planRelationService).selectAvailablePlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        } else if (mockPageInfo != null) {
            doReturn(mockPageInfo).when(planRelationService).selectAvailablePlanSystemList(any(PlanSystemQueryDto.class), anyInt(), anyInt());
        }

        // 执行测试方法
        R<PageInfo<PlanSystemListDto>> result = planRelationController.availableSystemList(queryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        if (expectedMessage != null) {
            assertTrue(result.getMessage().contains(expectedMessage) || result.getMessage().equals(expectedMessage));
        }

        // 验证方法调用
        if (queryDto != null && queryDto.getQueryParam() != null && queryDto.getQueryParam().getPlanId() != null) {
            if (mockException == null && mockSystemException == null) {
                verify(planRelationService).selectAvailablePlanSystemList(
                        queryDto.getQueryParam(),
                        queryDto.getPageNum(),
                        queryDto.getPageSize()
                );
            }
        }
    }

    static Stream<Object[]> availableSystemListParams() {
        // 有效查询条件
        PlanSystemQueryDto validQuery = new PlanSystemQueryDto();
        validQuery.setPlanId(100L);
        TableQueryDto<PlanSystemQueryDto> validTableQuery = new TableQueryDto<>();
        validTableQuery.setQueryParam(validQuery);
        validTableQuery.setPageNum(1);
        validTableQuery.setPageSize(10);

        // 无效查询条件 - queryParam为null
        TableQueryDto<PlanSystemQueryDto> invalidTableQuery1 = new TableQueryDto<>();
        invalidTableQuery1.setQueryParam(null);
        invalidTableQuery1.setPageNum(1);
        invalidTableQuery1.setPageSize(10);

        // 无效查询条件 - planId为null
        PlanSystemQueryDto invalidQuery = new PlanSystemQueryDto();
        invalidQuery.setPlanId(null);
        TableQueryDto<PlanSystemQueryDto> invalidTableQuery2 = new TableQueryDto<>();
        invalidTableQuery2.setQueryParam(invalidQuery);
        invalidTableQuery2.setPageNum(1);
        invalidTableQuery2.setPageSize(10);

        // 分页信息
        PageInfo<PlanSystemListDto> pageInfo = new PageInfo<>(new ArrayList<>());
        
        return Stream.of(
                // 查询参数为null的情况
                new Object[]{invalidTableQuery1, null, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空"},
                // planId为null的情况
                new Object[]{invalidTableQuery2, null, null, null, ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "方案ID不能为空"},
                // 查询成功的情况
                new Object[]{validTableQuery, pageInfo, null, null, ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc()},
                // 业务异常的情况
                new Object[]{validTableQuery, null, new ContrastBusinessException("查询失败"), null, ResponseCodeEnum.QUERY_FAIL.getCode(), "查询失败"},
                // 系统异常的情况
                new Object[]{validTableQuery, null, null, new RuntimeException("系统异常"), ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc()}
        );
    }
} 