package com.ideal.envc.producer;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.RunInstanceStateMessage;
import com.ideal.envc.model.enums.MessageTopicEnum;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 运行实例状态生产者单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RunInstanceStateProducerTest {

    @Mock
    private IPublisher publisher;

    @InjectMocks
    private RunInstanceStateProducer runInstanceStateProducer;

    private RunInstanceStateMessage stateMessage;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        stateMessage = new RunInstanceStateMessage();
        stateMessage.setInstanceId(1L);
        stateMessage.setInstanceInfoId(100L);
        stateMessage.setTimestamp(System.currentTimeMillis());
        stateMessage.setStatus(1);
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_成功")
    void testSendRunInstanceStateMessage_Success() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        String expectedChannel = MessageTopicEnum.RUN_INSTANCE_STATE_TOPIC.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_消息为null")
    void testSendRunInstanceStateMessage_NullMessage() {
        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(null);

        // 验证结果
        assertFalse(result);

        // 验证没有调用publisher
        verifyNoInteractions(publisher);
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_发送异常")
    void testSendRunInstanceStateMessage_CommunicationException() throws CommunicationException {
        // 设置Mock行为 - 抛出异常
        doThrow(new CommunicationException("发送失败")).when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertFalse(result);

        // 验证方法调用
        String expectedChannel = MessageTopicEnum.RUN_INSTANCE_STATE_TOPIC.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_验证消息内容")
    void testSendRunInstanceStateMessage_VerifyMessageContent() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证消息内容
        verify(publisher, times(1)).apply(anyString(), argThat(msg -> {
            Message<?> message = (Message<?>) msg;
            // 验证消息头
            Object instanceIdHeader = message.getHeaders().get("instanceId");
            assertEquals(1L, instanceIdHeader);

            // 验证消息体
            String payload = (String) message.getPayload();
            RunInstanceStateMessage parsedMessage = JSON.parseObject(payload, RunInstanceStateMessage.class);
            assertEquals(stateMessage.getInstanceId(), parsedMessage.getInstanceId());
            assertEquals(stateMessage.getInstanceInfoId(), parsedMessage.getInstanceInfoId());
            assertEquals(stateMessage.getTimestamp(), parsedMessage.getTimestamp());
            assertEquals(stateMessage.getStatus(), parsedMessage.getStatus());

            return true;
        }));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_验证频道名称")
    void testSendRunInstanceStateMessage_VerifyChannelName() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证频道名称
        String expectedChannel = MessageTopicEnum.RUN_INSTANCE_STATE_TOPIC.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_instanceId为null")
    void testSendRunInstanceStateMessage_NullInstanceId() throws CommunicationException {
        // 设置测试数据
        stateMessage.setInstanceId(null);

        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证消息头中instanceId为null
        verify(publisher, times(1)).apply(anyString(), argThat(msg -> {
            Message<?> message = (Message<?>) msg;
            Object instanceIdHeader = message.getHeaders().get("instanceId");
            assertNull(instanceIdHeader);
            return true;
        }));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_instanceInfoId为null")
    void testSendRunInstanceStateMessage_NullInstanceInfoId() throws CommunicationException {
        // 设置测试数据
        stateMessage.setInstanceInfoId(null);

        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(publisher, times(1)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_timestamp为null")
    void testSendRunInstanceStateMessage_NullTimestamp() throws CommunicationException {
        // 设置测试数据
        stateMessage.setTimestamp(null);

        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(publisher, times(1)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_多次发送")
    void testSendRunInstanceStateMessage_MultipleSends() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行多次发送
        boolean result1 = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);
        boolean result2 = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);
        boolean result3 = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result1);
        assertTrue(result2);
        assertTrue(result3);

        // 验证方法调用次数
        verify(publisher, times(3)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_不同的消息内容")
    void testSendRunInstanceStateMessage_DifferentMessages() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 创建不同的消息
        RunInstanceStateMessage message1 = new RunInstanceStateMessage();
        message1.setInstanceId(1L);
        message1.setInstanceInfoId(100L);
        message1.setTimestamp(1000L);
        message1.setStatus(0);

        RunInstanceStateMessage message2 = new RunInstanceStateMessage();
        message2.setInstanceId(2L);
        message2.setInstanceInfoId(200L);
        message2.setTimestamp(2000L);
        message2.setStatus(1);

        // 执行测试方法
        boolean result1 = runInstanceStateProducer.sendRunInstanceStateMessage(message1);
        boolean result2 = runInstanceStateProducer.sendRunInstanceStateMessage(message2);

        // 验证结果
        assertTrue(result1);
        assertTrue(result2);

        // 验证方法调用次数
        verify(publisher, times(2)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_运行时异常")
    void testSendRunInstanceStateMessage_RuntimeException() throws CommunicationException {
        // 设置Mock行为 - 抛出运行时异常
        doThrow(new RuntimeException("系统异常")).when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法并验证异常
        assertThrows(RuntimeException.class, () -> {
            runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);
        });

        // 验证方法调用
        verify(publisher, times(1)).apply(anyString(), any(Message.class));
    }

    @Test
    @DisplayName("测试发送运行实例状态消息_JSON序列化")
    void testSendRunInstanceStateMessage_JsonSerialization() throws CommunicationException {
        // 设置Mock行为
        doNothing().when(publisher).apply(anyString(), any(Message.class));

        // 执行测试方法
        boolean result = runInstanceStateProducer.sendRunInstanceStateMessage(stateMessage);

        // 验证结果
        assertTrue(result);

        // 验证JSON序列化
        verify(publisher, times(1)).apply(anyString(), argThat(msg -> {
            Message<?> message = (Message<?>) msg;
            String payload = (String) message.getPayload();
            assertNotNull(payload);
            assertFalse(payload.isEmpty());
            
            // 验证可以反序列化
            RunInstanceStateMessage parsedMessage = JSON.parseObject(payload, RunInstanceStateMessage.class);
            assertNotNull(parsedMessage);
            
            return true;
        }));
    }
} 