package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 启动功能-统一查询对象，用于封装所有启动场景的查询条件
 *
 * <AUTHOR>
 */
public class StartContrastQueryBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 方案名称模糊查询 */
    private String planNameLike;
    /** 方案ID列表 */
    private List<Long> planIds;
    /** 系统ID列表 */
    private List<Long> systemIds;
    /** 节点ID列表 */
    private List<Long> nodeIds;
    /** 规则ID列表 */
    private List<Long> ruleIds;
    /** 任务ID列表 */
    private List<Long> taskIds;
    /** 启动类型（1:方案级，2:系统级，3:节点级，4:规则级，5:任务级） */
    private Integer startType;
    /** 用户ID */
    private Long userId;
    /** 用户名称 */
    private String userName;
    /** 源中心ID */
    private Long sourceCenterId;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 触发来源（1：周期触发，2：手动触发，3：重试） */
    private Integer triggerFrom;

    public List<Long> getPlanIds() {
        return planIds;
    }

    public void setPlanIds(List<Long> planIds) {
        this.planIds = planIds;
    }

    public List<Long> getSystemIds() {
        return systemIds;
    }

    public void setSystemIds(List<Long> systemIds) {
        this.systemIds = systemIds;
    }

    public List<Long> getNodeIds() {
        return nodeIds;
    }

    public void setNodeIds(List<Long> nodeIds) {
        this.nodeIds = nodeIds;
    }

    public List<Long> getRuleIds() {
        return ruleIds;
    }

    public void setRuleIds(List<Long> ruleIds) {
        this.ruleIds = ruleIds;
    }

    public List<Long> getTaskIds() {
        return taskIds;
    }

    public void setTaskIds(List<Long> taskIds) {
        this.taskIds = taskIds;
    }

    public Integer getStartType() {
        return startType;
    }

    public void setStartType(Integer startType) {
        this.startType = startType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public Integer getTriggerFrom() {
        return triggerFrom;
    }

    public void setTriggerFrom(Integer triggerFrom) {
        this.triggerFrom = triggerFrom;
    }

    public String getPlanNameLike() {
        return planNameLike;
    }

    public void setPlanNameLike(String planNameLike) {
        this.planNameLike = planNameLike;
    }

    @Override
    public String toString() {
        return "StartContrastQueryBean{" +
                "planNameLike='" + planNameLike + '\'' +
                ", planIds=" + planIds +
                ", systemIds=" + systemIds +
                ", nodeIds=" + nodeIds +
                ", ruleIds=" + ruleIds +
                ", taskIds=" + taskIds +
                ", startType=" + startType +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", sourceCenterId=" + sourceCenterId +
                ", targetCenterId=" + targetCenterId +
                ", triggerFrom=" + triggerFrom +
                '}';
    }
}
