<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.SystemComputerMapper">

    <resultMap type="com.ideal.envc.model.entity.SystemComputerEntity" id="SystemComputerResult">
            <result property="id" column="iid"/>
            <result property="businessSystemId" column="ibusiness_system_id"/>
            <result property="computerId" column="icomputer_id"/>
            <result property="computerIp" column="icomputer_ip"/>
            <result property="computerName" column="icomputer_name"/>
            <result property="centerId" column="icenter_id"/>
            <result property="centerName" column="icenter_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
    </resultMap>

    <sql id="selectSystemComputer">
        select iid, ibusiness_system_id, icomputer_id, icomputer_ip, icomputer_name, icenter_id, icenter_name, icreate_time, icreator_id, icreator_name
        from ieai_envc_system_computer
    </sql>

    <select id="selectSystemComputerList" parameterType="com.ideal.envc.model.entity.SystemComputerEntity" resultMap="SystemComputerResult">
        <include refid="selectSystemComputer"/>
        <where>
                        <if test="businessSystemId != null ">
                            and ibusiness_system_id = #{businessSystemId}
                        </if>
                        <if test="computerId != null ">
                            and icomputer_id = #{computerId}
                        </if>
                        <if test="computerIp != null  and computerIp != ''">
                            and icomputer_ip = #{computerIp}
                        </if>
                        <if test="computerName != null  and computerName != ''">
                            and icomputer_name like concat('%', #{computerName}, '%')
                        </if>
                        <if test="centerId != null ">
                            and icenter_id = #{centerId}
                        </if>
                        <if test="centerName != null  and centerName != ''">
                            and icenter_name like concat('%', #{centerName}, '%')
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
        </where>
    </select>

    <select id="selectSystemComputerById" parameterType="Long"
            resultMap="SystemComputerResult">
            <include refid="selectSystemComputer"/>
            where iid = #{id}
    </select>

    <insert id="insertSystemComputer" parameterType="com.ideal.envc.model.entity.SystemComputerEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_system_computer
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="businessSystemId != null">ibusiness_system_id,
                    </if>
                    <if test="computerId != null">icomputer_id,
                    </if>
                    <if test="computerIp != null">icomputer_ip,
                    </if>
                    <if test="computerName != null">icomputer_name,
                    </if>
                    <if test="centerId != null">icenter_id,
                    </if>
                    <if test="centerName != null">icenter_name,
                    </if>
                    icreate_time,
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="businessSystemId != null">#{businessSystemId},
                    </if>
                    <if test="computerId != null">#{computerId},
                    </if>
                    <if test="computerIp != null">#{computerIp},
                    </if>
                    <if test="computerName != null">#{computerName},
                    </if>
                    <if test="centerId != null">#{centerId},
                    </if>
                    <if test="centerName != null">#{centerName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
        </trim>
    </insert>

    <update id="updateSystemComputer" parameterType="com.ideal.envc.model.entity.SystemComputerEntity">
        update ieai_envc_system_computer
        <trim prefix="SET" suffixOverrides=",">
                    <if test="businessSystemId != null">ibusiness_system_id = #{businessSystemId},</if>
                    <if test="computerId != null">icomputer_id = #{computerId},</if>
                    <if test="computerIp != null">icomputer_ip = #{computerIp},</if>
                    <if test="computerName != null">icomputer_name = #{computerName},</if>
                    <if test="centerId != null">icenter_id = #{centerId},</if>
                    <if test="centerName != null">icenter_name = #{centerName},</if>
                    <if test="updatorId != null">iupdator_id = #{updatorId},</if>
                    <if test="updatorName != null">iupdator_name = #{updatorName},</if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteSystemComputerById" parameterType="Long">
        delete
        from ieai_envc_system_computer where iid = #{id}
    </delete>

    <delete id="deleteSystemComputerByIds" parameterType="java.lang.Long">
        delete from ieai_envc_system_computer where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询已绑定设备的业务系统列表 -->
    <resultMap id="SystemListResult" type="com.ideal.envc.model.bean.SystemListBean">
        <result property="businessSystemId" column="ibusiness_system_id" />
        <result property="businessSystemName" column="ibusiness_system_name" />
        <result property="businessSystemCode" column="ibusiness_system_code" />
        <result property="businessSystemDesc" column="ibusiness_system_desc" />
        <result property="creatorName" column="icreator_name" />
    </resultMap>

    <!-- 查询已绑定设备的业务系统列表 -->
    <select id="selectSystemList" resultMap="SystemListResult">
        SELECT
            p.ibusiness_system_id,
            p.ibusiness_system_name,
            p.ibusiness_system_code,
            p.ibusiness_system_desc,
            p.icreator_name
        FROM
            ieai_envc_project p /*+ INDEX(p idx_envc_project_system_01) */
        WHERE
            p.istatus = 1
            <!-- 首先过滤有权限的业务系统ID -->
            <if test="businessSystemIds != null and businessSystemIds.size() > 0">
                <choose>
                    <!-- 当ID数量小于1000时，直接使用IN查询 -->
                    <when test="businessSystemIds.size() &lt;= 1000">
                        AND p.ibusiness_system_id IN
                        <foreach item="id" collection="businessSystemIds" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                    </when>
                    <!-- 当ID数量超过1000时，使用EXISTS子查询并分批处理 -->
                    <otherwise>
                        AND (
                            <foreach item="ids" collection="businessSystemIds" open="" separator=" OR " close="" index="index">
                                <if test="index % 1000 == 0">
                                    p.ibusiness_system_id IN
                                    <foreach item="id" collection="businessSystemIds" open="(" separator="," close=")" index="id_index">
                                        <if test="id_index >= index and id_index &lt; index + 1000 and id_index &lt; businessSystemIds.size()">
                                            #{id}
                                        </if>
                                    </foreach>
                                </if>
                            </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
            <!-- 确保业务系统已绑定设备 -->
            AND EXISTS (
                SELECT 1 FROM ieai_envc_system_computer c /*+ INDEX(c idx_system_computer_01) */
                WHERE c.ibusiness_system_id = p.ibusiness_system_id
            )
            <!-- 根据查询条件过滤 -->
            <if test="businessSystemName != null and businessSystemName != ''">
                AND p.ibusiness_system_name LIKE CONCAT('%', #{businessSystemName}, '%')
            </if>
            <if test="businessSystemDesc != null and businessSystemDesc != ''">
                AND p.ibusiness_system_desc LIKE CONCAT('%', #{businessSystemDesc}, '%')
            </if>
    </select>

    <!-- 根据设备ID集合和业务系统ID查询设备信息 -->
    <select id="selectComputerIpMapByIdsAndSystemId" resultMap="SystemComputerResult">
        <include refid="selectSystemComputer"/>
        <where>
            ibusiness_system_id = #{businessSystemId}
            <if test="computerIds != null and computerIds.size() > 0">
                AND icomputer_id IN
                <foreach item="id" collection="computerIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 根据业务系统ID、中心ID和排除设备ID集合查询设备列表（支持分页） -->
    <select id="selectComputerListByCondition" resultMap="SystemComputerResult">
        <include refid="selectSystemComputer"/>
        <where>
            <if test="businessSystemId != null">
                ibusiness_system_id = #{businessSystemId}
            </if>
            <if test="centerId != null">
                AND icenter_id = #{centerId}
            </if>
            <if test="excludeComputerIds != null and excludeComputerIds.size() > 0">
                AND icomputer_id NOT IN
                <foreach item="id" collection="excludeComputerIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectSystemComputerByBusinessSystemIds" parameterType="java.util.List" resultMap="SystemComputerResult">
        select * from ieai_envc_system_computer
        where ibusiness_system_id in
        <foreach collection="businessSystemIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectByBusinessSystemIdAndComputerIds" parameterType="map" resultMap="SystemComputerResult">
        select * from ieai_envc_system_computer
        where ibusiness_system_id = #{businessSystemId}
        <if test="computerIds != null and computerIds.size() > 0">
            and icomputer_id in
            <foreach collection="computerIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 根据ID集合批量查询系统计算机 -->
    <select id="selectSystemComputerByIds" parameterType="java.lang.Long" resultMap="SystemComputerResult">
        <include refid="selectSystemComputer"/>
        where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 根据业务系统ID查询已绑定的设备列表 -->
    <select id="selectByBusinessSystemId" parameterType="java.lang.Long" resultMap="SystemComputerResult">
        <include refid="selectSystemComputer"/>
        where ibusiness_system_id = #{businessSystemId}
    </select>

    <!-- 根据IP地址查询主机名 -->
    <select id="selectComputerNameByIp" parameterType="java.lang.String" resultType="java.lang.String">
        select icomputer_name
        from ieai_envc_system_computer
        where icomputer_ip = #{computerIp}
        limit 1
    </select>

    <!-- 批量根据IP地址查询主机名 -->
    <select id="selectComputerNameMapByIps" parameterType="java.util.List" resultMap="SystemComputerResult">
        select icomputer_ip, icomputer_name
        from ieai_envc_system_computer
        where icomputer_ip in
        <foreach collection="list" item="ip" open="(" separator="," close=")">
            #{ip}
        </foreach>
    </select>

</mapper>