package com.ideal.envc.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.model.dto.RunFlowResultDto;
import com.ideal.envc.model.dto.RunFlowResultQueryDto;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * RunFlowResultServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RunFlowResultServiceImplTest {

    @Mock
    private RunFlowResultMapper runFlowResultMapper;

    @InjectMocks
    private RunFlowResultServiceImpl runFlowResultService;

    private RunFlowResultEntity entity;
    private RunFlowResultDto dto;
    private RunFlowResultQueryDto queryDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        entity = new RunFlowResultEntity();
        entity.setId(1L);
        entity.setFlowid(100L);
        entity.setContent("test content");
        entity.setStderr("test stderr");

        dto = new RunFlowResultDto();
        dto.setId(1L);
        dto.setFlowid(100L);
        dto.setContent("test content");
        dto.setStderr("test stderr");

        queryDto = new RunFlowResultQueryDto();
        queryDto.setFlowid(100L);
    }

    @Test
    @DisplayName("测试根据ID查询流程输出结果 - 正常场景")
    void testSelectRunFlowResultById_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(entity).when(runFlowResultMapper).selectRunFlowResultById(anyLong());

        // 执行测试
        RunFlowResultDto result = runFlowResultService.selectRunFlowResultById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(entity.getId(), result.getId());
        assertEquals(entity.getFlowid(), result.getFlowid());
        assertEquals(entity.getContent(), result.getContent());
        assertEquals(entity.getStderr(), result.getStderr());
        verify(runFlowResultMapper).selectRunFlowResultById(1L);
    }

    @Test
    @DisplayName("测试根据ID查询流程输出结果 - 异常场景")
    void testSelectRunFlowResultById_Exception() {
        // 准备测试数据
        doThrow(new RuntimeException("测试异常")).when(runFlowResultMapper).selectRunFlowResultById(anyLong());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowResultService.selectRunFlowResultById(1L));
        verify(runFlowResultMapper).selectRunFlowResultById(1L);
    }

    @Test
    @DisplayName("测试查询流程输出结果列表 - 正常场景")
    void testSelectRunFlowResultList_Normal() throws ContrastBusinessException {
        // 准备测试数据
        List<RunFlowResultEntity> entityList = Arrays.asList(entity);
        
        // 创建Page对象包装实体列表
        Page<RunFlowResultEntity> page = new Page<>(1, 10);
        page.addAll(entityList);
        page.setTotal(entityList.size());
        
        // 模拟查询结果
        doReturn(page).when(runFlowResultMapper).selectRunFlowResultList(any());
        
        // 执行测试
        PageInfo<RunFlowResultDto> result = runFlowResultService.selectRunFlowResultList(queryDto, 1, 10);
        
        // 验证结果
        assertNotNull(result);
        verify(runFlowResultMapper).selectRunFlowResultList(any());
    }

    @Test
    @DisplayName("测试查询流程输出结果列表 - 异常场景")
    void testSelectRunFlowResultList_Exception() {
        // 准备测试数据
        doThrow(new RuntimeException("测试异常")).when(runFlowResultMapper).selectRunFlowResultList(any());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowResultService.selectRunFlowResultList(queryDto, 1, 10));
        verify(runFlowResultMapper).selectRunFlowResultList(any());
    }

    @Test
    @DisplayName("测试新增流程输出结果 - 正常场景")
    void testInsertRunFlowResult_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(1).when(runFlowResultMapper).insertRunFlowResult(any());

        // 执行测试
        int result = runFlowResultService.insertRunFlowResult(dto);

        // 验证结果
        assertEquals(1, result);
        verify(runFlowResultMapper).insertRunFlowResult(any());
    }

    @Test
    @DisplayName("测试新增流程输出结果 - 异常场景")
    void testInsertRunFlowResult_Exception() {
        // 准备测试数据
        doThrow(new RuntimeException("测试异常")).when(runFlowResultMapper).insertRunFlowResult(any());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowResultService.insertRunFlowResult(dto));
        verify(runFlowResultMapper).insertRunFlowResult(any());
    }

    @Test
    @DisplayName("测试修改流程输出结果 - 正常场景")
    void testUpdateRunFlowResult_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(1).when(runFlowResultMapper).updateRunFlowResult(any());

        // 执行测试
        int result = runFlowResultService.updateRunFlowResult(dto);

        // 验证结果
        assertEquals(1, result);
        verify(runFlowResultMapper).updateRunFlowResult(any());
    }

    @Test
    @DisplayName("测试修改流程输出结果 - 异常场景")
    void testUpdateRunFlowResult_Exception() {
        // 准备测试数据
        doThrow(new RuntimeException("测试异常")).when(runFlowResultMapper).updateRunFlowResult(any());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowResultService.updateRunFlowResult(dto));
        verify(runFlowResultMapper).updateRunFlowResult(any());
    }

    @Test
    @DisplayName("测试批量删除流程输出结果 - 正常场景")
    void testDeleteRunFlowResultByIds_Normal() {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        doReturn(2).when(runFlowResultMapper).deleteRunFlowResultByIds(any());

        // 执行测试
        int result = runFlowResultService.deleteRunFlowResultByIds(ids);

        // 验证结果
        assertEquals(2, result);
        verify(runFlowResultMapper).deleteRunFlowResultByIds(ids);
    }

    @Test
    @DisplayName("测试删除单个流程输出结果 - 正常场景")
    void testDeleteRunFlowResultById_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(1).when(runFlowResultMapper).deleteRunFlowResultById(anyLong());

        // 执行测试
        int result = runFlowResultService.deleteRunFlowResultById(1L);

        // 验证结果
        assertEquals(1, result);
        verify(runFlowResultMapper).deleteRunFlowResultById(1L);
    }

    @Test
    @DisplayName("测试删除单个流程输出结果 - 异常场景")
    void testDeleteRunFlowResultById_Exception() {
        // 准备测试数据
        doThrow(new RuntimeException("测试异常")).when(runFlowResultMapper).deleteRunFlowResultById(anyLong());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowResultService.deleteRunFlowResultById(1L));
        verify(runFlowResultMapper).deleteRunFlowResultById(1L);
    }
} 