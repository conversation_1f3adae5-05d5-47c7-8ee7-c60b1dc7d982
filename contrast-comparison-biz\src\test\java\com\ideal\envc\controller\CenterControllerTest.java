package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.interaction.model.CenterDto;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.service.ICenterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 物理中心Controller的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class CenterControllerTest {

    @Mock
    private ICenterService centerService;

    @InjectMocks
    private CenterController centerController;

    private CenterQueryDto centerQueryDto;
    private List<CenterDto> centerDtoList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        centerQueryDto = new CenterQueryDto();
        centerQueryDto.setName("测试中心");

        centerDtoList = new ArrayList<>();
        CenterDto centerDto = new CenterDto();
        centerDto.setId(1L);
        centerDto.setName("测试中心");
        centerDtoList.add(centerDto);
    }

    @Test
    @DisplayName("测试获取中心列表 - 成功场景")
    void testList_Success() {
        // 设置 Mock 行为
        doReturn(centerDtoList).when(centerService).getCenterList(any(CenterQueryDto.class));

        // 执行方法
        R<List<CenterDto>> result = centerController.list(centerQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("测试中心", result.getData().get(0).getName());
        
        // 验证方法调用
        verify(centerService).getCenterList(centerQueryDto);
    }

    @Test
    @DisplayName("测试获取中心列表 - 空列表场景")
    void testList_EmptyList() {
        // 设置 Mock 行为
        doReturn(new ArrayList<>()).when(centerService).getCenterList(any(CenterQueryDto.class));

        // 执行方法
        R<List<CenterDto>> result = centerController.list(centerQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
        
        // 验证方法调用
        verify(centerService).getCenterList(centerQueryDto);
    }

    @Test
    @DisplayName("测试获取中心列表 - 查询条件为空场景")
    void testList_NullQuery() {
        // 设置 Mock 行为
        doReturn(centerDtoList).when(centerService).getCenterList(null);

        // 执行方法
        R<List<CenterDto>> result = centerController.list(null);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        
        // 验证方法调用
        verify(centerService).getCenterList(null);
    }

    @Test
    @DisplayName("测试获取中心列表 - 服务异常场景")
    void testList_ServiceException() {
        // 设置 Mock 行为
        doThrow(new RuntimeException("服务异常")).when(centerService).getCenterList(any(CenterQueryDto.class));

        // 执行方法并验证异常
        assertThrows(RuntimeException.class, () -> centerController.list(centerQueryDto));
        
        // 验证方法调用
        verify(centerService).getCenterList(centerQueryDto);
    }
} 