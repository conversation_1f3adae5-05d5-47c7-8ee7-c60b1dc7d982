package com.ideal.envc.common;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ContrastToolUtils Base64解码安全性测试
 */
class ContrastToolUtilsSecurityTest {

    @Test
    @DisplayName("测试正常的Base64解码")
    void testNormalBase64Decode() {
        // 正常的Base64编码字符串 "Hello World"
        String validBase64 = "SGVsbG8gV29ybGQ=";
        String result = ContrastToolUtils.getFromBase64(validBase64);
        assertEquals("Hello World", result);
    }

    @Test
    @DisplayName("测试空输入和null输入")
    void testEmptyAndNullInput() {
        assertNull(ContrastToolUtils.getFromBase64(null));
        assertNull(ContrastToolUtils.getFromBase64(""));
        assertNull(ContrastToolUtils.getFromBase64("   "));
    }

    @Test
    @DisplayName("测试无效的Base64格式")
    void testInvalidBase64Format() {
        // 长度不是4的倍数
        assertNull(ContrastToolUtils.getFromBase64("SGVsbG8"));
        
        // 包含非Base64字符
        assertNull(ContrastToolUtils.getFromBase64("SGVsbG8@"));
        assertNull(ContrastToolUtils.getFromBase64("SGVsbG8#"));
        assertNull(ContrastToolUtils.getFromBase64("SGVsbG8$"));
        
        // 填充字符位置错误
        assertNull(ContrastToolUtils.getFromBase64("SGV=bG8="));
        
        // 填充字符过多
        assertNull(ContrastToolUtils.getFromBase64("SGVs==="));
    }

    @Test
    @DisplayName("测试超大Base64输入 - 应该被拒绝")
    void testOversizedBase64Input() {
        // 创建一个超过10MB的Base64字符串
        StringBuilder largeInput = new StringBuilder();
        String baseUnit = "SGVsbG8gV29ybGQ="; // 16字符
        
        // 创建约11MB的Base64字符串 (超过MAX_BASE64_INPUT_LENGTH)
        int targetSize = 11 * 1024 * 1024; // 11MB
        while (largeInput.length() < targetSize) {
            largeInput.append(baseUnit);
        }
        
        String result = ContrastToolUtils.getFromBase64(largeInput.toString());
        assertNull(result, "超大Base64输入应该被拒绝");
    }

    @Test
    @DisplayName("测试解码后内容过大 - 应该被拒绝")
    void testOversizedDecodedContent() {
        // 创建一个Base64字符串，解码后会超过8MB
        // 使用重复的字符来创建大内容
        StringBuilder content = new StringBuilder();
        
        // 创建约9MB的原始内容
        int targetSize = 9 * 1024 * 1024; // 9MB
        while (content.length() < targetSize) {
            content.append("A"); // 简单重复字符
        }
        
        // 手动进行Base64编码（用于测试）
        String base64Content = java.util.Base64.getEncoder().encodeToString(content.toString().getBytes());
        
        String result = ContrastToolUtils.getFromBase64(base64Content);
        assertNull(result, "解码后内容过大应该被拒绝");
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "SGVsbG8=",      // "Hello"
        "V29ybGQ=",      // "World"  
        "VGVzdA==",      // "Test"
        "MTIzNDU2",      // "123456"
        "YWJjZGVm"       // "abcdef"
    })
    @DisplayName("测试各种有效的Base64字符串")
    void testValidBase64Strings(String base64Input) {
        String result = ContrastToolUtils.getFromBase64(base64Input);
        assertNotNull(result, "有效的Base64字符串应该能够正常解码");
        assertFalse(result.isEmpty(), "解码结果不应该为空");
    }

    @Test
    @DisplayName("测试边界情况 - 刚好达到限制的输入")
    void testBoundaryConditions() {
        // 测试刚好在限制内的Base64输入长度
        StringBuilder boundaryInput = new StringBuilder();
        String baseUnit = "SGVs"; // 4字符的Base64单元
        
        // 创建接近但不超过10MB的Base64字符串
        int maxLength = 10 * 1024 * 1024 - 100; // 略小于10MB
        while (boundaryInput.length() < maxLength) {
            boundaryInput.append(baseUnit);
        }
        
        // 确保长度是4的倍数
        while (boundaryInput.length() % 4 != 0) {
            boundaryInput.append("A");
        }
        
        String result = ContrastToolUtils.getFromBase64(boundaryInput.toString());
        // 这个测试主要验证不会因为长度限制而失败
        // 实际结果可能因为内容大小限制而为null，这是正常的
        // 重点是不应该抛出异常
        assertDoesNotThrow(() -> ContrastToolUtils.getFromBase64(boundaryInput.toString()));
    }

    @Test
    @DisplayName("测试中文字符的Base64编码解码")
    void testChineseCharacters() {
        // "你好世界" 的Base64编码
        String chineseBase64 = "5L2g5aW95LiW55WM";
        String result = ContrastToolUtils.getFromBase64(chineseBase64);
        assertNotNull(result);
        // 验证UTF-8编码处理正确
        assertTrue(result.length() > 0);
    }

    @Test
    @DisplayName("测试异常情况下的安全处理")
    void testExceptionHandling() {
        // 测试格式看起来正确但实际解码会失败的情况
        String malformedBase64 = "SGVsbG8gV29ybGQh"; // 缺少填充
        
        // 应该返回null而不是抛出异常
        assertDoesNotThrow(() -> {
            String result = ContrastToolUtils.getFromBase64(malformedBase64);
            // 可能返回null或者解码结果，但不应该抛出异常
        });
    }

    @Test
    @DisplayName("测试特殊字符的Base64编码")
    void testSpecialCharacters() {
        // 包含特殊字符的Base64编码
        String specialCharsBase64 = "IUAjJCVeJiooKQ=="; // "!@#$%^&*()"
        String result = ContrastToolUtils.getFromBase64(specialCharsBase64);
        assertNotNull(result);
        assertEquals("!@#$%^&*()", result);
    }
}
