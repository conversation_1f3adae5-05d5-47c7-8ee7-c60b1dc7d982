package com.ideal.envc.exception;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ScheduleJobOperateException单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("定时任务操作异常测试")
class ScheduleJobOperateExceptionTest {

    @Test
    @DisplayName("测试带消息的构造函数")
    void testConstructorWithMessage() {
        // given
        String message = "定时任务操作异常消息";

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(message);

        // then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试带代码和消息的构造函数")
    void testConstructorWithCodeAndMessage() {
        // given
        String code = "SCHEDULE_001";
        String message = "定时任务操作异常消息";
        String expectedMessage = code + message;

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(code, message);

        // then
        assertNotNull(exception);
        assertEquals(expectedMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试带代码、消息和原因的构造函数")
    void testConstructorWithCodeMessageAndCause() {
        // given
        String code = "SCHEDULE_002";
        String message = "定时任务操作异常消息";
        String expectedMessage = code + message;
        Throwable cause = new RuntimeException("原始异常");

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(code, message, cause);

        // then
        assertNotNull(exception);
        assertEquals(expectedMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带消息和Exception的构造函数")
    void testConstructorWithMessageAndException() {
        // given
        String message = "定时任务操作异常消息";
        Exception cause = new RuntimeException("原始异常");

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(message, cause);

        // then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带Exception的构造函数")
    void testConstructorWithException() {
        // given
        Exception cause = new RuntimeException("原始异常");

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(cause);

        // then
        assertNotNull(exception);
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带Throwable的构造函数")
    void testConstructorWithThrowable() {
        // given
        Throwable cause = new Error("严重错误");

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(cause);

        // then
        assertNotNull(exception);
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带NoSuchMethodException的构造函数")
    void testConstructorWithNoSuchMethodException() {
        // given
        NoSuchMethodException cause = new NoSuchMethodException("方法不存在");

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(cause);

        // then
        assertNotNull(exception);
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试异常继承关系")
    void testExceptionInheritance() {
        // given
        ScheduleJobOperateException exception = new ScheduleJobOperateException("测试消息");

        // then
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    @Test
    @DisplayName("测试空消息构造函数")
    void testConstructorWithNullMessage() {
        // given
        String message = null;

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(message);

        // then
        assertNotNull(exception);
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试空代码和消息构造函数")
    void testConstructorWithNullCodeAndMessage() {
        // given
        String code = null;
        String message = null;
        String expectedMessage = "nullnull";

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(code, message);

        // then
        assertNotNull(exception);
        assertEquals(expectedMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试空原因构造函数")
    void testConstructorWithNullCause() {
        // given
        String message = "测试消息";
        Exception cause = null;

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(message, cause);

        // then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试异常抛出和捕获")
    void testExceptionThrowAndCatch() {
        // given
        String message = "定时任务操作失败";

        // when & then
        assertThrows(ScheduleJobOperateException.class, () -> {
            throw new ScheduleJobOperateException(message);
        });

        try {
            throw new ScheduleJobOperateException(message);
        } catch (ScheduleJobOperateException e) {
            assertEquals(message, e.getMessage());
        }
    }

    @Test
    @DisplayName("测试异常链传递")
    void testExceptionChaining() {
        // given
        RuntimeException originalException = new RuntimeException("原始异常");
        String message = "定时任务操作异常";

        // when
        ScheduleJobOperateException exception = new ScheduleJobOperateException(message, originalException);

        // then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(originalException, exception.getCause());
        assertEquals("原始异常", exception.getCause().getMessage());
    }

    @Test
    @DisplayName("测试多层异常嵌套")
    void testNestedExceptions() {
        // given
        IllegalArgumentException level1 = new IllegalArgumentException("参数错误");
        RuntimeException level2 = new RuntimeException("运行时异常", level1);
        ScheduleJobOperateException level3 = new ScheduleJobOperateException("定时任务异常", level2);

        // then
        assertEquals("定时任务异常", level3.getMessage());
        assertEquals(level2, level3.getCause());
        assertEquals(level1, level3.getCause().getCause());
        assertEquals("参数错误", level3.getCause().getCause().getMessage());
    }
} 