package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 比对业务系统对象 ieai_envc_project
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class ProjectDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 系统ID */
    private Long businessSystemId;
    /** 系统编码 */
    private String businessSystemCode;
    /** 系统名称 */
    private String businessSystemName;
    /** 系统唯一标识 */
    private String businessSystemUnique;
    /** 系统描述 */
    private String businessSystemDesc;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人 */
    private String creatorName;
    /** 添加时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 是否有效（1：有效，0：失效） */
    private Integer status;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setBusinessSystemCode(String businessSystemCode){
        this.businessSystemCode = businessSystemCode;
    }

    public String getBusinessSystemCode(){
        return businessSystemCode;
    }

    public void setBusinessSystemName(String businessSystemName){
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemName(){
        return businessSystemName;
    }

    public void setBusinessSystemUnique(String businessSystemUnique){
        this.businessSystemUnique = businessSystemUnique;
    }

    public String getBusinessSystemUnique(){
        return businessSystemUnique;
    }

    public void setBusinessSystemDesc(String businessSystemDesc){
        this.businessSystemDesc = businessSystemDesc;
    }

    public String getBusinessSystemDesc(){
        return businessSystemDesc;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setStatus(Integer status){
        this.status = status;
    }

    public Integer getStatus(){
        return status;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",businessSystemId="+getBusinessSystemId()+
                    ",businessSystemCode="+getBusinessSystemCode()+
                    ",businessSystemName="+getBusinessSystemName()+
                    ",businessSystemUnique="+getBusinessSystemUnique()+
                    ",businessSystemDesc="+getBusinessSystemDesc()+
                    ",creatorId="+getCreatorId()+
                    ",creatorName="+getCreatorName()+
                    ",createTime="+getCreateTime()+
                    ",status="+getStatus()+
                    ",updatorId="+getUpdatorId()+
                    ",updatorName="+getUpdatorName()+
                    ",updateTime="+getUpdateTime()+
                "]";
    }
}

