package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IResultMonitorService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ResultDetailController的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ResultDetailController单元测试")
class ResultDetailControllerTest {

    @Mock
    private IResultMonitorService resultMonitorService;

    @InjectMocks
    private ResultDetailController resultDetailController;

    @BeforeEach
    void setUp() {
        resultDetailController = new ResultDetailController(resultMonitorService);
    }

    @Test
    @DisplayName("查询文件比对详情 - 成功场景（que=0）")
    void detail_Success_QueZero() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;
        Integer que = 0;
        String content = "<tr height=\"5\" class=\"cps_tr1\">test1</tr><tr class=\"cps_tr2\">test2</tr>";

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId)).thenReturn(content);

        // 执行测试
        R<String> result = resultDetailController.detail(flowId, que);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(content, result.getData());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }

    @Test
    @DisplayName("查询文件比对详情 - 成功场景（que=1）")
    void detail_Success_QueOne() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;
        Integer que = 1;
        String content = "<tr height=\"5\" class=\"cps_tr1\">test1</tr><tr class=\"cps_tr2\">test2</tr>";
        String expectedContent = "<tr hidden height=\"5\" class=\"cps_tr1\">test1</tr><tr hidden class=\"cps_tr2\">test2</tr>";

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId)).thenReturn(content);

        // 执行测试
        R<String> result = resultDetailController.detail(flowId, que);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(expectedContent, result.getData());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }

    @Test
    @DisplayName("查询文件比对详情 - que为空")
    void detail_NullQue() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;
        String content = "<tr height=\"5\" class=\"cps_tr1\">test1</tr><tr class=\"cps_tr2\">test2</tr>";

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId)).thenReturn(content);

        // 执行测试
        R<String> result = resultDetailController.detail(flowId, null);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(content, result.getData());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }

    @Test
    @DisplayName("查询文件比对详情 - 业务异常")
    void detail_BusinessException() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;
        Integer que = 0;
        String errorMessage = "数据不存在";

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId))
            .thenThrow(new ContrastBusinessException(errorMessage));

        // 执行测试
        R<String> result = resultDetailController.detail(flowId, que);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), result.getCode());
        assertEquals(errorMessage, result.getMessage());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }

    @Test
    @DisplayName("查询文件比对详情 - 系统异常")
    void detail_SystemException() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;
        Integer que = 0;

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId))
            .thenThrow(new RuntimeException("系统错误"));

        // 执行测试
        R<String> result = resultDetailController.detail(flowId, que);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }

    @Test
    @DisplayName("查询目录比对详情 - 成功场景")
    void dirDetail_Success() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;
        String content = "<div>directory content</div>";

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId)).thenReturn(content);

        // 执行测试
        R<String> result = resultDetailController.dirDetail(flowId);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(content, result.getData());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }

    @Test
    @DisplayName("查询目录比对详情 - 业务异常")
    void dirDetail_BusinessException() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;
        String errorMessage = "数据不存在";

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId))
            .thenThrow(new ContrastBusinessException(errorMessage));

        // 执行测试
        R<String> result = resultDetailController.dirDetail(flowId);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), result.getCode());
        assertEquals(errorMessage, result.getMessage());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }

    @Test
    @DisplayName("查询目录比对详情 - 系统异常")
    void dirDetail_SystemException() throws ContrastBusinessException {
        // 准备测试数据
        Long flowId = 1L;

        // Mock依赖
        when(resultMonitorService.selectContentForCompareFileByFlowId(flowId))
            .thenThrow(new RuntimeException("系统错误"));

        // 执行测试
        R<String> result = resultDetailController.dirDetail(flowId);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        verify(resultMonitorService).selectContentForCompareFileByFlowId(flowId);
    }
} 