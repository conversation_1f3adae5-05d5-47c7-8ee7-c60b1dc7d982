package com.ideal.envc.model.dto;

/**
 * <AUTHOR>
 */
public class ContrastScheduleJobTaskDto {

    /** 任务ID */
    private Long taskId;

    /** 任务名称 */
    private String taskName;

    /** 方案ID */
    private Long planId;

    /** 方案名称 */
    private String planName;

    /** cron表达式 */
    private String cron;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /** xxjob任务ID */
    private Long scheduleJobId;

    /** job处理器名称 */
    private String jobHandlerName;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }


    public Long getScheduleJobId() {
        return scheduleJobId;
    }

    public void setScheduleJobId(Long scheduleJobId) {
        this.scheduleJobId = scheduleJobId;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getJobHandlerName() {
        return jobHandlerName;
    }

    public void setJobHandlerName(String jobHandlerName) {
        this.jobHandlerName = jobHandlerName;
    }

    @Override
    public String toString() {
        return "ContrastScheduleJobTaskDto{" +
                "taskId=" + taskId +
                ", taskName='" + taskName + '\'' +
                ", planId=" + planId +
                ", planName='" + planName + '\'' +
                ", cron='" + cron + '\'' +
                ", createName='" + createName + '\'' +
                ", creatorId=" + creatorId +
                ", scheduleJobId=" + scheduleJobId +
                ", jobHandlerName='" + jobHandlerName + '\'' +
                '}';
    }
}
