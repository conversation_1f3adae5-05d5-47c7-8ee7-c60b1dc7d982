#!/bin/bash

#应用完整根路径
SYSTEM_DIR="$(dirname "$( cd "$( dirname "${'$'}{BASH_SOURCE[0]}" )" &> /dev/null && pwd )")"
cd $SYSTEM_DIR
echo "$SYSTEM_DIR"
#bin的完整路径
BIN_DIR="$SYSTEM_DIR/bin"
#config文件的完整路径
CONF_DIR="$SYSTEM_DIR/conf"
#JRE完整路径
JRE_DIR="$SYSTEM_DIR/jre"
#应用JAR的完整路径，名称需要动态
JAR_FILE="$BIN_DIR/${appName}"
#java命令
JAVA_COMMAND="java"

# 检查 jre 文件夹是否存在，并且不为空
if [ -d "$JRE_DIR" ] && [ -f "$JRE_DIR/bin/java" ]; then
echo "Using JRE from $JRE_DIR"
JAVA_COMMAND="$JRE_DIR/bin/java"
else
echo "Using system default JRE"
# 不设置JAVA_HOME，使用系统PATH中的java
fi
#启动命令
#增加jvm参数的配置，动态配置
nohup "$JAVA_COMMAND" -jar ${jvmOption} "$JAR_FILE" --spring.cloud.bootstrap.location="$CONF_DIR"/bootstrap.yml >/dev/null 2>&1 & echo $! > $BIN_DIR/pid_file