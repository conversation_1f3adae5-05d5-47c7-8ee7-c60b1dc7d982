package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.bean.SystemComputerNodeListBean;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 系统与设备节点关系Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface SystemComputerNodeMapper {
    /**
     * 查询系统与设备节点关系
     *
     * @param id 系统与设备节点关系主键
     * @return 系统与设备节点关系
     */
    SystemComputerNodeEntity selectSystemComputerNodeById(Long id);

    /**
     * 查询系统与设备节点关系列表
     *
     * @param systemComputerNode 系统与设备节点关系
     * @return 系统与设备节点关系集合
     */
    List<SystemComputerNodeEntity> selectSystemComputerNodeList(SystemComputerNodeEntity systemComputerNode);

    /**
     * 新增系统与设备节点关系
     *
     * @param systemComputerNode 系统与设备节点关系
     * @return 结果
     */
    int insertSystemComputerNode(SystemComputerNodeEntity systemComputerNode);

    /**
     * 修改系统与设备节点关系
     *
     * @param systemComputerNode 系统与设备节点关系
     * @return 结果
     */
    int updateSystemComputerNode(SystemComputerNodeEntity systemComputerNode);

    /**
     * 删除系统与设备节点关系
     *
     * @param id 系统与设备节点关系主键
     * @return 结果
     */
    int deleteSystemComputerNodeById(Long id);

    /**
     * 批量删除系统与设备节点关系
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSystemComputerNodeByIds(Long[] ids);

    /**
     * 查询系统已绑定源目标设备列表
     *
     * @param businessSystemId 业务系统ID
     * @param sourceCenterId 源中心ID
     * @param targetCenterId 目标中心ID
     * @param sourceComputerIp 源设备IP
     * @param targetComputerIp 目标设备IP
     * @return 系统已绑定源目标设备列表
     */
    List<SystemComputerNodeListBean> selectSystemComputerNodeListByCondition(@Param("businessSystemId") Long businessSystemId,@Param("sourceCenterId") Long sourceCenterId, @Param("targetCenterId") Long targetCenterId, @Param("sourceComputerIp") String sourceComputerIp, @Param("targetComputerIp") String targetComputerIp);

    /**
     * 根据业务系统ID集合批量查询系统与设备节点关系
     * @param businessSystemIdList 业务系统ID集合
     * @return 系统与设备节点关系集合
     */
    List<SystemComputerNodeEntity> selectSystemComputerNodeByBusinessSystemIds(List<Long> businessSystemIdList);

    /**
     * 根据系统计算机ID集合查询系统计算机节点
     *
     * @param systemComputerIds 系统计算机ID集合
     * @return 系统计算机节点集合
     */
    List<SystemComputerNodeEntity> selectSystemComputerNodeBySystemComputerIds(List<Long> systemComputerIds);

    /**
     * 根据业务系统ID和设备ID列表查询需要删除的节点记录
     *
     * @param businessSystemId 业务系统ID
     * @param computerIds 设备ID列表
     * @return 需要删除的节点记录列表
     */
    List<SystemComputerNodeEntity> selectSystemComputerNodeByBusinessSystemIdAndComputerIds(@Param("businessSystemId") Long businessSystemId, @Param("computerIds") List<Long> computerIds);
}
