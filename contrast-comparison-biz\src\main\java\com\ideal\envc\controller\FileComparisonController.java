package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IFileComparisonService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 文件比较控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/fileComparison")
@Validated
public class FileComparisonController {
    private static final Logger logger = LoggerFactory.getLogger(FileComparisonController.class);

    private final IFileComparisonService fileComparisonService;
    private final UserinfoComponent userinfoComponent;

    public FileComparisonController(IFileComparisonService fileComparisonService, 
                                   UserinfoComponent userinfoComponent) {
        this.fileComparisonService = fileComparisonService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 比较文件内容
     *
     * @param request 比较请求参数
     * @return 比较结果
     */
    @PostMapping("/compare")
    @MethodPermission("@dp.hasBtnPermission('file-comparison')")
    public R<FileComparisonResultDto> compare(@Valid @RequestBody FileComparisonRequestDto request) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            logger.info("用户{}开始比较文件内容，基线服务器：{}，目标服务器：{}", 
                    userDto.getFullName(), request.getBaselineServer(), request.getTargetServer());

            FileComparisonResultDto result = fileComparisonService.compareFileContents(request);
            
            logger.info("文件比较完成，结果统计 - 基线文件：{}个，目标文件：{}个，一致：{}个，不一致：{}个，缺失：{}个，多出：{}个", 
                    result.getTotalSourceFiles(), result.getTotalTargetFiles(),
                    result.getConsistentCount(), result.getInconsistentCount(),
                    result.getMissingCount(), result.getExtraCount());

            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());

        } catch (Exception e) {
            logger.error("文件比较失败", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), "文件比较失败：" + e.getMessage());
        }
    }

    /**
     * 导出文件比较结果到Excel
     *
     * @param request 比较请求参数
     * @param response HTTP响应对象
     */
    @PostMapping("/export")
    @MethodPermission("@dp.hasBtnPermission('file-comparison')")
    public void export(@Valid @RequestBody FileComparisonRequestDto request, HttpServletResponse response) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            logger.info("用户{}开始导出文件比较结果，基线服务器：{}，目标服务器：{}", 
                    userDto.getFullName(), request.getBaselineServer(), request.getTargetServer());

            fileComparisonService.exportComparisonResult(request, response);
            
            logger.info("文件比较结果导出完成");

        } catch (Exception e) {
            logger.error("导出文件比较结果失败", e);
            try {
                response.reset();
                response.setContentType("application/json;charset=utf-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 快速比较（简化版接口，用于测试）
     *
     * @param request 比较请求参数
     * @return 比较结果摘要
     */
    @PostMapping("/quickCompare")
    @MethodPermission("@dp.hasBtnPermission('file-comparison')")
    public R<String> quickCompare(@Valid @RequestBody FileComparisonRequestDto request) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            logger.info("用户{}开始快速比较文件内容", userDto.getFullName());

            FileComparisonResultDto result = fileComparisonService.compareFileContents(request);
            
            // 构建摘要信息
            StringBuilder summary = new StringBuilder();
            summary.append("比较完成！");
            summary.append("基线文件：").append(result.getTotalSourceFiles()).append("个，");
            summary.append("目标文件：").append(result.getTotalTargetFiles()).append("个；");
            summary.append("一致：").append(result.getConsistentCount()).append("个");
            
            if (result.getConsistentRate() != null) {
                summary.append("（").append(result.getConsistentRate()).append("%）");
            }
            
            summary.append("，不一致：").append(result.getInconsistentCount()).append("个");
            
            if (result.getInconsistentRate() != null) {
                summary.append("（").append(result.getInconsistentRate()).append("%）");
            }
            
            summary.append("，缺失：").append(result.getMissingCount()).append("个");
            
            if (result.getMissingRate() != null) {
                summary.append("（").append(result.getMissingRate()).append("%）");
            }
            
            summary.append("，多出：").append(result.getExtraCount()).append("个");
            
            if (result.getExtraRate() != null) {
                summary.append("（").append(result.getExtraRate()).append("%）");
            }

            logger.info("快速比较完成：{}", summary.toString());
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), summary.toString(), ResponseCodeEnum.SUCCESS.getDesc());

        } catch (Exception e) {
            logger.error("快速比较失败", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), "快速比较失败：" + e.getMessage());
        }
    }
}
