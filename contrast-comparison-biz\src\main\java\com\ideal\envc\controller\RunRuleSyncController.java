package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.RunRuleSyncDto;
import com.ideal.envc.model.dto.RunRuleSyncQueryDto;
import com.ideal.envc.service.IRunRuleSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 节点规则同步结果Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@RestController
@RequestMapping("/envc/sync")
public class RunRuleSyncController {
    private final Logger logger = LoggerFactory.getLogger(RunRuleSyncController.class);

    private final IRunRuleSyncService runRuleSyncService;

    public RunRuleSyncController(IRunRuleSyncService runRuleSyncService) {
        this.runRuleSyncService = runRuleSyncService;
    }

    /**
     * 查询节点规则同步结果列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<RunRuleSyncDto>> list(TableQueryDto<RunRuleSyncQueryDto> tableQueryDto) {
        PageInfo<RunRuleSyncDto> list = runRuleSyncService.selectRunRuleSyncList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询节点规则同步结果详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<RunRuleSyncDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(runRuleSyncService.selectRunRuleSyncById(id));
    }

    /**
     * 新增保存节点规则同步结果
     *
     * @param runRuleSyncDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody RunRuleSyncDto runRuleSyncDto) {
        try {
            if (runRuleSyncService.insertRunRuleSync(runRuleSyncDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("新增节点规则同步结果系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存节点规则同步结果
     *
     * @param runRuleSyncDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody RunRuleSyncDto runRuleSyncDto) {
        runRuleSyncService.updateRunRuleSync(runRuleSyncDto);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
    }


    /**
     * 删除节点规则同步结果
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        runRuleSyncService.deleteRunRuleSyncByIds(ids);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
    }
}
