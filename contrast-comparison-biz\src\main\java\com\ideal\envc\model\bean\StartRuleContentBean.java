package com.ideal.envc.model.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class StartRuleContentBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 节点规则结果ID */
    private Long envcRunRuleId;
    /** 内容类型（0：源内容，1：目标内容，2：差异内容） */
    private Integer contentType;
    /** 内容 */
    private String content;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcRunRuleId() {
        return envcRunRuleId;
    }

    public void setEnvcRunRuleId(Long envcRunRuleId) {
        this.envcRunRuleId = envcRunRuleId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
