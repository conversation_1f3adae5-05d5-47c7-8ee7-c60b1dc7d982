package com.ideal.envc.service;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.NodeBatchSaveRequestDto;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryPageDto;
import com.ideal.envc.model.dto.UserDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface INodeIndexService {

    /**
     * 批量保存节点规则信息关系
     *
     * @param nodeBatchSaveRequestDto 节点规则关系
     * @param userDto 用户信息
     * @return 结果
     */
    boolean batchSaveNodeRelation(NodeBatchSaveRequestDto nodeBatchSaveRequestDto, UserDto userDto);

    /**
     * 系统设备查询分页
     * @param tableQueryDto 查询条件
     * @return 设备列表分页信息
     * @throws ContrastBusinessException 业务异常
     */
    PageInfo<SystemComputerDto> selectSystemComputerListPage(TableQueryDto<SystemComputerQueryPageDto> tableQueryDto) throws ContrastBusinessException;

    /**
     * 系统设备查询列表（不分页）
     * @param businessSystemId 业务系统ID
     * @param centerId 中心ID
     * @param excludeComputerIds 排除设备ID集合
     * @return 设备列表
     */
    List<SystemComputerDto> selectSystemComputerList(Long businessSystemId, Long centerId, List<Long> excludeComputerIds);
}
