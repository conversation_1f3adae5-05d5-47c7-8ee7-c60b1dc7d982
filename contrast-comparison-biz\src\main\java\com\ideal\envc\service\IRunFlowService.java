package com.ideal.envc.service;

import com.ideal.envc.model.dto.RunFlowDto;
import com.ideal.envc.model.dto.RunFlowQueryDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.exception.ContrastBusinessException;

/**
 * 节点规则流程Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IRunFlowService {
    /**
     * 查询节点规则流程
     *
     * @param id 节点规则流程主键
     * @return 节点规则流程
     */
    RunFlowDto selectRunFlowById(Long id) throws ContrastBusinessException;

    /**
     * 查询节点规则流程列表
     *
     * @param runFlowQueryDto 节点规则流程
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点规则流程集合
     */
    PageInfo<RunFlowDto> selectRunFlowList(RunFlowQueryDto runFlowQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;

    /**
     * 新增节点规则流程
     *
     * @param runFlowDto 节点规则流程
     * @return 结果
     */
    int insertRunFlow(RunFlowDto runFlowDto) throws ContrastBusinessException;

    /**
     * 修改节点规则流程
     *
     * @param runFlowDto 节点规则流程
     * @return 结果
     */
    int updateRunFlow(RunFlowDto runFlowDto) throws ContrastBusinessException;

    /**
     * 批量删除节点规则流程
     *
     * @param ids 需要删除的节点规则流程主键集合
     * @return 结果
     */
    int deleteRunFlowByIds(Long[] ids) throws ContrastBusinessException;
}
