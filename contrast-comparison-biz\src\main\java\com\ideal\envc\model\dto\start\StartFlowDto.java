package com.ideal.envc.model.dto.start;

/**
 * <AUTHOR>
 */
import com.ideal.envc.model.dto.UserDto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *  启动工作流Dto
 * <AUTHOR>
 */
public class StartFlowDto implements Serializable {
    private static final long serialVersionUID = 1L;
    // 用户信息
    private UserDto userInfo;
    // 工程名
    private String prjName;
    // 工作流名
    private String flowName;
    // 工作流输入参数
    private List<Serializable > args;
    // 环境参数
    private Map<String,Serializable > envVars;
    // 实例名
    private String iinsName;
    // 描述
    private String comment;

    private Boolean forceEfficiencyPrior;

    private Date startTime;

    private Integer type;
    private Integer prjType;
    private Boolean isAdvance;

    private WorkflowLogConfigDto logConfig;

    private Boolean starterSucElem;

    private Long taskFlowId;


    public String getPrjName() {
        return prjName;
    }

    public void setPrjName(String prjName) {
        this.prjName = prjName;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public List<Serializable> getArgs() {
        return args;
    }

    public void setArgs(List<Serializable> args) {
        this.args = args;
    }

    public Map<String, Serializable> getEnvVars() {
        return envVars;
    }

    public void setEnvVars(Map<String, Serializable> envVars) {
        this.envVars = envVars;
    }

    public String getIinsName() {
        return iinsName;
    }

    public void setIinsName(String iinsName) {
        this.iinsName = iinsName;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Boolean getForceEfficiencyPrior() {
        return forceEfficiencyPrior;
    }

    public void setForceEfficiencyPrior(Boolean forceEfficiencyPrior) {
        this.forceEfficiencyPrior = forceEfficiencyPrior;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getPrjType() {
        return prjType;
    }

    public void setPrjType(Integer prjType) {
        this.prjType = prjType;
    }

    public Boolean getIsAdvance() {
        return isAdvance;
    }

    public void setIsAdvance(Boolean isAdvance) {
        this.isAdvance = isAdvance;
    }

    public WorkflowLogConfigDto getLogConfig() {
        return logConfig;
    }

    public void setLogConfig(WorkflowLogConfigDto logConfig) {
        this.logConfig = logConfig;
    }

    public Boolean getStarterSucElem() {
        return starterSucElem;
    }

    public void setStarterSucElem(Boolean starterSucElem) {
        this.starterSucElem = starterSucElem;
    }

    public Long getTaskFlowId() {
        return taskFlowId;
    }

    public void setTaskFlowId(Long taskFlowId) {
        this.taskFlowId = taskFlowId;
    }

    public UserDto getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserDto userInfo) {
        this.userInfo = userInfo;
    }

    public Boolean getAdvance() {
        return isAdvance;
    }

    public void setAdvance(Boolean advance) {
        isAdvance = advance;
    }
}
