package com.ideal.envc;

import com.ideal.message.center.ISubscriber;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import java.util.Map;
import java.util.function.Consumer;

/**
 * MQ consumer config
 * 
 * 说明：
 * 1. 此配置类只定义消费者函数（Consumer）
 * 2. 生产者通过StreamBridge发送消息，不需要在此定义函数
 * 3. function.definition中只需要包含消费者函数名称
 * 
 * <AUTHOR>
 */
@Component
public class ContrastMqRegisterConfig {
    private static final Logger logger = LoggerFactory.getLogger(ContrastMqRegisterConfig.class);

    private final Map<String, ISubscriber> subscribers;

    public ContrastMqRegisterConfig(Map<String, ISubscriber> subscribers) {
        this.subscribers = subscribers;
    }

    /**
     * 接收任务发送结果消费者
     *
     * @return {@link Consumer }<{@link String }>
     */
    @Bean
    @ConditionalOnBean(name = "contrastReceiveTaskSendResultHandler")
    public Consumer<String> contrastReceiveTaskSendResult() {
        return message -> {
            ISubscriber subscriber = subscribers.get("contrastReceiveTaskSendResultHandler");
            if (subscriber != null) {
                subscriber.notice(message);
            } else {
                logger.warn("未找到订阅者: contrastReceiveTaskSendResultHandler");
            }
        };
    }

//    /**
//     * 实例详情状态变更消费者
//     *
//     * @return {@link Consumer }<{@link String }>
//     */
//    @Bean
//    @ConditionalOnBean(name = "runInstanceInfoStateConsumer")
//    public Consumer<String> runInstanceInfoState() {
//        return message -> subscribers.get("runInstanceInfoStateConsumer").notice(message);
//    }
//
//    /**
//     * 实例状态变更消费者
//     *
//     * @return {@link Consumer }<{@link String }>
//     */
//    @Bean
//    @ConditionalOnBean(name = "runInstanceStateConsumer")
//    public Consumer<String> runInstanceState() {
//        return message -> subscribers.get("runInstanceStateConsumer").notice(message);
//    }
}
