<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.StartContrastBaseMapper">

    <!-- 实例结果映射 -->
    <resultMap type="com.ideal.envc.model.entity.RunInstanceEntity" id="RunInstanceResult">
        <id property="id" column="iid"/>
        <result property="envcPlanId" column="ienvc_plan_id"/>
        <result property="envcTaskId" column="ienvc_task_id"/>
        <result property="result" column="iresult"/>
        <result property="state" column="istate"/>
        <result property="from" column="ifrom"/>
        <result property="starterName" column="istarter_name"/>
        <result property="starterId" column="istarter_id"/>
        <result property="startTime" column="istart_time"/>
        <result property="endTime" column="iend_time"/>
        <result property="elapsedTime" column="ielapsed_time"/>
    </resultMap>

    <!-- 实例详情结果映射 -->
    <resultMap type="com.ideal.envc.model.entity.RunInstanceInfoEntity" id="RunInstanceInfoResult">
        <id property="id" column="iid"/>
        <result property="envcRunInstanceId" column="ienvc_run_instance_id"/>
        <result property="envcPlanId" column="ienvc_plan_id"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="sourceCenterId" column="isource_center_id"/>
        <result property="sourceCenterName" column="isource_center_name"/>
        <result property="targetCenterId" column="itarget_center_id"/>
        <result property="targetCenterName" column="itarget_center_name"/>
        <result property="sourceComputerId" column="isource_computer_id"/>
        <result property="sourceComputerIp" column="isource_computer_ip"/>
        <result property="sourceComputerPort" column="isource_computer_port"/>
        <result property="sourceComputerOs" column="isource_computer_os"/>
        <result property="targetComputerId" column="itarget_computer_id"/>
        <result property="targetComputerIp" column="itarget_computer_ip"/>
        <result property="targetComputerPort" column="itarget_computer_port"/>
        <result property="targetComputerOs" column="itarget_computer_os"/>
        <result property="storeTime" column="istore_time"/>
        <result property="result" column="iresult"/>
        <result property="state" column="istate"/>
    </resultMap>

    <!-- 规则结果映射 -->
    <resultMap type="com.ideal.envc.model.entity.RunRuleEntity" id="RunRuleResult">
        <id property="id" column="iid"/>
        <result property="envcRunInstanceInfoId" column="ienvc_run_instance_info_id"/>
        <result property="model" column="imodel"/>
        <result property="type" column="itype"/>
        <result property="path" column="ipath"/>
        <result property="encode" column="iencode"/>
        <result property="way" column="iway"/>
        <result property="ruleType" column="irule_type"/>
        <result property="enabled" column="ienabled"/>
        <result property="childLevel" column="ichild_level"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="endTime" column="iend_time"/>
        <result property="result" column="iresult"/>
        <result property="state" column="istate"/>
        <result property="elapsedTime" column="ielapsed_time"/>
    </resultMap>

    <!-- 规则同步结果映射 -->
    <resultMap type="com.ideal.envc.model.entity.RunRuleSyncEntity" id="RunRuleSyncResult">
        <id property="id" column="iid"/>
        <result property="envcRunRuleId" column="ienvc_run_rule_id"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="endTime" column="iend_time"/>
        <result property="result" column="iresult"/>
        <result property="state" column="istate"/>
        <result property="elapsedTime" column="ielapsed_time"/>
    </resultMap>

    <!-- 根据实例ID查询实例信息 -->
    <select id="selectRunInstanceById" parameterType="java.lang.Long" resultMap="RunInstanceResult">
        SELECT
            iid, ienvc_plan_id, ienvc_task_id, iresult, istate, ifrom,
            istarter_name, istarter_id, istart_time, iend_time, ielapsed_time
        FROM
            ieai_envc_run_instance
        WHERE
            iid = #{instanceId}
    </select>

    <!-- 根据方案ID查询实例信息列表 -->
    <select id="selectRunInstancesByPlanId" parameterType="java.lang.Long" resultMap="RunInstanceResult">
        SELECT
            iid, ienvc_plan_id, ienvc_task_id, iresult, istate, ifrom,
            istarter_name, istarter_id, istart_time, iend_time, ielapsed_time
        FROM
            ieai_envc_run_instance
        WHERE
            ienvc_plan_id = #{planId}
        ORDER BY
            istart_time DESC
    </select>

    <!-- 根据任务ID查询实例信息列表 -->
    <select id="selectRunInstancesByTaskId" parameterType="java.lang.Long" resultMap="RunInstanceResult">
        SELECT
            iid, ienvc_plan_id, ienvc_task_id, iresult, istate, ifrom,
            istarter_name, istarter_id, istart_time, iend_time, ielapsed_time
        FROM
            ieai_envc_run_instance
        WHERE
            ienvc_task_id = #{taskId}
        ORDER BY
            istart_time DESC
    </select>

    <!-- 根据实例ID查询实例详情列表 -->
    <select id="selectRunInstanceInfosByInstanceId" parameterType="java.lang.Long" resultMap="RunInstanceInfoResult">
        SELECT
            iid, ienvc_run_instance_id, ienvc_plan_id, ibusiness_system_id,
            isource_center_id, isource_center_name, itarget_center_id, itarget_center_name, isource_computer_id,
            isource_computer_ip, isource_computer_port, isource_computer_os,
            itarget_computer_id, itarget_computer_ip, itarget_computer_port,
            itarget_computer_os, istore_time, iresult, istate
        FROM
            ieai_envc_run_instance_info
        WHERE
            ienvc_run_instance_id = #{instanceId}
    </select>

    <!-- 根据实例详情ID查询规则列表 -->
    <select id="selectRunRulesByInstanceInfoId" parameterType="java.lang.Long" resultMap="RunRuleResult">
        SELECT
            iid, ienvc_run_instance_info_id, imodel, itype, ipath, iencode,
            iway, irule_type, ienabled, ichild_level, icreator_id, icreator_name,
            icreate_time, iend_time, iresult, istate, ielapsed_time
        FROM
            ieai_envc_run_rule
        WHERE
            ienvc_run_instance_info_id = #{instanceInfoId}
    </select>

    <!-- 根据规则ID查询规则同步信息 -->
    <select id="selectRunRuleSyncByRuleId" parameterType="java.lang.Long" resultMap="RunRuleSyncResult">
        SELECT
            iid, ienvc_run_rule_id, icreator_id, icreator_name,
            icreate_time, iend_time, iresult, istate, ielapsed_time
        FROM
            ieai_envc_run_rule_sync
        WHERE
            ienvc_run_rule_id = #{ruleId}
    </select>

    <!-- 层次化实例结果映射 -->
    <resultMap type="com.ideal.envc.model.bean.HierarchicalRunInstanceBean" id="HierarchicalRunInstanceResult">
        <id property="id" column="instance_id"/>
        <result property="envcPlanId" column="instance_plan_id"/>
        <result property="envcTaskId" column="instance_task_id"/>
        <result property="result" column="instance_result"/>
        <result property="state" column="instance_state"/>
        <result property="from" column="instance_from"/>
        <result property="starterName" column="instance_starter_name"/>
        <result property="starterId" column="instance_starter_id"/>
        <result property="startTime" column="instance_start_time"/>
        <result property="endTime" column="instance_end_time"/>
        <result property="elapsedTime" column="instance_elapsed_time"/>

        <!-- 实例详情集合 -->
        <collection property="instanceInfoList" ofType="com.ideal.envc.model.bean.HierarchicalRunInstanceInfoBean" resultMap="RunInstanceInfoResultMap"/>
    </resultMap>

    <!-- 实例详情结果映射 -->
    <resultMap id="RunInstanceInfoResultMap" type="com.ideal.envc.model.bean.HierarchicalRunInstanceInfoBean">
        <id property="id" column="info_id"/>
        <result property="envcRunInstanceId" column="info_instance_id"/>
        <result property="envcPlanId" column="info_plan_id"/>
        <result property="businessSystemId" column="info_business_system_id"/>
        <result property="sourceCenterId" column="info_source_center_id"/>
        <result property="sourceCenterName" column="info_source_center_name"/>
        <result property="targetCenterId" column="info_target_center_id"/>
        <result property="targetCenterName" column="info_target_center_name"/>
        <result property="sourceComputerId" column="info_source_computer_id"/>
        <result property="sourceComputerIp" column="info_source_computer_ip"/>
        <result property="sourceComputerPort" column="info_source_computer_port"/>
        <result property="sourceComputerOs" column="info_source_computer_os"/>
        <result property="targetComputerId" column="info_target_computer_id"/>
        <result property="targetComputerIp" column="info_target_computer_ip"/>
        <result property="targetComputerPort" column="info_target_computer_port"/>
        <result property="targetComputerOs" column="info_target_computer_os"/>
        <result property="storeTime" column="info_store_time"/>
        <result property="result" column="info_result"/>
        <result property="state" column="info_state"/>

        <!-- 规则集合 -->
        <collection property="ruleList" ofType="com.ideal.envc.model.bean.HierarchicalRunRuleBean" resultMap="RunRuleResultMap"/>
    </resultMap>

    <!-- 规则结果映射 -->
    <resultMap id="RunRuleResultMap" type="com.ideal.envc.model.bean.HierarchicalRunRuleBean">
        <id property="id" column="rule_id"/>
        <result property="envcRunInstanceInfoId" column="rule_instance_info_id"/>
        <result property="model" column="rule_model"/>
        <result property="type" column="rule_type"/>
        <result property="path" column="rule_path"/>
        <result property="encode" column="rule_encode"/>
        <result property="way" column="rule_way"/>
        <result property="ruleType" column="rule_rule_type"/>
        <result property="enabled" column="rule_enabled"/>
        <result property="childLevel" column="rule_child_level"/>
        <result property="creatorId" column="rule_creator_id"/>
        <result property="creatorName" column="rule_creator_name"/>
        <result property="createTime" column="rule_create_time"/>
        <result property="endTime" column="rule_end_time"/>
        <result property="result" column="rule_result"/>
        <result property="state" column="rule_state"/>
        <result property="elapsedTime" column="rule_elapsed_time"/>

        <!-- 规则同步信息 -->
        <association property="ruleSync" javaType="com.ideal.envc.model.bean.HierarchicalRunRuleSyncBean" resultMap="RunRuleSyncResultMap"/>

        <!-- 规则流程信息 -->
        <association property="ruleFlow" javaType="com.ideal.envc.model.bean.HierarchicalRunFlowBean" resultMap="RunFlowResultMap"/>
    </resultMap>

    <!-- 规则流程结果映射 -->
    <resultMap id="RunFlowResultMap" type="com.ideal.envc.model.bean.HierarchicalRunFlowBean">
        <id property="id" column="flow_id"/>
        <result property="flowid" column="flow_flowid"/>
        <result property="runBizId" column="flow_run_biz_id"/>
        <result property="model" column="flow_model"/>
        <result property="state" column="flow_state"/>
        <result property="elapsedTime" column="flow_elapsed_time"/>
        <result property="ret" column="flow_ret"/>
        <result property="updateOrderTime" column="flow_update_order_time"/>
    </resultMap>

    <!-- 规则同步结果映射 -->
    <resultMap id="RunRuleSyncResultMap" type="com.ideal.envc.model.bean.HierarchicalRunRuleSyncBean">
        <id property="id" column="sync_id"/>
        <result property="envcRunRuleId" column="sync_rule_id"/>
        <result property="creatorId" column="sync_creator_id"/>
        <result property="creatorName" column="sync_creator_name"/>
        <result property="createTime" column="sync_create_time"/>
        <result property="endTime" column="sync_end_time"/>
        <result property="result" column="sync_result"/>
        <result property="state" column="sync_state"/>
        <result property="elapsedTime" column="sync_elapsed_time"/>
    </resultMap>

    <!-- 根据实例ID查询完整的层次化实例信息 -->
    <select id="selectHierarchicalRunInstanceById" parameterType="java.lang.Long" resultMap="HierarchicalRunInstanceResult">
        SELECT
            -- 实例信息
            ri.iid AS instance_id,
            ri.ienvc_plan_id AS instance_plan_id,
            ri.ienvc_task_id AS instance_task_id,
            ri.iresult AS instance_result,
            ri.istate AS instance_state,
            ri.ifrom AS instance_from,
            ri.istarter_name AS instance_starter_name,
            ri.istarter_id AS instance_starter_id,
            ri.istart_time AS instance_start_time,
            ri.iend_time AS instance_end_time,
            ri.ielapsed_time AS instance_elapsed_time,

            -- 实例详情信息
            rii.iid AS info_id,
            rii.ienvc_run_instance_id AS info_instance_id,
            rii.ienvc_plan_id AS info_plan_id,
            rii.ibusiness_system_id AS info_business_system_id,
            rii.isource_center_id AS info_source_center_id,
            rii.itarget_center_id AS info_target_center_id,
            rii.isource_computer_id AS info_source_computer_id,
            rii.isource_computer_ip AS info_source_computer_ip,
            rii.isource_computer_port AS info_source_computer_port,
            rii.isource_computer_os AS info_source_computer_os,
            rii.itarget_computer_id AS info_target_computer_id,
            rii.itarget_computer_ip AS info_target_computer_ip,
            rii.itarget_computer_port AS info_target_computer_port,
            rii.itarget_computer_os AS info_target_computer_os,
            rii.istore_time AS info_store_time,
            rii.iresult AS info_result,
            rii.istate AS info_state,

            -- 规则信息
            rr.iid AS rule_id,
            rr.ienvc_run_instance_info_id AS rule_instance_info_id,
            rr.imodel AS rule_model,
            rr.itype AS rule_type,
            rr.ipath AS rule_path,
            rr.iencode AS rule_encode,
            rr.iway AS rule_way,
            rr.irule_type AS rule_rule_type,
            rr.ienabled AS rule_enabled,
            rr.ichild_level AS rule_child_level,
            rr.icreator_id AS rule_creator_id,
            rr.icreator_name AS rule_creator_name,
            rr.icreate_time AS rule_create_time,
            rr.iend_time AS rule_end_time,
            rr.iresult AS rule_result,
            rr.istate AS rule_state,
            rr.ielapsed_time AS rule_elapsed_time,

            -- 规则同步信息
            rrs.iid AS sync_id,
            rrs.ienvc_run_rule_id AS sync_rule_id,
            rrs.icreator_id AS sync_creator_id,
            rrs.icreator_name AS sync_creator_name,
            rrs.icreate_time AS sync_create_time,
            rrs.iend_time AS sync_end_time,
            rrs.iresult AS sync_result,
            rrs.istate AS sync_state,
            rrs.ielapsed_time AS sync_elapsed_time,

            -- 规则流程信息
            rf.iid AS flow_id,
            rf.iflowid AS flow_flowid,
            rf.irun_biz_id AS flow_run_biz_id,
            rf.imodel AS flow_model,
            rf.istate AS flow_state,
            rf.ielapsed_time AS flow_elapsed_time,
            rf.iret AS flow_ret,
            rf.iupdate_order_time AS flow_update_order_time
        FROM
            ieai_envc_run_instance ri
        LEFT JOIN
            ieai_envc_run_instance_info rii ON ri.iid = rii.ienvc_run_instance_id
        LEFT JOIN
            ieai_envc_run_rule rr ON rii.iid = rr.ienvc_run_instance_info_id
        LEFT JOIN
            ieai_envc_run_rule_sync rrs ON rr.iid = rrs.ienvc_run_rule_id
        LEFT JOIN
            ieai_envc_run_flow rf ON rr.iid = rf.irun_biz_id
        WHERE
            ri.iid = #{instanceId}
    </select>

    <!-- 根据方案ID查询完整的层次化实例信息列表 -->
    <select id="selectHierarchicalRunInstancesByPlanId" parameterType="java.lang.Long" resultMap="HierarchicalRunInstanceResult">
        SELECT
            -- 实例信息
            ri.iid AS instance_id,
            ri.ienvc_plan_id AS instance_plan_id,
            ri.ienvc_task_id AS instance_task_id,
            ri.iresult AS instance_result,
            ri.istate AS instance_state,
            ri.ifrom AS instance_from,
            ri.istarter_name AS instance_starter_name,
            ri.istarter_id AS instance_starter_id,
            ri.istart_time AS instance_start_time,
            ri.iend_time AS instance_end_time,
            ri.ielapsed_time AS instance_elapsed_time,

            -- 实例详情信息
            rii.iid AS info_id,
            rii.ienvc_run_instance_id AS info_instance_id,
            rii.ienvc_plan_id AS info_plan_id,
            rii.ibusiness_system_id AS info_business_system_id,
            rii.isource_center_id AS info_source_center_id,
            rii.itarget_center_id AS info_target_center_id,
            rii.isource_computer_id AS info_source_computer_id,
            rii.isource_computer_ip AS info_source_computer_ip,
            rii.isource_computer_port AS info_source_computer_port,
            rii.isource_computer_os AS info_source_computer_os,
            rii.itarget_computer_id AS info_target_computer_id,
            rii.itarget_computer_ip AS info_target_computer_ip,
            rii.itarget_computer_port AS info_target_computer_port,
            rii.itarget_computer_os AS info_target_computer_os,
            rii.istore_time AS info_store_time,
            rii.iresult AS info_result,
            rii.istate AS info_state,

            -- 规则信息
            rr.iid AS rule_id,
            rr.ienvc_run_instance_info_id AS rule_instance_info_id,
            rr.imodel AS rule_model,
            rr.itype AS rule_type,
            rr.ipath AS rule_path,
            rr.iencode AS rule_encode,
            rr.iway AS rule_way,
            rr.irule_type AS rule_rule_type,
            rr.ienabled AS rule_enabled,
            rr.ichild_level AS rule_child_level,
            rr.icreator_id AS rule_creator_id,
            rr.icreator_name AS rule_creator_name,
            rr.icreate_time AS rule_create_time,
            rr.iend_time AS rule_end_time,
            rr.iresult AS rule_result,
            rr.istate AS rule_state,
            rr.ielapsed_time AS rule_elapsed_time,

            -- 规则同步信息
            rrs.iid AS sync_id,
            rrs.ienvc_run_rule_id AS sync_rule_id,
            rrs.icreator_id AS sync_creator_id,
            rrs.icreator_name AS sync_creator_name,
            rrs.icreate_time AS sync_create_time,
            rrs.iend_time AS sync_end_time,
            rrs.iresult AS sync_result,
            rrs.istate AS sync_state,
            rrs.ielapsed_time AS sync_elapsed_time,

            -- 规则流程信息
            rf.iid AS flow_id,
            rf.iflowid AS flow_flowid,
            rf.irun_biz_id AS flow_run_biz_id,
            rf.imodel AS flow_model,
            rf.istate AS flow_state,
            rf.ielapsed_time AS flow_elapsed_time,
            rf.iret AS flow_ret,
            rf.iupdate_order_time AS flow_update_order_time
        FROM
            ieai_envc_run_instance ri
        LEFT JOIN
            ieai_envc_run_instance_info rii ON ri.iid = rii.ienvc_run_instance_id
        LEFT JOIN
            ieai_envc_run_rule rr ON rii.iid = rr.ienvc_run_instance_info_id
        LEFT JOIN
            ieai_envc_run_rule_sync rrs ON rr.iid = rrs.ienvc_run_rule_id
        LEFT JOIN
            ieai_envc_run_flow rf ON (rr.iid = rf.irun_biz_id AND rf.imodel = 0) OR (rrs.iid = rf.irun_biz_id AND rf.imodel = 1)
        WHERE
            ri.ienvc_plan_id = #{planId}
        ORDER BY
            ri.istart_time DESC
    </select>

    <!-- 根据任务ID查询完整的层次化实例信息列表 -->
    <select id="selectHierarchicalRunInstancesByTaskId" parameterType="java.lang.Long" resultMap="HierarchicalRunInstanceResult">
        SELECT
            -- 实例信息
            ri.iid AS instance_id,
            ri.ienvc_plan_id AS instance_plan_id,
            ri.ienvc_task_id AS instance_task_id,
            ri.iresult AS instance_result,
            ri.istate AS instance_state,
            ri.ifrom AS instance_from,
            ri.istarter_name AS instance_starter_name,
            ri.istarter_id AS instance_starter_id,
            ri.istart_time AS instance_start_time,
            ri.iend_time AS instance_end_time,
            ri.ielapsed_time AS instance_elapsed_time,

            -- 实例详情信息
            rii.iid AS info_id,
            rii.ienvc_run_instance_id AS info_instance_id,
            rii.ienvc_plan_id AS info_plan_id,
            rii.ibusiness_system_id AS info_business_system_id,
            rii.isource_center_id AS info_source_center_id,
            rii.itarget_center_id AS info_target_center_id,
            rii.isource_computer_id AS info_source_computer_id,
            rii.isource_computer_ip AS info_source_computer_ip,
            rii.isource_computer_port AS info_source_computer_port,
            rii.isource_computer_os AS info_source_computer_os,
            rii.itarget_computer_id AS info_target_computer_id,
            rii.itarget_computer_ip AS info_target_computer_ip,
            rii.itarget_computer_port AS info_target_computer_port,
            rii.itarget_computer_os AS info_target_computer_os,
            rii.istore_time AS info_store_time,
            rii.iresult AS info_result,
            rii.istate AS info_state,

            -- 规则信息
            rr.iid AS rule_id,
            rr.ienvc_run_instance_info_id AS rule_instance_info_id,
            rr.imodel AS rule_model,
            rr.itype AS rule_type,
            rr.ipath AS rule_path,
            rr.iencode AS rule_encode,
            rr.iway AS rule_way,
            rr.irule_type AS rule_rule_type,
            rr.ienabled AS rule_enabled,
            rr.ichild_level AS rule_child_level,
            rr.icreator_id AS rule_creator_id,
            rr.icreator_name AS rule_creator_name,
            rr.icreate_time AS rule_create_time,
            rr.iend_time AS rule_end_time,
            rr.iresult AS rule_result,
            rr.istate AS rule_state,
            rr.ielapsed_time AS rule_elapsed_time,

            -- 规则同步信息
            rrs.iid AS sync_id,
            rrs.ienvc_run_rule_id AS sync_rule_id,
            rrs.icreator_id AS sync_creator_id,
            rrs.icreator_name AS sync_creator_name,
            rrs.icreate_time AS sync_create_time,
            rrs.iend_time AS sync_end_time,
            rrs.iresult AS sync_result,
            rrs.istate AS sync_state,
            rrs.ielapsed_time AS sync_elapsed_time,

            -- 规则流程信息
            rf.iid AS flow_id,
            rf.iflowid AS flow_flowid,
            rf.irun_biz_id AS flow_run_biz_id,
            rf.imodel AS flow_model,
            rf.istate AS flow_state,
            rf.ielapsed_time AS flow_elapsed_time,
            rf.iret AS flow_ret,
            rf.iupdate_order_time AS flow_update_order_time
        FROM
            ieai_envc_run_instance ri
        LEFT JOIN
            ieai_envc_run_instance_info rii ON ri.iid = rii.ienvc_run_instance_id
        LEFT JOIN
            ieai_envc_run_rule rr ON rii.iid = rr.ienvc_run_instance_info_id
        LEFT JOIN
            ieai_envc_run_rule_sync rrs ON rr.iid = rrs.ienvc_run_rule_id
        LEFT JOIN
            ieai_envc_run_flow rf ON (rr.iid = rf.irun_biz_id AND rf.imodel = 0) OR (rrs.iid = rf.irun_biz_id AND rf.imodel = 1)
        WHERE
            ri.ienvc_task_id = #{taskId}
        ORDER BY
            ri.istart_time DESC
    </select>

    <!-- 根据实例ID更新实例状态为初始状态 -->
    <update id="updateRunInstanceStatusToInitial" parameterType="java.lang.Long">
        UPDATE ieai_envc_run_instance
        SET iresult = -1,
            istate = 0
        WHERE iid = #{instanceId}
    </update>

    <!-- 根据实例ID更新实例详情状态为初始状态 -->
    <update id="updateRunInstanceInfoStatusToInitial" parameterType="java.lang.Long">
        UPDATE ieai_envc_run_instance_info
        SET iresult = -1,
            istate = 0
        WHERE ienvc_run_instance_id = #{instanceId}
    </update>

    <!-- 根据实例ID更新规则状态为初始状态 -->
    <update id="updateRunRuleStatusToInitial" parameterType="java.lang.Long">
        UPDATE ieai_envc_run_rule
        SET iresult = -1,
            istate = 0
        WHERE EXISTS (
            SELECT 1 FROM ieai_envc_run_instance_info rii
            WHERE rii.iid = ieai_envc_run_rule.ienvc_run_instance_info_id
            AND rii.ienvc_run_instance_id = #{instanceId}
        )
    </update>

    <!-- 根据实例ID更新流程状态为初始状态 -->
    <update id="updateRunFlowStatusToInitial" parameterType="java.lang.Long">
        UPDATE ieai_envc_run_flow
        SET istate = 0
        WHERE EXISTS (
            SELECT 1 FROM ieai_envc_run_rule rr
            INNER JOIN ieai_envc_run_instance_info rii ON rr.ienvc_run_instance_info_id = rii.iid
            WHERE rr.iid = ieai_envc_run_flow.irun_biz_id
            AND rii.ienvc_run_instance_id = #{instanceId}
        )
    </update>
</mapper>
