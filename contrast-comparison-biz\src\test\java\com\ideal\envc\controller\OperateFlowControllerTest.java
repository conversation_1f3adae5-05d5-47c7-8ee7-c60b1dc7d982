package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.FlowOperateResultDto;
import com.ideal.envc.model.dto.OperateFlowStopRequestDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IFlowOperateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * OperateFlowController单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class OperateFlowControllerTest {

    @Mock
    private IFlowOperateService flowOperateService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private OperateFlowController operateFlowController;

    private UserDto mockUserDto;
    private FlowOperateResultDto mockResultDto;

    @BeforeEach
    void setUp() {
        mockUserDto = new UserDto();
        mockUserDto.setId(1L);
        mockUserDto.setLoginName("testUser");
        mockUserDto.setFullName("Test User");
        mockUserDto.setOrgCode("TEST_ORG");

        mockResultDto = new FlowOperateResultDto();
        mockResultDto.setTaskId(1L);
        mockResultDto.setFailFlowIds("");
        mockResultDto.setFlowIds(new Long[]{1L, 2L});
    }

    @Test
    @DisplayName("终止流程 - 成功场景")
    void stopFlow_Success() throws Exception {
        // 准备测试数据
        OperateFlowStopRequestDto requestDto = new OperateFlowStopRequestDto();
        requestDto.setFlowIds(Arrays.asList("1", "2"));
        Long[] expectedFlowIds = new Long[]{1L, 2L};

        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);
        when(flowOperateService.operateTerminatedFlow(eq(expectedFlowIds), any(UserDto.class))).thenReturn(mockResultDto);

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.stopFlow(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(mockResultDto, result.getData());

        // 验证Mock调用
        verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, times(1)).operateTerminatedFlow(eq(expectedFlowIds), any(UserDto.class));
    }

    @Test
    @DisplayName("终止流程 - 系统异常")
    void stopFlow_Exception() throws Exception {
        // 准备测试数据
        OperateFlowStopRequestDto requestDto = new OperateFlowStopRequestDto();
        requestDto.setFlowIds(Arrays.asList("1", "2"));
        Long[] expectedFlowIds = new Long[]{1L, 2L};

        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);
        when(flowOperateService.operateTerminatedFlow(eq(expectedFlowIds), any(UserDto.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.stopFlow(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());

        // 验证Mock调用
        verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, times(1)).operateTerminatedFlow(eq(expectedFlowIds), any(UserDto.class));
    }

    @Test
    @DisplayName("终止流程 - 请求参数为空")
    void stopFlow_NullRequestDto() throws Exception {
        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.stopFlow(null);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getDesc(), result.getMessage());

        // 验证Mock调用 - 会调用getUser但不会调用service方法
        verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, never()).operateTerminatedFlow(any(), any());
    }

    @Test
    @DisplayName("终止流程 - 流程ID列表为空")
    void stopFlow_EmptyFlowIds() throws Exception {
        // 准备测试数据
        OperateFlowStopRequestDto requestDto = new OperateFlowStopRequestDto();
        requestDto.setFlowIds(Collections.emptyList());

        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.stopFlow(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getDesc(), result.getMessage());

        // 验证Mock调用 - 会调用getUser但不会调用service方法
        UserDto user = verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, never()).operateTerminatedFlow(any(), any());
    }

    @Test
    @DisplayName("终止流程 - 流程ID列表为null")
    void stopFlow_NullFlowIds() throws Exception {
        // 准备测试数据
        OperateFlowStopRequestDto requestDto = new OperateFlowStopRequestDto();
        requestDto.setFlowIds(null);

        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.stopFlow(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getDesc(), result.getMessage());

        // 验证Mock调用 - 会调用getUser但不会调用service方法
        UserDto user = verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, never()).operateTerminatedFlow(any(), any());
    }

    @Test
    @DisplayName("终止流程 - 包含无效流程ID")
    void stopFlow_InvalidFlowIds() throws Exception {
        // 准备测试数据 - 包含无效的字符串ID
        OperateFlowStopRequestDto requestDto = new OperateFlowStopRequestDto();
        requestDto.setFlowIds(Arrays.asList("1", "invalid", "2"));
        // 期望转换后的数组：1L, -1L, 2L
        Long[] expectedFlowIds = new Long[]{1L, -1L, 2L};

        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);
        when(flowOperateService.operateTerminatedFlow(eq(expectedFlowIds), any(UserDto.class))).thenReturn(mockResultDto);

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.stopFlow(requestDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(mockResultDto, result.getData());

        // 验证Mock调用
        verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, times(1)).operateTerminatedFlow(eq(expectedFlowIds), any(UserDto.class));
    }

    @Test
    @DisplayName("重试流程 - 成功场景")
    void retryFlow_Success() throws Exception {
        // 准备测试数据
        Long flowId = 1L;

        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);
        when(flowOperateService.operateRetryFlow(eq(flowId), any(UserDto.class))).thenReturn(mockResultDto);

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.retryFlow(flowId);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(mockResultDto, result.getData());

        // 验证Mock调用
        verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, times(1)).operateRetryFlow(eq(flowId), any(UserDto.class));
    }

    @Test
    @DisplayName("重试流程 - 系统异常")
    void retryFlow_Exception() throws Exception {
        // 准备测试数据
        Long flowId = 1L;

        // 配置Mock行为
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);
        when(flowOperateService.operateRetryFlow(eq(flowId), any(UserDto.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        R<FlowOperateResultDto> result = operateFlowController.retryFlow(flowId);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());

        // 验证Mock调用
        verify(userinfoComponent, times(1)).getUser();
        verify(flowOperateService, times(1)).operateRetryFlow(eq(flowId), any(UserDto.class));
    }

    @Test
    @DisplayName("终止流程 - 事务回滚验证")
    void stopFlow_TransactionRollback() throws Exception {
        OperateFlowStopRequestDto requestDto = new OperateFlowStopRequestDto();
        requestDto.setFlowIds(Arrays.asList("1", "2"));
        
        when(userinfoComponent.getUser()).thenReturn(mockUserDto);
        when(flowOperateService.operateTerminatedFlow(any(), any()))
            .thenThrow(new RuntimeException("强制事务回滚"));
        
        R<FlowOperateResultDto> result = operateFlowController.stopFlow(requestDto);
        
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
    }
} 