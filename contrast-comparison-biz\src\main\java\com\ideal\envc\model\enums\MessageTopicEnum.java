package com.ideal.envc.model.enums;

/**
 * 消息主题枚举
 *
 * <AUTHOR>
 */
public enum MessageTopicEnum {

    /**
     * 发送任务到引擎
     */
    SEND_TASK_TO_ENGINE("contrastSendTaskToEngine", "发送任务到引擎"),
    RUN_INSTANCE_INFO_STATE_TOPIC("runInstanceInfoState", "更新实例详情状态"),
    RUN_INSTANCE_STATE_TOPIC("runInstanceState", "更新实例状态");

    /**
     * 通道名称
     */
    private final String channel;

    /**
     * 描述
     */
    private final String description;

    /**
     * 构造函数
     *
     * @param channel 通道名称
     * @param description 描述
     */
    MessageTopicEnum(String channel, String description) {
        this.channel = channel;
        this.description = description;
    }

    /**
     * 获取通道名称
     *
     * @return 通道名称
     */
    public String getChannel() {
        return channel;
    }

    /**
     * 获取描述
     *
     * @return 描述
     */
    public String getDescription() {
        return description;
    }
}
