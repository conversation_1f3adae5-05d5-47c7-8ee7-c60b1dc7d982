package com.ideal.envc.service;

import com.github.pagehelper.PageInfo;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.ContentDetailDto;
import com.ideal.envc.model.dto.ResultMonitorDto;
import com.ideal.envc.model.dto.ResultMonitorQueryDto;

import javax.servlet.http.HttpServletResponse;

/**
 * 比对结果监控Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface IResultMonitorService {

    /**
     * 查询比对结果列表
     *
     * @param resultMonitorQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 比对结果列表
     */
    PageInfo<ResultMonitorDto> selectResultMonitorList(ResultMonitorQueryDto resultMonitorQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 根据流程ID查询比对详情
     *
     * @param flowId 流程ID
     * @return 比对详情
     */
    ContentDetailDto selectContentDetailByFlowId(Long flowId) throws ContrastBusinessException;


    /**
     * 根据流程ID查询比对详情
     *
     * @param flowId 流程ID
     * @return 比对详情
     */
    String selectContentForCompareFileByFlowId(Long flowId) throws ContrastBusinessException;

    /**
     * 根据流程ID导出比对报表
     *
     * @param flowId 流程ID
     * @param response HTTP响应对象
     * @throws ContrastBusinessException 业务异常
     */
    void exportComparisonReportByFlowId(Long flowId, HttpServletResponse response) throws ContrastBusinessException;
}
