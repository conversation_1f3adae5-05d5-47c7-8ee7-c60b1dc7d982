package com.ideal.envc.model.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务操作结果DTO
 *
 * <AUTHOR>
 */
public class TaskOperateResultDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作是否全部成功
     */
    private boolean allSuccess;

    /**
     * 成功操作的任务ID列表
     */
    private List<Long> successTaskIds;

    /**
     * 成功操作的任务名称列表
     */
    private List<String> successTaskNames;

    /**
     * 失败操作的任务ID列表
     */
    private List<Long> failedTaskIds;

    /**
     * 失败操作的任务名称列表
     */
    private List<String> failedTaskNames;

    /**
     * 失败任务的详细信息，键为任务ID，值为任务名称
     */
    private Map<Long, String> failedTaskDetails;

    /**
     * 失败原因
     */
    private String failReason;

    public TaskOperateResultDto() {
        this.allSuccess = true;
        this.successTaskIds = new ArrayList<>();
        this.successTaskNames = new ArrayList<>();
        this.failedTaskIds = new ArrayList<>();
        this.failedTaskNames = new ArrayList<>();
        this.failedTaskDetails = new HashMap<>();
    }

    public boolean isAllSuccess() {
        return allSuccess;
    }

    public void setAllSuccess(boolean allSuccess) {
        this.allSuccess = allSuccess;
    }

    public List<Long> getSuccessTaskIds() {
        return successTaskIds;
    }

    public void setSuccessTaskIds(List<Long> successTaskIds) {
        this.successTaskIds = successTaskIds;
    }

    public List<String> getSuccessTaskNames() {
        return successTaskNames;
    }

    public void setSuccessTaskNames(List<String> successTaskNames) {
        this.successTaskNames = successTaskNames;
    }

    public List<Long> getFailedTaskIds() {
        return failedTaskIds;
    }

    public void setFailedTaskIds(List<Long> failedTaskIds) {
        this.failedTaskIds = failedTaskIds;
    }

    public List<String> getFailedTaskNames() {
        return failedTaskNames;
    }

    public void setFailedTaskNames(List<String> failedTaskNames) {
        this.failedTaskNames = failedTaskNames;
    }

    public Map<Long, String> getFailedTaskDetails() {
        return failedTaskDetails;
    }

    public void setFailedTaskDetails(Map<Long, String> failedTaskDetails) {
        this.failedTaskDetails = failedTaskDetails;
    }

    public String getFailReason() {
        return failReason;
    }

    public void setFailReason(String failReason) {
        this.failReason = failReason;
    }

    /**
     * 添加成功的任务
     *
     * @param taskId 任务ID
     * @param taskName 任务名称
     */
    public void addSuccessTask(Long taskId, String taskName) {
        if (taskId != null) {
            this.successTaskIds.add(taskId);
            if (taskName != null) {
                this.successTaskNames.add(taskName);
            }
        }
    }

    /**
     * 添加失败的任务
     *
     * @param taskId 任务ID
     * @param taskName 任务名称
     */
    public void addFailedTask(Long taskId, String taskName) {
        if (taskId != null) {
            this.failedTaskIds.add(taskId);
            if (taskName != null) {
                this.failedTaskNames.add(taskName);
                this.failedTaskDetails.put(taskId, taskName);
            }
            this.allSuccess = false;
        }
    }

    /**
     * 添加成功的任务ID
     *
     * @param taskId 任务ID
     */
    public void addSuccessTaskId(Long taskId) {
        if (taskId != null) {
            this.successTaskIds.add(taskId);
        }
    }

    /**
     * 添加失败的任务ID
     *
     * @param taskId 任务ID
     */
    public void addFailedTaskId(Long taskId) {
        if (taskId != null) {
            this.failedTaskIds.add(taskId);
            this.allSuccess = false;
        }
    }

    @Override
    public String toString() {
        return "TaskOperateResultDto{" +
                "allSuccess=" + allSuccess +
                ", successTaskIds=" + successTaskIds +
                ", successTaskNames=" + successTaskNames +
                ", failedTaskIds=" + failedTaskIds +
                ", failedTaskNames=" + failedTaskNames +
                ", failedTaskDetails=" + failedTaskDetails +
                ", failReason='" + failReason + '\'' +
                '}';
    }
}
