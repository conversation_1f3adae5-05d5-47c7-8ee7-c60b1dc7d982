---
description: 
globs: 
alwaysApply: true
---

# Your rule content

---
description: Develop constraint specifications for writing code
globs: *.java
---
# 开发编码时必须遵循以下所有约束规范
- **执行任何任务的前提**
  - **每次执行任务前必须调用工具了解代码库后，再结合指令和代码库进行 COT 思考**
  - **不允许有编译语法错误**
- **项目结构**
  - Controller生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/controller`目录中。
  - Service接口生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/service`目录中。
  - Service接口实现生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/service/impl`目录中。
  - Entity实体类生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/model/entity`目录中，所有表的注解都叫iid，Entity实体类使用 `@IdGenerator`注解标记主键属性。
  - DTO对象生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/model/dto`目录中。
  - Bean对象生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/model/bean`目录中，当涉及多表关联查询时，请创建Bean对象类使用，即mapper接口返回的是Bean对象，xml中也使用bean对象，当Service返回Controller时，请将Bean对象转换为对应的DTO对象。
  - MyBatis的Mapper接口生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/mapper`目录中。
  - MyBatis的Mapper接口的XML文件生成在 `contrast-comparison-biz/src/main/resources/mapper`目录中，需要和Mapper接口包层级一致。
  - 与各服务间调用获取数据公共接口处理生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/interaction`目录中。

- **工具类使用**
  - 使用 `com.ideal.common.util.BeanUtils`进行DTO和Entity对象的转换。该工具类同时支持List<DTO>和List<Entity>之间进行转换。
  - 使用 `com.ideal.common.util.PageDataUtil`分页工具类。
  - 使用 `com.ideal.common.util.spring.SpringUtil`工具类获取spring中的bean对象。
  - service实现类方法中如果涉及分页使用`com.github.pagehelper.PageHelper.startPage(pageNum, pageSize)`
  - 导入导出Excel功能使用 `easyexcel`库实现。
  - 在Java类中使用项目中已有的工具类依赖，看有哪些工具类可以使用；每次新增、修改代码时必须先扫描要修改新增的目标类是否存在，不能随便新增类！！可以使用`commons-lang3`的类库、`hutool`类库中提供的常用方法，如判断字符串是否为空等，不要自己这种原始API调用`== null || isEmpty()`，注意MyBatis mapper接口的xml中不需要使用类库提供的方法。
- **代码规范**
  - 项目使用JDK 1.8，不允许使用1.8以上的API。
  - 所有方法要有javadoc注释，复杂逻辑方法内的代码，也要写上代码注释
  - 所有类名、包名使用驼峰命名，所有类都需要有@author，名字用lch
  - 所有涉及事务操作的Service层的方法必须要加上`@Transactional`注解，并且指定`rollbackFor = Exception.class`属性，注意事务注解失效的各种情况，禁止同类中内调用本类内其他事务方法，如有此场景，请使用`com.ideal.common.util.spring.SpringUtil#getBean(Class<T> clz)`获取本类对象，在调用方法。
  - 所有Spring注入请使用构造函数方式注入，不要使用其他方式。
  - 此项目所有表名均已`ieai_envc_`开头，实际生成DTO、Bean、Entity类时，不要包含此前缀。
  - Controller中参数DTO上、DTO类中需要使用 `jsr303注解`和 `@Validated`、`@Valid`，不可以使用任何lombok注解，使用`jsr303注解`时，当处理增删改查业务逻辑时，需要考虑是否应该给jsr303注解添加`groups`属性，可以使用这俩个类 [Update.java](mdc:contrast-comparison-biz/src/main/java/com/ideal/envc/utils/validation/annotations/Update.java) [Save.java](mdc:contrast-comparison-biz/src/main/java/com/ideal/envc/utils/validation/annotations/Save.java)
  - 关键业务逻辑处需要添加日志打印
  - 每个功能的Controller、Service、Mapper接口、Mapper的XML使用单独的包文件夹。
  - 所有DTO、Bean、Entity类都需要实现 `Serializable`接口，都需要包含这句`private static final long serialVersionUID = 1L;`
  - 开发的代码中 `jsr303注解`的约束和提供的表结构字段长度保持一致。
  - mapper接口下方法出入参数不允许使用Dto类型对象。尽量使用Entity和Bean对象，不要使用Dto对象。如果入参参数比较少可以实用String以及基本数据类型或者基本数据类型的封装类型。
  - *Mapper.xml中sql编写不允许使用自定义函数或者某种数据库的特定函数，仅能使用如sum 、max、min、等支持所有数据类型的内置函数。
  - service层实现类方法中，尽可能使用Dto类型对象，在调用mapper的接口时，如果需要可转换为Entity或者Bean，不能将service层Dto直接传递Mapper接口。
- **功能实现**
  - Controller返回值使用泛型类 `com.ideal.common.dto.R<泛型类>`，如果不带翻页的接口直接返回  `com.ideal.common.dto.R<泛型类>`，如果是带翻页的接口，返回 `com.ideal.common.dto.R<PageInfo<泛型类>>`。
  - Controller带翻页的接口，@RequestBody参数对象，使用`com.ideal.common.dto.TableQueryDto<泛型类>`包装。
  - 导入导出excel功能使用 `easyexcel`库实现。
- **公共类使用**
  - 上述开发规范中提到的公共类，在本项目的第三方依赖中都已经存在，所以不包含源码，请不要再次自行创建这几个类`com.ideal.common.dto.R`、`com.ideal.common.dto.TableQueryDto`、`com.ideal.common.util.PageDataUtil`、`com.ideal.common.util.BeanUtils`、`com.ideal.common.util.spring.SpringUtil`
- **开发参照示例**
  - 以上所有开发约束请严格参照 [DictionaryController.java](mdc:contrast-comparison-biz/src/main/java/com/ideal/envc/controller/DictionaryController.java) [DictionaryServiceImpl.java](mdc:contrast-comparison-biz/src/main/java/com/ideal/envc/service/impl/DictionaryServiceImpl.java) [DictionaryMapper.java](mdc:contrast-comparison-biz/src/main/java/com/ideal/envc/mapper/DictionaryMapper.java)

- **Controller层规范**
  - 对R.fail()等失败返回也做统一，根据可能的失败类型结合@响应码及描述.md，改为调用R.fail(String code, String msg)；code和message来源于ResponseCodeEnum的SUCCESS的code和desc。
  - 返回结果使用R.ok();需要改为调用R.ok(String code，T data,String message);这个方法，code和message来源于ResponseCodeEnum的SUCCESS的code和desc。
  - 对于失败原因需要结合调用的业务实现层可能的失败原因分析，同时也需要考虑是否需要使用业务实现层向上抛出的具体原因来分析和对应响应码类型及响应码，对于目前枚举中缺失的可按照之前我们的要求进行补充。
  - Controller层代码进行补获异常来响应具体的响应码和描述，Controller层需要对异常进行分析结合响应码及描述.md分析出具体的响应异常类型和响应码。

- **Controller层失败响应的处理原则**
  - 优先使用业务实现层（Service/Mapper）向上抛出的异常信息，如throw new ContrastBusinessException("xxx")，Controller捕获后根据异常内容和类型决定响应码和描述。
  - 常见失败类型（如新增、删除、修改、查询等）优先使用ResponseCodeEnum中对应的响应码和描述。
  - 如枚举中无合适响应码，则根据你的规范，补充到ResponseCodeEnum，并在doc/响应码及描述.md中同步补充。
  - 未知异常统一用SYSTEM_ERROR（139900）响应码和描述。

- **Service层规范和原则**
  - service层需要对调用方法传递参数进行必要验证，验证需要结合业务逻辑比如是否可为空等。
  - service层对于异常抛出需要有分析，有考量，明确规范的异常类型。
  - service层对于传入参数必要的条件和属性要求在参数验证失败时应抛出业务异常由调用方补获处理。
  - service层接口定义和impl实现类代码中应考虑需对代码的异常补获并且需要分析是抛出业务异常还是其他类型异常，如对于引擎操作的EngineServiceException需要在响应码的枚举类中有此分类，如果没有需要补充，并且枚举类补充类型或者编码时需要同步维护《响应码及描述.md》这个规范文件。如对验证类的，由Controller层代码进行补获异常来响应具体的响应码和描述，Controller层需要对异常进行分析结合响应码及描述.md分析出具体的响应异常类型和响应码。
  - 为了使得Controller层或者调用方能够明确知道异常点，需要service层对于不满足情况下抛出准确的异常信息。

