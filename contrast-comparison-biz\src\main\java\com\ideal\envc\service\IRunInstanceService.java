package com.ideal.envc.service;

import com.ideal.envc.model.dto.RunInstanceDto;
import com.ideal.envc.model.dto.RunInstanceQueryDto;
import com.github.pagehelper.PageInfo;

/**
 * 实例Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IRunInstanceService {
    /**
     * 查询实例
     *
     * @param id 实例主键
     * @return 实例
     */
    RunInstanceDto selectRunInstanceById(Long id);

    /**
     * 查询实例列表
     *
     * @param runInstanceQueryDto 实例
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 实例集合
     */
    PageInfo<RunInstanceDto> selectRunInstanceList(RunInstanceQueryDto runInstanceQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增实例
     *
     * @param runInstanceDto 实例
     * @return 结果
     */
    int insertRunInstance(RunInstanceDto runInstanceDto);

    /**
     * 修改实例
     *
     * @param runInstanceDto 实例
     * @return 结果
     */
    int updateRunInstance(RunInstanceDto runInstanceDto);

    /**
     * 批量删除实例
     *
     * @param ids 需要删除的实例主键集合
     * @return 结果
     */
    int deleteRunInstanceByIds(Long[] ids);
}
