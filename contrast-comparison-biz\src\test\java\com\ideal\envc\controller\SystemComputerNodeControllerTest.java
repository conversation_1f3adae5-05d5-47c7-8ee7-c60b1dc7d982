package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.SystemComputerNodeDto;
import com.ideal.envc.model.dto.SystemComputerNodeQueryDto;
import com.ideal.envc.service.ISystemComputerNodeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 系统与设备节点关系控制器测试
 */
@ExtendWith(MockitoExtension.class)
public class SystemComputerNodeControllerTest {

    @Mock
    private ISystemComputerNodeService systemComputerNodeService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private SystemComputerNodeController systemComputerNodeController;

    private TableQueryDto<SystemComputerNodeQueryDto> tableQueryDto;
    private SystemComputerNodeQueryDto queryDto;
    private SystemComputerNodeDto systemComputerNodeDto;
    private PageInfo<SystemComputerNodeDto> pageInfo;
    private List<SystemComputerNodeDto> systemComputerNodeDtoList;

    @BeforeEach
    void setUp() {
        // 初始化查询参数
        queryDto = new SystemComputerNodeQueryDto();
        queryDto.setBusinessSystemId(1L);
        queryDto.setSourceComputerId(101L);
        queryDto.setSourceComputerIp("*************");
        queryDto.setTargetComputerId(102L);
        queryDto.setTargetComputerIp("*************");
        
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        // 初始化DTO对象
        systemComputerNodeDto = new SystemComputerNodeDto();
        systemComputerNodeDto.setId(1L);
        systemComputerNodeDto.setBusinessSystemId(1L);
        systemComputerNodeDto.setSourceCenterId(1L);
        systemComputerNodeDto.setTargetCenterId(2L);
        systemComputerNodeDto.setSourceComputerId(101L);
        systemComputerNodeDto.setSourceComputerIp("*************");
        systemComputerNodeDto.setTargetComputerId(102L);
        systemComputerNodeDto.setTargetComputerIp("*************");
        systemComputerNodeDto.setCreatorId(1001L);
        systemComputerNodeDto.setCreatorName("测试用户");
        systemComputerNodeDto.setCreateTime(new Date());

        systemComputerNodeDtoList = new ArrayList<>();
        systemComputerNodeDtoList.add(systemComputerNodeDto);

        // 初始化分页对象
        pageInfo = new PageInfo<>(systemComputerNodeDtoList);
        pageInfo.setTotal(1);
    }

    @Test
    @DisplayName("测试查询系统与设备节点关系列表")
    void testList() {
        // 设置Mock行为
        when(systemComputerNodeService.selectSystemComputerNodeList(
                any(SystemComputerNodeQueryDto.class), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试方法
        R<PageInfo<SystemComputerNodeDto>> result = systemComputerNodeController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());
        
        // 验证服务调用
        verify(systemComputerNodeService).selectSystemComputerNodeList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试根据ID查询系统与设备节点关系")
    void testGetSystemComputerNodeDtoById() {
        // 设置Mock行为
        when(systemComputerNodeService.selectSystemComputerNodeById(anyLong())).thenReturn(systemComputerNodeDto);

        // 执行测试方法
        R<SystemComputerNodeDto> result = systemComputerNodeController.getSystemComputerNodeDtoById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());
        assertEquals("*************", result.getData().getSourceComputerIp());
        assertEquals("*************", result.getData().getTargetComputerIp());
        
        // 验证服务调用
        verify(systemComputerNodeService).selectSystemComputerNodeById(1L);
    }

    @Test
    @DisplayName("测试新增系统与设备节点关系")
    void testSave() {
        // 设置Mock行为
        when(systemComputerNodeService.insertSystemComputerNode(any(SystemComputerNodeDto.class))).thenReturn(1);

        // 执行测试方法
        R<Void> result = systemComputerNodeController.save(systemComputerNodeDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(systemComputerNodeService).insertSystemComputerNode(systemComputerNodeDto);
    }

    @Test
    @DisplayName("测试新增系统与设备节点关系失败")
    void testSaveFail() {
        // 设置Mock行为
        when(systemComputerNodeService.insertSystemComputerNode(any(SystemComputerNodeDto.class))).thenReturn(0);

        // 执行测试方法
        R<Void> result = systemComputerNodeController.save(systemComputerNodeDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());
        
        // 验证服务调用
        verify(systemComputerNodeService).insertSystemComputerNode(systemComputerNodeDto);
    }

    @Test
    @DisplayName("测试修改系统与设备节点关系")
    void testUpdate() {
        // 设置Mock行为
        doReturn(1).when(systemComputerNodeService).updateSystemComputerNode(any(SystemComputerNodeDto.class));

        // 执行测试方法
        R<Void> result = systemComputerNodeController.update(systemComputerNodeDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(systemComputerNodeService).updateSystemComputerNode(systemComputerNodeDto);
    }

    @Test
    @DisplayName("测试删除系统与设备节点关系")
    void testRemove() {
        // 设置测试数据
        Long[] ids = {1L, 2L};
        
        // 设置Mock行为
        doReturn(2).when(systemComputerNodeService).deleteSystemComputerNodeByIds(any());

        // 执行测试方法
        R<Void> result = systemComputerNodeController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(systemComputerNodeService).deleteSystemComputerNodeByIds(ids);
    }
} 