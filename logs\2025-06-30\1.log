2025-06-30 17:04:34 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:04:34 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:04:35 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:04:35 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:05:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:05:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:05:26 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:05:26 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:06:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:06:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:06:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:06:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:07:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:07:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:07:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:07:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:08:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:08:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:08:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:08:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:09:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:09:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:09:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:09:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:10:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:10:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:10:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:10:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:11:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:11:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:11:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:11:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:12:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:12:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:12:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:12:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:13:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:13:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:13:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:13:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:14:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:14:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:14:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:14:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:15:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:15:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:15:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:15:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:16:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:16:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:16:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:16:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:17:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:17:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:17:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:17:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:18:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:18:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:18:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:18:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:19:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:19:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:19:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:19:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:20:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:20:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:20:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:20:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:21:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:21:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:21:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:21:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:22:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:22:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:22:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:22:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:23:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:23:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:23:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:23:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:24:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:24:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:24:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:24:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
2025-06-30 17:25:25 [com.xxl.job.core.thread.JobThread#run]-[133]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute start -----------<br>----------- Param:{"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:25:25 [com.ideal.envc.xxjob.ScheduleJobHandler#contrastJobHandler]-[43]-[xxl-job, JobThread-613-1751274274492] receive schedule job have to execute ,task has params : {"createName":"仲崇龙","creatorId":1099186824579489792,"cron":"0 * * * * ? *","jobHandlerName":"contrastJobHandler","planId":1087984379731021824,"taskId":1099589819695775744,"taskName":"对比任务_1099589819695775744"}
2025-06-30 17:25:25 [com.xxl.job.core.thread.JobThread#run]-[179]-[xxl-job, JobThread-613-1751274274492] <br>----------- xxl-job job execute end(finish) -----------<br>----------- Result: handleCode=200, handleMsg = task start is success!
2025-06-30 17:25:25 [com.xxl.job.core.thread.TriggerCallbackThread#callbackLog]-[197]-[xxl-job, executor TriggerCallbackThread] <br>----------- xxl-job job callback finish.
