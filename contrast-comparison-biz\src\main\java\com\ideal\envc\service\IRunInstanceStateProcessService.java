package com.ideal.envc.service;

/**
 * 运行实例状态处理服务接口
 * 
 * 主要功能：
 * 1. 处理实例详情状态变更后的实例状态计算和变更
 * 2. 管理实例计数器，实现实例详情计数器归0时的状态更新
 * 3. 确保在实例状态更新成功后才清理计数器
 * 
 * <AUTHOR>
 */
public interface IRunInstanceStateProcessService {

    /**
     * 处理实例状态更新
     * 
     * 处理流程：
     * 1. 检查消息有效性和过期性
     * 2. 查询实例信息
     * 3. 检查和更新实例计数器
     * 4. 当计数器归0时，触发状态计算和更新
     * 5. 状态更新成功后清理计数器
     * 
     * @param instanceId 实例ID
     * @param instanceInfoId 实例详情ID
     * @param messageTimestamp 消息时间戳
     * @return 是否处理成功
     */
    boolean processInstanceStateUpdate(Long instanceId, Long instanceInfoId, Long messageTimestamp);

    /**
     * 检查并减少实例计数器
     * 
     * @param instanceId 实例ID
     * @return 减少后的计数器值，-1表示减少失败
     */
    int decrementInstanceCounter(Long instanceId);

    /**
     * 计算实例状态和结果
     * 基于关联的运行实例详情状态计算汇总状态
     * 
     * @param instanceId 实例ID
     * @return [状态, 结果] 数组
     */
    int[] calculateInstanceStateAndResult(Long instanceId);

    /**
     * 更新实例状态和结果
     * 使用乐观锁机制防止并发冲突
     * 
     * @param instanceId 实例ID
     * @param state 状态
     * @param result 结果
     * @param currentTimestamp 当前时间戳
     * @return 是否更新成功
     */
    boolean updateInstanceStateAndResult(Long instanceId, int state, int result, Long currentTimestamp);

    /**
     * 清理实例计数器
     * 在状态更新成功后调用
     * 
     * @param instanceId 实例ID
     * @return 是否清理成功
     */
    boolean clearInstanceCounter(Long instanceId);
} 