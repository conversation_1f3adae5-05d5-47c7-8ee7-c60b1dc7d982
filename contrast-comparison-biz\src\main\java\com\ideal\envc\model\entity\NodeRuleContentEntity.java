package com.ideal.envc.model.entity;

import java.io.Serializable;
import com.ideal.snowflake.annotion.IdGenerator;

/**
 * 节点关系规则对象 ieai_envc_node_rule_content
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class NodeRuleContentEntity implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @IdGenerator
    private Long id;
    /** 信息配置ID */
    private Long envcNodeRelationId;
    /** 规则内容 */
    private String ruleContent;
    /** 规则名称 */
    private String ruleName;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcNodeRelationId(Long envcNodeRelationId){
        this.envcNodeRelationId = envcNodeRelationId;
    }

    public Long getEnvcNodeRelationId(){
        return envcNodeRelationId;
    }

    public void setRuleContent(String ruleContent){
        this.ruleContent = ruleContent;
    }

    public String getRuleContent(){
        return ruleContent;
    }

    public void setRuleName(String ruleName){
        this.ruleName = ruleName;
    }

    public String getRuleName(){
        return ruleName;
    }

    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",envcNodeRelationId="+getEnvcNodeRelationId()+
                    ",ruleContent="+getRuleContent()+
                    ",ruleName="+getRuleName()+
                "]";
    }
}

