package com.ideal.envc.service.impl;

import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.interaction.engine.EngineInteract;
import com.ideal.envc.mapper.*;
import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.bean.RetryRuleBean;
import com.ideal.envc.model.dto.FlowOperateResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.model.entity.*;
import com.ideal.envc.model.enums.StateEnum;
import com.ideal.envc.model.enums.TaskFlowOperationTypeEnum;
import com.ideal.envc.service.IStartContrastCommonBaseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class FlowOperateServiceImplTest {

    @InjectMocks
    private FlowOperateServiceImpl flowOperateService;

    @Mock
    private EngineInteract engineInteract;

    @Mock
    private RunFlowMapper runFlowMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private RunInstanceMapper runInstanceMapper;

    @Mock
    private RunInstanceInfoMapper runInstanceInfoMapper;

    @Mock
    private RunRuleContentMapper runRuleContentMapper;

    @Mock
    private IStartContrastCommonBaseService startContrastCommonBaseService;

    private UserDto userDto;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
    }

    @Test
    @DisplayName("测试正常终止流程的场景")
    void operateTerminatedFlow_Success() throws Exception {
        // 准备测试数据
        Long[] flowIds = new Long[]{1L, 2L};
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setFlowid(1L);
        runFlowEntity.setRunBizId(1L);
        
        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);

        // 配置mock行为
        when(engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode()))
                .thenReturn("");
        when(runFlowMapper.selectRunFlowByFlowId(anyLong())).thenReturn(runFlowEntity);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateTerminatedFlow(flowIds, userDto);

        // 验证结果
        assertNotNull(result);
        assertArrayEquals(flowIds, result.getFlowIds());
        assertEquals("", result.getFailFlowIds());
        
        // 验证方法调用 - 现在使用批量操作
        verify(runFlowMapper).selectRunFlowByFlowIds(any());
        verify(runFlowMapper).batchUpdateRunFlowState(any(), any(), any());
        verify(runRuleMapper).batchUpdateRunRuleStateAndResult(any(), any(), any());
    }

    @Test
    @DisplayName("测试传入空flowIds的异常场景")
    void operateTerminatedFlow_WithEmptyFlowIds() {
        // 准备测试数据
        Long[] flowIds = new Long[]{};

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateTerminatedFlow(flowIds, userDto);
        });

        assertEquals("flowIds is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试批量操作优化 - 大量流程ID")
    void operateTerminatedFlow_BatchOptimization() throws Exception {
        // 准备大量测试数据
        Long[] flowIds = new Long[100];
        List<RunFlowEntity> runFlowEntities = new ArrayList<>();

        for (int i = 0; i < 100; i++) {
            flowIds[i] = (long) (i + 1);

            RunFlowEntity runFlowEntity = new RunFlowEntity();
            runFlowEntity.setId((long) (i + 1));
            runFlowEntity.setFlowid((long) (i + 1));
            runFlowEntity.setRunBizId((long) (i + 100));
            runFlowEntities.add(runFlowEntity);
        }

        // 配置mock行为
        when(engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode()))
                .thenReturn("");
        when(runFlowMapper.selectRunFlowByFlowIds(any())).thenReturn(runFlowEntities);
        when(runFlowMapper.batchUpdateRunFlowState(any(), any(), any())).thenReturn(100);
        when(runRuleMapper.batchUpdateRunRuleStateAndResult(any(), any(), any())).thenReturn(100);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateTerminatedFlow(flowIds, userDto);

        // 验证结果
        assertNotNull(result);
        assertArrayEquals(flowIds, result.getFlowIds());
        assertEquals("", result.getFailFlowIds());

        // 验证批量操作被调用
        verify(runFlowMapper).selectRunFlowByFlowIds(any());
        verify(runFlowMapper).batchUpdateRunFlowState(any(), eq(StateEnum.TERMINATED.getCode()), any());
        verify(runRuleMapper).batchUpdateRunRuleStateAndResult(any(), eq(StateEnum.TERMINATED.getCode()), eq(1));

        // 验证没有调用单个操作方法
        verify(runFlowMapper, never()).selectRunFlowByFlowId(any());
        verify(runFlowMapper, never()).updateRunFlow(any());
        verify(runRuleMapper, never()).updateRunRule(any());
    }

    @Test
    @DisplayName("测试批量操作失败回退到逐个操作")
    void operateTerminatedFlow_BatchFailureFallback() throws Exception {
        // 准备测试数据
        Long[] flowIds = new Long[]{1L, 2L};

        RunFlowEntity runFlowEntity1 = new RunFlowEntity();
        runFlowEntity1.setId(1L);
        runFlowEntity1.setFlowid(1L);
        runFlowEntity1.setRunBizId(101L);

        RunFlowEntity runFlowEntity2 = new RunFlowEntity();
        runFlowEntity2.setId(2L);
        runFlowEntity2.setFlowid(2L);
        runFlowEntity2.setRunBizId(102L);

        RunRuleEntity runRuleEntity1 = new RunRuleEntity();
        runRuleEntity1.setId(101L);

        RunRuleEntity runRuleEntity2 = new RunRuleEntity();
        runRuleEntity2.setId(102L);

        // 配置mock行为 - 批量操作失败，逐个操作成功
        when(engineInteract.engineKillPauseResumeFlow(flowIds, TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode()))
                .thenReturn("");
        when(runFlowMapper.selectRunFlowByFlowIds(any())).thenThrow(new RuntimeException("批量查询失败"));
        when(runFlowMapper.selectRunFlowByFlowId(1L)).thenReturn(runFlowEntity1);
        when(runFlowMapper.selectRunFlowByFlowId(2L)).thenReturn(runFlowEntity2);
        when(runRuleMapper.selectRunRuleById(101L)).thenReturn(runRuleEntity1);
        when(runRuleMapper.selectRunRuleById(102L)).thenReturn(runRuleEntity2);
        when(runFlowMapper.updateRunFlow(any())).thenReturn(1);
        when(runRuleMapper.updateRunRule(any())).thenReturn(1);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateTerminatedFlow(flowIds, userDto);

        // 验证结果
        assertNotNull(result);
        assertArrayEquals(flowIds, result.getFlowIds());
        assertEquals("", result.getFailFlowIds());

        // 验证回退到逐个操作
        verify(runFlowMapper, times(2)).selectRunFlowByFlowId(any());
        verify(runFlowMapper, times(2)).updateRunFlow(any());
        verify(runRuleMapper, times(2)).selectRunRuleById(any());
        verify(runRuleMapper, times(2)).updateRunRule(any());
    }

    @Test
    @DisplayName("测试正常重试流程的场景")
    void operateRetryFlow_Success() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);
        retryRuleBean.setRuleContent("test content");

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runFlowMapper.selectRunFlowByFlowId(flowId)).thenReturn(runFlowEntity);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(any(), any())).thenReturn(new ArrayList<>());
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(any(), anyString())).thenReturn(true);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateRetryFlow(flowId, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(flowId, result.getFlowIds()[0]);
        assertEquals("", result.getFailFlowIds());

        // 验证方法调用
        verify(runInstanceMapper).insertRunInstance(any());
        verify(runInstanceInfoMapper).insertRunInstanceInfo(any());
        verify(runRuleMapper).insertRunRule(any());
        verify(runRuleContentMapper).insertRunRuleContent(any());
        verify(runFlowMapper).insertRunFlow(any());
    }

    @Test
    @DisplayName("测试传入空flowId的异常场景")
    void operateRetryFlow_WithNullFlowId() {
        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(null, userDto);
        });

        assertEquals("flowId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean为空的异常场景")
    void operateRetryFlow_WithNullRetryRuleBean() throws Exception {
        // 准备测试数据
        Long flowId = 1L;

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean的runRuleId为空的异常场景")
    void operateRetryFlow_WithNullRunRuleId() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(null);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean runRuleId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean的runInstanceId为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceId() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(null);
        retryRuleBean.setRunInstanceInfoId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean runInstanceId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RetryRuleBean的runInstanceInfoId为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceInfoId() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(null);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("retryRuleBean runInstanceInfoId is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RunRuleEntity为空的异常场景")
    void operateRetryFlow_WithNullRunRuleEntity() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("runRuleEntity is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RunInstanceEntity为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceEntity() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("runInstanceEntity is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试RunInstanceInfoEntity为空的异常场景")
    void operateRetryFlow_WithNullRunInstanceInfoEntity() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(null);

        // 执行测试并验证异常
        Exception exception = assertThrows(Exception.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("runInstanceInfoEntity is empty", exception.getMessage());
    }

    @Test
    @DisplayName("测试MQ发送失败的场景")
    void operateRetryFlow_WithMQSendFailure() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);
        retryRuleBean.setRuleContent("test content");

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runFlowMapper.selectRunFlowByFlowId(flowId)).thenReturn(runFlowEntity);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(any(), any())).thenReturn(new ArrayList<>());
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(any(), anyString())).thenReturn(false);

        // 执行测试
        FlowOperateResultDto result = flowOperateService.operateRetryFlow(flowId, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(flowId, result.getFlowIds()[0]);
        assertEquals(flowId.toString(), result.getFailFlowIds());
    }

    @Test
    @DisplayName("测试EngineServiceException异常场景")
    void operateRetryFlow_WithEngineServiceException() throws Exception {
        // 准备测试数据
        Long flowId = 1L;
        RetryRuleBean retryRuleBean = new RetryRuleBean();
        retryRuleBean.setRunRuleId(1L);
        retryRuleBean.setRunInstanceId(1L);
        retryRuleBean.setRunInstanceInfoId(1L);
        retryRuleBean.setRuleContent("test content");

        RunRuleEntity runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        RunInstanceInfoEntity runInstanceInfoEntity = new RunInstanceInfoEntity();
        runInstanceInfoEntity.setId(1L);
        RunInstanceEntity runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        RunFlowEntity runFlowEntity = new RunFlowEntity();
        runFlowEntity.setId(1L);

        // 配置mock行为
        when(runInstanceMapper.selectRetryRuleByFlowId(flowId)).thenReturn(retryRuleBean);
        when(runRuleMapper.selectRunRuleById(anyLong())).thenReturn(runRuleEntity);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(runInstanceInfoEntity);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);
        when(runFlowMapper.selectRunFlowByFlowId(flowId)).thenReturn(runFlowEntity);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(any(), any())).thenReturn(new ArrayList<>());
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(any(), anyString()))
                .thenThrow(new EngineServiceException("Engine service error"));

        // 执行测试并验证异常
        EngineServiceException exception = assertThrows(EngineServiceException.class, () -> {
            flowOperateService.operateRetryFlow(flowId, userDto);
        });

        assertEquals("Engine service error", exception.getMessage());
    }
} 