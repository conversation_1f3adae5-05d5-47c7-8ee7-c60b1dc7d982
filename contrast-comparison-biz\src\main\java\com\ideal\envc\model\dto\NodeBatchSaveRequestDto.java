package com.ideal.envc.model.dto;

import java.util.List;

/**
 * <AUTHOR>
 */
public class NodeBatchSaveRequestDto {

    /**
     * 业务系统ID
     */
    private Long businessSystemId;
    /**
     * 源中心ID
     */
    private Long sourceCenterId;

    /**
     * 源设备IP
     */
    private String sourceIp;

    /**
     * 源设备ID
     */
    private Long sourceComputerId;

    /**
     * 目标中心ID
     */
    private Long targetCenterId;

    /**
     * 目标设备ID集合
     */
    private List<Long> targetComputerIdList;

    /**
     * 节点关系规则信息集合
     */
    private List<NodeRelationContentDto> nodeRelationContentDtoList;

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public String getSourceIp() {
        return sourceIp;
    }

    public void setSourceIp(String sourceIp) {
        this.sourceIp = sourceIp;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public List<Long> getTargetComputerIdList() {
        return targetComputerIdList;
    }

    public void setTargetComputerIdList(List<Long> targetComputerIdList) {
        this.targetComputerIdList = targetComputerIdList;
    }

    public List<NodeRelationContentDto> getNodeRelationContentDtoList() {
        return nodeRelationContentDtoList;
    }

    public void setNodeRelationContentDtoList(List<NodeRelationContentDto> nodeRelationContentDtoList) {
        this.nodeRelationContentDtoList = nodeRelationContentDtoList;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    @Override
    public String toString() {
        return "NodeBatchSaveRequestDto{" +
                "businessSystemId=" + businessSystemId +
                ", sourceCenterId=" + sourceCenterId +
                ", sourceIp='" + sourceIp + '\'' +
                ", sourceComputerId=" + sourceComputerId +
                ", targetCenterId=" + targetCenterId +
                ", targetComputerIdList=" + targetComputerIdList +
                ", nodeRelationContentDtoList=" + nodeRelationContentDtoList +
                '}';
    }
}
