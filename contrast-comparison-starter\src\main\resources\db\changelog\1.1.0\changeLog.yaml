databaseChangeLog:
  - changeSet:
      id: 1733104767689-1
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_project
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ibusiness_system_id
                  type: bigint
                  remarks: 系统ID
              - column:
                  name: ibusiness_system_code
                  type: VARCHAR(100)
                  remarks: 系统编码
              - column:
                  name: ibusiness_system_name
                  type: VARCHAR(100)
                  remarks: 系统名称
              - column:
                  name: ibusiness_system_unique
                  type: VARCHAR(100)
                  remarks: 系统唯一标识
              - column:
                  name: ibusiness_system_desc
                  type: VARCHAR(150)
                  remarks: 系统描述
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: VARCHAR(50)
                  remarks: 创建人
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 添加时间
              - column:
                  name: istatus
                  type: smallint
                  defaultValue: 1
                  remarks: 是否有效（1：有效，0：失效）
              - column:
                  name: iupdator_id
                  type: bigint
                  remarks: 更新人ID
              - column:
                  name: iupdator_name
                  type: varchar(50)
                  remarks: 更新人名称
              - column:
                  name: iupdate_time
                  type: timestamp
                  remarks: 更新时间
            remarks: 比对业务系统表
        - createIndex:
            indexName: idx_envc_project_system_01
            tableName: ieai_envc_project
            columns:
              - column:
                  name: ibusiness_system_id
              - column:
                  name: ibusiness_system_name
        - createIndex:
            indexName: idx_envc_project_system_02
            tableName: ieai_envc_project
            columns:
              - column:
                  name: ibusiness_system_name
        - createIndex:
            indexName: idx_envc_project_system_03
            tableName: ieai_envc_project
            columns:
              - column:
                  name: ibusiness_system_desc

  - changeSet:
      id: 1733104767689-2
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_system_computer
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ibusiness_system_id
                  type: bigint
                  remarks: 系统ID
              - column:
                  name: icomputer_id
                  type: bigint
                  remarks: 设备ID
              - column:
                  name: icomputer_ip
                  type: varchar(255)
                  remarks: 代理IP
              - column:
                  name: icomputer_name
                  type: varchar(150)
                  remarks: 代理名称
              - column:
                  name: icenter_id
                  type: bigint
                  defaultValue: -1
                  remarks: 中心ID
              - column:
                  name: icenter_name
                  type: varchar(100)
                  remarks: 中心名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 存储时间
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 添加人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
            remarks: 系统设备关系表
        - createIndex:
            indexName: idx_system_computer_01
            tableName: ieai_envc_system_computer
            columns:
              - column:
                  name: ibusiness_system_id
              - column:
                  name: icomputer_id
        - createIndex:
            indexName: idx_system_computer_02
            tableName: ieai_envc_system_computer
            columns:
              - column:
                  name: icomputer_id

  - changeSet:
      id: 1733104767689-3
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_system_computer_node
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ibusiness_system_id
                  type: bigint
                  remarks: 系统ID
              - column:
                  name: isource_center_id
                  type: bigint
                  remarks: 源中心ID
              - column:
                  name: itarget_center_id
                  type: bigint
                  remarks: 目标中心ID
              - column:
                  name: isource_computer_id
                  type: bigint
                  remarks: 源设备ID
              - column:
                  name: isource_computer_ip
                  type: varchar(255)
                  remarks: 源设备IP
              - column:
                  name: itarget_computer_id
                  type: bigint
                  remarks: 目标设备ID
              - column:
                  name: itarget_computer_ip
                  type: varchar(255)
                  remarks: 目标设备IP
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
            remarks: 系统与设备节点关系表
        - createIndex:
            indexName: idx_system_computer_node_01
            tableName: ieai_envc_system_computer_node
            columns:
              - column:
                  name: ibusiness_system_id
              - column:
                  name: isource_center_id
        - createIndex:
            indexName: idx_system_computer_node_02
            tableName: ieai_envc_system_computer_node
            columns:
              - column:
                  name: ibusiness_system_id
              - column:
                  name: itarget_center_id
        - createIndex:
            indexName: idx_system_computer_node_03
            tableName: ieai_envc_system_computer_node
            columns:
              - column:
                  name: isource_computer_id
        - createIndex:
            indexName: idx_system_computer_node_04
            tableName: ieai_envc_system_computer_node
            columns:
              - column:
                  name: itarget_computer_id

  - changeSet:
      id: 1733104767689-4
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_node_relation
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ienvc_system_computer_node_id
                  type: bigint
                  remarks: 节点关系ID
              - column:
                  name: imodel
                  type: smallint
                  remarks: 模式（0：比对，1：同步，2：比对后同步）
              - column:
                  name: itype
                  type: bigint
                  defaultValue: 0
                  remarks: 模块类型（0：目录，1;文件，2：脚本）
              - column:
                  name: ipath
                  type: varchar(255)
                  remarks: 路径
              - column:
                  name: iencode
                  type: varchar(32)
                  remarks: 字符集
              - column:
                  name: iway
                  type: smallint
                  defaultValue: 0
                  remarks: 方式（0：全部:1：部分）
              - column:
                  name: irule_type
                  type: smallint
                  remarks: 规则类型（0：匹配，1：排除）
              - column:
                  name: ienabled
                  type: smallint
                  defaultValue: 0
                  remarks: 是否有效（0：有效，1：无效）
              - column:
                  name: ichild_level
                  type: smallint
                  defaultValue: 1
                  remarks: 是否子集（0:是，1：否）
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iupdator_id
                  type: bigint
                  remarks: 更新人ID
              - column:
                  name: iupdator_name
                  type: varchar(50)
                  remarks: 更新人名称
              - column:
                  name: iupdate_time
                  type: timestamp
                  remarks: 更新时间
            remarks: 节点关系规则
        - createIndex:
            indexName: idx_node_relation_01
            tableName: ieai_envc_node_relation
            columns:
              - column:
                  name: ienvc_system_computer_node_id

  - changeSet:
      id: 1733104767689-5
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_node_rule_content
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ienvc_node_relation_id
                  type: bigint
                  remarks: 信息配置ID
              - column:
                  name: irule_content
                  type: longtext
                  remarks: 规则内容
            remarks: 节点关系规则
        - createIndex:
            indexName: idx_node_rule_content_01
            tableName: ieai_envc_node_rule_content
            columns:
              - column:
                  name: ienvc_node_relation_id

  - changeSet:
      id: 1733104767689-6
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_plan
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: iname
                  type: varchar(100)
                  remarks: 方案名称
              - column:
                  name: iplan_desc
                  type: varchar(150)
                  remarks: 方案描述
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iupdator_id
                  type: bigint
                  remarks: 更新人ID
              - column:
                  name: iupdator_name
                  type: varchar(50)
                  remarks: 更新人名称
              - column:
                  name: iupdate_time
                  type: timestamp
            remarks: 方案信息
        - createIndex:
            indexName: idx_envc_plan_01
            tableName: ieai_envc_plan
            columns:
              - column:
                  name: iname

  - changeSet:
      id: 1733104767689-7
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_plan_relation
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: ienvc_plan_id
                  type: bigint
                  remarks: 方案ID
              - column:
                  name: ibusiness_system_id
                  type: bigint
                  remarks: 业务系统ID
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
            remarks: 方案信息
        - createIndex:
            indexName: idx_plan_relation_01
            tableName: ieai_envc_plan_relation
            columns:
              - column:
                  name: ienvc_plan_id
              - column:
                  name: ibusiness_system_id
            unique: true
        - createIndex:
            indexName: idx_plan_relation_02
            tableName: ieai_envc_plan_relation
            columns:
              - column:
                  name: ibusiness_system_id

  - changeSet:
      id: 1733104767689-8
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_task
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: ienvc_plan_id
                  type: bigint
                  remarks: 方案ID
              - column:
                  name: icron
                  type: varchar(300)
                  remarks: 周期表达式
              - column:
                  name: ienabled
                  type: smallint
                  defaultValue: 1
                  remarks: 是否启用（1:启用，0：禁用）
              - column:
                  name: istate
                  type: smallint
                  remarks: 启停状态（0:启动，1：停止）
              - column:
                  name: isource_center_id
                  type: bigint
                  remarks: 源中心ID
              - column:
                  name: itarget_center_id
                  type: bigint
                  remarks: 目标中心ID
              - column:
                  name: ischeduled_id
                  type: bigint
                  remarks: 定时ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iupdator_id
                  type: bigint
                  remarks: 更新人ID
              - column:
                  name: iupdator_name
                  type: varchar(50)
                  remarks: 更新人名称
              - column:
                  name: iupdate_time
                  type: timestamp
            remarks: 任务表
        - createIndex:
            indexName: idx_envc_task_01
            tableName: ieai_envc_task
            columns:
              - column:
                  name: ienvc_plan_id
        - createIndex:
            indexName: idx_envc_task_02
            tableName: ieai_envc_task
            columns:
              - column:
                  name: istate
        - createIndex:
            indexName: idx_task_center_01
            tableName: ieai_envc_task
            columns:
              - column:
                  name: isource_center_id
              - column:
                  name: itarget_center_id

  - changeSet:
      id: 1733104767689-9
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_run_instance
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: ienvc_plan_id
                  type: bigint
                  remarks: 方案ID
              - column:
                  name: ienvc_task_id
                  type: bigint
                  defaultValue: -1
                  remarks: 周期任务ID（方案启动和重试无任务id）
              - column:
                  name: iresult
                  type: smallint
                  defaultValue: -1
                  remarks: 结果状态（-1:运行中，0:一致/成功，1：不一致/失败）
              - column:
                  name: istate
                  type: smallint
                  defaultValue: 0
                  remarks: 启停状态（0：运行中，1：已完成，2：终止）
              - column:
                  name: ifrom
                  type: smallint
                  remarks: 触发来源：（1：周期触发，2：手动触发，3：重试）
              - column:
                  name: istarter_name
                  type: varchar(50)
                  remarks: 启动人名称
              - column:
                  name: istarter_id
                  type: bigint
                  remarks: 启动人ID
              - column:
                  name: istart_time
                  type: timestamp
                  remarks: 启动时间
              - column:
                  name: iend_time
                  type: timestamp
                  remarks: 结束时间
              - column:
                  name: ielapsed_time
                  type: bigint
                  remarks: 耗时
            remarks: 实例表
        - createIndex:
            indexName: idx_run_instance_01
            tableName: ieai_envc_run_instance
            columns:
              - column:
                  name: ienvc_plan_id
              - column:
                  name: ienvc_task_id
        - createIndex:
            indexName: idx_run_instance_02
            tableName: ieai_envc_run_instance
            columns:
              - column:
                  name: ienvc_task_id

  - changeSet:
      id: 1733104767689-10
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_run_instance_info
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ienvc_run_instance_id
                  type: bigint
                  remarks: 实例ID
              - column:
                  name: ienvc_plan_id
                  type: bigint
                  remarks: 方案ID
              - column:
                  name: ibusiness_system_id
                  type: bigint
                  remarks: 系统ID
              - column:
                  name: isource_center_id
                  type: bigint
                  remarks: 源中心ID
              - column:
                  name: itarget_center_id
                  type: bigint
                  remarks: 目标中心ID
              - column:
                  name: isource_computer_id
                  type: bigint
                  remarks: 源设备ID
              - column:
                  name: isource_computer_ip
                  type: varchar(255)
                  remarks: 源设备IP
              - column:
                  name: isource_computer_port
                  type: integer
                  remarks: 源设备端口
              - column:
                  name: isource_computer_os
                  type: varchar(150)
                  remarks: 源设备操作系统
              - column:
                  name: itarget_computer_id
                  type: bigint
                  remarks: 目标设备ID
              - column:
                  name: itarget_computer_ip
                  type: varchar(255)
                  remarks: 目标设备IP
              - column:
                  name: itarget_computer_port
                  type: integer
                  remarks: 目标设备端口
              - column:
                  name: itarget_computer_os
                  type: varchar(150)
                  remarks: 目标设备操作系统
              - column:
                  name: istore_time
                  type: timestamp
                  remarks: 存储时间
              - column:
                  name: iresult
                  type: smallint
                  remarks: 结果状态（-1:运行中，0:一致/成功，1：不一致/失败）
              - column:
                  name: istate
                  type: smallint
                  remarks: 启停状态（0：运行中，1：已完成，2：终止）
            remarks: 实例详情
        - createIndex:
            indexName: idx_run_instance_info_01
            tableName: ieai_envc_run_instance_info
            columns:
              - column:
                  name: ienvc_run_instance_id
        - createIndex:
            indexName: idx_run_instance_info_02
            tableName: ieai_envc_run_instance_info
            columns:
              - column:
                  name: ienvc_plan_id
              - column:
                  name: ibusiness_system_id
        - createIndex:
            indexName: idx_run_instance_info_03
            tableName: ieai_envc_run_instance_info
            columns:
              - column:
                  name: isource_computer_ip
        - createIndex:
            indexName: idx_run_instance_info_04
            tableName: ieai_envc_run_instance_info
            columns:
              - column:
                  name: itarget_computer_ip

  - changeSet:
      id: 1733104767689-11
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_run_rule
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ienvc_run_instance_info_id
                  type: bigint
                  remarks: 实例详情ID
              - column:
                  name: imodel
                  type: smallint
                  defaultValue: -1
                  remarks: 模式（0：比对，1：同步，2：比对后同步）
              - column:
                  name: itype
                  type: bigint
                  defaultValue: 0
                  remarks: 模块类型（0：目录，1;文件，2：脚本）
              - column:
                  name: ipath
                  type: varchar(255)
                  remarks: 路径
              - column:
                  name: iencode
                  type: varchar(32)
                  remarks: 字符集
              - column:
                  name: iway
                  type: smallint
                  defaultValue: 0
                  remarks: 方式（0：全部:1：部分）
              - column:
                  name: irule_type
                  type: smallint
                  remarks: 规则类型（0：匹配，1：排除）
              - column:
                  name: ienabled
                  type: smallint
                  defaultValue: 0
                  remarks: 是否有效（0：有效，1：无效）
              - column:
                  name: ichild_level
                  type: smallint
                  defaultValue: 1
                  remarks: 是否子集（0:是，1：否）
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iend_time
                  type: timestamp
                  remarks: 更新时间
              - column:
                  name: iresult
                  type: smallint
                  remarks: 结果状态（-1:运行中，0:一致/成功，1：不一致/失败）
              - column:
                  name: istate
                  type: smallint
                  remarks: 启停状态（0：运行中，1：已完成，2：终止）
              - column:
                  name: ielapsed_time
                  type: bigint
                  remarks: 耗时
            remarks: 节点规则结果表
        - createIndex:
            indexName: idx_run_rule_01
            tableName: ieai_envc_run_rule
            columns:
              - column:
                  name: ienvc_run_instance_info_id

  - changeSet:
      id: 1733104767689-12
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_run_rule_sync
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ienvc_system_computer_node_id
                  type: bigint
                  remarks: 节点关系ID
              - column:
                  name: ienvc_run_rule_id
                  type: bigint
                  remarks: 节点规则结果ID
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iend_time
                  type: timestamp
                  remarks: 更新时间
              - column:
                  name: iresult
                  type: smallint
                  remarks: 结果状态（-1:运行中，0:一致/成功，1：不一致/失败）
              - column:
                  name: istate
                  type: smallint
                  remarks: 启停状态（0：运行中，1：已完成，2：终止）
              - column:
                  name: ielapsed_time
                  type: bigint
                  remarks: 耗时
            remarks: 节点规则同步结果
        - createIndex:
            indexName: idx_run_rule_sync_01
            tableName: ieai_envc_run_rule_sync
            columns:
              - column:
                  name: ienvc_run_rule_id

  - changeSet:
      id: 1733104767689-13
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_run_flow
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: iflowid
                  type: bigint
                  remarks: 流程ID
              - column:
                  name: irun_biz_id
                  type: bigint
                  remarks: 比对规则ID或者比对规则对应的同步id
              - column:
                  name: imodel
                  type: smallint
                  remarks: 来源标识（0：比对，1：同步）
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iend_time
                  type: timestamp
                  remarks: 更新时间
              - column:
                  name: istate
                  type: smallint
                  remarks: 启停状态（0：运行中，1：已完成，2：终止）
              - column:
                  name: ielapsed_time
                  type: bigint
                  remarks: 耗时
              - column:
                  name: iret
                  type: varchar(10)
                  remarks: 执行结束码
            remarks: 节点规则流程表
        - createIndex:
            indexName: idx_run_flow_01
            tableName: ieai_envc_run_flow
            columns:
              - column:
                  name: irun_biz_id
              - column:
                  name: iflowid
        - createIndex:
            indexName: idx_run_flow_02
            tableName: ieai_envc_run_flow
            columns:
              - column:
                  name: iflowid

  - changeSet:
      id: 1733104767689-14
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_run_flow_result
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                    autoIncrement: true
                  remarks: 主键ID
              - column:
                  name: ienvc_run_flow_id
                  type: bigint
                  remarks: 节点规则流程主键
              - column:
                  name: iflowid
                  type: bigint
                  remarks: 流程ID
              - column:
                  name: icontent
                  type: longtext
                  remarks: 输出内容
              - column:
                  name: istderr
                  type: longtext
                  remarks: 错误输出内容
              - column:
                  name: istore_time
                  type: timestamp
                  remarks: 结果入库时间
            remarks: 流程输出结果表
        - createIndex:
            indexName: idx_run_flow_result_01
            tableName: ieai_envc_run_flow_result
            columns:
              - column:
                  name: ienvc_run_flow_id
        - createIndex:
            indexName: idx_run_flow_result_02
            tableName: ieai_envc_run_flow_result
            columns:
              - column:
                  name: iflowid

  - changeSet:
      id: 1733104767689-15
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_dictionary
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: icode
                  type: varchar(50)
                  remarks: 字典码
              - column:
                  name: idescription
                  type: varchar(150)
                  remarks: 字典描述
              - column:
                  name: init
                  type: smallint
                  remarks: 是否初始化 0：否，1：是
              - column:
                  name: ideleted
                  type: smallint
                  defaultValue: 0
                  remarks: 删除标识 0：否，1：是
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iupdator_id
                  type: bigint
                  remarks: 更新人ID
              - column:
                  name: iupdator_name
                  type: varchar(50)
                  remarks: 更新人名称
              - column:
                  name: iupdate_time
                  type: timestamp
            remarks: 字典码表
        - createIndex:
            indexName: idx_envc_dictionary_01
            tableName: ieai_envc_dictionary
            columns:
              - column:
                  name: icode
            unique: true

  - changeSet:
      id: 1733104767689-16
      author: Administrator (generated)
      changes:
        - createTable:
            tableName: ieai_envc_dictionary_detail
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键
              - column:
                  name: ienvc_dictionary_id
                  type: bigint
                  remarks: 码表主键
              - column:
                  name: icode
                  type: varchar(50)
                  remarks: 字典码
              - column:
                  name: ilable
                  type: varchar(100)
                  remarks: 显示名称
              - column:
                  name: ivalue
                  type: varchar(200)
                  remarks: 显示值
              - column:
                  name: isort
                  type: bigint
                  remarks: 排序序号
              - column:
                  name: ideleted
                  type: smallint
                  defaultValue: 0
                  remarks: 删除标识 0：否，1：是
              - column:
                  name: iarray_flag
                  type: smallint
                  defaultValue: 0
                  remarks: 数组标识 0：否，1：是
              - column:
                  name: ivalue_type
                  type: varchar(20)
                  remarks: 字典值类型（四类八种+String）
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iupdator_id
                  type: bigint
                  remarks: 更新人ID
              - column:
                  name: iupdator_name
                  type: varchar(50)
                  remarks: 更新人名称
              - column:
                  name: iupdate_time
                  type: timestamp
            remarks: 字典详情表
        - createIndex:
            indexName: idx_dictionary_detail_01
            tableName: ieai_envc_dictionary_detail
            columns:
              - column:
                  name: icode
        - createIndex:
            indexName: idx_dictionary_detail_02
            tableName: ieai_envc_dictionary_detail
            columns:
              - column:
                  name: ienvc_dictionary_id

  - changeSet:
      id: 1733104767689-20
      author: lch
      changes:
        - createTable:
            tableName: ieai_envc_run_rule_content
            columns:
              - column:
                  name: iid
                  type: bigint
                  constraints:
                    primaryKey: true
                    nullable: false
                  remarks: 主键ID
              - column:
                  name: ienvc_run_rule_id
                  type: bigint
                  remarks: 节点规则结果ID
              - column:
                  name: icontent_type
                  type: smallint
                  remarks: 内容类型（0：源内容，1：目标内容，2：差异内容）
              - column:
                  name: icontent
                  type: text
                  remarks: 内容
              - column:
                  name: icreator_id
                  type: bigint
                  remarks: 创建人ID
              - column:
                  name: icreator_name
                  type: varchar(50)
                  remarks: 创建人名称
              - column:
                  name: icreate_time
                  type: timestamp
                  remarks: 创建时间
              - column:
                  name: iupdate_time
                  type: timestamp
                  remarks: 更新时间
            remarks: 节点规则内容表
        - createIndex:
            indexName: idx_run_rule_content_01
            tableName: ieai_envc_run_rule_content
            columns:
              - column:
                  name: ienvc_run_rule_id

  - changeSet:
      id: 1733104767689-21
      author: lch
      changes:
        - addColumn:
            tableName: ieai_envc_run_instance_info
            columns:
              - column:
                  name: isource_center_name
                  type: varchar(150)
                  remarks: 源中心名称
              - column:
                  name: itarget_center_name
                  type: varchar(150)
                  remarks: 目标中心名称

  - changeSet:
      id: 1733104767689-22
      author: lch
      changes:
        - addColumn:
            tableName: ieai_envc_run_flow
            columns:
              - column:
                  name: iupdate_order_time
                  type: bigint
                  remarks: 引擎消息时间字段

  - changeSet:
      id: 1733104767689-23
      author: lch
      changes:
        - addColumn:
            tableName: ieai_envc_run_flow_result
            columns:
              - column:
                  name: iupdate_order_time
                  type: bigint
                  remarks: 引擎消息时间字段

  - changeSet:
      id: 1715768038000
      author: lch
      changes:
        - addColumn:
            tableName: ieai_envc_node_relation
            columns:
              - column:
                  name: isource_path
                  type: varchar(255)
                  remarks: "原路径"
        - addColumn:
            tableName: ieai_envc_run_rule
            columns:
              - column:
                  name: isource_path
                  type: varchar(255)
                  remarks: "原路径"

  - changeSet:
      id: 1715768038111
      author: lch
      changes:
        - addColumn:
            tableName: ieai_envc_run_instance
            columns:
              - column:
                  name: iupdate_time
                  type: timestamp
                  remarks: "更新时间"
        - addColumn:
            tableName: ieai_envc_run_instance_info
            columns:
              - column:
                  name: iupdate_time
                  type: timestamp
                  remarks: "更新时间"
        - addColumn:
            tableName: ieai_envc_run_rule
            columns:
              - column:
                  name: iupdate_time
                  type: timestamp
                  remarks: "更新时间"