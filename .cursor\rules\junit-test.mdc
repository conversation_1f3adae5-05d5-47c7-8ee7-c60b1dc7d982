---
description: junit test rules
globs: */src/test/**/*Test.java
alwaysApply: false
---
# 单元测试约束遵循如下所有的规则
- **每次执行任务前必须调用工具了解代码库后，再结合指令和代码库进行 COT 思考**
- **已有测试用例的方法不要修改，需要检查是否有必要调整或者与源码不同了， 可以修改单元测试代码以保证与源码匹配**
- **不允许有编译语法错误**
- **编写每一个单元测试场景的测试用例方法时，深度分析此测试用例调用的待测试方法的整个链路的源码，都需要哪写mock方法、mock数据，补充完全需要mock的方法或者属性数据，保证执行过程中不抛出任何异常，预期结果与实际方法的结果完全一致！**
- **新增、修改单元测试用例时，要深度扫描待执行的方法、属性的源码，不要自以为是什么，不要猜测，要以源码为准！**
- **要覆盖所有分支行的代码，即`if else`代码块中的所有行，需要mock的方法、属性都需要mock，不能够有遗漏**
- **当存在分支异常代码的情况，分析之后如果可以使用`ParameterizedTest`和`MethodSource`和`ValueSource`注解实现参数化测试，那就使用参数化注解，保证一个测试方法可以覆盖所有代码行；对于同一个待测试方法，如果使用了参数化测试，那就不能有不用参数化测试注解的测试用例方法，不要多个情况共存，即如下代码示例的情况不应该存在**
- `PageHelper.startPage`和`BeanUtils.copy`、`BeanUtil.copyToList`这俩个方法不需要mock!!!!
  ```java
    @ParameterizedTest
    @MethodSource("createPams")
    //@ValueSource(1,2)
    void xxMethod_test(String parms1,String parms2){
        //省略参数化判断逻辑
        xxxService.xxMethod()
    }

    @Test
    void xxMethod_test(){
        //省略其他
        xxxService.xxMethod()
    }
  ```
- **每一个测试用例方法中，要扫描待测试用方法的整个链路，添加`verify(xx).xxxMethod(xx)`验证的时候，要确保此用例执行过程中一定能调用到此方法，如果无法确定会调用到对应方法，那么不要加`verify(xx).xxxMethod(xx)`！！**
- 使用Junit5相关注解生成单元测试，所有单元测试类的名称都以Test结尾
- 完全使用Mockito 4.5.1版本单元测试框架的注解来生成每个方法的单元测试，使用的注解包括不限于`@Mock`、`@InjectMocks`、`@Spy`、`@ExtendWith`

- 每个单元测试用例方法中每个单元测试方法使用自己的mock参数类，除非有多个测试用例方法使用了相同的参数，才将这种参数对象写到`setUp()`方法中
- 当在一个方法调用中使用参数匹配器时，所有参数都必须使用匹配器。
- 没有我的允许，不要mock静态方法
- easyExcel导入功能的单元测试写法参照如下写法，使用工具类方法`CommonTools.mockEasyExcelFactory`：
  ```java
  try(MockedStatic<EasyExcelFactory> mockedEasyExcelFactory =  mockStatic(EasyExcelFactory.class)){
            CommonTools.mockEasyExcelFactory(mockedEasyExcelFactory, dataMap, ComputerDto.class);
            //其他mock方法、属性
             
            // 执行测试方法
        }
  ```
- 不要启动Spring boot相关上下文来进行单元测试编写，也不要使用`spring boot test`的相关注解
- 使用如下方式来mock对象方法调用
  ```java
  doReturn(false).when(xxxClass).xxxMethod(xxxArgs);
  ```
- 每个单元测试方法要添加`@DisplayName`注解用中文描述测试用例
- private方法也需要写单元测试用例，并确保所有待测试的public方法会调用到private方法。
- 对于单元测试中调用的方法有异常抛出定义时，一定要有所体现，或抛出或补获异常验证否则将出现编译错误。