package com.ideal.envc.model.bean;

import java.io.Serializable;

/**
 * 任务查询Bean
 *
 * <AUTHOR>
 */
public class TaskQueryBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;
    
    /** 方案ID */
    private Long envcPlanId;
    
    /** 方案名称 */
    private String planName;
    
    /** 周期表达式 */
    private String cron;
    
    /** 是否启用（1:启用，0：禁用） */
    private Integer enabled;
    
    /** 启停状态（0:启动，1：停止） */
    private Integer state;
    
    /** 源中心ID */
    private Long sourceCenterId;
    
    /** 目标中心ID */
    private Long targetCenterId;
    
    /** 定时ID */
    private Long scheduledId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcPlanId() {
        return envcPlanId;
    }

    public void setEnvcPlanId(Long envcPlanId) {
        this.envcPlanId = envcPlanId;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public Long getScheduledId() {
        return scheduledId;
    }

    public void setScheduledId(Long scheduledId) {
        this.scheduledId = scheduledId;
    }
} 