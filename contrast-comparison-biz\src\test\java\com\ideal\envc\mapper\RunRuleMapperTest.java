package com.ideal.envc.mapper;

import com.ideal.envc.model.bean.RunFlowDetailBean;
import com.ideal.envc.model.entity.RunRuleEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * RunRuleMapper的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class RunRuleMapperTest {

    @Mock
    private RunRuleMapper runRuleMapper;

    private RunRuleEntity runRuleEntity;
    private RunFlowDetailBean runFlowDetailBean;

    @BeforeEach
    void setUp() {
        // 初始化运行规则实体
        runRuleEntity = new RunRuleEntity();
        runRuleEntity.setId(1L);
        runRuleEntity.setEnvcRunInstanceInfoId(100L);
        runRuleEntity.setModel(1);
        runRuleEntity.setType(1L);
        runRuleEntity.setPath("/target/path");
        runRuleEntity.setSourcePath("/source/path");
        runRuleEntity.setEncode("UTF-8");
        runRuleEntity.setWay(1);
        runRuleEntity.setRuleType(1);
        runRuleEntity.setEnabled(1);
        runRuleEntity.setChildLevel(0);
        runRuleEntity.setCreatorId(1L);
        runRuleEntity.setCreatorName("测试用户");
        runRuleEntity.setResult(1);
        runRuleEntity.setState(1);
        runRuleEntity.setElapsedTime(1000L);

        // 初始化流程详情Bean
        runFlowDetailBean = new RunFlowDetailBean();
        runFlowDetailBean.setSourcePath("/source/path");
        runFlowDetailBean.setPath("/target/path");
        runFlowDetailBean.setBusinessSystemId(1L);
        runFlowDetailBean.setSourceComputerId(1L);
        runFlowDetailBean.setTargetComputerId(2L);
        runFlowDetailBean.setSourceCenterName("源中心");
        runFlowDetailBean.setTargetCenterName("目标中心");
        runFlowDetailBean.setSourceComputerIp("*************");
        runFlowDetailBean.setTargetComputerIp("*************");
        runFlowDetailBean.setSourceComputerName("源服务器");
        runFlowDetailBean.setTargetComputerName("目标服务器");
        runFlowDetailBean.setBusinessSystemName("测试系统");
    }

    @Test
    @DisplayName("测试根据ID查询运行规则")
    void testSelectRunRuleById() {
        // 设置Mock行为
        doReturn(runRuleEntity).when(runRuleMapper).selectRunRuleById(anyLong());

        // 执行测试
        RunRuleEntity result = runRuleMapper.selectRunRuleById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("/target/path", result.getPath());
        assertEquals("/source/path", result.getSourcePath());
        assertEquals("测试用户", result.getCreatorName());

        // 验证方法调用
        verify(runRuleMapper, times(1)).selectRunRuleById(1L);
    }

    @Test
    @DisplayName("测试查询运行规则列表")
    void testSelectRunRuleList() {
        // 准备测试数据
        List<RunRuleEntity> mockList = Arrays.asList(runRuleEntity);

        // 设置Mock行为
        doReturn(mockList).when(runRuleMapper).selectRunRuleList(any(RunRuleEntity.class));

        // 执行测试
        RunRuleEntity queryParam = new RunRuleEntity();
        queryParam.setEnvcRunInstanceInfoId(100L);
        List<RunRuleEntity> result = runRuleMapper.selectRunRuleList(queryParam);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(runRuleEntity.getId(), result.get(0).getId());

        // 验证方法调用
        verify(runRuleMapper, times(1)).selectRunRuleList(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试新增运行规则")
    void testInsertRunRule() {
        // 设置Mock行为 - 返回1表示插入成功
        doReturn(1).when(runRuleMapper).insertRunRule(any(RunRuleEntity.class));

        // 执行测试
        int result = runRuleMapper.insertRunRule(runRuleEntity);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleMapper, times(1)).insertRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试更新运行规则")
    void testUpdateRunRule() {
        // 设置Mock行为 - 返回1表示更新成功
        doReturn(1).when(runRuleMapper).updateRunRule(any(RunRuleEntity.class));

        // 执行测试
        int result = runRuleMapper.updateRunRule(runRuleEntity);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleMapper, times(1)).updateRunRule(any(RunRuleEntity.class));
    }

    @Test
    @DisplayName("测试更新运行规则结果")
    void testUpdateRunRuleOfResult() {
        // 设置Mock行为 - 返回1表示更新成功
        doReturn(1).when(runRuleMapper).updateRunRuleOfResult(anyInt(), anyLong());

        // 执行测试
        int result = runRuleMapper.updateRunRuleOfResult(1, 1L);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleMapper, times(1)).updateRunRuleOfResult(1, 1L);
    }

    @Test
    @DisplayName("测试删除运行规则")
    void testDeleteRunRuleById() {
        // 设置Mock行为 - 返回1表示删除成功
        doReturn(1).when(runRuleMapper).deleteRunRuleById(anyLong());

        // 执行测试
        int result = runRuleMapper.deleteRunRuleById(1L);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleMapper, times(1)).deleteRunRuleById(1L);
    }

    @Test
    @DisplayName("测试批量删除运行规则")
    void testDeleteRunRuleByIds() {
        // 设置Mock行为 - 返回2表示删除了2条记录
        doReturn(2).when(runRuleMapper).deleteRunRuleByIds(any(Long[].class));

        // 执行测试
        Long[] ids = {1L, 2L};
        int result = runRuleMapper.deleteRunRuleByIds(ids);

        // 验证结果
        assertEquals(2, result);

        // 验证方法调用
        verify(runRuleMapper, times(1)).deleteRunRuleByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试根据实例信息ID查询规则列表")
    void testSelectRulesByInstanceInfoId() {
        // 准备测试数据
        List<RunRuleEntity> mockList = Arrays.asList(runRuleEntity);

        // 设置Mock行为
        doReturn(mockList).when(runRuleMapper).selectRulesByInstanceInfoId(anyLong());

        // 执行测试
        List<RunRuleEntity> result = runRuleMapper.selectRulesByInstanceInfoId(100L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(runRuleEntity.getId(), result.get(0).getId());

        // 验证方法调用
        verify(runRuleMapper, times(1)).selectRulesByInstanceInfoId(100L);
    }

    @Test
    @DisplayName("测试根据流程ID查询运行规则详细信息")
    void testSelectRunRuleDetailByFlowId() {
        // 准备测试数据
        List<RunFlowDetailBean> mockList = Arrays.asList(runFlowDetailBean);

        // 设置Mock行为
        doReturn(mockList).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        // 执行测试
        List<RunFlowDetailBean> result = runRuleMapper.selectRunRuleDetailByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        RunFlowDetailBean detailBean = result.get(0);
        assertEquals("/source/path", detailBean.getSourcePath());
        assertEquals("/target/path", detailBean.getPath());
        assertEquals(1L, detailBean.getBusinessSystemId());
        assertEquals("源中心", detailBean.getSourceCenterName());
        assertEquals("目标中心", detailBean.getTargetCenterName());
        assertEquals("*************", detailBean.getSourceComputerIp());
        assertEquals("*************", detailBean.getTargetComputerIp());
        assertEquals("源服务器", detailBean.getSourceComputerName());
        assertEquals("目标服务器", detailBean.getTargetComputerName());
        assertEquals("测试系统", detailBean.getBusinessSystemName());

        // 验证方法调用
        verify(runRuleMapper, times(1)).selectRunRuleDetailByFlowId(1L);
    }

    @Test
    @DisplayName("测试根据流程ID查询运行规则详细信息 - 返回空列表")
    void testSelectRunRuleDetailByFlowId_EmptyResult() {
        // 设置Mock行为 - 返回空列表
        doReturn(new ArrayList<>()).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        // 执行测试
        List<RunFlowDetailBean> result = runRuleMapper.selectRunRuleDetailByFlowId(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());

        // 验证方法调用
        verify(runRuleMapper, times(1)).selectRunRuleDetailByFlowId(1L);
    }

    @Test
    @DisplayName("测试根据流程ID查询运行规则详细信息 - 返回null")
    void testSelectRunRuleDetailByFlowId_NullResult() {
        // 设置Mock行为 - 返回null
        doReturn(null).when(runRuleMapper).selectRunRuleDetailByFlowId(anyLong());

        // 执行测试
        List<RunFlowDetailBean> result = runRuleMapper.selectRunRuleDetailByFlowId(1L);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(runRuleMapper, times(1)).selectRunRuleDetailByFlowId(1L);
    }
}
