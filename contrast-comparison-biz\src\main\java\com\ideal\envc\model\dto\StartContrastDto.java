package com.ideal.envc.model.dto;

import java.util.List;

/**
 * <AUTHOR>
 */
public class StartContrastDto {

    /**
     * 方案名称。对应ieai_envc_plan表的iname字段 模糊查询
     */
    private String planNameLike;
    /**
     * 计划ID列表。对应ieai_envc_plan表的iid字段
     */
    private List<Long> planIdList;

    /**
     * 源中心ID，对应ieai_envc_system_computer_node表的isource_center_id字段
     */
    private Long sourceCenterId;

    /**
     * 目标中心ID，对应ieai_envc_system_computer_node表的itarget_center_id字段
     */
    private Long targetCenterId;

    /**
     * 任务启动来源.0:方案级比对，1：设备级，2：规则级，3：任务级
     */
    private int startSource;

    /**
     * 业务系统ID集合，对应ieai_envc_plan_relation表的ibusiness_system_id字段
     */
    private List<Long> businessSystemIdList;

    /**
     * 节点关系ID集合，对应ieai_envc_system_computer_node表的iid字段
     */
    private List<Long> systemComputerNodeIdList;

    /**
     * 节点关系规则ID集合，对应ieai_envc_node_relation的iid字段
     */
    private List<Long> nodeRelationIdList;


    /**
     * 任务ID列表
     */
    private List<Long> taskIdList;

    public String getPlanNameLike() {
        return planNameLike;
    }

    public void setPlanNameLike(String planNameLike) {
        this.planNameLike = planNameLike;
    }

    public List<Long> getPlanIdList() {
        return planIdList;
    }

    public void setPlanIdList(List<Long> planIdList) {
        this.planIdList = planIdList;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public int getStartSource() {
        return startSource;
    }

    public void setStartSource(int startSource) {
        this.startSource = startSource;
    }

    public List<Long> getBusinessSystemIdList() {
        return businessSystemIdList;
    }

    public void setBusinessSystemIdList(List<Long> businessSystemIdList) {
        this.businessSystemIdList = businessSystemIdList;
    }

    public List<Long> getSystemComputerNodeIdList() {
        return systemComputerNodeIdList;
    }

    public void setSystemComputerNodeIdList(List<Long> systemComputerNodeIdList) {
        this.systemComputerNodeIdList = systemComputerNodeIdList;
    }

    public List<Long> getNodeRelationIdList() {
        return nodeRelationIdList;
    }

    public void setNodeRelationIdList(List<Long> nodeRelationIdList) {
        this.nodeRelationIdList = nodeRelationIdList;
    }

    public List<Long> getTaskIdList() {
        return taskIdList;
    }

    public void setTaskIdList(List<Long> taskIdList) {
        this.taskIdList = taskIdList;
    }
}
