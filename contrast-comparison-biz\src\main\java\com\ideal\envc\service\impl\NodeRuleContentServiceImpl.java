package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.model.entity.NodeRuleContentEntity;
import com.ideal.envc.service.INodeRuleContentService;
import com.ideal.envc.model.dto.NodeRuleContentDto;
import com.ideal.envc.model.dto.NodeRuleContentQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 节点关系规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class NodeRuleContentServiceImpl implements INodeRuleContentService {
    private final Logger logger = LoggerFactory.getLogger(NodeRuleContentServiceImpl.class);

    private final NodeRuleContentMapper nodeRuleContentMapper;

    public NodeRuleContentServiceImpl(NodeRuleContentMapper nodeRuleContentMapper) {
        this.nodeRuleContentMapper = nodeRuleContentMapper;
    }

    /**
     * 查询节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 节点关系规则
     */
    @Override
    public NodeRuleContentDto selectNodeRuleContentById(Long id) {
        NodeRuleContentEntity nodeRuleContent = nodeRuleContentMapper.selectNodeRuleContentById(id);
        return BeanUtils.copy(nodeRuleContent, NodeRuleContentDto.class);
    }

    /**
     * 查询节点关系规则列表
     *
     * @param nodeRuleContentQueryDto 节点关系规则
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点关系规则
     */
    @Override
    public PageInfo<NodeRuleContentDto> selectNodeRuleContentList(NodeRuleContentQueryDto nodeRuleContentQueryDto, Integer pageNum, Integer pageSize) {
        NodeRuleContentEntity query = BeanUtils.copy(nodeRuleContentQueryDto, NodeRuleContentEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<NodeRuleContentEntity> nodeRuleContentList = nodeRuleContentMapper.selectNodeRuleContentList(query);
        return PageDataUtil.toDtoPage(nodeRuleContentList, NodeRuleContentDto.class);
    }

    /**
     * 新增节点关系规则
     *
     * @param nodeRuleContentDto 节点关系规则
     * @return 结果
     */
    @Override
    public int insertNodeRuleContent(NodeRuleContentDto nodeRuleContentDto) {
        NodeRuleContentEntity nodeRuleContent = BeanUtils.copy(nodeRuleContentDto, NodeRuleContentEntity.class);
        return nodeRuleContentMapper.insertNodeRuleContent(nodeRuleContent);
    }

    /**
     * 修改节点关系规则
     *
     * @param nodeRuleContentDto 节点关系规则
     * @return 结果
     */
    @Override
    public int updateNodeRuleContent(NodeRuleContentDto nodeRuleContentDto) {
        NodeRuleContentEntity nodeRuleContent = BeanUtils.copy(nodeRuleContentDto, NodeRuleContentEntity.class);
        return nodeRuleContentMapper.updateNodeRuleContent(nodeRuleContent);
    }

    /**
     * 批量删除节点关系规则
     *
     * @param ids 需要删除的节点关系规则主键
     * @return 结果
     */
    @Override
    public int deleteNodeRuleContentByIds(Long[] ids) {
        return nodeRuleContentMapper.deleteNodeRuleContentByIds(ids);
    }
}
