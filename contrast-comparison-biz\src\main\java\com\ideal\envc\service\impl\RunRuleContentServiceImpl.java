package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.envc.mapper.RunRuleContentMapper;
import com.ideal.envc.model.dto.RunRuleContentDto;
import com.ideal.envc.model.dto.RunRuleContentQueryDto;
import com.ideal.envc.model.entity.RunRuleContentEntity;
import com.ideal.envc.service.IRunRuleContentService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 节点规则内容Service实现
 *
 * <AUTHOR>
 */
@Service
public class RunRuleContentServiceImpl implements IRunRuleContentService {
    private static final Logger logger = LoggerFactory.getLogger(RunRuleContentServiceImpl.class);

    private final RunRuleContentMapper runRuleContentMapper;

    public RunRuleContentServiceImpl(RunRuleContentMapper runRuleContentMapper) {
        this.runRuleContentMapper = runRuleContentMapper;
    }

    /**
     * 查询节点规则内容
     *
     * @param id 节点规则内容ID
     * @return 节点规则内容
     */
    @Override
    public RunRuleContentDto selectRunRuleContentById(Long id) {
        RunRuleContentEntity entity = runRuleContentMapper.selectRunRuleContentById(id);
        return BeanUtils.copy(entity, RunRuleContentDto.class);
    }

    /**
     * 查询节点规则内容列表
     *
     * @param queryDto 节点规则内容查询条件
     * @return 节点规则内容
     */
    @Override
    public List<RunRuleContentDto> selectRunRuleContentList(RunRuleContentQueryDto queryDto) {
        RunRuleContentEntity entity = new RunRuleContentEntity();
        if (queryDto != null) {
            entity.setId(queryDto.getId());
            entity.setEnvcRunRuleId(queryDto.getEnvcRunRuleId());
        }
        List<RunRuleContentEntity> list = runRuleContentMapper.selectRunRuleContentList(entity);
        return BeanUtils.copy(list, RunRuleContentDto.class);
    }

    /**
     * 查询节点规则内容分页列表
     *
     * @param queryDto 节点规则内容查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 节点规则内容分页列表
     */
    @Override
    public PageInfo<RunRuleContentDto> selectRunRuleContentPage(RunRuleContentQueryDto queryDto, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<RunRuleContentDto> list = selectRunRuleContentList(queryDto);
        return new PageInfo<>(list);
    }

    /**
     * 根据规则ID查询节点规则内容列表
     *
     * @param envcRunRuleId 规则ID
     * @return 节点规则内容集合
     */
    @Override
    public List<RunRuleContentDto> selectRunRuleContentListByRuleId(Long envcRunRuleId) {
        List<RunRuleContentEntity> list = runRuleContentMapper.selectRunRuleContentListByRuleId(envcRunRuleId);
        return BeanUtils.copy(list, RunRuleContentDto.class);
    }

    /**
     * 新增节点规则内容
     *
     * @param runRuleContentDto 节点规则内容
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRunRuleContent(RunRuleContentDto runRuleContentDto) {
        RunRuleContentEntity entity = BeanUtils.copy(runRuleContentDto, RunRuleContentEntity.class);
        return runRuleContentMapper.insertRunRuleContent(entity);
    }

    /**
     * 修改节点规则内容
     *
     * @param runRuleContentDto 节点规则内容
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRunRuleContent(RunRuleContentDto runRuleContentDto) {
        RunRuleContentEntity entity = BeanUtils.copy(runRuleContentDto, RunRuleContentEntity.class);
        return runRuleContentMapper.updateRunRuleContent(entity);
    }

    /**
     * 批量删除节点规则内容
     *
     * @param ids 需要删除的节点规则内容ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRunRuleContentByIds(Long[] ids) {
        return runRuleContentMapper.deleteRunRuleContentByIds(ids);
    }

    /**
     * 删除节点规则内容信息
     *
     * @param id 节点规则内容ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRunRuleContentById(Long id) {
        return runRuleContentMapper.deleteRunRuleContentById(id);
    }

    /**
     * 根据规则ID删除节点规则内容
     *
     * @param envcRunRuleId 规则ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRunRuleContentByRuleId(Long envcRunRuleId) {
        return runRuleContentMapper.deleteRunRuleContentByRuleId(envcRunRuleId);
    }
}
