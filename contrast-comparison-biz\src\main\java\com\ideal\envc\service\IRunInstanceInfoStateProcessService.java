package com.ideal.envc.service;

/**
 * 运行实例信息状态处理服务接口
 * 
 * 主要功能：
 * 1. 处理规则层状态变更后的实例详情状态计算和变更
 * 2. 管理实例详情计数器，实现规则计数器归0时的状态更新
 * 3. 确保在实例详情状态更新成功后才清理计数器
 * 
 * <AUTHOR>
 */
public interface IRunInstanceInfoStateProcessService {

    /**
     * 处理实例详情状态更新
     * 
     * 处理流程：
     * 1. 检查消息有效性和过期性
     * 2. 查询实例详情信息
     * 3. 检查和更新实例详情计数器
     * 4. 当计数器归0时，触发状态计算和更新
     * 5. 状态更新成功后清理计数器
     * 
     * @param instanceInfoId 实例详情ID
     * @param ruleId 规则ID
     * @param messageTimestamp 消息时间戳
     * @return 是否处理成功
     */
    boolean processInstanceInfoStateUpdate(Long instanceInfoId, Long ruleId, Long messageTimestamp);

    /**
     * 检查并减少实例详情计数器
     * 
     * @param instanceInfoId 实例详情ID
     * @param ruleId 规则ID
     * @return 减少后的计数器值，-1表示减少失败
     */
    int decrementInstanceInfoCounter(Long instanceInfoId, Long ruleId);

    /**
     * 计算实例详情状态和结果
     * 基于关联的运行规则状态计算汇总状态
     * 
     * @param instanceInfoId 实例详情ID
     * @return [状态, 结果] 数组
     */
    int[] calculateInstanceInfoStateAndResult(Long instanceInfoId);

    /**
     * 更新实例详情状态和结果
     * 使用乐观锁机制防止并发冲突
     * 
     * @param instanceInfoId 实例详情ID
     * @param state 状态
     * @param result 结果
     * @param currentTimestamp 当前时间戳
     * @return 是否更新成功
     */
    boolean updateInstanceInfoStateAndResult(Long instanceInfoId, int state, int result, Long currentTimestamp);

    /**
     * 清理实例详情计数器
     * 在状态更新成功后调用
     * 
     * @param instanceInfoId 实例详情ID
     * @return 是否清理成功
     */
    boolean clearInstanceInfoCounter(Long instanceInfoId);

}