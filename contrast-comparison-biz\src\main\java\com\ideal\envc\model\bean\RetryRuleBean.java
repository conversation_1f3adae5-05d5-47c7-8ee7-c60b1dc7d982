package com.ideal.envc.model.bean;

/**
 * <AUTHOR>
 */
public class RetryRuleBean {

    /**
     * 原运行规则ID
     */
    private Long runRuleId;

    /**
     * 原运行实例详情ID
     */
    private Long runInstanceInfoId;

    /**
     * 原实例ID
     */
    private Long runInstanceId;

    /**
     * 原规则内容
     */
    private  String ruleContent;

    public Long getRunRuleId() {
        return runRuleId;
    }

    public void setRunRuleId(Long runRuleId) {
        this.runRuleId = runRuleId;
    }

    public Long getRunInstanceInfoId() {
        return runInstanceInfoId;
    }

    public void setRunInstanceInfoId(Long runInstanceInfoId) {
        this.runInstanceInfoId = runInstanceInfoId;
    }

    public Long getRunInstanceId() {
        return runInstanceId;
    }

    public void setRunInstanceId(Long runInstanceId) {
        this.runInstanceId = runInstanceId;
    }

    public String getRuleContent() {
        return ruleContent;
    }

    public void setRuleContent(String ruleContent) {
        this.ruleContent = ruleContent;
    }
}
