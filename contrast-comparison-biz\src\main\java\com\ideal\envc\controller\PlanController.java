package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.PlanDto;
import com.ideal.envc.model.dto.PlanQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.service.IPlanService;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.exception.ContrastBusinessException;
import java.util.Arrays;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 方案管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/plan")
@MethodPermission("@dp.hasBtnPermission('programme-management')")
public class PlanController {
    private static final Logger logger = LoggerFactory.getLogger(PlanController.class);

    private final IPlanService planService;
    private final UserinfoComponent userinfoComponent;

    public PlanController(IPlanService planService, UserinfoComponent userinfoComponent) {
        this.planService = planService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 查询方案信息列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<PlanDto>> list(@RequestBody TableQueryDto<PlanQueryDto> tableQueryDto) {
        try {
            PageInfo<PlanDto> list = planService.selectPlanList(
                    tableQueryDto.getQueryParam(),
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("查询方案列表失败", e);
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), ResponseCodeEnum.QUERY_FAIL.getDesc());
        }
    }

    /**
     * 查询方案信息详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<PlanDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        try {
            PlanDto planDto = planService.selectPlanById(id);
            if (planDto == null) {
                return R.fail(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), ResponseCodeEnum.DATA_NOT_FOUND.getDesc());
            }
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), planDto, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("查询方案详情失败，ID={}", id, e);
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), ResponseCodeEnum.QUERY_FAIL.getDesc());
        }
    }

    /**
     * 新增保存方案信息
     *
     * @param planDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('savePlan')")
    public R<Void> save(@RequestBody PlanDto planDto) {
        try {
            // 获取当前登录用户信息
            UserDto userDto = userinfoComponent.getUser();
            
            int result = planService.insertPlan(planDto, userDto);
            if (result > 0) {
                logger.info("新增方案成功，操作人：{}", userDto.getFullName());
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("新增方案业务异常：{}", e.getMessage());
            if (e.getMessage().contains("已存在")) {
                return R.fail(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode(), e.getMessage());
            } else if (e.getMessage().contains("不能为空")) {
                return R.fail(ResponseCodeEnum.ADD_PARAM_ERROR.getCode(), e.getMessage());
            } else {
                return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("新增方案系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存方案信息
     *
     * @param planDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    @MethodPermission("@dp.hasBtnPermission('updatePlan')")
    public R<Void> update(@RequestBody PlanDto planDto) {
        try {
            // 获取当前登录用户信息
            UserDto userDto = userinfoComponent.getUser();
            
            int result = planService.updatePlan(planDto, userDto);
            logger.info("修改方案成功，操作人：{}，ID：{}", userDto.getFullName(), planDto.getId());
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("修改方案业务异常：{}", e.getMessage());
            // 根据异常消息判断响应码
            if (e.getMessage().contains("不存在")) {
                return R.fail(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode(), e.getMessage());
            } else if (e.getMessage().contains("已存在")) {
                return R.fail(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode(), e.getMessage());
            } else if (e.getMessage().contains("不能为空")) {
                return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), e.getMessage());
            } else {
                return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("修改方案系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 删除方案信息
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('removePlan')")
    public R<Void> remove(@RequestBody Long[] ids) {
        try {
            // 获取当前登录用户信息
            UserDto userDto = userinfoComponent.getUser();
            
            int result = planService.deletePlanByIds(ids, userDto);
            logger.info("删除方案成功，操作人：{}，删除ID：{}", userDto.getFullName(), 
                    String.join(",", Arrays.stream(ids).map(String::valueOf).toArray(String[]::new)));
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("删除方案业务异常：{}", e.getMessage());
            if (e.getMessage().contains("不存在")) {
                return R.fail(ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getCode(), e.getMessage());
            } else if (e.getMessage().contains("不能为空")) {
                return R.fail(ResponseCodeEnum.DELETE_PARAM_ERROR.getCode(), e.getMessage());
            } else {
                return R.fail(ResponseCodeEnum.DELETE_FAIL.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("删除方案系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
