package com.ideal.envc.service.impl;

import com.ideal.envc.component.TaskCounterComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.service.IRunInstanceStateProcessService;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 运行实例状态处理服务实现
 * 
 * 主要功能：
 * 1. 处理实例详情状态变更后的实例状态计算和变更
 * 2. 管理实例计数器，实现实例详情计数器归0时的状态更新
 * 3. 确保在实例状态更新成功后才清理计数器
 * 
 * <AUTHOR>
 */
@Service
public class RunInstanceStateProcessServiceImpl implements IRunInstanceStateProcessService {
    private static final Logger logger = LoggerFactory.getLogger(RunInstanceStateProcessServiceImpl.class);
    
    /** 消息过期时间（毫秒） */
    private static final long MESSAGE_EXPIRE_TIME = 30000L;
    
    private final RunInstanceMapper runInstanceMapper;
    private final RunInstanceInfoMapper runInstanceInfoMapper;
    private final TaskCounterComponent taskCounterComponent;

    public RunInstanceStateProcessServiceImpl(RunInstanceMapper runInstanceMapper,
                                            RunInstanceInfoMapper runInstanceInfoMapper,
                                            TaskCounterComponent taskCounterComponent) {
        this.runInstanceMapper = runInstanceMapper;
        this.runInstanceInfoMapper = runInstanceInfoMapper;
        this.taskCounterComponent = taskCounterComponent;
    }

    /**
     * 处理实例状态更新
     * 
     * @param instanceId 实例ID
     * @param instanceInfoId 实例详情ID
     * @param messageTimestamp 消息时间戳
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processInstanceStateUpdate(Long instanceId, Long instanceInfoId, Long messageTimestamp) {
        try {
            // 参数校验
            if (instanceId == null || instanceInfoId == null) {
                logger.warn("参数不能为空，instanceId: {}, instanceInfoId: {}", instanceId, instanceInfoId);
                return false;
            }

            // 1. 根据instanceId查询对应的运行实例
            RunInstanceEntity instance = runInstanceMapper.selectRunInstanceById(instanceId);
            if (instance == null) {
                logger.warn("未找到对应的运行实例，instanceId: {}", instanceId);
                // 记录不存在，认为处理成功，避免重试
                return true;
            }

            // 2. 检查消息是否过期
            Long currentTimestamp = getCurrentTimestamp(instance);
            if (isMessageExpired(messageTimestamp, currentTimestamp, instanceId)) {
                return true;
            }

            // 3. 检查和减少实例计数器
            int counterValue = decrementInstanceCounter(instanceId);
            
            // 4. 处理计数器结果
            return handleCounterValue(instanceId, instanceInfoId, currentTimestamp, counterValue);
            
        } catch (Exception e) {
            logger.error("处理实例状态更新异常，instanceId: {}", instanceId, e);
            // 需要重试
            return false;
        }
    }
    
    /**
     * 获取当前时间戳
     * @param instance 实例实体
     * @return 当前时间戳
     */
    Long getCurrentTimestamp(RunInstanceEntity instance) {
        return instance.getUpdateTime() != null ? 
            instance.getUpdateTime().getTime() : System.currentTimeMillis();
    }
    
    /**
     * 检查消息是否过期
     * @param messageTimestamp 消息时间戳
     * @param currentTimestamp 当前时间戳
     * @param instanceId 实例ID
     * @return 是否过期
     */
    boolean isMessageExpired(Long messageTimestamp, Long currentTimestamp, Long instanceId) {
        if (messageTimestamp != null && currentTimestamp > messageTimestamp + MESSAGE_EXPIRE_TIME) {
            logger.warn("消息已过期，跳过处理，instanceId: {}, messageTimestamp: {}, currentTimestamp: {}", 
                instanceId, messageTimestamp, currentTimestamp);
            // 过期消息，认为处理成功，避免重试
            return true;
        }
        return false;
    }
    
    /**
     * 处理计数器值
     * @param instanceId 实例ID
     * @param instanceInfoId 实例详情ID
     * @param currentTimestamp 当前时间戳
     * @param counterValue 计数器值
     * @return 处理结果
     */
    boolean handleCounterValue(Long instanceId, Long instanceInfoId, Long currentTimestamp, int counterValue) {
        if (counterValue == 0) {
            logger.info("实例计数器已归0，开始进行状态计算和更新，instanceId: {}", instanceId);
            return processZeroCounter(instanceId, currentTimestamp);
        } else if (counterValue > 0) {
            logger.info("实例计数器未归0，暂不进行状态更新，instanceId: {}, counterValue: {}", 
                instanceId, counterValue);
            // 计数器未归0，认为处理成功
            return true;
        } else {
            logger.error("实例计数器减值失败，instanceId: {}", instanceId);
            // 计数器减值失败，需要重试
            return false;
        }
    }
    
    /**
     * 处理计数器为0的情况
     * @param instanceId 实例ID
     * @param currentTimestamp 当前时间戳
     * @return 处理结果
     */
    boolean processZeroCounter(Long instanceId, Long currentTimestamp) {
        String businessLockKey = "instance_state_process:" + instanceId;
        boolean processSuccess = false;
        RLock lock = null;
        boolean isLocked = false;
        try {
            // 尝试获取业务处理锁
            lock = taskCounterComponent.getBusinessLock(businessLockKey, 30);
            if(lock==null){
                logger.info("获取业务处理锁失败，可能其他线程正在处理，instanceId: {}", instanceId);
                return processSuccess;
            }
            isLocked = lock.tryLock(30, TaskCounterComponent.LOCK_TIMEOUT, TimeUnit.SECONDS);
            if(!isLocked){
                logger.info("获取业务处理锁失败，可能其他线程正在处理，instanceId: {}", instanceId);
                return processSuccess;
            }
            processSuccess= processWithLock(instanceId, currentTimestamp);
        } catch (Exception e) {
            logger.error("业务处理过程异常，instanceId: {}", instanceId, e);
            // 如果处理成功了但是在finally中出现异常，不要返回失败
            return processSuccess;
        }finally {
            if (lock != null && isLocked  ) {
                lock.unlock();
            }
        }
        return processSuccess;
    }
    
    /**
     * 在获取锁的情况下处理
     * @param instanceId 实例ID
     * @param currentTimestamp 当前时间戳
     * @return 处理结果
     */
    boolean processWithLock(Long instanceId, Long currentTimestamp) throws ContrastBusinessException {
        // 重新检查计数器，确保没有被其他线程处理
        long recheck = taskCounterComponent.getInstanceCounterValue(instanceId);
        if (recheck != 0) {
            logger.info("实例计数器值已变化，跳过处理，instanceId: {}, counterValue: {}", 
                instanceId, recheck);
            return true;
        }
        
        // 获取实例信息（用于状态比较）
        RunInstanceEntity instance = runInstanceMapper.selectRunInstanceById(instanceId);
        if (instance == null) {
            logger.warn("实例不存在，instanceId: {}", instanceId);
            return true;
        }
        
        // 计算汇总状态
        int[] statusAndResult = calculateInstanceStateAndResult(instanceId);
        int status = statusAndResult[0];
        int resultVal = statusAndResult[1];

        // 检查状态是否需要更新
        if (isStateUnchanged(instance, status, resultVal)) {
            return handleUnchangedState(instanceId);
        }

        // 更新状态
        return updateState(instanceId, status, resultVal, currentTimestamp);
    }
    
    /**
     * 检查状态是否未变化
     * @param instance 实例实体
     * @param status 计算的状态
     * @param resultVal 计算的结果
     * @return 是否未变化
     */
    boolean isStateUnchanged(RunInstanceEntity instance, int status, int resultVal) {
        return instance.getState() != null && instance.getResult() != null &&
               instance.getState() == status && instance.getResult() == resultVal;
    }
    
    /**
     * 处理状态未变化的情况
     * @param instanceId 实例ID
     * @return 处理结果
     */
    boolean handleUnchangedState(Long instanceId) {
        logger.info("运行实例状态未发生变化，无需更新，instanceId: {}", instanceId);
        // 状态未变化，但处理已完成，清理计数器
        clearInstanceCounter(instanceId);
        return true;
    }
    
    /**
     * 更新状态
     * @param instanceId 实例ID
     * @param status 状态
     * @param resultVal 结果
     * @param currentTimestamp 当前时间戳
     * @return 更新结果
     */
    boolean updateState(Long instanceId, int status, int resultVal, Long currentTimestamp) {
        boolean updateSuccess = updateInstanceStateAndResult(instanceId, status, resultVal, currentTimestamp);
        
        if (updateSuccess) {
            logger.info("更新运行实例状态成功，instanceId: {}, status: {}, resultVal: {}", 
                instanceId, status, resultVal);
            
            // 只有状态更新成功后才清理计数器
            clearInstanceCounter(instanceId);
            return true;
        } else {
            logger.warn("更新运行实例状态失败，保留计数器以便重试，instanceId: {}", instanceId);
            // 状态更新失败，不清理计数器，保持数据一致性
            return false;
        }
    }

    /**
     * 检查并减少实例计数器
     * 
     * @param instanceId 实例ID
     * @return 减少后的计数器值，-1表示减少失败
     */
    @Override
    public int decrementInstanceCounter(Long instanceId) {
        try {
            /*
             * 调用TaskCounterComponent进行原子计数器减值操作
             * 纯计数器操作，不包含业务逻辑
             */
            long counterValue = taskCounterComponent.decrementInstanceCounter(instanceId);
            logger.info("实例计数器减值操作完成，instanceId: {}, counterValue: {}", 
                instanceId, counterValue);
            return (int) counterValue;
        } catch (ContrastBusinessException e) {
            logger.error("实例计数器减值失败，instanceId: {}", instanceId, e);
            return -1;
        }
    }

    /**
     * 计算实例状态和结果
     * 
     * @param instanceId 实例ID
     * @return [状态, 结果] 数组
     */
    @Override
    public int[] calculateInstanceStateAndResult(Long instanceId) {
        // 根据instanceId查询对应的运行实例信息对象集合
        List<RunInstanceInfoEntity> instanceInfos = runInstanceInfoMapper.selectInstanceInfosByInstanceId(instanceId);
        if (instanceInfos.isEmpty()) {
            logger.warn("未找到关联的运行实例信息，instanceId: {}", instanceId);
            // 默认返回运行中状态
            return new int[]{0, -1};
        }

        int status = 0;
        int resultVal = -1;
        
        /*
         * 计算status状态
         * 状态优先级：终止(2) > 运行中(0) > 已完成(1)
         * 只要有一个终止，整体就是终止状态
         */
        // 1表示已完成
        boolean allCompleted = instanceInfos.stream().allMatch(info -> info.getState() != null && info.getState() == 1);
        // 2表示终止
        boolean hasTerminated = instanceInfos.stream().anyMatch(info -> info.getState() != null && info.getState() == 2);
        // 0表示运行中（只有在没有终止状态的情况下，才考虑运行中状态）
        boolean hasRunning = !hasTerminated && instanceInfos.stream().anyMatch(info -> info.getState() != null && info.getState() == 0);

        // 按照规则计算status
        if (allCompleted) {
            // 已完成
            status = 1;
        } else if (hasTerminated) {
            // 终止
            status = 2;
        } else if (hasRunning) {
            // 运行中
            status = 0;
        }

        /*
         * 计算resultVal结果
         * 结果优先级：失败(1) > 运行中(-1) > 成功(0)
         * 只要有一个失败，整体就是失败结果
         */
        // 1表示不一致/失败
        boolean hasFailed = instanceInfos.stream().anyMatch(info -> info.getResult() != null && info.getResult() == 1);
        // -1表示运行中（只有在没有失败状态的情况下，才考虑运行中状态）
        boolean hasRunningResult = !hasFailed && instanceInfos.stream().anyMatch(info -> info.getResult() != null && info.getResult() == -1);
        // 0表示一致/成功
        boolean allSuccess = instanceInfos.stream().allMatch(info -> info.getResult() != null && info.getResult() == 0);

        // 按照规则计算resultVal
        if (hasFailed) {
            // 不一致/失败
            resultVal = 1;
        } else if (hasRunningResult) {
            // 运行中
            resultVal = -1;
        } else if (allSuccess) {
            // 一致/成功
            resultVal = 0;
        }
        
        return new int[]{status, resultVal};
    }

    /**
     * 更新实例状态和结果
     * 
     * @param instanceId 实例ID
     * @param state 状态
     * @param result 结果
     * @param currentTimestamp 当前时间戳
     * @return 是否更新成功
     */
    @Override
    public boolean updateInstanceStateAndResult(Long instanceId, int state, int result, Long currentTimestamp) {
        try {
            /*
             * 使用当前数据库时间戳作为乐观锁条件更新数据
             * 防止并发更新导致的数据不一致问题
             */
            int updatedRows = runInstanceMapper.updateStateAndResultByIdAndTimestamp(
                instanceId, state, result);
            
            return updatedRows > 0;
        } catch (Exception e) {
            logger.error("更新实例状态和结果失败，instanceId: {}", instanceId, e);
            return false;
        }
    }

    /**
     * 清理实例计数器
     * 
     * @param instanceId 实例ID
     * @return 是否清理成功
     */
    @Override
    public boolean clearInstanceCounter(Long instanceId) {
        try {
            taskCounterComponent.clearInstanceCounter(instanceId);
            logger.info("成功清理实例计数器，instanceId: {}", instanceId);
            return true;
        } catch (ContrastBusinessException e) {
            logger.error("清理实例计数器失败，instanceId: {}", instanceId, e);
            return false;
        }
    }
} 