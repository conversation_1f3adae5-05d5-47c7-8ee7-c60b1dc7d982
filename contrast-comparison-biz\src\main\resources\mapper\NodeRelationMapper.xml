<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.NodeRelationMapper">

    <resultMap type="com.ideal.envc.model.entity.NodeRelationEntity" id="NodeRelationResult">
            <result property="id" column="iid"/>
            <result property="envcSystemComputerNodeId" column="ienvc_system_computer_node_id"/>
            <result property="model" column="imodel"/>
            <result property="type" column="itype"/>
            <result property="path" column="ipath"/>
            <result property="sourcePath" column="isource_path"/>
            <result property="encode" column="iencode"/>
            <result property="way" column="iway"/>
            <result property="ruleType" column="irule_type"/>
            <result property="enabled" column="ienabled"/>
            <result property="childLevel" column="ichild_level"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <resultMap type="com.ideal.envc.model.bean.NodeRelationListBean" id="NodeRelationListResult">
        <result property="id" column="iid"/>
        <result property="content" column="irule_content"/>
        <result property="envcSystemComputerNodeId" column="ienvc_system_computer_node_id"/>
        <result property="model" column="imodel"/>
        <result property="type" column="itype"/>
        <result property="path" column="ipath"/>
        <result property="sourcePath" column="isource_path"/>
        <result property="encode" column="iencode"/>
        <result property="way" column="iway"/>
        <result property="ruleType" column="irule_type"/>
        <result property="enabled" column="ienabled"/>
        <result property="childLevel" column="ichild_level"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectNodeRelation">
        select iid, ienvc_system_computer_node_id, imodel, itype, ipath, isource_path, iencode, iway, irule_type, ienabled, ichild_level, icreator_id, icreator_name, icreate_time, iupdator_id, iupdator_name, iupdate_time
        from ieai_envc_node_relation
    </sql>

    <select id="selectNodeRelationList" parameterType="com.ideal.envc.model.entity.NodeRelationEntity" resultMap="NodeRelationListResult">
        SELECT 
            nr.iid,
            nrc.irule_content,
            nr.ienvc_system_computer_node_id,
            nr.imodel,
            nr.itype,
            nr.ipath,
            nr.isource_path,
            nr.iencode,
            nr.iway,
            nr.irule_type,
            nr.ienabled,
            nr.ichild_level,
            nr.icreator_id,
            nr.icreator_name,
            nr.icreate_time,
            nr.iupdator_id,
            nr.iupdator_name,
            nr.iupdate_time
        FROM ieai_envc_node_relation nr
        LEFT JOIN ieai_envc_node_rule_content nrc ON nr.iid = nrc.ienvc_node_relation_id
        <where>
            <if test="id != null">
                AND nr.iid = #{id}
            </if>
            <if test="envcSystemComputerNodeId != null">
                AND nr.ienvc_system_computer_node_id = #{envcSystemComputerNodeId}
            </if>
            <if test="model != null">
                AND nr.imodel = #{model}
            </if>
            <if test="type != null">
                AND nr.itype = #{type}
            </if>
            <if test="path != null and path != ''">
                AND nr.ipath LIKE CONCAT('%', #{path}, '%')
            </if>
            <if test="sourcePath != null and sourcePath != ''">
                AND nr.isource_path LIKE CONCAT('%', #{sourcePath}, '%')
            </if>
            <if test="encode != null and encode != ''">
                AND nr.iencode = #{encode}
            </if>
            <if test="way != null">
                AND nr.iway = #{way}
            </if>
            <if test="ruleType != null">
                AND nr.irule_type = #{ruleType}
            </if>
            <if test="enabled != null">
                AND nr.ienabled = #{enabled}
            </if>
            <if test="childLevel != null">
                AND nr.ichild_level = #{childLevel}
            </if>
            <if test="creatorId != null">
                AND nr.icreator_id = #{creatorId}
            </if>
            <if test="creatorName != null and creatorName != ''">
                AND nr.icreator_name LIKE CONCAT('%', #{creatorName}, '%')
            </if>
            <if test="updatorId != null">
                AND nr.iupdator_id = #{updatorId}
            </if>
            <if test="updatorName != null and updatorName != ''">
                AND nr.iupdator_name LIKE CONCAT('%', #{updatorName}, '%')
            </if>
        </where>
        ORDER BY nr.icreate_time DESC
    </select>

    <select id="selectNodeRelationById" parameterType="Long"
            resultMap="NodeRelationResult">
            <include refid="selectNodeRelation"/>
            where iid = #{id}
    </select>

    <insert id="insertNodeRelation" parameterType="com.ideal.envc.model.entity.NodeRelationEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_node_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="envcSystemComputerNodeId != null">ienvc_system_computer_node_id,
                    </if>
                    <if test="model != null">imodel,
                    </if>
                    <if test="type != null">itype,
                    </if>
                    <if test="path != null">ipath,
                    </if>
                    <if test="sourcePath != null">isource_path,
                    </if>
                    <if test="encode != null">iencode,
                    </if>
                    <if test="way != null">iway,
                    </if>
                    <if test="ruleType != null">irule_type,
                    </if>
                    <if test="enabled != null">ienabled,
                    </if>
                    <if test="childLevel != null">ichild_level,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    icreate_time,
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="envcSystemComputerNodeId != null">#{envcSystemComputerNodeId},
                    </if>
                    <if test="model != null">#{model},
                    </if>
                    <if test="type != null">#{type},
                    </if>
                    <if test="path != null">#{path},
                    </if>
                    <if test="sourcePath != null">#{sourcePath},
                    </if>
                    <if test="encode != null">#{encode},
                    </if>
                    <if test="way != null">#{way},
                    </if>
                    <if test="ruleType != null">#{ruleType},
                    </if>
                    <if test="enabled != null">#{enabled},
                    </if>
                    <if test="childLevel != null">#{childLevel},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateNodeRelation" parameterType="com.ideal.envc.model.entity.NodeRelationEntity">
        update ieai_envc_node_relation
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcSystemComputerNodeId != null">ienvc_system_computer_node_id =
                        #{envcSystemComputerNodeId},
                    </if>
                    <if test="model != null">imodel =
                        #{model},
                    </if>
                    <if test="type != null">itype =
                        #{type},
                    </if>
                    <if test="path != null">ipath =
                        #{path},
                    </if>
                    <if test="sourcePath != null">isource_path = #{sourcePath},</if>
                    <if test="encode != null">iencode =
                        #{encode},
                    </if>
                    <if test="way != null">iway =
                        #{way},
                    </if>
                    <if test="ruleType != null">irule_type =
                        #{ruleType},
                    </if>
                    <if test="enabled != null">ienabled =
                        #{enabled},
                    </if>
                    <if test="childLevel != null">ichild_level =
                        #{childLevel},
                    </if>

                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time =  ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteNodeRelationById" parameterType="Long">
        delete
        from ieai_envc_node_relation where iid = #{id}
    </delete>

    <delete id="deleteNodeRelationByIds" parameterType="String">
        delete from ieai_envc_node_relation where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateNodeRelationEnabledByIds">
        update ieai_envc_node_relation set ienabled = #{enabled}
        where iid in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="deleteNodeRelationBySystemComputerNodeIds">
        delete from ieai_envc_node_relation
        where ienvc_system_computer_node_id in
        <foreach item="systemComputerNodeId" collection="array" open="(" separator="," close=")">
            #{systemComputerNodeId}
        </foreach>
    </delete>

    <select id="selectNodeRelationBySystemComputerNodeIds" parameterType="java.util.List" resultMap="NodeRelationResult">
        select * from ieai_envc_node_relation
        where ienvc_system_computer_node_id in
        <foreach collection="systemComputerNodeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectNodeRelationByIds" parameterType="java.lang.Long" resultMap="NodeRelationResult">
        <include refid="selectNodeRelation"/>
        where iid in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>