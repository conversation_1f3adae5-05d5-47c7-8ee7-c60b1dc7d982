# 响应码和异常处理指南

本文档详细介绍了项目中响应码的定义和使用，以及异常处理的最佳实践。

## 1. 响应码体系

项目中定义了完整的响应码体系，位于 `com.ideal.envc.model.enums.ResponseCodeEnum` 枚举类中。响应码按照业务领域和功能分类进行编码，具有层次化的组织结构。

### 1.1 响应码结构

响应码采用 5 位数字编码，格式为：
- 首位：1表示成功，其他表示失败
- 第二、三位：表示业务模块编码
- 第四、五位：表示具体错误码

例如：
- `10000`：表示操作成功
- `130101`：表示查询类异常中的"数据不存在"

### 1.2 响应码分类

响应码按照功能分为以下几类：

#### 1.2.1 成功响应码

| 响应码 | 描述 |
|-------|------|
| 10000 | 操作成功 |

#### 1.2.2 查询类异常码 (1301xx)

| 响应码 | 描述 |
|-------|------|
| 130100 | 查询失败 |
| 130101 | 数据不存在 |
| 130102 | 查询参数错误 |
| 130103 | 查询超时 |
| 130104 | 查询权限不足 |

#### 1.2.3 新增类异常码 (1302xx)

| 响应码 | 描述 |
|-------|------|
| 130200 | 新增失败 |
| 130201 | 数据已存在 |
| 130202 | 新增参数错误 |
| 130203 | 新增权限不足 |
| 130204 | 批量新增失败 |
| 130205 | 验证错误 |

#### 1.2.4 修改类异常码 (1303xx)

| 响应码 | 描述 |
|-------|------|
| 130300 | 修改失败 |
| 130301 | 数据不存在 |
| 130302 | 修改参数错误 |
| 130303 | 修改权限不足 |
| 130304 | 批量修改失败 |

#### 1.2.5 删除类异常码 (1304xx)

| 响应码 | 描述 |
|-------|------|
| 130400 | 删除失败 |
| 130401 | 数据不存在 |
| 130402 | 删除参数错误 |
| 130403 | 删除权限不足 |
| 130404 | 批量删除失败 |

#### 1.2.6 系统级别异常码 (1399xx)

| 响应码 | 描述 |
|-------|------|
| 139900 | 系统异常 |
| 139901 | 服务不可用 |
| 139902 | 未知异常 |
| 139903 | 网络异常 |
| 139904 | 数据库异常 |
| 139905 | 外部服务调用失败 |
| 139906 | 未授权访问 |
| 139907 | 会话失效 |
| 139908 | 资源受限 |

## 2. ResponseCodeEnum 使用方法

`ResponseCodeEnum` 枚举提供了以下方法：

```java
// 获取响应码
String code = ResponseCodeEnum.SUCCESS.getCode();  // 返回 "10000"

// 获取响应描述
String desc = ResponseCodeEnum.SUCCESS.getDesc();  // 返回 "操作成功"

// 根据响应码获取枚举实例
ResponseCodeEnum enum = ResponseCodeEnum.getByCode("130101");  // 返回 DATA_NOT_FOUND 枚举实例

// 根据响应码获取描述
String desc = ResponseCodeEnum.getDescByCode("130101");  // 返回 "数据不存在"

// 判断响应码是否有效
boolean isValid = ResponseCodeEnum.isValidCode("130101");  // 返回 true
```

## 3. 异常体系

项目采用分层的异常处理机制，清晰区分业务异常和系统异常。

### 3.1 异常类型

项目中主要使用以下几种异常类型：

1. **ContrastBusinessException**：业务异常，用于表示可预见的业务逻辑错误
2. **RuntimeException**：运行时异常，用于表示程序运行中的系统级错误
3. **Exception**：通用异常，作为异常层次的根

### 3.2 异常处理原则

1. **使用业务异常表达业务规则违反**：当业务规则被违反时，抛出 `ContrastBusinessException`
2. **异常信息应明确具体**：异常消息应详细描述问题，便于定位和解决
3. **捕获异常并转换为合适的响应码**：在 Controller 层捕获异常并转换为适当的响应码和消息
4. **记录异常日志**：对所有异常进行日志记录，包含足够的上下文信息

## 4. 异常处理最佳实践

### 4.1 Service 层异常处理

Service 层负责具体业务逻辑处理，应当：

1. **参数验证**：验证输入参数的合法性，对不合法参数抛出业务异常
2. **业务规则验证**：检查业务规则，如数据存在性、唯一性等，违反则抛出业务异常
3. **异常信息明确**：异常信息应包含足够的上下文，如 ID、名称等关键信息
4. **事务管理**：使用 `@Transactional` 注解并指定回滚规则

```java
@Override
@Transactional(rollbackFor = {ContrastBusinessException.class, RuntimeException.class})
public int updatePlan(PlanDto planDto, UserDto userDto) throws ContrastBusinessException {
    if (planDto == null) {
        logger.error("修改方案失败：方案信息为空");
        throw new ContrastBusinessException("方案信息不能为空");
    }
    if (planDto.getId() == null) {
        logger.error("修改方案失败：方案ID为空");
        throw new ContrastBusinessException("方案ID不能为空");
    }

    // 验证方案是否存在
    PlanEntity existingPlan = planMapper.selectPlanById(planDto.getId());
    if (existingPlan == null) {
        logger.error("修改方案失败：方案不存在，ID={}", planDto.getId());
        throw new ContrastBusinessException("方案不存在");
    }

    // 业务逻辑处理...
    try {
        // 业务操作...
    } catch (Exception e) {
        logger.error("修改方案系统异常，ID={}", planDto.getId(), e);
        throw new ContrastBusinessException("修改方案失败：" + e.getMessage());
    }
}
```

### 4.2 Controller 层异常处理

Controller 层负责接收请求并返回响应，应当：

1. **捕获并分类异常**：捕获 Service 层抛出的异常，根据异常类型和消息内容转换为合适的响应码
2. **处理不同类型的异常**：区分业务异常和系统异常，返回不同的响应码
3. **记录异常日志**：记录异常日志，便于问题排查
4. **返回统一格式响应**：使用 `R` 工具类封装响应结果

```java
@PostMapping("/update")
public R<Void> update(@RequestBody PlanDto planDto) {
    try {
        // 获取当前登录用户信息
        UserDto userDto = userinfoComponent.getUser();
        
        int result = planService.updatePlan(planDto, userDto);
        logger.info("修改方案成功，操作人：{}，ID：{}", userDto.getFullName(), planDto.getId());
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
    } catch (ContrastBusinessException e) {
        logger.error("修改方案业务异常：{}", e.getMessage());
        // 根据异常消息判断响应码
        if (e.getMessage().contains("不存在")) {
            return R.fail(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode(), e.getMessage());
        } else if (e.getMessage().contains("已存在")) {
            return R.fail(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode(), e.getMessage());
        } else if (e.getMessage().contains("不能为空")) {
            return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), e.getMessage());
        } else {
            return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
        }
    } catch (Exception e) {
        logger.error("修改方案系统异常", e);
        return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
    }
}
```

### 4.3 统一返回结果 R 类

项目使用 `R<T>` 类统一封装返回结果，包含响应码、响应消息和数据。

```java
// 成功响应
R.ok(ResponseCodeEnum.SUCCESS.getCode(), data, ResponseCodeEnum.SUCCESS.getDesc());

// 失败响应
R.fail(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), ResponseCodeEnum.DATA_NOT_FOUND.getDesc());

// 自定义消息的失败响应
R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), "参数不能为空");
```

## 5. 异常处理场景示例

### 5.1 数据不存在

```java
// Service 层
if (existingPlan == null) {
    logger.error("修改方案失败：方案不存在，ID={}", planDto.getId());
    throw new ContrastBusinessException("方案不存在");
}

// Controller 层
if (e.getMessage().contains("不存在")) {
    return R.fail(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode(), e.getMessage());
}
```

### 5.2 数据已存在

```java
// Service 层
if (count > 0) {
    logger.error("新增方案失败：方案名称已存在 {}", planDto.getName());
    throw new ContrastBusinessException("方案名称已存在，请重新输入");
}

// Controller 层
if (e.getMessage().contains("已存在")) {
    return R.fail(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode(), e.getMessage());
}
```

### 5.3 参数错误

```java
// Service 层
if (planDto == null) {
    logger.error("新增方案失败：方案信息为空");
    throw new ContrastBusinessException("方案信息不能为空");
}

// Controller 层
if (e.getMessage().contains("不能为空")) {
    return R.fail(ResponseCodeEnum.ADD_PARAM_ERROR.getCode(), e.getMessage());
}
```

### 5.4 系统异常

```java
// Service 层
try {
    // 业务操作...
} catch (Exception e) {
    logger.error("新增方案系统异常", e);
    throw new ContrastBusinessException("新增方案失败：" + e.getMessage());
}

// Controller 层
catch (Exception e) {
    logger.error("新增方案系统异常", e);
    return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
}
```

## 6. 日志记录规范

异常处理与日志记录紧密相关，建议遵循以下日志记录规范：

1. **异常前记录关键信息**：在抛出异常前记录足够的上下文信息
2. **区分日志级别**：使用适当的日志级别（ERROR、WARN、INFO）
3. **系统异常需包含堆栈**：记录系统异常时包含完整堆栈信息
4. **关键操作的日志**：记录关键业务操作的日志，包括操作类型、操作人、关键数据等

```java
// 错误日志
logger.error("修改方案失败：方案不存在，ID={}", planDto.getId());

// 系统异常日志（包含异常堆栈）
logger.error("修改方案系统异常，ID={}", planDto.getId(), e);

// 成功操作日志
logger.info("修改方案成功，ID={}，名称={}，操作人：{}", plan.getId(), plan.getName(), userDto.getFullName());
```

## 7. 总结

合理的响应码体系和异常处理机制对于构建健壮的系统至关重要。通过遵循本文档中的最佳实践，可以提高系统的可维护性、用户体验和问题排查效率。 