package com.ideal.envc.service.impl;

import com.ideal.envc.component.TaskCounterComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.service.IRunInstanceStateProcessService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.InjectMocks;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RunInstanceInfoStateProcessServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RunInstanceInfoStateProcessServiceImpl单元测试")
class RunInstanceInfoStateProcessServiceImplTest {

    @Mock
    private RunInstanceInfoMapper runInstanceInfoMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private TaskCounterComponent taskCounterComponent;

    @Mock
    private IRunInstanceStateProcessService runInstanceStateProcessService;

    @InjectMocks
    private RunInstanceInfoStateProcessServiceImpl runInstanceInfoStateProcessService;

    @BeforeEach
    void setUp() {
        runInstanceInfoStateProcessService = new RunInstanceInfoStateProcessServiceImpl(
            runInstanceInfoMapper,
            runRuleMapper,
            taskCounterComponent,
            runInstanceStateProcessService
        );
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 参数为空")
    void processInstanceInfoStateUpdate_NullParams() {
        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(null, null, null);

        // 验证结果
        assertFalse(result);
        verify(runInstanceInfoMapper, never()).selectRunInstanceInfoById(any());
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 实例详情不存在")
    void processInstanceInfoStateUpdate_InstanceInfoNotFound() throws ContrastBusinessException {
        // 准备测试数据
        Long instanceInfoId = 1L;
        Long ruleId = 1L;
        Long messageTimestamp = System.currentTimeMillis();

        // Mock依赖
        when(runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId)).thenReturn(null);

        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(instanceInfoId, ruleId, messageTimestamp);

        // 验证结果
        assertTrue(result);
        verify(runInstanceInfoMapper).selectRunInstanceInfoById(instanceInfoId);
        verify(taskCounterComponent, never()).decrementInstanceInfoCounter(any());
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 消息过期")
    void processInstanceInfoStateUpdate_MessageExpired() throws ContrastBusinessException {
        // 准备测试数据
        Long instanceInfoId = 1L;
        Long ruleId = 1L;
        Long messageTimestamp = System.currentTimeMillis() - 40000; // 消息已过期
        RunInstanceInfoEntity instanceInfo = createRunInstanceInfoEntity(instanceInfoId);

        // Mock依赖
        when(runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId)).thenReturn(instanceInfo);

        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(instanceInfoId, ruleId, messageTimestamp);

        // 验证结果
        assertTrue(result);
        verify(runInstanceInfoMapper).selectRunInstanceInfoById(instanceInfoId);
        verify(taskCounterComponent, never()).decrementInstanceInfoCounter(any());
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 计数器未归零")
    void processInstanceInfoStateUpdate_CounterNotZero() throws ContrastBusinessException {
        // 准备测试数据
        Long instanceInfoId = 1L;
        Long ruleId = 1L;
        Long messageTimestamp = System.currentTimeMillis();
        RunInstanceInfoEntity instanceInfo = createRunInstanceInfoEntity(instanceInfoId);

        // Mock依赖 - 允许多次调用
        when(runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId)).thenReturn(instanceInfo);
        // 计数器返回1，表示未归零
        when(taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId)).thenReturn(1L);
        
        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(instanceInfoId, ruleId, messageTimestamp);

        // 验证结果
        assertTrue(result);
        // 修改验证：允许多次调用
        verify(runInstanceInfoMapper, atLeastOnce()).selectRunInstanceInfoById(instanceInfoId);
        verify(taskCounterComponent).decrementInstanceInfoCounter(instanceInfoId);
        // 移除对 tryBusinessLock 的验证
        verify(taskCounterComponent, never()).getBusinessLock(any(), anyLong());
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 计数器归零且状态需要更新")
    void processInstanceInfoStateUpdate_CounterZeroAndStateChanged() throws ContrastBusinessException, InterruptedException {
        // 准备测试数据
        Long instanceInfoId = 1L;
        Long ruleId = 1L;
        Long messageTimestamp = System.currentTimeMillis();
        RunInstanceInfoEntity instanceInfo = createRunInstanceInfoEntity(instanceInfoId);
        RunRuleEntity ruleEntity = createRunRuleEntity(ruleId);

        // Mock依赖 - 允许多次调用selectRunInstanceInfoById
        when(runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId)).thenReturn(instanceInfo);
        // 计数器返回0，表示已归零
        when(taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId)).thenReturn(0L);
        // 模拟锁对象和锁获取
        org.redisson.api.RLock mockLock = mock(org.redisson.api.RLock.class);
        when(taskCounterComponent.getBusinessLock(anyString(), anyLong())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any())).thenReturn(true);
        // 重新检查计数器值仍为0
        when(taskCounterComponent.getInstanceInfoCounterValue(instanceInfoId)).thenReturn(0L);
        // 查询规则
        when(runRuleMapper.selectRulesByInstanceInfoId(instanceInfoId))
            .thenReturn(Collections.singletonList(ruleEntity));
        // 更新状态成功
        when(runInstanceInfoMapper.updateStateAndResultByIdAndTimestamp(eq(instanceInfoId), anyInt(), anyInt()))
            .thenReturn(1);
        // 上层处理成功
        when(runInstanceStateProcessService.processInstanceStateUpdate(eq(instanceInfo.getEnvcRunInstanceId()), eq(instanceInfoId), any()))
            .thenReturn(true);

        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(instanceInfoId, ruleId, messageTimestamp);

        // 验证结果
        assertTrue(result);
        // 允许多次调用selectRunInstanceInfoById
        verify(runInstanceInfoMapper, times(2)).selectRunInstanceInfoById(instanceInfoId);
        verify(taskCounterComponent).decrementInstanceInfoCounter(instanceInfoId);
        verify(taskCounterComponent).getBusinessLock(anyString(), anyLong());
        verify(mockLock).tryLock(anyLong(), anyLong(), any());
        verify(taskCounterComponent).getInstanceInfoCounterValue(instanceInfoId);
        verify(runRuleMapper).selectRulesByInstanceInfoId(instanceInfoId);
        verify(runInstanceInfoMapper).updateStateAndResultByIdAndTimestamp(eq(instanceInfoId), anyInt(), anyInt());
        verify(taskCounterComponent).clearInstanceInfoCounter(instanceInfoId);
        verify(runInstanceStateProcessService).processInstanceStateUpdate(eq(instanceInfo.getEnvcRunInstanceId()), eq(instanceInfoId), any());
        verify(mockLock).unlock();
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 计数器归零但状态未变化")
    void processInstanceInfoStateUpdate_CounterZeroButStateUnchanged() throws ContrastBusinessException, InterruptedException {
        // 准备测试数据
        Long instanceInfoId = 1L;
        Long ruleId = 1L;
        Long messageTimestamp = System.currentTimeMillis();
        RunInstanceInfoEntity instanceInfo = createRunInstanceInfoEntity(instanceInfoId);
        instanceInfo.setState(1);
        instanceInfo.setResult(0);
        RunRuleEntity ruleEntity = createRunRuleEntity(ruleId);
        ruleEntity.setState(1);
        ruleEntity.setResult(0);

        // Mock依赖 - 允许多次调用selectRunInstanceInfoById
        when(runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId)).thenReturn(instanceInfo);
        // 计数器返回0，表示已归零
        when(taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId)).thenReturn(0L);
        // 模拟锁对象和锁获取
        org.redisson.api.RLock mockLock = mock(org.redisson.api.RLock.class);
        when(taskCounterComponent.getBusinessLock(anyString(), anyLong())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any())).thenReturn(true);
        // 重新检查计数器值仍为0
        when(taskCounterComponent.getInstanceInfoCounterValue(instanceInfoId)).thenReturn(0L);
        // 查询规则
        when(runRuleMapper.selectRulesByInstanceInfoId(instanceInfoId))
            .thenReturn(Collections.singletonList(ruleEntity));
        // 上层处理成功
        when(runInstanceStateProcessService.processInstanceStateUpdate(eq(instanceInfo.getEnvcRunInstanceId()), eq(instanceInfoId), any()))
            .thenReturn(true);

        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(instanceInfoId, ruleId, messageTimestamp);

        // 验证结果
        assertTrue(result);
        // 允许多次调用selectRunInstanceInfoById
        verify(runInstanceInfoMapper, times(2)).selectRunInstanceInfoById(instanceInfoId);
        verify(taskCounterComponent).decrementInstanceInfoCounter(instanceInfoId);
        verify(taskCounterComponent).getBusinessLock(anyString(), anyLong());
        verify(mockLock).tryLock(anyLong(), anyLong(), any());
        verify(taskCounterComponent).getInstanceInfoCounterValue(instanceInfoId);
        verify(runRuleMapper).selectRulesByInstanceInfoId(instanceInfoId);
        verify(taskCounterComponent).clearInstanceInfoCounter(instanceInfoId);
        verify(runInstanceStateProcessService).processInstanceStateUpdate(eq(instanceInfo.getEnvcRunInstanceId()), eq(instanceInfoId), any());
        verify(mockLock).unlock();
        verify(runInstanceInfoMapper, never()).updateStateAndResultByIdAndTimestamp(any(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 无法获取业务锁")
    void processInstanceInfoStateUpdate_CannotGetLock() throws ContrastBusinessException {
        // 准备测试数据
        Long instanceInfoId = 1L;
        Long ruleId = 1L;
        Long messageTimestamp = System.currentTimeMillis();
        RunInstanceInfoEntity instanceInfo = createRunInstanceInfoEntity(instanceInfoId);

        // Mock依赖 - 允许多次调用selectRunInstanceInfoById
        when(runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId)).thenReturn(instanceInfo);
        // 计数器返回0，表示已归零
        when(taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId)).thenReturn(0L);
        // 模拟锁对象返回null
        when(taskCounterComponent.getBusinessLock(anyString(), anyLong())).thenReturn(null);

        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(instanceInfoId, ruleId, messageTimestamp);

        // 验证结果
        assertFalse(result);  // 当获取锁失败时，应该返回false
        // 允许多次调用selectRunInstanceInfoById
        verify(runInstanceInfoMapper, times(2)).selectRunInstanceInfoById(instanceInfoId);
        verify(taskCounterComponent).decrementInstanceInfoCounter(instanceInfoId);
        verify(taskCounterComponent).getBusinessLock(anyString(), anyLong());
        verify(taskCounterComponent, never()).getInstanceInfoCounterValue(any());
    }

    @Test
    @DisplayName("计算实例详情状态和结果 - 成功场景")
    void calculateInstanceInfoStateAndResult_Success() {
        // 准备测试数据
        Long instanceInfoId = 1L;
        RunRuleEntity rule1 = createRunRuleEntity(1L);
        rule1.setState(1);
        rule1.setResult(0);
        RunRuleEntity rule2 = createRunRuleEntity(2L);
        rule2.setState(2);
        rule2.setResult(1);

        // Mock依赖
        when(runRuleMapper.selectRulesByInstanceInfoId(instanceInfoId))
            .thenReturn(Arrays.asList(rule1, rule2));

        // 执行测试
        int[] result = runInstanceInfoStateProcessService.calculateInstanceInfoStateAndResult(instanceInfoId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals(2, result[0]); // 取最大状态值
        assertEquals(1, result[1]); // 取最大结果值
        verify(runRuleMapper).selectRulesByInstanceInfoId(instanceInfoId);
    }

    @Test
    @DisplayName("更新实例详情状态和结果 - 成功场景")
    void updateInstanceInfoStateAndResult_Success() {
        // 准备测试数据
        Long instanceInfoId = 1L;
        int state = 1;
        int result = 0;
        Long timestamp = System.currentTimeMillis();

        // Mock依赖
        when(runInstanceInfoMapper.updateStateAndResultByIdAndTimestamp(instanceInfoId, state, result))
            .thenReturn(1);

        // 执行测试
        boolean success = runInstanceInfoStateProcessService.updateInstanceInfoStateAndResult(instanceInfoId, state, result, timestamp);

        // 验证结果
        assertTrue(success);
        verify(runInstanceInfoMapper).updateStateAndResultByIdAndTimestamp(instanceInfoId, state, result);
    }

    @Test
    @DisplayName("清理实例详情计数器 - 成功场景")
    void clearInstanceInfoCounter_Success() throws ContrastBusinessException {
        // 准备测试数据
        Long instanceInfoId = 1L;

        // 执行测试
        boolean result = runInstanceInfoStateProcessService.clearInstanceInfoCounter(instanceInfoId);

        // 验证结果
        assertTrue(result);
        verify(taskCounterComponent).clearInstanceInfoCounter(instanceInfoId);
    }

    @Test
    @DisplayName("获取当前时间戳 - 使用实体更新时间")
    void getCurrentTimestamp_WithUpdateTime() {
        // 准备测试数据
        RunInstanceInfoEntity instanceInfo = new RunInstanceInfoEntity();
        Timestamp updateTime = new Timestamp(System.currentTimeMillis());
        instanceInfo.setUpdateTime(updateTime);
        
        // 使用反射调用私有方法
        Long result = runInstanceInfoStateProcessService.getCurrentTimestamp(instanceInfo);
        
        // 验证结果
        assertEquals(updateTime.getTime(), result);
    }
    
    @Test
    @DisplayName("获取当前时间戳 - 无实体更新时间")
    void getCurrentTimestamp_WithoutUpdateTime() {
        // 准备测试数据
        RunInstanceInfoEntity instanceInfo = new RunInstanceInfoEntity();
        
        // 使用反射调用私有方法
        Long result = runInstanceInfoStateProcessService.getCurrentTimestamp(instanceInfo);
        
        // 验证结果
        assertTrue(result > 0); // 应该返回当前系统时间
    }
    
    @Test
    @DisplayName("检查消息是否过期 - 已过期")
    void isMessageExpired_True() {
        // 准备测试数据
        Long currentTimestamp = System.currentTimeMillis();
        Long messageTimestamp = currentTimestamp - 40000; // 消息已过期
        
        // 使用反射调用私有方法
        boolean result = runInstanceInfoStateProcessService.isMessageExpired(messageTimestamp, currentTimestamp);
        
        // 验证结果
        assertTrue(result);
    }
    
    @Test
    @DisplayName("检查消息是否过期 - 未过期")
    void isMessageExpired_False() {
        // 准备测试数据
        Long currentTimestamp = System.currentTimeMillis();
        Long messageTimestamp = currentTimestamp - 10000; // 消息未过期
        
        // 使用反射调用私有方法
        boolean result = runInstanceInfoStateProcessService.isMessageExpired(messageTimestamp, currentTimestamp);
        
        // 验证结果
        assertFalse(result);
    }
    
    @Test
    @DisplayName("检查状态是否未变化 - 状态相同")
    void isStateUnchanged_True() {
        // 准备测试数据
        RunInstanceInfoEntity instanceInfo = new RunInstanceInfoEntity();
        instanceInfo.setState(1);
        instanceInfo.setResult(0);
        int status = 1;
        int resultVal = 0;
        
        // 使用反射调用私有方法
        boolean result = runInstanceInfoStateProcessService.isStateUnchanged(instanceInfo, status, resultVal);
        
        // 验证结果
        assertTrue(result);
    }
    
    @Test
    @DisplayName("检查状态是否未变化 - 状态不同")
    void isStateUnchanged_False() {
        // 准备测试数据
        RunInstanceInfoEntity instanceInfo = new RunInstanceInfoEntity();
        instanceInfo.setState(0);
        instanceInfo.setResult(0);
        int status = 1;
        int resultVal = 0;
        
        // 使用反射调用私有方法
        boolean result = runInstanceInfoStateProcessService.isStateUnchanged(instanceInfo, status, resultVal);
        
        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("处理实例详情状态更新 - 获取锁对象成功但tryLock失败")
    void processInstanceInfoStateUpdate_TryLockFails() throws ContrastBusinessException, InterruptedException {
        // 准备测试数据
        Long instanceInfoId = 1L;
        Long ruleId = 1L;
        Long messageTimestamp = System.currentTimeMillis();
        RunInstanceInfoEntity instanceInfo = createRunInstanceInfoEntity(instanceInfoId);

        // Mock依赖
        when(runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId)).thenReturn(instanceInfo);
        when(taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId)).thenReturn(0L);
        
        // 模拟锁对象获取成功但tryLock失败
        org.redisson.api.RLock mockLock = mock(org.redisson.api.RLock.class);
        when(taskCounterComponent.getBusinessLock(anyString(), anyLong())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any())).thenReturn(false);

        // 执行测试
        boolean result = runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(instanceInfoId, ruleId, messageTimestamp);

        // 验证结果
        assertFalse(result);
        verify(runInstanceInfoMapper, times(2)).selectRunInstanceInfoById(instanceInfoId);
        verify(taskCounterComponent).decrementInstanceInfoCounter(instanceInfoId);
        verify(taskCounterComponent).getBusinessLock(anyString(), anyLong());
        verify(mockLock).tryLock(anyLong(), anyLong(), any());
        verify(taskCounterComponent, never()).getInstanceInfoCounterValue(any());
        verify(mockLock, never()).unlock();
    }

    private RunInstanceInfoEntity createRunInstanceInfoEntity(Long id) {
        RunInstanceInfoEntity entity = new RunInstanceInfoEntity();
        entity.setId(id);
        entity.setEnvcRunInstanceId(id + 100L); // 设置一个不同的实例ID
        entity.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        return entity;
    }

    private RunRuleEntity createRunRuleEntity(Long id) {
        RunRuleEntity entity = new RunRuleEntity();
        entity.setId(id);
        entity.setState(1);
        entity.setResult(0);
        return entity;
    }
} 