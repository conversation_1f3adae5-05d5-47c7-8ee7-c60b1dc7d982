package com.ideal.envc.service;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件比较服务接口
 *
 * <AUTHOR>
 */
public interface IFileComparisonService {

    /**
     * 比较两个文件内容字符串
     *
     * @param request 比较请求参数
     * @return 比较结果
     */
    FileComparisonResultDto compareFileContents(FileComparisonRequestDto request) throws ContrastBusinessException;

    /**
     * 导出比较结果到Excel
     *
     * @param request 比较请求参数
     * @param response HTTP响应对象
     */
    void exportComparisonResult(FileComparisonRequestDto request, HttpServletResponse response) throws ContrastBusinessException;


    /**
     * 导出比较结果到Excel（带IP和主机名信息）
     *
     * @param result 比较结果
     * @param response HTTP响应对象
     */
    void exportComparisonResult(FileComparisonRequestDto request, FileComparisonResultDto result, HttpServletResponse response) throws ContrastBusinessException;
}
