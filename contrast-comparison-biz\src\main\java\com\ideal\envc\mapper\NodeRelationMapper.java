package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.NodeRelationEntity;
import com.ideal.envc.model.bean.NodeRelationListBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 节点关系规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Mapper
public interface NodeRelationMapper {
    /**
     * 查询节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 节点关系规则
     */
    NodeRelationEntity selectNodeRelationById(Long id);

    /**
     * 查询节点关系规则列表
     *
     * @param nodeRelation 查询条件
     * @return 节点关系规则列表
     */
    List<NodeRelationListBean> selectNodeRelationList(NodeRelationEntity nodeRelation);

    /**
     * 新增节点关系规则
     *
     * @param nodeRelation 节点关系规则
     * @return 结果
     */
    int insertNodeRelation(NodeRelationEntity nodeRelation);

    /**
     * 修改节点关系规则
     *
     * @param nodeRelation 节点关系规则
     * @return 结果
     */
    int updateNodeRelation(NodeRelationEntity nodeRelation);

    /**
     * 删除节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 结果
     */
    int deleteNodeRelationById(Long id);

    /**
     * 批量删除节点关系规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteNodeRelationByIds(Long[] ids);

    /**
     * 批量启用或禁用节点关系规则
     *
     * @param ids 节点关系规则ID集合
     * @param enabled 启用状态（0：启用，1：禁用）
     * @return 结果
     */
    int updateNodeRelationEnabledByIds(Long[] ids, Integer enabled);

    /**
     * 根据系统设备节点 ID 集合批量删除节点关系规则
     *
     * @param systemComputerNodeIds 系统设备节点 ID 集合
     * @return 结果
     */
    int deleteNodeRelationBySystemComputerNodeIds(Long[] systemComputerNodeIds);

    /**
     * 根据系统与设备节点ID集合批量查询节点关系规则
     * @param systemComputerNodeIds 系统与设备节点ID集合
     * @return 节点关系规则集合
     */
    List<NodeRelationEntity> selectNodeRelationBySystemComputerNodeIds(List<Long> systemComputerNodeIds);

    /**
     * 根据ID集合批量查询节点关系规则
     *
     * @param ids 节点关系规则ID集合
     * @return 节点关系规则集合
     */
    List<NodeRelationEntity> selectNodeRelationByIds(Long[] ids);

}
