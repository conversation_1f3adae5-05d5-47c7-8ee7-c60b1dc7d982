package com.ideal.envc;


import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.ideal.envc"},exclude = {QuartzAutoConfiguration.class})
@EnableDubbo
@ConfigurationPropertiesScan({"com.ideal.envc.config"})
@MapperScan(basePackages = { "com.ideal.envc.**.mapper","com.ideal.monitor.**.mapper"})
public class Bootstrap {
    private static final Logger logger = LoggerFactory.getLogger(Bootstrap.class);


    public static void main(String[] args) {
        SpringApplication.run(Bootstrap.class, args);
        logger.info(" Contrast-comparison Server is already start successfully");
    }
}