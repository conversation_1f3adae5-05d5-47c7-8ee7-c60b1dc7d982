package com.ideal.envc;


import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.ConfigurableApplicationContext;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Contrast-comparison应用启动类
 * 提供完善的异常处理机制和启动状态监控
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.ideal.envc"},exclude = {QuartzAutoConfiguration.class})
@EnableDubbo
@ConfigurationPropertiesScan({"com.ideal.envc.config"})
@MapperScan(basePackages = { "com.ideal.envc.**.mapper","com.ideal.monitor.**.mapper"})
public class Bootstrap {
    private static final Logger logger = LoggerFactory.getLogger(Bootstrap.class);

    /**
     * 应用启动退出码
     */
    private static final int SUCCESS_EXIT_CODE = 0;
    private static final int FAILURE_EXIT_CODE = 1;

    public static void main(String[] args) {
        // 记录启动开始时间
        long startTime = System.currentTimeMillis();

        // 打印启动信息
        printStartupInfo();

        ConfigurableApplicationContext context = null;
        try {
            // 执行启动前检查
            performPreStartupChecks();

            logger.info("正在启动 Contrast-comparison Server...");

            // 启动Spring Boot应用
            context = SpringApplication.run(Bootstrap.class, args);

            // 计算启动耗时
            long duration = System.currentTimeMillis() - startTime;

            // 打印启动成功信息
            printStartupSuccessInfo(context, duration);

            // 注册优雅关闭钩子
            registerShutdownHook(context);

        } catch (Exception e) {
            // 处理启动异常
            handleStartupException(e, startTime);

            // 清理资源
            if (context != null) {
                try {
                    context.close();
                } catch (Exception closeException) {
                    logger.error("关闭应用上下文时发生异常", closeException);
                }
            }

            // 异常退出
            System.exit(FAILURE_EXIT_CODE);
        }
    }

    /**
     * 打印启动信息
     */
    private static void printStartupInfo() {
        logger.info("========================================");
        logger.info("    Contrast-comparison Server 启动中");
        logger.info("========================================");
        logger.info("Java版本: {}", System.getProperty("java.version"));
        logger.info("操作系统: {} {}", System.getProperty("os.name"), System.getProperty("os.version"));
        logger.info("JVM参数: {}", String.join(" ", ManagementFactory.getRuntimeMXBean().getInputArguments()));

        try {
            logger.info("主机名: {}", InetAddress.getLocalHost().getHostName());
            logger.info("IP地址: {}", InetAddress.getLocalHost().getHostAddress());
        } catch (UnknownHostException e) {
            logger.warn("无法获取主机信息: {}", e.getMessage());
        }
    }

    /**
     * 执行启动前检查
     */
    private static void performPreStartupChecks() {
        logger.info("执行启动前环境检查...");

        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        if (javaVersion.startsWith("1.8") || javaVersion.startsWith("8")) {
            logger.info("Java版本检查通过: {}", javaVersion);
        } else {
            logger.warn("当前Java版本为: {}，推荐使用JDK 1.8", javaVersion);
        }

        // 检查内存
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();

        logger.info("内存信息 - 最大内存: {}MB, 总内存: {}MB, 可用内存: {}MB",
                   maxMemory / 1024 / 1024, totalMemory / 1024 / 1024, freeMemory / 1024 / 1024);

        if (maxMemory < 512 * 1024 * 1024) { // 小于512MB
            logger.warn("可用内存较少，建议增加JVM堆内存设置");
        }

        logger.info("启动前环境检查完成");
    }

    /**
     * 打印启动成功信息
     */
    private static void printStartupSuccessInfo(ConfigurableApplicationContext context, long duration) {
        logger.info("========================================");
        logger.info("  Contrast-comparison Server 启动成功!");
        logger.info("========================================");
        logger.info("启动耗时: {}ms", duration);
        logger.info("应用上下文ID: {}", context.getId());
        logger.info("活跃配置文件: {}", String.join(",", context.getEnvironment().getActiveProfiles()));

        // 获取服务器端口信息
        try {
            String port = context.getEnvironment().getProperty("server.port", "8080");
            String contextPath = context.getEnvironment().getProperty("server.servlet.context-path", "");
            logger.info("服务端口: {}", port);
            logger.info("上下文路径: {}", contextPath.isEmpty() ? "/" : contextPath);
            logger.info("访问地址: http://localhost:{}{}", port, contextPath);
        } catch (Exception e) {
            logger.warn("无法获取服务器端口信息: {}", e.getMessage());
        }

        logger.info("应用已准备就绪，可以接收请求");
    }

    /**
     * 处理启动异常
     */
    private static void handleStartupException(Exception e, long startTime) {
        long duration = System.currentTimeMillis() - startTime;

        logger.error("========================================");
        logger.error("  Contrast-comparison Server 启动失败!");
        logger.error("========================================");
        logger.error("启动失败耗时: {}ms", duration);
        logger.error("失败原因: {}", e.getMessage());

        // 根据异常类型提供具体的解决建议
        String exceptionType = e.getClass().getSimpleName();
        switch (exceptionType) {
            case "BindException":
                logger.error("端口绑定失败，请检查端口是否被占用或修改配置文件中的端口设置");
                break;
            case "ConnectException":
                logger.error("连接失败，请检查数据库、Redis等外部服务是否正常运行");
                break;
            case "ClassNotFoundException":
                logger.error("类未找到，请检查依赖包是否完整");
                break;
            case "BeanCreationException":
                logger.error("Bean创建失败，请检查配置文件和依赖注入设置");
                break;
            case "ConfigurationPropertiesBindException":
                logger.error("配置属性绑定失败，请检查配置文件格式和属性设置");
                break;
            default:
                logger.error("启动过程中发生未知异常，请查看详细错误信息");
                break;
        }

        // 打印详细的异常堆栈
        logger.error("详细错误信息:", e);

        logger.error("应用启动失败，程序即将退出");
    }

    /**
     * 注册优雅关闭钩子
     */
    private static void registerShutdownHook(final ConfigurableApplicationContext context) {
        Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
            @Override
            public void run() {
                logger.info("接收到关闭信号，正在优雅关闭应用...");
                try {
                    if (context != null && context.isActive()) {
                        context.close();
                        logger.info("应用已优雅关闭");
                    }
                } catch (Exception e) {
                    logger.error("关闭应用时发生异常", e);
                }
            }
        }, "shutdown-hook"));

        logger.info("优雅关闭钩子已注册");
    }
}