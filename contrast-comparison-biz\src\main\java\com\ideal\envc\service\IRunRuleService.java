package com.ideal.envc.service;

import com.ideal.envc.model.dto.RunRuleDto;
import com.ideal.envc.model.dto.RunRuleQueryDto;
import com.github.pagehelper.PageInfo;

/**
 * 节点规则结果Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IRunRuleService {
    /**
     * 查询节点规则结果
     *
     * @param id 节点规则结果主键
     * @return 节点规则结果
     */
    RunRuleDto selectRunRuleById(Long id);

    /**
     * 查询节点规则结果列表
     *
     * @param runRuleQueryDto 节点规则结果
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点规则结果集合
     */
    PageInfo<RunRuleDto> selectRunRuleList(RunRuleQueryDto runRuleQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增节点规则结果
     *
     * @param runRuleDto 节点规则结果
     * @return 结果
     */
    int insertRunRule(RunRuleDto runRuleDto);

    /**
     * 修改节点规则结果
     *
     * @param runRuleDto 节点规则结果
     * @return 结果
     */
    int updateRunRule(RunRuleDto runRuleDto);

    /**
     * 批量删除节点规则结果
     *
     * @param ids 需要删除的节点规则结果主键集合
     * @return 结果
     */
    int deleteRunRuleByIds(Long[] ids);
}
